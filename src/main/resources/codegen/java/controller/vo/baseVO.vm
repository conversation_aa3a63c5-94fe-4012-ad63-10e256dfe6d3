package ${basePackage}.module.${table.moduleName}.controller.${sceneEnum.basePackage}.${table.businessName}.vo;

import lombok.*;

import jakarta.validation.constraints.*;
import java.io.Serializable;
###foreach ($column in $columns)
###if (${column.javaType} == "BigDecimal")
##import java.math.BigDecimal;
###break
###end
###end
#### 处理 Date 字段的引入
###foreach ($column in $columns)
###if (${column.aditOperation} && ${column.javaType} == "LocalDateTime")## 时间类型
##import java.time.LocalDateTime;
##import org.springframework.format.annotation.DateTimeFormat;
##import static ${DateUtilsClassName}.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
###break
###end
###end
###foreach ($column in $columns)
##    #if (${column.aditOperation} && ${column.javaType} == "LocalDate")
##import org.springframework.format.annotation.DateTimeFormat;
##import java.time.LocalDate;
##import static ${DateUtilsClassName}.FORMAT_yyyy_MM_dd;
##        #break
##    #end
###end
###foreach ($column in $columns)
##    #if (${column.aditOperation} && ${column.javaType} == "JSONObject")
##import com.alibaba.fastjson.JSONObject;
##        #break
##    #end
###end

###parse("codegen/java/controller/vo/_import.vm")
#set($importedPackages = []) ## 使用列表跟踪已导入的包
#set($isDateTimeFormatImported = false) ## 标志变量，跟踪 DateTimeFormat 是否已导入

#foreach ($column in $columns)
    #if (${column.aditOperation})##通用字段
        #if (${column.javaType} == "BigDecimal")
            #if (!($importedPackages.contains("java.math.BigDecimal")))
import java.math.BigDecimal;
                #set($importedPackages = $importedPackages.add("java.math.BigDecimal")) ## 记录已导入的包
            #end
        #elseif (${column.javaType} == "LocalDateTime") ## 时间类型
            #if (!$isDateTimeFormatImported)
import org.springframework.format.annotation.DateTimeFormat;
                #set($isDateTimeFormatImported = true) ## 设置为已导入
            #end
import java.time.LocalDateTime;
import static ${DateUtilsClassName}.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
            #if (!($importedPackages.contains("java.time.LocalDateTime")))
                #set($importedPackages = $importedPackages.add("java.time.LocalDateTime"))
            #end
        #elseif (${column.javaType} == "LocalDate") ## 时间类型
            #if (!$isDateTimeFormatImported)
import org.springframework.format.annotation.DateTimeFormat;
                #set($isDateTimeFormatImported = true) ## 设置为已导入
            #end
import java.time.LocalDate;
import static ${DateUtilsClassName}.FORMAT_yyyy_MM_dd;
            #if (!($importedPackages.contains("java.time.LocalDate")))
                #set($importedPackages = $importedPackages.add("java.time.LocalDate"))
            #end
        #elseif (${column.javaType} == "JSONObject")
            #if (!($importedPackages.contains("com.alibaba.fastjson.JSONObject")))
import com.alibaba.fastjson.JSONObject;
                #set($importedPackages = $importedPackages.add("com.alibaba.fastjson.JSONObject"))
            #end
        #end
    #end
#end

/**
 * ${table.classComment} Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class ${sceneEnum.prefixClass}${table.className}BaseVO implements Serializable {

#foreach ($column in $columns)
#if (${column.aditOperation})##通用操作
    #parse("codegen/java/controller/vo/_column.vm")

#end
#end
}
