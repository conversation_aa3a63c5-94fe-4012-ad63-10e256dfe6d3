package ${basePackage}.module.${table.moduleName}.controller.${sceneEnum.basePackage}.${table.businessName}.vo;

import lombok.*;

#parse("codegen/java/controller/vo/_import.vm")
###set($importedPackages = {}) ## 使用集合跟踪已导入的包
###set($isDateTimeFormatImported = false) ## 标志变量，跟踪 DateTimeFormat 是否已导入
##
###foreach ($column in $columns)
##    #if (${column.listOperationResult} && (!${column.aditOperation})) ## 不是通用字段
##        #set($packageToImport = "")
##
##        #if (${column.javaType} == "BigDecimal")
##            #set($packageToImport = "java.math.BigDecimal")
##        #elseif (${column.javaType} == "LocalDateTime") ## 时间类型
##            #set($packageToImport = "java.time.LocalDateTime")
##            #if (!$isDateTimeFormatImported)
##import org.springframework.format.annotation.DateTimeFormat;
##                #set($isDateTimeFormatImported = true) ## 设置为已导入
##            #end
##        #elseif (${column.javaType} == "LocalDate") ## 时间类型
##            #set($packageToImport = "java.time.LocalDate")
##            #if (!$isDateTimeFormatImported)
##import org.springframework.format.annotation.DateTimeFormat;
##                #set($isDateTimeFormatImported = true) ## 设置为已导入
##            #end
##        #elseif (${column.javaType} == "JSONObject")
##            #set($packageToImport = "com.alibaba.fastjson.JSONObject")
##        #end
##
##        ## 检查并导入包
##        #if ($packageToImport && !$importedPackages.contains($packageToImport))
##import ${packageToImport};
##            #set($importedPackages[$packageToImport] = true) ## 记录已导入的包
##        #end
##
##        ## 引入日期格式常量
##        #if (${column.javaType} == "LocalDateTime")
##import static ${DateUtilsClassName}.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
##        #elseif (${column.javaType} == "LocalDate")
##import static ${DateUtilsClassName}.FORMAT_yyyy_MM_dd;
##        #end
##    #end
###end


/**
 * ${table.classComment} RespVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ${sceneEnum.prefixClass}${table.className}RespVO extends ${sceneEnum.prefixClass}${table.className}BaseVO {

#foreach ($column in $columns)
#if (${column.listOperationResult} && (!${column.aditOperation}))##不是通用字段
    /** ${column.columnComment} */
    #if (${column.javaType} == "LocalDateTime")## 时间类型
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    #end
    #if (${column.javaType} == "LocalDate")## 时间类型
    @DateTimeFormat(pattern = FORMAT_yyyy_MM_dd)
    #end
    private ${column.javaType} ${column.javaField};

#end
#end
##    /** 创建时间 */
##    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
##    private LocalDateTime createdDt;
##
##    /**
##    * 更新时间
##    */
##    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
##    private LocalDateTime updatedDt;
##    /**
##    * 创建人
##    */
##    private String createdBy;
##    /**
##    * 更新人
##    */
##    private String updatedBy;
}
