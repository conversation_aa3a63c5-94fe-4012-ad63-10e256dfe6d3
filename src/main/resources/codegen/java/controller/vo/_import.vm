## 特殊字段导入

#set($importedPackages = []) ## 使用列表跟踪已导入的包
#set($isDateTimeFormatImported = false) ## 标志变量，跟踪 DateTimeFormat 是否已导入
#set($isLocalDateImported = false) ## 标志变量，跟踪 LocalDate 是否已导入
#set($isLocalDateTimeImported = false) ## 标志变量，跟踪 LocalDateTime 是否已导入

#foreach ($column in $columns)
    #if (${column.javaType} == "BigDecimal")
        #if (!($importedPackages.contains("java.math.BigDecimal")))
import java.math.BigDecimal;
            #set($importedPackages = $importedPackages.add("java.math.BigDecimal")) ## 记录已导入的包
        #end
    #elseif (${column.javaType} == "LocalDateTime" || ${column.javaType} == "LocalDate") ## 合并 LocalDateTime 和 LocalDate 的处理逻辑
        #if (!$isDateTimeFormatImported)
import org.springframework.format.annotation.DateTimeFormat;
            #set($isDateTimeFormatImported = true) ## 设置为已导入
        #end

        #if (${column.javaType} == "LocalDateTime")
            #if (!$isLocalDateTimeImported)
import java.time.LocalDateTime;
import static ${DateUtilsClassName}.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
                #set($isLocalDateTimeImported = true) ## 设置为已导入
            #end
        #elseif (${column.javaType} == "LocalDate")
            #if (!$isLocalDateImported)
import java.time.LocalDate;
import static ${DateUtilsClassName}.FORMAT_yyyy_MM_dd;
                #set($isLocalDateImported = true) ## 设置为已导入
            #end
        #end
    #elseif (${column.javaType} == "JSONObject")
        #if (!($importedPackages.contains("com.alibaba.fastjson.JSONObject")))
import com.alibaba.fastjson.JSONObject;
            #set($importedPackages = $importedPackages.add("com.alibaba.fastjson.JSONObject"))
        #end
    #end
#end