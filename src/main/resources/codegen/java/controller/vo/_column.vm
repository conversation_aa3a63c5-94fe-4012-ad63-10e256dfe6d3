## 提供给 baseVO、aditVO、respVO 生成字段
    /** ${column.columnComment} */
#if (!${column.nullable})## 判断 @NotEmpty 和 @NotNull 注解
#if (${column.javaType} == 'String')
    @NotEmpty(message = "${column.columnComment}不能为空")
#else
    @NotNull(message = "${column.columnComment}不能为空")
#end
#end
#if (${column.javaType} == "LocalDateTime")## 时间类型
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
#end
#if (${column.javaType} == "LocalDate")## 时间类型
    @DateTimeFormat(pattern = FORMAT_yyyy_MM_dd)
#end
    private ${column.javaType} ${column.javaField};
