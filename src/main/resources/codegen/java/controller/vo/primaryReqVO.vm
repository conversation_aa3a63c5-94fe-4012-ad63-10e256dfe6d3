package ${basePackage}.module.${table.moduleName}.controller.${sceneEnum.basePackage}.${table.businessName}.vo;

import lombok.Data;
import jakarta.validation.constraints.*;

/**
 * ${table.classComment} PrimaryReqVO
 *
 * <AUTHOR>
 */
@Data
public class ${sceneEnum.prefixClass}${table.className}PrimaryReqVO {

    #if (${primaryColumn.javaType} == "String")
    @NotEmpty(message = "${primaryColumn.columnComment}不能为空")
    #else
    @NotNull(message = "${primaryColumn.columnComment}不能为空")
    #end
    private ${primaryColumn.javaType} ${primaryColumn.javaField};
}
