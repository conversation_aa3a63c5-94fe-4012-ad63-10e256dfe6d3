package ${basePackage}.module.${table.moduleName}.controller.${sceneEnum.basePackage}.${table.businessName}.vo;

import lombok.*;
## 处理 Date 字段的引入
###foreach ($column in $columns)
###if (${column.listOperationResult} && (!${column.aditOperation}))##不是通用字段
###if (${column.javaType} == "LocalDateTime")
##import org.springframework.format.annotation.DateTimeFormat;
##import java.time.LocalDateTime;
##import static ${DateUtilsClassName}.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
###break
###end
###if (${column.javaType} == "LocalDate")
##import org.springframework.format.annotation.DateTimeFormat;
##import java.time.LocalDate;
##import static ${DateUtilsClassName}.FORMAT_yyyy_MM_dd;
###break
###end
###end
###end

/**
 * ${table.classComment} AditReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ${sceneEnum.prefixClass}${table.className}AditReqVO extends ${sceneEnum.prefixClass}${table.className}BaseVO {

###foreach ($column in $columns)
###if (${column.listOperationResult} && (!${column.aditOperation}))##不是通用字段
##    /** ${column.columnComment} */
##    #if (${column.javaType} == "LocalDateTime")## 时间类型
##    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
##    #end
##    #if (${column.javaType} == "LocalDate")## 时间类型
##    @DateTimeFormat(pattern = FORMAT_yyyy_MM_dd)
##    #end
##    private ${column.javaType} ${column.javaField};
##
###end
###end

}
