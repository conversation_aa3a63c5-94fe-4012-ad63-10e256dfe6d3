package ${basePackage}.module.${table.moduleName}.controller.${sceneEnum.basePackage}.${table.businessName}.vo;

import lombok.*;
#parse("codegen/java/controller/vo/_import.vm")

## 字段模板
#macro(columnTpl $prefix $prefixStr)
    private ${column.javaType}#if ("$!prefix" != "") ${prefix}${JavaField}#else ${column.javaField}#end;
#end

/**
 * ${table.classComment} QueryReqVO
 *
 * <AUTHOR>
 */
@Data
public class ${sceneEnum.prefixClass}${table.className}QueryReqVO {

#foreach ($column in $columns)
#if (${column.listOperationResult})##列表查询操作
    /** ${column.columnComment} */
#if (${column.listOperationCondition} == "BETWEEN")## 情况一，Between 的时候
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private ${column.javaType} start${column.javaFieldUpper};

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private ${column.javaType} end${column.javaFieldUpper};
#else##情况二，非 Between 的时间
    #if (${column.javaType} == "LocalDateTime")## 时间类型
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    #end
    #if (${column.javaType} == "LocalDate")## 时间类型
    @DateTimeFormat(pattern = FORMAT_yyyy_MM_dd)
    #end
    #columnTpl('', '')
#end

#end
#end
}
