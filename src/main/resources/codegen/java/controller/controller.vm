package ${basePackage}.module.${table.moduleName}.controller.${sceneEnum.basePackage}.${table.businessName};

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
#if ($sceneEnum.scene == 1)import org.springframework.security.access.prepost.PreAuthorize;#end

import jakarta.validation.*;
import java.util.*;

import ${PageResultClassName};
import ${ResultXClassName};
import ${BeanUtilsClassName};
import static ${ResultXClassName}.success;

#if($primaryColumn.javaType == "String")
import ${basePackage}.framework.common.util.StrUtilX;
#end
import ${basePackage}.framework.operatelog.core.annotations.OperateLog;
import ${basePackage}.module.${table.moduleName}.controller.${sceneEnum.basePackage}.${table.businessName}.vo.*;
import ${basePackage}.module.${table.moduleName}.dal.db.${table.businessName}.${table.className}DO;
import ${basePackage}.module.${table.moduleName}.service.${table.businessName}.${table.className}Service;

/**
 * ${table.classComment} Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/${table.moduleName}")
@Validated
public class ${sceneEnum.prefixClass}${table.className}Controller {

    @Resource
    private ${table.className}Service ${classNameVar}Service;

    @OperateLog("${table.classComment}添加或编辑")
    @PostMapping("/${lowerClassName}Adit")
#if ($sceneEnum.scene == 1)    @PreAuthorize("@ss.hasPermission('${lowerClassName}:adit')")#end

    public ResultX<${primaryColumn.javaType}> ${lowerClassName}Adit(@Valid @RequestBody ${sceneEnum.prefixClass}${table.className}AditReqVO reqVO) {
        return success(#if($primaryColumn.javaType == "String")StrUtilX.isEmpty(reqVO.get${primaryColumn.javaFieldUpper}())#end#if($primaryColumn.javaType != "String")reqVO.get${primaryColumn.javaFieldUpper}() == null#end
                            ? ${classNameVar}Service.${lowerClassName}Add(reqVO)
                            : ${classNameVar}Service.${lowerClassName}Edit(reqVO));
    }

    @OperateLog("${table.classComment}删除")
    @PostMapping("/${lowerClassName}Delete")
#if ($sceneEnum.scene == 1)    @PreAuthorize("@ss.hasPermission('${lowerClassName}:delete')")#end

    public ResultX<Boolean> ${lowerClassName}Delete(@Valid @RequestBody ${sceneEnum.prefixClass}${table.className}PrimaryReqVO reqVO) {
        ${classNameVar}Service.${lowerClassName}Delete(reqVO.get${primaryColumn.javaFieldUpper}());
        return success(true);
    }

    @OperateLog("${table.classComment}详情")
    @PostMapping("/${lowerClassName}Detail")
#if ($sceneEnum.scene == 1)    @PreAuthorize("@ss.hasPermission('${lowerClassName}:query')")#end

    public ResultX<${sceneEnum.prefixClass}${table.className}RespVO> ${lowerClassName}Detail(@Valid @RequestBody ${sceneEnum.prefixClass}${table.className}PrimaryReqVO reqVO) {
        return success(${classNameVar}Service.${lowerClassName}Detail(reqVO.get${primaryColumn.javaFieldUpper}()));
    }

    @OperateLog("${table.classComment}列表")
    @PostMapping("/${lowerClassName}List")
#if ($sceneEnum.scene == 1)    @PreAuthorize("@ss.hasPermission('${lowerClassName}:query')")#end

    public ResultX<List<${sceneEnum.prefixClass}${table.className}RespVO>> ${lowerClassName}List(@Valid @RequestBody ${sceneEnum.prefixClass}${table.className}QueryReqVO reqVO) {
        return success(${classNameVar}Service.${lowerClassName}List(reqVO));
    }

    @OperateLog("${table.classComment}分页")
    @PostMapping("/${lowerClassName}Page")
#if ($sceneEnum.scene == 1)    @PreAuthorize("@ss.hasPermission('${lowerClassName}:query')")#end

    public ResultX<PageResult<${sceneEnum.prefixClass}${table.className}RespVO>> ${lowerClassName}Page(@Valid @RequestBody ${sceneEnum.prefixClass}${table.className}PageReqVO reqVO) {
        return success(${classNameVar}Service.${lowerClassName}Page(reqVO));
    }

}
