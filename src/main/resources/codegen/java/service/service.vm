package ${basePackage}.module.${table.moduleName}.service.${table.businessName};

import java.util.*;
import jakarta.validation.*;
import ${basePackage}.module.${table.moduleName}.controller.${sceneEnum.basePackage}.${table.businessName}.vo.*;
import ${basePackage}.module.${table.moduleName}.dal.db.${table.businessName}.${table.className}DO;
import ${PageResultClassName};

/**
 * ${table.classComment} Service 接口
 *
 * <AUTHOR>
 */
public interface ${table.className}Service {

    /**
     * 创建${table.classComment}
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    ${primaryColumn.javaType} ${lowerClassName}Add(@Valid ${sceneEnum.prefixClass}${table.className}AditReqVO reqVO);

    /**
     * 更新${table.classComment}
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    ${primaryColumn.javaType} ${lowerClassName}Edit(@Valid ${sceneEnum.prefixClass}${table.className}AditReqVO reqVO);

    /**
     * 删除${table.classComment}
     *
     * @param ${primaryColumn.javaField} 编号
     */
    void ${lowerClassName}Delete(${primaryColumn.javaType} ${primaryColumn.javaField});

    /**
     * 获得${table.classComment}信息
     *
     * @param ${primaryColumn.javaField} 编号
     * @return ${table.classComment}信息
     */
    ${table.className}RespVO ${lowerClassName}Detail(${primaryColumn.javaType} ${primaryColumn.javaField});

    /**
     * 获得${table.classComment}列表
     *
     * @param reqVO 查询条件
     * @return ${table.classComment}列表
     */
    List<${table.className}RespVO> ${lowerClassName}List(@Valid ${sceneEnum.prefixClass}${table.className}QueryReqVO reqVO);

    /**
     * 获得${table.classComment}分页
     *
     * @param reqVO 查询条件
     * @return ${table.classComment}分页
     */
    PageResult<${table.className}RespVO> ${lowerClassName}Page(@Valid ${sceneEnum.prefixClass}${table.className}PageReqVO reqVO);

}
