package ${basePackage}.module.${table.moduleName}.service.${table.businessName};

import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import ${basePackage}.module.${table.moduleName}.controller.${sceneEnum.basePackage}.${table.businessName}.vo.*;
import ${basePackage}.module.${table.moduleName}.dal.db.${table.businessName}.${table.className}DO;
import ${PageResultClassName};
import ${BeanUtilsClassName};
import ${basePackage}.module.${table.moduleName}.dal.mysql.${table.businessName}.${table.className}Mapper;

// import static ${ServiceExceptionUtilClassName}.exception;
import com.mongoso.mgs.framework.common.exception.BizException;
// import static ${basePackage}.module.${table.moduleName}.enums.ErrorCodeConstants.*;


/**
 * ${table.classComment} Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ${table.className}ServiceImpl implements ${table.className}Service {

    @Resource
    private ${table.className}Mapper ${classNameVar}Mapper;

    @Override
    public ${primaryColumn.javaType} ${lowerClassName}Add(${sceneEnum.prefixClass}${table.className}AditReqVO reqVO) {
        // 插入
        ${table.className}DO ${classNameVar} = BeanUtilX.copy(reqVO, ${table.className}DO::new);
        ${classNameVar}Mapper.insert(${classNameVar});
        // 返回
        return ${classNameVar}.get${primaryColumn.javaFieldUpper}();
    }

    @Override
    public ${primaryColumn.javaType} ${lowerClassName}Edit(${sceneEnum.prefixClass}${table.className}AditReqVO reqVO) {
        // 校验存在
        this.${lowerClassName}ValidateExists(reqVO.get${primaryColumn.javaFieldUpper}());
        // 更新
        ${table.className}DO ${classNameVar} = BeanUtilX.copy(reqVO, ${table.className}DO::new);
        ${classNameVar}Mapper.updateById(${classNameVar});
        // 返回
        return ${classNameVar}.get${primaryColumn.javaFieldUpper}();
    }

    @Override
    public void ${lowerClassName}Delete(${primaryColumn.javaType} ${primaryColumn.javaField}) {
        // 校验存在
        this.${lowerClassName}ValidateExists(${primaryColumn.javaField});
        // 删除
        ${classNameVar}Mapper.deleteById(${primaryColumn.javaField});
    }

    private ${table.className}DO ${lowerClassName}ValidateExists(${primaryColumn.javaType} ${primaryColumn.javaField}) {
        ${table.className}DO ${classNameVar} = ${classNameVar}Mapper.selectById(${primaryColumn.javaField});
        if (${classNameVar} == null) {
            // throw exception(${simpleClassName_underlineCase.toUpperCase()}_NOT_EXISTS);
            throw new BizException("5001", "${table.classComment}不存在");
        }
        return ${classNameVar};
    }

    @Override
    public ${table.className}RespVO ${lowerClassName}Detail(${primaryColumn.javaType} ${primaryColumn.javaField}) {
        ${table.className}DO data = ${classNameVar}Mapper.selectById(${primaryColumn.javaField});
        return BeanUtilX.copy(data, ${table.className}RespVO::new);
    }

    @Override
    public List<${table.className}RespVO> ${lowerClassName}List(${sceneEnum.prefixClass}${table.className}QueryReqVO reqVO) {
        List<${table.className}DO> data = ${classNameVar}Mapper.selectList(reqVO);
        return BeanUtilX.copy(data, ${table.className}RespVO::new);
    }

    @Override
    public PageResult<${table.className}RespVO> ${lowerClassName}Page(${sceneEnum.prefixClass}${table.className}PageReqVO reqVO) {
        PageResult<${table.className}DO> data = ${classNameVar}Mapper.selectPage(reqVO);
        return BeanUtilX.copy(data, ${table.className}RespVO::new);
    }

}
