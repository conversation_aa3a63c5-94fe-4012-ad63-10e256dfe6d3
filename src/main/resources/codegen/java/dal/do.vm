package ${basePackage}.module.${table.moduleName}.dal.db.${table.businessName};

import lombok.*;
#foreach ($column in $columns)
#if (${column.javaType} == "BigDecimal")
import java.math.BigDecimal;
#break
#end
#end
#foreach ($column in $columns)
#if (${column.javaType} == "LocalDateTime")
import java.time.LocalDateTime;
#break
#end
#end
#foreach ($column in $columns)
#if (${column.javaType} == "LocalDate")
import java.time.LocalDate;
#break
#end
#end
#foreach ($column in $columns)
#if (${column.javaType} == "JSONObject")
import com.alibaba.fastjson.JSONObject;
import com.mongoso.mgs.framework.typehandler.JsonbTypeHandler;
#break
#end
#end

import com.baomidou.mybatisplus.annotation.*;
import ${OpDOClassName};

/**
 * ${table.classComment} DO
 *
 * <AUTHOR>
 */
@TableName(value = "${schemaName}.${table.tableName.toLowerCase()}", autoResultMap = true)
//@KeySequence("${table.tableName.toLowerCase()}_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ${table.className}DO extends OperateDO {

#foreach ($column in $columns)
#if (!${baseDOFields.contains(${column.javaField})})##排除 BaseDO 的字段
    /** ${column.columnComment} */
    #if (${column.primaryKey})##处理主键
    ##    @TableId#if (${column.javaType} == 'String')(type = IdType.INPUT)#end
    @TableId(type = IdType.ASSIGN_ID)
    #else
        #if (${column.javaType} == 'JSONObject')##处理JSON格式的字段
    @TableField(typeHandler = JsonbTypeHandler.class,fill = FieldFill.INSERT)
        #end
    #end
    private ${column.javaType} ${column.javaField};

#end
#end

}
