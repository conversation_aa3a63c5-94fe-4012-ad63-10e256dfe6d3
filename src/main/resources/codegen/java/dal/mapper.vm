package ${basePackage}.module.${table.moduleName}.dal.mysql.${table.businessName};

import java.util.*;

import ${PageResultClassName};
import ${QueryWrapperClassName};
import ${BaseMapperClassName};
import ${basePackage}.module.${table.moduleName}.dal.db.${table.businessName}.${table.className}DO;
import org.apache.ibatis.annotations.Mapper;
import ${basePackage}.module.${table.moduleName}.controller.${sceneEnum.basePackage}.${table.businessName}.vo.*;

## 字段模板
#macro(pageCondition)
#foreach ($column in $columns)
#if (${column.pageOperation})
#if (${column.listOperationCondition} == "=")##情况一，= 的时候
                .eqIfPresent(${table.className}DO::get${column.javaFieldUpper}, reqVO.get${column.javaFieldUpper}())
#end
#if (${column.listOperationCondition} == "!=")##情况二，!= 的时候
                .neIfPresent(${table.className}DO::get${column.javaFieldUpper}, reqVO.get${column.javaFieldUpper}())
#end
#if (${column.listOperationCondition} == ">")##情况三，> 的时候
                .gtIfPresent(${table.className}DO::get${column.javaFieldUpper}, reqVO.get${column.javaFieldUpper}())
#end
#if (${column.listOperationCondition} == ">=")##情况四，>= 的时候
                .geIfPresent(${table.className}DO::get${column.javaFieldUpper}, reqVO.get${column.javaFieldUpper}())
#end
#if (${column.listOperationCondition} == "<")##情况五，< 的时候
                .ltIfPresent(${table.className}DO::get${column.javaFieldUpper}, reqVO.get${column.javaFieldUpper}())
#end
#if (${column.listOperationCondition} == "<=")##情况五，<= 的时候
                .leIfPresent(${table.className}DO::get${column.javaFieldUpper}, reqVO.get${column.javaFieldUpper}())
#end
#if (${column.listOperationCondition} == "LIKE")##情况七，Like 的时候
                .likeIfPresent(${table.className}DO::get${column.javaFieldUpper}, reqVO.get${column.javaFieldUpper}())
#end
#if (${column.listOperationCondition} == "BETWEEN")##情况八，Between 的时候
                .betweenIfPresent(${table.className}DO::get${column.javaFieldUpper}, reqVO.getStart${column.javaFieldUpper}(), reqVO.getEnd${column.javaFieldUpper}())
#end
#end
#end
#end
#macro(listCondition)
#foreach ($column in $columns)
#if (${column.listOperationResult})
#if (${column.listOperationCondition} == "=")##情况一，= 的时候
                .eqIfPresent(${table.className}DO::get${column.javaFieldUpper}, reqVO.get${column.javaFieldUpper}())
#end
#if (${column.listOperationCondition} == "!=")##情况二，!= 的时候
                .neIfPresent(${table.className}DO::get${column.javaFieldUpper}, reqVO.get${column.javaFieldUpper}())
#end
#if (${column.listOperationCondition} == ">")##情况三，> 的时候
                .gtIfPresent(${table.className}DO::get${column.javaFieldUpper}, reqVO.get${column.javaFieldUpper}())
#end
#if (${column.listOperationCondition} == ">=")##情况四，>= 的时候
                .geIfPresent(${table.className}DO::get${column.javaFieldUpper}, reqVO.get${column.javaFieldUpper}())
#end
#if (${column.listOperationCondition} == "<")##情况五，< 的时候
                .ltIfPresent(${table.className}DO::get${column.javaFieldUpper}, reqVO.get${column.javaFieldUpper}())
#end
#if (${column.listOperationCondition} == "<=")##情况五，<= 的时候
                .leIfPresent(${table.className}DO::get${column.javaFieldUpper}, reqVO.get${column.javaFieldUpper}())
#end
#if (${column.listOperationCondition} == "LIKE")##情况七，Like 的时候
                .likeIfPresent(${table.className}DO::get${column.javaFieldUpper}, reqVO.get${column.javaFieldUpper}())
#end
#if (${column.listOperationCondition} == "BETWEEN")##情况八，Between 的时候
                .betweenIfPresent(${table.className}DO::get${column.javaFieldUpper}, reqVO.getStart${column.javaFieldUpper}(), reqVO.getEnd${column.javaFieldUpper}())
#end
#end
#end
#end
/**
 * ${table.classComment} Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ${table.className}Mapper extends BaseMapperX<${table.className}DO> {

    default PageResult<${table.className}DO> selectPage(${sceneEnum.prefixClass}${table.className}PageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<${table.className}DO>lambdaQueryX()
			#pageCondition()
                .orderByDesc(${table.className}DO::get${primaryColumn.javaFieldUpper}));## 大多数情况下，id 倒序
##                .orderByDesc(${table.className}DO::getCreatedDt));## 创建时间倒序

    }



##    default PageResult<${table.className}DO> selectPageNew(${sceneEnum.prefixClass}${table.className}PageReqVO reqVO) {
##        return jsonbSelectPage(reqVO, LambdaQueryWrapperX.<${table.className}DO>lambdaQueryX()
##            #pageCondition()
##            ##                .orderByDesc(${table.className}DO::get${primaryColumn.javaFieldUpper}));## 大多数情况下，id 倒序
##            .orderByDesc(${table.className}DO::getCreatedDt));## 创建时间倒序
##
##    }

    default List<${table.className}DO> selectList(${sceneEnum.prefixClass}${table.className}QueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<${table.className}DO>lambdaQueryX()
			#listCondition()
                .orderByDesc(${table.className}DO::get${primaryColumn.javaFieldUpper}));## 大多数情况下，id 倒序
##                    .orderByDesc(${table.className}DO::getCreatedDt));## 创建时间倒序

    }

##    default List<${table.className}DO> selectList(${sceneEnum.prefixClass}${table.className}QueryReqVO reqVO) {
##        return jsonbSelectList(reqVO, LambdaQueryWrapperX.<${table.className}DO>lambdaQueryX()
##            #pageCondition()
##            ##                .orderByDesc(${table.className}DO::get${primaryColumn.javaFieldUpper}));## 大多数情况下，id 倒序
##            .orderByDesc(${table.className}DO::getCreatedDt));## 创建时间倒序
##
##    }

}