<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
             xmlns:flowable="http://flowable.org/bpmn"
             targetNamespace="http://flowable.org/test">

    <process id="simpleApproval" name="Simple Approval Process" isExecutable="true">
        
        <startEvent id="startEvent" name="Start"/>
        
        <userTask id="approvalTask" name="Approval Task" flowable:assignee="${assignee}">
            <documentation>Please approve this request</documentation>
        </userTask>
        
        <exclusiveGateway id="decision" name="Approved?"/>
        
        <userTask id="rejectedTask" name="Handle Rejection" flowable:assignee="${assignee}">
            <documentation>Request was rejected</documentation>
        </userTask>
        
        <endEvent id="approvedEnd" name="Approved"/>
        <endEvent id="rejectedEnd" name="Rejected"/>
        
        <sequenceFlow id="flow1" sourceRef="startEvent" targetRef="approvalTask"/>
        <sequenceFlow id="flow2" sourceRef="approvalTask" targetRef="decision"/>
        <sequenceFlow id="flow3" sourceRef="decision" targetRef="approvedEnd">
            <conditionExpression>${approved == true}</conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="flow4" sourceRef="decision" targetRef="rejectedTask">
            <conditionExpression>${approved == false}</conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="flow5" sourceRef="rejectedTask" targetRef="rejectedEnd"/>
        
    </process>
    
</definitions>