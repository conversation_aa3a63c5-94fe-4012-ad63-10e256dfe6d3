(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[8],{

/***/ "./src/api/infra/dataSourceConfig.js":
/*!*******************************************!*\
  !*** ./src/api/infra/dataSourceConfig.js ***!
  \*******************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval("\n\nvar _interopRequireDefault = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/interopRequireDefault.js */ \"./node_modules/@babel/runtime/helpers/interopRequireDefault.js\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.createDataSourceConfig = createDataSourceConfig;\nexports.deleteDataSourceConfig = deleteDataSourceConfig;\nexports.getDataSourceConfig = getDataSourceConfig;\nexports.getDataSourceConfigList = getDataSourceConfigList;\nexports.updateDataSourceConfig = updateDataSourceConfig;\nvar _request = _interopRequireDefault(__webpack_require__(/*! @/utils/request */ \"./src/utils/request.js\"));\n// 创建数据源配置\nfunction createDataSourceConfig(data) {\n  return (0, _request.default)({\n    url: '/admin-api/infra/data-source-config/create',\n    method: 'post',\n    data: data\n  });\n}\n\n// 更新数据源配置\nfunction updateDataSourceConfig(data) {\n  return (0, _request.default)({\n    url: '/admin-api/infra/data-source-config/update',\n    method: 'put',\n    data: data\n  });\n}\n\n// 删除数据源配置\nfunction deleteDataSourceConfig(id) {\n  return (0, _request.default)({\n    url: '/admin-api/infra/data-source-config/delete?id=' + id,\n    method: 'delete'\n  });\n}\n\n// 获得数据源配置\nfunction getDataSourceConfig(id) {\n  return (0, _request.default)({\n    url: '/admin-api/infra/data-source-config/get?id=' + id,\n    method: 'get'\n  });\n}\n\n// 获得数据源配置列表\nfunction getDataSourceConfigList() {\n  return (0, _request.default)({\n    url: '/admin-api/infra/data-source-config/list',\n    method: 'get'\n  });\n}\n\n//# sourceURL=webpack:///./src/api/infra/dataSourceConfig.js?");

/***/ })

}]);