(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[7],{

/***/ "./src/api/infra/codegen.js":
/*!**********************************!*\
  !*** ./src/api/infra/codegen.js ***!
  \**********************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval("\n\nvar _interopRequireDefault = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/interopRequireDefault.js */ \"./node_modules/@babel/runtime/helpers/interopRequireDefault.js\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.createCodegenList = createCodegenList;\nexports.deleteCodegen = deleteCodegen;\nexports.downloadCodegen = downloadCodegen;\nexports.getCodegenDetail = getCodegenDetail;\nexports.getCodegenTablePage = getCodegenTablePage;\nexports.getSchemaTableList = getSchemaTableList;\nexports.previewCodegen = previewCodegen;\nexports.syncCodegenFromDB = syncCodegenFromDB;\nexports.syncCodegenFromSQL = syncCodegenFromSQL;\nexports.updateCodegen = updateCodegen;\nvar _request = _interopRequireDefault(__webpack_require__(/*! @/utils/request */ \"./src/utils/request.js\"));\n// 获得表定义分页\nfunction getCodegenTablePage(query) {\n  return (0, _request.default)({\n    url: '/admin-api/infra/codegen/table/page',\n    method: 'get',\n    params: query\n  });\n}\n\n// 获得表和字段的明细\nfunction getCodegenDetail(tableId) {\n  return (0, _request.default)({\n    url: '/admin-api/infra/codegen/detail?tableId=' + tableId,\n    method: 'get'\n  });\n}\n\n// 修改代码生成信息\nfunction updateCodegen(data) {\n  return (0, _request.default)({\n    url: '/admin-api/infra/codegen/update',\n    method: 'put',\n    data: data\n  });\n}\n\n// 基于数据库的表结构，同步数据库的表和字段定义\nfunction syncCodegenFromDB(tableId) {\n  return (0, _request.default)({\n    url: '/admin-api/infra/codegen/sync-from-db?tableId=' + tableId,\n    method: 'put'\n  });\n}\n\n// 基于 SQL 建表语句，同步数据库的表和字段定义\nfunction syncCodegenFromSQL(tableId, sql) {\n  return (0, _request.default)({\n    url: '/admin-api/infra/codegen/sync-from-sql?tableId=' + tableId,\n    method: 'put',\n    headers: {\n      'Content-type': 'application/x-www-form-urlencoded'\n    },\n    data: 'tableId=' + tableId + \"&sql=\" + sql\n  });\n}\n\n// 预览生成代码\nfunction previewCodegen(tableId) {\n  return (0, _request.default)({\n    url: '/admin-api/infra/codegen/preview?tableId=' + tableId,\n    method: 'get'\n  });\n}\n\n// 下载生成代码\nfunction downloadCodegen(tableId) {\n  return (0, _request.default)({\n    url: '/admin-api/infra/codegen/download?tableId=' + tableId,\n    method: 'get',\n    responseType: 'blob'\n  });\n}\n\n// 获得表定义分页\nfunction getSchemaTableList(query) {\n  return (0, _request.default)({\n    url: '/admin-api/infra/codegen/db/table/list',\n    method: 'get',\n    params: query\n  });\n}\n\n// 基于数据库的表结构，创建代码生成器的表定义\nfunction createCodegenList(data) {\n  return (0, _request.default)({\n    url: '/admin-api/infra/codegen/create-list',\n    method: 'post',\n    data: data\n  });\n}\n\n// 删除数据库的表和字段定义\nfunction deleteCodegen(tableId) {\n  return (0, _request.default)({\n    url: '/admin-api/infra/codegen/delete?tableId=' + tableId,\n    method: 'delete'\n  });\n}\n\n//# sourceURL=webpack:///./src/api/infra/codegen.js?");

/***/ })

}]);