package com.mongoso.mgs.rules.base.validation

import com.mongoso.mgs.module.rule.domain.RuleValidationResult
import com.mongoso.mgs.module.model.dal.db.modeltable.ModelTableDO
import com.mongoso.mgs.module.model.dal.db.modelfield.ModelFieldDO
import java.util.Map
import java.math.BigDecimal

// 必填字段校验
rule "必填字段校验"
    when
        $field: ModelFieldDO(isNullable == 1)
        $data: Map()
        $result: RuleValidationResult(valid == true)
        eval($data.get($field.getFieldCode()) == null || 
             ($data.get($field.getFieldCode()) instanceof String && 
              ((String)$data.get($field.getFieldCode())).trim().isEmpty()))
    then
        $result.setValid(false);
        $result.setErrorMessage($field.getFieldName() + "不能为空");
end

// 字段长度校验
rule "字段长度校验"
    when
        $field: ModelFieldDO(leng != null && leng > 0)
        $data: Map()
        $result: RuleValidationResult(valid == true)
        $value: Object() from $data.get($field.getFieldCode())
        eval($value != null && $value.toString().length() > $field.getLeng())
    then
        $result.setValid(false);
        $result.setErrorMessage($field.getFieldName() + "长度不能超过" + $field.getLeng() + "个字符");
end

// 数值类型校验
rule "数值类型校验"
    when
        $field: ModelFieldDO(fieldType in ("INT", "INT2", "INT4", "INT8", "BIGINT", "NUMERIC", "DECIMAL"))
        $data: Map()
        $result: RuleValidationResult(valid == true)
        $value: Object() from $data.get($field.getFieldCode())
        eval($value != null && !isValidNumber($value.toString(), $field.getFieldType()))
    then
        $result.setValid(false);
        $result.setErrorMessage($field.getFieldName() + "格式不正确，应为" + $field.getFieldType() + "类型");
end

function boolean isValidNumber(String value, String type) {
    try {
        if ("INT".equals(type) || "INT2".equals(type) || "INT4".equals(type) || "INTEGER".equals(type)) {
            Integer.parseInt(value);
        } else if ("INT8".equals(type) || "BIGINT".equals(type)) {
            Long.parseLong(value);
        } else if ("NUMERIC".equals(type) || "DECIMAL".equals(type)) {
            new BigDecimal(value);
        }
        return true;
    } catch (NumberFormatException e) {
        return false;
    }
}