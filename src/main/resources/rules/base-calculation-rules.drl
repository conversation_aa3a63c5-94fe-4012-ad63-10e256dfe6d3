package com.mongoso.mgs.rules.base.calculation

import com.mongoso.mgs.module.model.dal.db.modeltable.ModelTableDO
import com.mongoso.mgs.module.model.dal.db.modelfield.ModelFieldDO
import java.util.Map
import java.time.LocalDateTime
import java.math.BigDecimal

global LocalDateTime currentTime

// 自动设置创建时间
//rule "Base Auto Set Create Time"
//    when
//        $field: ModelFieldDO(fieldCode.toLowerCase().contains("create") && fieldCode.toLowerCase().contains("time") ||
//                           fieldCode == "created_dt" || fieldCode == "create_time" || fieldCode == "created_at")
//        $data: Map(this[$field.getFieldCode()] == null)
//    then
//        $data.put($field.getFieldCode(), currentTime);
//end

// 自动设置更新时间
//rule "Base Auto Set Update Time"
//    when
//        $field: ModelFieldDO(fieldCode.toLowerCase().contains("update") && fieldCode.toLowerCase().contains("time") ||
//                           fieldCode == "updated_dt" || fieldCode == "update_time" || fieldCode == "updated_at")
//        $data: Map()
//    then
//        $data.put($field.getFieldCode(), currentTime);
//end

// 自动生成编码
rule "Base Auto Generate Code"
    when
        $field: ModelFieldDO(fieldCode.toLowerCase().contains("code") && !fieldCode.toLowerCase().contains("qr"))
        $data: Map(this[$field.getFieldCode()] == null)
    then
        String prefix = $field.getFieldCode().toUpperCase().replace("_CODE", "").replace("CODE", "");
        if (prefix.isEmpty()) {
            prefix = "AUTO";
        }
        $data.put($field.getFieldCode(), prefix + "_" + System.currentTimeMillis());
end

// 自动生成序号
rule "Base Auto Generate Serial Number"
    when
        $field: ModelFieldDO(fieldCode.toLowerCase().contains("serial") || 
                           fieldCode.toLowerCase().contains("seq") ||
                           fieldCode.toLowerCase().contains("number"))
        $data: Map(this[$field.getFieldCode()] == null)
    then
        $data.put($field.getFieldCode(), System.currentTimeMillis() % 1000000);
end

// 设置默认状态
//rule "Base Set Default Status"
//    when
//        $field: ModelFieldDO(fieldCode.toLowerCase().contains("status") ||
//                           fieldCode.toLowerCase().contains("state"))
//        $data: Map(this[$field.getFieldCode()] == null)
//    then
//        $data.put($field.getFieldCode(), "1"); // 默认启用状态
//end

// 设置默认排序
//rule "Base Set Default Sort"
//    when
//        $field: ModelFieldDO(fieldCode.toLowerCase().contains("sort") ||
//                           fieldCode.toLowerCase().contains("order"))
//        $data: Map(this[$field.getFieldCode()] == null)
//    then
//        $data.put($field.getFieldCode(), 0);
//end

// 设置默认版本号
//rule "Base Set Default Version"
//    when
//        $field: ModelFieldDO(fieldCode.toLowerCase().contains("version"))
//        $data: Map(this[$field.getFieldCode()] == null)
//    then
//        $data.put($field.getFieldCode(), 1);
//end

// 设置默认删除标记
//rule "Base Set Default Delete Flag"
//    when
//        $field: ModelFieldDO(fieldCode.toLowerCase().contains("delete") &&
//                           (fieldCode.toLowerCase().contains("flag") || fieldCode.toLowerCase().contains("mark")))
//        $data: Map(this[$field.getFieldCode()] == null)
//    then
//        $data.put($field.getFieldCode(), false);
//end

// 自动设置创建人（如果有当前用户信息）
//rule "Base Auto Set Creator"
//    when
//        $field: ModelFieldDO(fieldCode.toLowerCase().contains("create") &&
//                           (fieldCode.toLowerCase().contains("user") || fieldCode.toLowerCase().contains("by")))
//        $data: Map(this[$field.getFieldCode()] == null)
//        eval($data.get("currentUserId") != null)
//    then
//        $data.put($field.getFieldCode(), $data.get("currentUserId"));
//end

// 自动设置更新人（如果有当前用户信息）
//rule "Base Auto Set Updater"
//    when
//        $field: ModelFieldDO(fieldCode.toLowerCase().contains("update") &&
//                           (fieldCode.toLowerCase().contains("user") || fieldCode.toLowerCase().contains("by")))
//        $data: Map()
//        eval($data.get("currentUserId") != null)
//    then
//        $data.put($field.getFieldCode(), $data.get("currentUserId"));
//end

// 自动计算年龄（基于生日）
//rule "Base Calculate Age From Birthday"
//    when
//        $birthdayField: ModelFieldDO(fieldCode.toLowerCase().contains("birthday") ||
//                                   fieldCode.toLowerCase().contains("birth"))
//        $ageField: ModelFieldDO(fieldCode.toLowerCase().contains("age"))
//        $data: Map()
//        $birthday: Object() from $data.get($birthdayField.getFieldCode())
//        eval($birthday != null)
//    then
//        // 这里可以添加年龄计算逻辑
//        // 简化处理，实际应该根据生日计算准确年龄
//        $data.put($ageField.getFieldCode(), 0);
//end

// 自动设置租户ID（如果有租户上下文）
//rule "Base Auto Set Tenant ID"
//    when
//        $field: ModelFieldDO(fieldCode.toLowerCase().contains("tenant"))
//        $data: Map(this[$field.getFieldCode()] == null)
//        eval($data.get("currentTenantId") != null)
//    then
//        $data.put($field.getFieldCode(), $data.get("currentTenantId"));
//end