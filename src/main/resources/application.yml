server:
  port: 8018
spring:
  profiles:
    active: dev
  application:
    name: lowcode
  main:
    allow-circular-references: true # 允许循环依赖，因为项目是三层架构，无法避免这个情况。
    allow-bean-definition-overriding: true # 允许循环依赖，因为项目是三层架构，无法避免这个情况。
  jackson:
    serialization:
      write-dates-as-timestamps: false # 不将日期序列化为时间戳
    deserialization:
      fail-on-unknown-properties: false # 忽略未知属性
    date-format: yyyy-MM-dd HH:mm:ss # 日期格式
    time-zone: GMT+8 # 时区设置

openapi:
  privateKey: Mongoso2018

model:
  schema:

### mybaties 配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true # 虽然默认为 true ，但是还是显示去指定下。
  global-config:
    db-config:
      id-type: NONE # “智能”模式，基于 IdTypeEnvironmentPostProcessor + 数据源的类型，自动适配成 AUTO、INPUT 模式。
      #      id-type: AUTO # 自增 ID，适合 MySQL 等直接自增的数据库
      #      id-type: INPUT # 用户输入 ID，适合 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库
      #      id-type: ASSIGN_ID # 分配 ID，默认使用雪花算法。注意，Oracle、PostgreSQL、Kingbase、DB2、H2 数据库时，需要去除实体类上的 @KeySequence 注解
      # field-strategy: not_empty
      table-underline: true
      logic-delete-value: 1 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)
  mapper-locations: classpath*:mapper/**/*Mapper.xml
  type-aliases-package: ${mgs.info.base-package}.module.*.dal.db

login:
  passErrorsCount: 1
  passErrorsTime: 1
  privateKey: MIIBVgIBADANBgkqhkiG9w0BAQEFAASCAUAwggE8AgEAAkEAxKq5FtE+gHrCO5Vd
    mf+Fk4yAW9TWsi1sFvcBgI4jewi4+MFGxvXG9EpwyhaqiNZv4QAANP70AXWB7s2g
    8tObtwIDAQABAkEAwlUMlf5na19iRKxAlKaIYJdKUvPKvr/hegPTySsq5iun+OII
    zrLqeXwkoeV9ZXM/iDwA7xObxUImjhzliS6SEQIhAPY3sSJP5ZHod4nHwkJVssB2
    5CyksUpMcGcFuvYDcLR/AiEAzHsLnw1myKVpctKljzefprAj2LztkC6uchJbhWho
    HMkCIHkId3K+g8Nt2xVHtR6WsgAacZ/gdaZoXPjHwFge6NBRAiEAr2xga/NzWm+O
    J5PPtS5jKP2zwx1SMX/a6MIXAjywFQECIQDoqtkLnZV0Kgeb33OMg7KuKkRaFekm
    9nEeX0oGXVfXyg==

mgs:
  data-permission:
    enable: false

  cloud:
    starter: single # mgs-cloud-starter包版本，single：单服务，cloud：微服务
  gateway:
    enable: false # 不使用网关的cors
  info:
    version: 1.0.0
    base-package: com.mongoso.mgs
    name: ${spring.application.name}
  security:
    token-header: token # token的请求头
    token-time: -1 # token有效期 单位小时 ，-1：永久有效
    mock-enable: true # 模拟用户开关
    mock-secret: test # 模拟用户的请求头，样例:test1
    request-header-encode: false # 请求头编解码开关,默认是开启，单服务不走网关需要关闭
    permit-all-urls:  # 无校验接口路劲
      - /admin-api/system/login
      - /admin-api/sse/**
      - /admin-api/system/sendSmsCode
      - /app/lowcodeapp/**
      - /app/lowcodeappdev/**
      - /admin-ui
      - /admin-ui/**
      - /gen-ui/**

  # 多租户相关配置项
  tenant:
    enable: false # 多租户 true：开启，false：关闭
    # 排除租户请求头校验的接口
    ignore-urls:
      - /admin-api/infra/*
      - /admin-api/ping
      - /admin-api/system/login
      - /admin-api/system/tenant*
      - /admin-api/system/file/*
    # 排除和租户无关的表，sql不会拼接tenant_id
    ignore-tables:
      - s_tenant
      - s_tenant_version
      - s_auth_menu
      - s_file_log

# Flowable配置
flowable:
  # 数据库配置
  database-schema-update: true
  db-history-used: true
  history-level: full

  # 流程引擎配置
  process:
    definition-cache-limit: 10

  # 异步执行器配置
  async:
    executor:
      default-async-job-acquire-wait-time: PT5S
      default-timer-job-acquire-wait-time: PT5S

  # UI配置
  modeler:
    app:
      deployment-url: http://localhost:8018/flowable-modeler
  admin:
    app:
      server-config:
        name: Flowable Admin
        description: Flowable Admin
        server-address: http://localhost
        port: 8018
        context-root: /
        rest-root: flowable-rest/
  task:
    app:
      server-config:
        name: Flowable Task
        description: Flowable Task
        server-address: http://localhost
        port: 8018
        context-root: /
        rest-root: flowable-rest/
