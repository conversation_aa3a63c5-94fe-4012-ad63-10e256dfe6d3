<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.model.dal.mysql.modeltable.ModelTableMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <update id="updateItemSeqAddOne">
        UPDATE sys_model_table
        SET seq = seq + 1
        <choose>
            <when test="parentId !=null">
                WHERE parent_id = #{parentId}
            </when>
            <otherwise>
                WHERE parent_id IS NULL
            </otherwise>
        </choose>
        AND seq > #{seq}
    </update>

    <update id="batchUpdateItemSeq">
        <foreach collection="items" item="item" separator=";">
            UPDATE sys_model_table
            SET seq = #{item.seq}
            WHERE table_id = #{item.tableId}
        </foreach>
    </update>
</mapper>
