<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.model.dal.mysql.item.ItemMapper">

    <insert id="insertItemInfo">
        INSERT INTO sys_item
        (
            item_id,
            item_name,
            parent_item_id,
            category,
            content,
            api_url,
            project_id,
            item_type,
            task_set_type,
            item_seq,
            query_type,
            active,
            is_default,
            creator,
            created_dt,
            updator,
            updated_dt
        )
        VALUES
            (
                #{itemId},
                #{itemName},
                #{parentItemId},
                #{category},
                #{content},
                #{apiUrl},
                #{projectId},
                #{itemType},
                #{taskSetType},
                #{itemSeq},
                #{queryType},
                1,
                #{isDefault},
                #{createUser},
                now(),
                #{createUser},
                now()
            )
    </insert>

    <!-- 更新文件排序号+1-->
    <update id="updateItemSeqAddOne">
        UPDATE sys_item
        SET item_seq = item_seq + 1
        <choose>
            <when test="parentItemId !=null">
                WHERE parent_item_id = #{parentItemId}
            </when>
            <otherwise>
                WHERE parent_item_id IS NULL
            </otherwise>
        </choose>
        AND item_seq > #{itemSeq}
    </update>

    <update id="batchUpdateItemSeq">
        <foreach collection="items" item="item" separator=";">
            UPDATE sys_item
            SET item_seq = #{item.itemSeq}
            WHERE item_id = #{item.itemId}
        </foreach>
    </update>

    <select id="queryItemOutList"
            resultType="com.mongoso.mgs.module.model.controller.admin.item.vo.ItemTreeVo">
        SELECT
        a.item_id itemId,
        a.parent_item_id parentItemId,
        a.item_name itemName,
        a.api_url itemCode,
        a.item_type itemType,
        a.item_seq itemSeq,
        a.category,
        a.project_id,
        a.task_set_type taskSetType

        FROM sys_item a

        where a.project_id = #{projectId,jdbcType=INTEGER}

        AND a.category = #{category,jdbcType=INTEGER}
        <choose>
            <when test="itemName !=null and itemName != ''">
                AND a.item_type IN(0,1)
                AND a.item_name like CONCAT('%',#{itemName,jdbcType=VARCHAR},'%') )
            </when>
            <otherwise>
                ORDER BY a.item_seq ASC, a.created_dt ASC
            </otherwise>
        </choose>
    </select>

    <select id="queryItemInList" resultType="com.mongoso.mgs.module.model.controller.admin.item.vo.ItemTreeVo">
        SELECT
        a.item_id itemId,
        a.parent_item_id parentItemId,
        a.item_name itemName,
        a.item_type itemType,
        a.task_set_type taskSetType,
        a.project_id projectId,
--         b.project_name projectName,
        a.user_id userId,
        a.has_dashboard hasDashboard
        FROM sys_item a
        <!-- project_id 为空则为项目外文件夹和项目文件夹，反之位项目内容文件夹 -->
        WHERE a.project_id IS NOT NULL

        <if test="projectIds != null and projectIds.size > 0">
            AND a.project_id IN
            <foreach item="item" index="index" collection="projectIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY a.parent_item_id ASC, a.item_seq ASC, a.created_dt ASC
    </select>



    <update id="updateApiField">
        UPDATE sys_item
        SET content = REPLACE(content,
        <choose>
            <when test="opType == 0">
                '"englishName":"${oldName}"', '"englishName":"${newName}"'
            </when>
            <when test="opType == 1">
                '"itemName":"${oldName}"', '"itemName":"${newName}"'
            </when>
            <when test="opType == 2">
                '"remark":"${oldName}"', '"remark":"${newName}"'
            </when>
            <otherwise>
                '"itemName":"${oldName}"', '"itemName":"${newName}"'
            </otherwise>
        </choose>
        )
        WHERE category=1
          and project_id = #{projectId}
          and api_url LIKE CONCAT(#{apiUrl,jdbcType=VARCHAR},'%')
    </update>

</mapper>