<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.model.dal.mysql.modeltable.TaskMapper">

    <select id="queryTaskCountByProjectId" resultType="java.lang.Integer">
        SELECT
        count(1)
        FROM t_task t
        WHERE t.project_id = #{projectId}
    </select>

</mapper>