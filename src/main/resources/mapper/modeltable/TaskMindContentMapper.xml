<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.model.dal.mysql.modeltable.TaskMindContentMapper">
    <insert id="insertTaskMindDetail">
        INSERT INTO sys_task_mind_detail
        (
            task_id,
            mind_type,
            api_type,
            api_url,
            task_mind_content,
            create_user,
            create_time
        )
        VALUES
            (
                #{taskId},
                #{mindType},
                #{apiType},
                #{apiUrl},
                #{itemContent},
                #{createUser},
                #{createTime}
            )
    </insert>

    <!-- 查询关联任务导图蜘图-->
    <select id="queryTaskMindConentByApiUrl" resultType="com.mongoso.mgs.module.model.controller.admin.modeltable.vo.TaskMindDetailVo">
        SELECT
            a.task_id taskId,
            a.mind_type mindType,
            a.api_type apiType,
            a.api_url apiUrl,
            a.task_mind_content itemContent
        FROM sys_task_mind_detail a
                 JOIN sys_task b on a.task_id = b.task_id AND b.active = 1
        WHERE a.api_url = #{apiUrl}
    </select>

    <!-- 更新思维导图内容-->
    <update id="updateTaskMindDetail" parameterType="com.mongoso.mgs.module.model.controller.admin.modeltable.vo.UpdateTaskMindDetailParams">
        UPDATE sys_task_mind_detail
        SET
        <if test="mindType !=null">
            mind_type = #{mindType},
        </if>
        <if test="apiType !=null">
            api_type = #{apiType},
        </if>
        <if test="apiUrl !=null and apiUrl!=''">
            api_url = #{apiUrl},
        </if>
        task_mind_content = #{itemContent},
        update_user = #{createUser},
        update_time = #{createTime}
        WHERE task_id = #{taskId}
    </update>

</mapper>