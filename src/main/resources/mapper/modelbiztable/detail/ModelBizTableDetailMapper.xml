<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.model.dal.mysql.modelbiztable.detail.ModelBizTableDetailMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <!-- 根据业务编码查询子表明细列表（连表查询获取表名称） -->
    <select id="selectListWithTableNameByBizCode" resultType="com.mongoso.mgs.module.model.controller.admin.modelbiztable.detail.vo.ModelBizTableDetailRespVO">
        SELECT
            mbtd.data_id,
            mbtd.table_id,
            mbtd.biz_code,
            mbtd.table_code,
            mbtd.level,
            mbtd.table_type,
            mbtd.is_null_able,
            mbtd.parent_data_id,
            mbtd.remark,
            mbtd.parent_row_path,
            mbtd.parent_row_no,
            mbtd.row_no,
            mt.table_name AS tableName
        FROM lowcode.sys_model_biz_table_detail mbtd
        LEFT JOIN lowcode.sys_model_table mt ON mbtd.table_id = mt.table_id
        WHERE mbtd.biz_code = #{bizCode}
        ORDER BY mbtd.parent_row_path ASC, mbtd.row_no ASC
    </select>

</mapper>
