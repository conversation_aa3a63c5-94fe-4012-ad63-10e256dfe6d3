<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.model.dal.mysql.modelbiztable.ModelBizTableMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <!-- 分页查询主表数据（连表查询获取表名称） -->
    <select id="selectPageWithTableName" resultType="com.mongoso.mgs.module.model.controller.admin.modelbiztable.vo.ModelBizTableRespVO">
        SELECT
            mbt.data_id,
            mbt.table_id,
            mbt.biz_code,
            mbt.biz_type,
            mbt.table_code,
            mbt.biz_name,
            mbt.remark,
            mt.table_name AS tableName,
            mbt.created_by,
            mbt.created_dt,
            mbt.updated_by,
            mbt.updated_dt
        FROM lowcode.sys_model_biz_table mbt
        LEFT JOIN lowcode.sys_model_table mt ON mbt.table_id = mt.table_id
        <where>
            <if test="reqVO.tableId != null">
                AND mbt.table_id = #{reqVO.tableId}
            </if>
            <if test="reqVO.bizCode != null and reqVO.bizCode != ''">
                AND mbt.biz_code LIKE CONCAT('%', #{reqVO.bizCode}, '%')
            </if>
            <if test="reqVO.bizType != null">
                AND mbt.biz_type = #{reqVO.bizType}
            </if>
            <if test="reqVO.tableCode != null and reqVO.tableCode != ''">
                AND mbt.table_code LIKE CONCAT('%', #{reqVO.tableCode}, '%')
            </if>
            <if test="reqVO.bizName != null and reqVO.bizName != ''">
                AND mbt.biz_name LIKE CONCAT('%', #{reqVO.bizName}, '%')
            </if>
            <if test="reqVO.remark != null and reqVO.remark != ''">
                AND mbt.remark = #{reqVO.remark}
            </if>
            <if test="reqVO.tableName != null and reqVO.tableName != ''">
                AND mt.table_name LIKE CONCAT('%', #{reqVO.tableName}, '%')
            </if>
        </where>
        ORDER BY mbt.created_dt DESC
    </select>

</mapper>
