<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mongoso.mgs.module.model.dal.mysql.pageconfig.PageConfigMapper">

    <select id="getMaxSeq" resultType="java.lang.Long">
        SELECT MAX(seq)
        FROM sys_page_config
        WHERE parent_id = #{parentId} and project_id = #{projectId}
    </select>

    <!-- 更新seq加1-->
    <update id="updateSeqAddOne">
        UPDATE sys_page_config
        SET seq = seq + 1
        <choose>
            <when test="parentId !=null and parentId != ''">
                WHERE project_id = #{projectId} and parent_id = #{parentId}
            </when>
            <otherwise>
                WHERE project_id = #{projectId} and (parent_id IS NULL OR parent_id = 0)
            </otherwise>
        </choose>
        AND seq > #{seq}
    </update>

</mapper>
