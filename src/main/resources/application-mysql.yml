# 本地环境
base-ip: 127.0.0.1 # 本地
db-port: 5432 # 端口
db-username: postgres # 账号
db-password: 123456 # 密码

base-db: mgs-platform # 数据库名称

base-package: com.mongoso.mgs # 代码生成的包路劲，样例：src/main/java/{包名}/module， src/main/java/vip/syyo/yux/module

#指定数据库，可选值有【mysql、oracle、sqlserver、postgresql】
syyo:
  database: mysql
  info:
    version: 1.0.0
  codegen:
    base-package: ${base-package}
    db-schemas: master

spring:
  datasource:
    dynamic:
      primary: master #设置默认的数据源或者数据源组,默认值即为master
      datasource:
        master: # 主数据源
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: *************************************************************************************************************************************
          username: root
          password: mysqlpass1
#        pgdb: # 平台公共库数据源
#          driver-class-name: org.postgresql.Driver
#          url: **************************************************************************************************************************************************************************************
#          username: postgres
#          password: postgres1
#spring:
#  datasource:
#    driver-class-name: com.mysql.cj.jdbc.Driver
#    url: *************************************************************************************************************************************
#    username: root
#    password: mysqlpass1
  servlet:
    multipart:
      max-request-size: 15MB
      max-file-size: 10MB
  redis:
    host: **************
    port: 6379
#    password: Was&vdfsfd5!ssw
    database: 11
    timeout: 5000ms # 连接超时时间（毫秒）
    jedis:
      pool:
        max-active: 100 # 连接池最大连接数（使用负值表示没有限制）
        max-idle: 30 # 连接池中的最大空闲连接
        min-idle: 30 # 连接池中的最小空闲连接
        max-wait: 5000ms # 连接池最大阻塞等待时间（使用负值表示没有限制）

  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
  web:
    resources:
      static-locations: classpath:/static/,classpath:/views/

# 阿里sls日志配置
#mgs:
#  log:
#    log-type: sls # 日志类型，local：本地，sls：阿里
#    access-log: true # 访问日志 true：开启，false：关闭
#    error-log: true # 访问日志
#    operate-log: true  # 操作日志
#  # 接入阿里SLS日志配置
#  sls:
#    endpoint: cn-shenzhen.log.aliyuncs.com
#    access-key-id: LTAI5tSG1z4d2vEZLgX4mxho
#    access-key-secret: ******************************
#    project: seikosmartcloud-test
#    logStore: platform-mes

logging:
  level:
    root: info # 打印所有的info日志
    com.mongoso.mgs.module.*.dal.mysql: DEBUG # 打印该包下的debug日志
    com.mongoso.mgs.module.model.dal.mysql: DEBUG # 打印该包下的debug日志

