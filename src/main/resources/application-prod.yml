# 正式环境
base-ip: pc-wz99f9rx9p2u8s74p.rwlb.rds.aliyuncs.com # 内网ip，测试环境部署用的
db-port: 1921 # 端口
db-username: mgs_platform # 账号
db-password: JeSCOPhio5sGOCc2 # 密码
db-name: mgs-jgzy # 租户

base-package: com.mongoso.mgs # 代码生成的包路劲，样例：src/main/java/{包名}/module， src/main/java/vip/syyo/yux/module

remote:
  #python远程执行地址
  python:
    executeurl: http://**************:5001/api/v2/execute
  chatgpt:
    url: https://xiaosong.mongoso.vip/api/v1/chat/completions
    token: Bearer mongoso-h6IJVQ6puZq46oIw5G3GXrSsEYyeAfEbfCwmspAShekfZJK82KrD25
    chatId: 67ac74d5423b4217bdb90de4

  #字段翻译地址
  translate:
    url: https://translated.mongoso.vip/translate_fields
  generate:
    url: https://max.mongoso.vip/max/taskSet/genApiDocJsonb
    privateKey: A8jD7KpQnxZsR6YgB4F2E3kJ8M1h

#指定数据库，可选值有【mysql、oracle、sqlserver、postgresql】
syyo:
  database: postgresql
  info:
    version: 1.0.0
  codegen:
    base-package: ${base-package}
    db-schemas: master


spring:
  datasource:
    dynamic:
      primary: master #设置默认的数据源或者数据源组,默认值即为master
      datasource:
        master: # tenant数据源
          driver-class-name: org.postgresql.Driver
          url: jdbc:postgresql://${base-ip}:${db-port}/${db-name}?currentSchema=lowcode&useSSL=false&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true&nullCatalogMeansCurrent=true
          username: ${db-username}
          password: ${db-password}
  data:
    redis:
      host: r-wz9by15jglm5bqhz5z.redis.rds.aliyuncs.com
      port: 6379
      password: K&p1RMdkq6yKT!DQ
      database: 9
      timeout: 5000ms # 连接超时时间（毫秒）
      jedis:
        pool:
          max-active: 100 # 连接池最大连接数（使用负值表示没有限制）
          max-idle: 30 # 连接池中的最大空闲连接
          min-idle: 30 # 连接池中的最小空闲连接
          max-wait: 5000ms # 连接池最大阻塞等待时间（使用负值表示没有限制）

  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
  web:
    resources:
      static-locations: classpath:/static/,classpath:/views/

file:
  local: # 本地配置,最终文件路劲 = /路劲/文件类型/文件名.png = /Users/<USER>/Desktop/beiyong/upload/ireve/image/aaa.png
    domain: http://image.mongoso.com # 图片服务器域名
    path: C:/Users/<USER>/Desktop/beiyong/image # win图片存放目录
    expires-time: 500
  #    path: /Users/<USER>/Desktop/beiyong/image/upload # mac路劲
  ali: # 阿里配置
    accessKey: LTAI5tSG1z4d2vEZLgX4mxho
    secret-key: ****************************** # 密码
    bucketname: jinggongzhiyun # 空间名
    end-point: oss-cn-shenzhen.aliyuncs.com # 模块名
    domain: https://jinggongzhiyun.oss-cn-shenzhen-internal.aliyuncs.com # 第三方域名,内网，部署用的
    path: /lowcode # 路劲
  qiniu: # 七牛配置,最终文件路劲 = /路劲/文件类型/文件名.png = /ireve/image/aaa.png
    access-key: 4UgnSE4vpzLfABDxGQoS4V6emcT2T79KJDSEkp9t # 账号
    secret-key: wkBBKRVSSqr1FpzHABtxhqo_tSuD54HC4b7pRmG5 # 密码
    bucketname: mgsfile # 空间名
    expires-time: 3600 #  token有效期，单位秒
    domain: https://files.mgsdev.cn # 第三方域名
    path: /ireve # 路劲
  upyun: # 又拍云配置,最终文件路劲 = /路劲/文件类型/文件名.png = /ireve/image/aaa.png
    access-key: fcimage # 账号
    secret-key: zbOkMYB3I9lcUDy85waA7j2fZ8BTqnR1 # 密码
    bucketname: fcimage # 空间名
    expires-time: 30 #  请求超时，单位秒
    domain: https://fcimage.fangcang.com # 第三方域名
    path: /ireve # 路劲

# 阿里sls日志配置
#mgs:
#  log:
#    log-type: sls # 日志类型，local：本地，sls：阿里
#    access-log: true # 访问日志 true：开启，false：关闭
#    error-log: true # 访问日志
#    operate-log: true  # 操作日志
#  # 接入阿里SLS日志配置
#  sls:
#    endpoint: cn-shenzhen.log.aliyuncs.com
#    access-key-id: LTAI5tSG1z4d2vEZLgX4mxho
#    access-key-secret: ******************************
#    project: seikosmartcloud-test
#    logStore: platform-mes

logging:
  level:
    root: info # 打印所有的info日志
    com.mongoso.mgs.module.*.dal.mysql: DEBUG # 打印该包下的debug日志
    com.mongoso.mgs.module.model.dal.mysql: DEBUG # 打印该包下的debug日志

