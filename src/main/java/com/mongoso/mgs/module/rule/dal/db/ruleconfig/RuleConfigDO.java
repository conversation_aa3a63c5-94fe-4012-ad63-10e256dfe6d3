package com.mongoso.mgs.module.rule.dal.db.ruleconfig;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 规则配置 DO
 */
@TableName("sys_rule_config")
@Data
@EqualsAndHashCode(callSuper = true)
public class RuleConfigDO extends OperateDO {

    @TableId
    private Long id;
    
    /**
     * 业务ID，用于区分不同业务场景下的规则
     */
    private Long bizId;
    
    /**
     * 规则名称
     */
    private String ruleName;
    
    /**
     * 规则类型：VALIDATION-校验规则，CALCULATION-计算规则
     */
    private String ruleType;
    
    /**
     * 适用表ID，null表示通用规则
     */
    private Long tableId;
    
    /**
     * 适用字段ID，null表示表级规则
     */
    private Long fieldId;
    
    /**
     * 规则条件（JSON格式）
     */
    private String ruleCondition;
    
    /**
     * 规则动作（JSON格式）
     */
    private String ruleAction;
    
    /**
     * 规则优先级
     */
    private Integer priority;
    
    /**
     * 是否启用
     */
    private Boolean enabled;
    
    /**
     * 规则描述
     */
    private String description;
}