package com.mongoso.mgs.module.rule.service;

import com.mongoso.mgs.module.rule.domain.RuleValidationResult;
import java.util.Map;

public interface RuleEngineService {
    
    /**
     * 执行业务规则校验
     */
    RuleValidationResult validateBusinessRules(Long tableId, Map<String, Object> data);
    
    /**
     * 执行业务规则校验（支持bizId维度）
     */
    RuleValidationResult validateBusinessRules(Long bizId, Long tableId, Map<String, Object> data);

    /**
     * 执行计算规则（带业务ID和表ID）
     */
    Map<String, Object> executeCalculationRules(Long bizId, Long tableId, Map<String, Object> data);

    /**
     * 执行计算规则（仅表ID）
     */
    Map<String, Object> executeCalculationRules(Long tableId, Map<String, Object> data);

    /**
     * 执行业务计算规则（基于bizId）
     */
    Map<String, Object> executeBusinessCalculationRules(Long bizId, Map<String, Object> data);

    /**
     * 执行数据校验（支持动态参数）
     * @param bizId 业务ID
     * @param tableId 表ID
     * @param data 数据
     * @param dynamicParams 动态参数
     * @return 校验结果
     */
    RuleValidationResult validateDataWithDynamicParams(Long bizId, Long tableId, Map<String, Object> data, Map<String, Object> dynamicParams);
}
