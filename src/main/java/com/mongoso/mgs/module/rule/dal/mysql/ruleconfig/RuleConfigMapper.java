package com.mongoso.mgs.module.rule.dal.mysql.ruleconfig;

import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.module.rule.dal.db.ruleconfig.RuleConfigDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

/**
 * 规则配置 Mapper
 */
@Mapper
public interface RuleConfigMapper extends BaseMapperX<RuleConfigDO> {

    /**
     * 获取表的最大优先级
     */
    @Select("SELECT MAX(priority) FROM rule_config WHERE biz_id = #{bizId} AND table_id = #{tableId}")
    Integer selectMaxPriority(Long bizId, Long tableId);
}