package com.mongoso.mgs.module.rule.service.impl;

import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.framework.mybatis.core.util.IDUtilX;
import com.mongoso.mgs.module.rule.dal.db.ruleconfig.RuleConfigDO;
import com.mongoso.mgs.module.rule.dal.mysql.ruleconfig.RuleConfigMapper;
import com.mongoso.mgs.module.rule.service.RuleConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import java.util.*;

/**
 * 规则配置 Service 实现类
 */
@Service
@Slf4j
@Validated
public class RuleConfigServiceImpl implements RuleConfigService {

    @Resource
    private RuleConfigMapper ruleConfigMapper;

    @Override
    public List<RuleConfigDO> getTableRules(Long bizId, Long tableId) {
        return ruleConfigMapper.selectList(
            LambdaQueryWrapperX.<RuleConfigDO>lambdaQueryX()
                .eq(RuleConfigDO::getBizId, bizId)
                .eq(RuleConfigDO::getTableId, tableId)
                .eq(RuleConfigDO::getEnabled, true)
                .orderByAsc(RuleConfigDO::getPriority)
        );
    }

    @Override
    public List<RuleConfigDO> getBizRules(Long bizId) {
        return ruleConfigMapper.selectList(
            LambdaQueryWrapperX.<RuleConfigDO>lambdaQueryX()
                .eq(RuleConfigDO::getBizId, bizId)
                .eq(RuleConfigDO::getEnabled, true)
                .orderByAsc(RuleConfigDO::getPriority)
        );
    }

    @Override
    public void saveRule(RuleConfigDO ruleConfig) {
        if (ruleConfig.getId() == null) {
            ruleConfig.setId(IDUtilX.getId());
            // 新增
            ruleConfig.setEnabled(true);
            if (ruleConfig.getPriority() == null) {
                ruleConfig.setPriority(getNextPriority(ruleConfig.getBizId(), ruleConfig.getTableId()));
            }
            ruleConfigMapper.insert(ruleConfig);
        } else {
            // 更新
            checkRuleExists(ruleConfig.getId());
            ruleConfigMapper.updateById(ruleConfig);
        }
    }

    @Override
    public Map<String, Object> getRuleTemplates() {
        Map<String, Object> templates = new HashMap<>();
        
        // 校验规则模板
        List<Map<String, Object>> validationTemplates = Arrays.asList(
            createTemplate("必填校验", "VALIDATION", "FIELD_REQUIRED", 
                "检查字段是否为空", 
                "{\"type\":\"FIELD_NOT_EMPTY\",\"fieldCode\":\"${fieldCode}\"}", 
                "{\"type\":\"ERROR_MESSAGE\",\"message\":\"${fieldName}不能为空\"}"),
                
            createTemplate("正则校验", "VALIDATION", "REGEX_VALIDATE", 
                "使用正则表达式校验字段格式", 
                "{\"type\":\"FIELD_NOT_EMPTY\",\"fieldCode\":\"${fieldCode}\"}", 
                "{\"type\":\"REGEX_VALIDATE\",\"pattern\":\"${pattern}\",\"message\":\"${message}\"}"),
                
            createTemplate("数值范围校验", "VALIDATION", "RANGE_VALIDATE", 
                "校验数值字段的取值范围", 
                "{\"type\":\"FIELD_NOT_EMPTY\",\"fieldCode\":\"${fieldCode}\"}", 
                "{\"type\":\"RANGE_VALIDATE\",\"minValue\":\"${minValue}\",\"maxValue\":\"${maxValue}\",\"message\":\"${message}\"}"),
                
            createTemplate("邮箱格式校验", "VALIDATION", "EMAIL_VALIDATE", 
                "校验邮箱地址格式", 
                "{\"type\":\"FIELD_NOT_EMPTY\",\"fieldCode\":\"${fieldCode}\"}", 
                "{\"type\":\"REGEX_VALIDATE\",\"pattern\":\"^[A-Za-z0-9+_.-]+@(.+)$\",\"message\":\"邮箱格式不正确\"}"),
                
            createTemplate("手机号校验", "VALIDATION", "PHONE_VALIDATE", 
                "校验手机号码格式", 
                "{\"type\":\"FIELD_NOT_EMPTY\",\"fieldCode\":\"${fieldCode}\"}", 
                "{\"type\":\"REGEX_VALIDATE\",\"pattern\":\"^1[3-9]\\\\d{9}$\",\"message\":\"手机号格式不正确\"}")
        );
        
        // 计算规则模板
        List<Map<String, Object>> calculationTemplates = Arrays.asList(
            createTemplate("设置默认值", "CALCULATION", "SET_DEFAULT_VALUE", 
                "为字段设置默认值", 
                "{\"type\":\"FIELD_EMPTY\",\"fieldCode\":\"${fieldCode}\"}", 
                "{\"type\":\"SET_VALUE\",\"fieldCode\":\"${fieldCode}\",\"value\":\"${defaultValue}\"}"),
                
            createTemplate("自动生成编码", "CALCULATION", "AUTO_GENERATE_CODE", 
                "自动生成唯一编码", 
                "{\"type\":\"FIELD_EMPTY\",\"fieldCode\":\"${fieldCode}\"}", 
                "{\"type\":\"SET_VALUE\",\"fieldCode\":\"${fieldCode}\",\"value\":\"${prefix}_${timestamp}\"}"),
                
            createTemplate("公式计算", "CALCULATION", "FORMULA_CALCULATE", 
                "根据公式计算字段值", 
                "{\"type\":\"ALWAYS\"}", 
                "{\"type\":\"CALCULATE_FORMULA\",\"targetField\":\"${targetField}\",\"formula\":\"${formula}\"}"),
                
            createTemplate("复制字段值", "CALCULATION", "COPY_FIELD_VALUE", 
                "将一个字段的值复制到另一个字段", 
                "{\"type\":\"FIELD_NOT_EMPTY\",\"fieldCode\":\"${sourceField}\"}", 
                "{\"type\":\"COPY_VALUE\",\"sourceField\":\"${sourceField}\",\"targetField\":\"${targetField}\"}"),
                
            createTemplate("设置当前时间", "CALCULATION", "SET_CURRENT_TIME", 
                "设置当前时间到指定字段", 
                "{\"type\":\"ALWAYS\"}", 
                "{\"type\":\"SET_VALUE\",\"fieldCode\":\"${fieldCode}\",\"value\":\"${currentTime}\"}")
        );
        
        // 初始化跨表数字比较规则模板
        List<Map<String, Object>> crossTableNumericTemplates = initCrossTableNumericTemplates();
        
        templates.put("validationTemplates", validationTemplates);
        templates.put("calculationTemplates", calculationTemplates);
        templates.put("crossTableNumericTemplates", crossTableNumericTemplates);
        
        return templates;
    }

    @Override
    public void deleteRule(Long id) {
        checkRuleExists(id);
        ruleConfigMapper.deleteById(id);
    }

    @Override
    public RuleConfigDO getRuleConfig(Long id) {
        return checkRuleExists(id);
    }

    @Override
    public void updateRule(RuleConfigDO ruleConfig) {
        checkRuleExists(ruleConfig.getId());
        ruleConfigMapper.updateById(ruleConfig);
    }

    /**
     * 创建规则模板
     */
    private Map<String, Object> createTemplate(String name, String type, String code, 
                                             String description, String condition, String action) {
        Map<String, Object> template = new HashMap<>();
        template.put("name", name);
        template.put("type", type);
        template.put("code", code);
        template.put("description", description);
        template.put("condition", condition);
        template.put("action", action);
        return template;
    }

    /**
     * 获取下一个优先级
     */
    private Integer getNextPriority(Long bizId, Long tableId) {
        Integer maxPriority = ruleConfigMapper.selectMaxPriority(bizId, tableId);
        return maxPriority == null ? 1 : maxPriority + 1;
    }

    /**
     * 校验规则是否存在
     */
    private RuleConfigDO checkRuleExists(Long id) {
        RuleConfigDO rule = ruleConfigMapper.selectById(id);
        if (rule == null) {
            throw new BizException("5001", "规则配置不存在");
        }
        return rule;
    }

    /**
     * 初始化跨表数字比较规则模板
     */
    private List<Map<String, Object>> initCrossTableNumericTemplates() {
        return Arrays.asList(
            // 动态参数比较模板
            createTemplate("动态参数比较", "VALIDATION", "DYNAMIC_PARAM_COMPARE", 
                "使用动态参数进行数字比较", 
                "{\"triggerFields\": [\"${sourceField}\"]}",
                "{\n" +
                "  \"type\": \"CROSS_TABLE_NUMERIC_COMPARE\",\n" +
                "  \"sourceField\": \"${sourceField}\",\n" +
                "  \"dynamicParam\": \"${dynamicParam}\",\n" +
                "  \"operator\": \"${operator}\",\n" +
                "  \"allowNull\": false,\n" +
                "  \"message\": \"${sourceField}值${sourceValue}与动态值${targetValue}比较失败\"\n" +
                "}"),
            
            // 接口数据比较模板
            createTemplate("接口数据比较", "VALIDATION", "API_DATA_COMPARE", 
                "与接口返回数据进行比较", 
                "{\"triggerFields\": [\"apply_amount\"]}",
                "{\n" +
                "  \"type\": \"CROSS_TABLE_NUMERIC_COMPARE\",\n" +
                "  \"sourceField\": \"apply_amount\",\n" +
                "  \"dynamicParam\": \"api_budget_limit\",\n" +
                "  \"operator\": \"<=\",\n" +
                "  \"allowNull\": false,\n" +
                "  \"message\": \"申请金额${sourceValue}超过接口返回的预算限额${targetValue}\"\n" +
                "}"),
            
            // 脚本计算结果比较模板
            createTemplate("脚本结果比较", "VALIDATION", "SCRIPT_RESULT_COMPARE", 
                "与脚本计算结果进行比较", 
                "{\"triggerFields\": [\"score\"]}",
                "{\n" +
                "  \"type\": \"CROSS_TABLE_NUMERIC_COMPARE\",\n" +
                "  \"sourceField\": \"score\",\n" +
                "  \"dynamicParam\": \"calculated_max_score\",\n" +
                "  \"operator\": \"<=\",\n" +
                "  \"allowNull\": false,\n" +
                "  \"message\": \"得分${sourceValue}超过计算的最高分${targetValue}\"\n" +
                "}"),
            
            // 库存校验模板（跨表查询）
            createTemplate("库存校验", "VALIDATION", "STOCK_CHECK", 
                "校验出库数量不能超过库存数量", 
                "{\"triggerFields\": [\"out_qty\", \"ma_detail_id\"]}",
                "{\n" +
                "  \"type\": \"CROSS_TABLE_NUMERIC_COMPARE\",\n" +
                "  \"linkField\": \"ma_detail_id\",\n" +
                "  \"targetLinkField\": \"id\",\n" +
                "  \"sourceField\": \"out_qty\",\n" +
                "  \"targetTableCode\": \"material_stock\",\n" +
                "  \"targetField\": \"available_qty\",\n" +
                "  \"operator\": \"<=\",\n" +
                "  \"allowNull\": false,\n" +
                "  \"message\": \"库存不足！出库数量${sourceValue}超过可用库存${targetValue}\"\n" +
                "}"),
            
            // 混合模式模板（支持两种方式）
            createTemplate("灵活比较", "VALIDATION", "FLEXIBLE_COMPARE", 
                "支持跨表查询或动态参数的灵活比较", 
                "{\"triggerFields\": [\"${sourceField}\"]}",
                "{\n" +
                "  \"type\": \"CROSS_TABLE_NUMERIC_COMPARE\",\n" +
                "  \"sourceField\": \"${sourceField}\",\n" +
                "  \"dynamicParam\": \"${dynamicParam}\",\n" +
                "  \"linkField\": \"${linkField}\",\n" +
                "  \"targetTableCode\": \"${targetTableCode}\",\n" +
                "  \"targetField\": \"${targetField}\",\n" +
                "  \"operator\": \"${operator}\",\n" +
                "  \"allowNull\": false,\n" +
                "  \"message\": \"${sourceField}值${sourceValue}比较失败\"\n" +
                "}")
        );
    }
}
