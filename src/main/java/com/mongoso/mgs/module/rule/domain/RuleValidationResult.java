package com.mongoso.mgs.module.rule.domain;

import java.util.ArrayList;
import java.util.List;

public class RuleValidationResult {
    private boolean valid;
    private String errorMessage;
    private List<String> errorMessages;
    
    public RuleValidationResult() {
        this.valid = true;
        this.errorMessages = new ArrayList<>();
    }
    
    public static RuleValidationResult success() {
        return new RuleValidationResult();
    }
    
    public static RuleValidationResult error(String message) {
        RuleValidationResult result = new RuleValidationResult();
        result.valid = false;
        result.errorMessage = message;
        result.errorMessages.add(message);
        return result;
    }
    
    // getters and setters
    public boolean isValid() { return valid; }
    public void setValid(boolean valid) { this.valid = valid; }
    public String getErrorMessage() { return errorMessage; }
    public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
    public List<String> getErrorMessages() { return errorMessages; }
    public void setErrorMessages(List<String> errorMessages) { this.errorMessages = errorMessages; }
}