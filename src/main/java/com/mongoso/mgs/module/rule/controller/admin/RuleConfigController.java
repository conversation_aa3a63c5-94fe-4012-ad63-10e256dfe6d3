package com.mongoso.mgs.module.rule.controller.admin;

import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.module.rule.dal.db.ruleconfig.RuleConfigDO;
import com.mongoso.mgs.module.rule.service.RuleConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/rule/config")
public class RuleConfigController {
    
    @Autowired
    private RuleConfigService ruleConfigService;
    
    // 获取表的规则配置
    @GetMapping("/table/{bizId}/{tableId}")
    public ResultX<List<RuleConfigDO>> getTableRules(@PathVariable Long bizId, @PathVariable Long tableId) {
        List<RuleConfigDO> rules = ruleConfigService.getTableRules(bizId, tableId);
        return ResultX.success(rules);
    }
    
    // 获取业务下的所有规则配置
    @GetMapping("/biz/{bizId}")
    public ResultX<List<RuleConfigDO>> getBizRules(@PathVariable Long bizId) {
        List<RuleConfigDO> rules = ruleConfigService.getBizRules(bizId);
        return ResultX.success(rules);
    }
    
    // 保存规则配置
    @PostMapping("/save")
    public ResultX<Boolean> saveRule(@RequestBody RuleConfigDO ruleConfig) {
        ruleConfigService.saveRule(ruleConfig);
        return ResultX.success(true);
    }
    
    // 删除规则配置
    @DeleteMapping("/{id}")
    public ResultX<Boolean> deleteRule(@PathVariable Long id) {
        ruleConfigService.deleteRule(id);
        return ResultX.success(true);
    }
    
    // 获取规则配置详情
    @GetMapping("/{id}")
    public ResultX<RuleConfigDO> getRuleConfig(@PathVariable Long id) {
        RuleConfigDO rule = ruleConfigService.getRuleConfig(id);
        return ResultX.success(rule);
    }
    
    // 更新规则配置
    @PutMapping("/update")
    public ResultX<Boolean> updateRule(@RequestBody RuleConfigDO ruleConfig) {
        ruleConfigService.updateRule(ruleConfig);
        return ResultX.success(true);
    }
    
    // 获取规则模板
    @GetMapping("/templates")
    public ResultX<Map<String, Object>> getRuleTemplates() {
        Map<String, Object> templates = ruleConfigService.getRuleTemplates();
        return ResultX.success(templates);
    }
}