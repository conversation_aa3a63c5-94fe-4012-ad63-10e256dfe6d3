package com.mongoso.mgs.module.rule.controller.admin;

import com.mongoso.mgs.module.codegen.dal.pojo.ResultX;
import com.mongoso.mgs.module.rule.domain.RuleValidationResult;
import com.mongoso.mgs.module.rule.service.RuleEngineService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/rule")
@Validated
public class RuleController {
    
    @Autowired
    private RuleEngineService ruleEngineService;
    
    // 执行规则校验
    @PostMapping("/validate/{bizId}/{tableId}")
    public ResultX<RuleValidationResult> validateRules(@PathVariable Long tableId,@PathVariable Long bizId,
                                                       @RequestBody Map<String, Object> data) {
        RuleValidationResult result = ruleEngineService.validateBusinessRules(tableId, data);
        return ResultX.success(result);
    }
    
    // 执行规则计算
    @PostMapping("/calculate/{bizId}/{tableId}")
    public ResultX<Map<String, Object>> calculateRules(@PathVariable Long tableId,@PathVariable Long bizId,
                                                       @RequestBody Map<String, Object> data) {
        Map<String, Object> result = ruleEngineService.executeBusinessCalculationRules(bizId, data);
        return ResultX.success(result);
    }
}