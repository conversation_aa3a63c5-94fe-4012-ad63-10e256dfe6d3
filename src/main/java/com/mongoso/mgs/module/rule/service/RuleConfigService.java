package com.mongoso.mgs.module.rule.service;

import com.mongoso.mgs.module.rule.dal.db.ruleconfig.RuleConfigDO;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 规则配置 Service 接口
 */
public interface RuleConfigService {

    /**
     * 获取表的规则配置
     *
     * @param bizId 业务ID
     * @param tableId 表ID
     * @return 规则配置列表
     */
    List<RuleConfigDO> getTableRules(Long bizId, Long tableId);

    /**
     * 获取业务下的所有规则配置
     *
     * @param bizId 业务ID
     * @return 规则配置列表
     */
    List<RuleConfigDO> getBizRules(Long bizId);

    /**
     * 保存规则配置
     *
     * @param ruleConfig 规则配置
     */
    void saveRule(@Valid RuleConfigDO ruleConfig);

    /**
     * 获取规则模板
     *
     * @return 规则模板
     */
    Map<String, Object> getRuleTemplates();

    /**
     * 删除规则配置
     *
     * @param id 规则ID
     */
    void deleteRule(Long id);

    /**
     * 获取规则配置详情
     *
     * @param id 规则ID
     * @return 规则配置
     */
    RuleConfigDO getRuleConfig(Long id);

    /**
     * 更新规则配置
     *
     * @param ruleConfig 规则配置
     */
    void updateRule(@Valid RuleConfigDO ruleConfig);
}