package com.mongoso.mgs.module.rule.service.impl;

import com.alibaba.fastjson.JSON;
import com.mongoso.mgs.framework.common.util.JsonUtilX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.model.dal.db.generator.BizRelationDO;
import com.mongoso.mgs.module.model.dal.db.modelfield.ModelFieldDO;
import com.mongoso.mgs.module.model.dal.db.modeltable.ModelTableDO;
import com.mongoso.mgs.module.model.dal.mysql.generator.BizRelationMapper;
import com.mongoso.mgs.module.model.dal.mysql.modelfield.ModelFieldMapper;
import com.mongoso.mgs.module.model.dal.mysql.modeltable.ModelTableMapper;
import com.mongoso.mgs.module.model.service.generator.GenericCrudService;
import com.mongoso.mgs.module.rule.dal.db.ruleconfig.RuleConfigDO;
import com.mongoso.mgs.module.rule.dal.mysql.ruleconfig.RuleConfigMapper;
import com.mongoso.mgs.module.rule.domain.RuleValidationResult;
import com.mongoso.mgs.module.rule.service.RuleEngineService;
import jakarta.annotation.PostConstruct;
import org.drools.kiesession.rulebase.InternalKnowledgeBase;
import org.drools.kiesession.rulebase.KnowledgeBaseFactory;
import org.kie.api.io.ResourceType;
import org.kie.api.runtime.KieSession;
import org.kie.internal.builder.KnowledgeBuilder;
import org.kie.internal.builder.KnowledgeBuilderFactory;
import org.kie.internal.io.ResourceFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
public class RuleEngineServiceImpl implements RuleEngineService {
    
    private static final Logger log = LoggerFactory.getLogger(RuleEngineServiceImpl.class);
    
    @Autowired
    private ModelTableMapper modelTableMapper;
    
    @Autowired
    private ModelFieldMapper modelFieldMapper;
    
    @Autowired
    private RuleConfigMapper ruleConfigMapper;

    @Autowired
    private BizRelationMapper bizRelationMapper;

    @Autowired
    private GenericCrudService genericCrudService;

    private InternalKnowledgeBase knowledgeBase;
    
    @PostConstruct
    public void initRuleEngine() {
        try {
            KnowledgeBuilder builder = KnowledgeBuilderFactory.newKnowledgeBuilder();
            
            // 加载基础通用规则
            builder.add(ResourceFactory.newClassPathResource("rules/base-validation-rules.drl"), ResourceType.DRL);
            builder.add(ResourceFactory.newClassPathResource("rules/base-calculation-rules.drl"), ResourceType.DRL);
            
            if (builder.hasErrors()) {
                log.error("基础规则引擎初始化失败: {}", builder.getErrors());
                throw new RuntimeException("基础规则引擎初始化失败");
            }
            
            knowledgeBase = KnowledgeBaseFactory.newKnowledgeBase();
            knowledgeBase.addPackages(builder.getKnowledgePackages());
            
            log.info("规则引擎初始化成功");
        } catch (Exception e) {
            log.error("规则引擎初始化异常", e);
            throw new RuntimeException("规则引擎初始化异常", e);
        }
    }
    
    @Override
    public RuleValidationResult validateBusinessRules(Long tableId, Map<String, Object> data) {
        return validateBusinessRules(null, tableId, data);
    }
    
    @Override
    public RuleValidationResult validateBusinessRules(Long bizId, Long tableId, Map<String, Object> data) {
        log.info("开始执行规则校验, bizId: {}, tableId: {}", bizId, tableId);
        
        try {
            // 如果是树形结构数据，需要递归校验
            if (data.containsKey("table_id") && data.containsKey("data")) {
                return validateTreeStructureDataWithCache(bizId, data);
            }
            
            // 原有的单表数据校验逻辑
            List<Long> tableIds = new ArrayList<>();
            
            if (bizId != null) {
                // 通过bizId获取所有关联的表ID
                List<BizRelationDO> bizRelations = bizRelationMapper.selectByBizId(bizId);
                tableIds = bizRelations.stream()
                    .map(BizRelationDO::getTableId)
                    .distinct()
                    .collect(Collectors.toList());
                
                // 如果指定了tableId，只校验该表
                if (tableId != null && !tableIds.contains(tableId)) {
                    tableIds = Arrays.asList(tableId);
                }
            } else if (tableId != null) {
                // 只校验指定的表
                tableIds = Arrays.asList(tableId);
            } else {
                return RuleValidationResult.error("bizId和tableId不能同时为空");
            }
            
            // 批量预加载数据以优化性能
            Map<Long, ModelTableDO> tableCache = new HashMap<>();
            Map<Long, List<ModelFieldDO>> fieldCache = new HashMap<>();
            Map<Long, List<RuleConfigDO>> ruleCache = new HashMap<>();
            
            if (!tableIds.isEmpty()) {
                // 批量查询表信息
                List<ModelTableDO> tables = modelTableMapper.selectBatchIds(tableIds);
                tableCache = tables.stream().collect(Collectors.toMap(ModelTableDO::getTableId, t -> t));
                
                // 批量查询字段信息
                List<ModelFieldDO> allFields = modelFieldMapper.selectList(
                    LambdaQueryWrapperX.<ModelFieldDO>lambdaQueryX()
                        .in(ModelFieldDO::getTableId, tableIds)
                        .orderByAsc(ModelFieldDO::getSort)
                );
                fieldCache = allFields.stream().collect(Collectors.groupingBy(ModelFieldDO::getTableId));
                
                // 批量查询自定义规则配置
                LambdaQueryWrapperX<RuleConfigDO> ruleQuery = LambdaQueryWrapperX.<RuleConfigDO>lambdaQueryX()
                    .eq(RuleConfigDO::getRuleType, "VALIDATION")
                    .eq(RuleConfigDO::getEnabled, true);

                List<Long> finalTableIds = tableIds;
                if (bizId != null) {
                    ruleQuery.and(wrapper -> 
                        wrapper.eq(RuleConfigDO::getBizId, bizId)
                               .or()
                               .isNull(RuleConfigDO::getBizId)
                    );

                    ruleQuery.and(wrapper ->
                            wrapper.in(RuleConfigDO::getTableId, finalTableIds)
                                    .or()
                                    .isNull(RuleConfigDO::getTableId)
                    );
                }else{
                    ruleQuery.and(wrapper ->
                            wrapper.isNull(RuleConfigDO::getBizId)
                    );

                    ruleQuery.and(wrapper ->
                            wrapper.in(RuleConfigDO::getTableId, finalTableIds)
                                    .or()
                                    .isNull(RuleConfigDO::getTableId)
                    );
                }

                
                List<RuleConfigDO> allRules = ruleConfigMapper.selectList(ruleQuery);
                ruleCache = allRules.stream().collect(Collectors.groupingBy(
                    rule -> rule.getTableId() != null ? rule.getTableId() : 0L
                ));
            }
            
            // 对每个表执行校验（使用缓存）
            for (Long currentTableId : tableIds) {
                RuleValidationResult result = validateSingleTableWithCache(
                    bizId, currentTableId, data, tableCache, fieldCache, ruleCache);
                if (!result.isValid()) {
                    return result;
                }
            }
            
            return RuleValidationResult.success();
            
        } catch (Exception e) {
            log.error("规则校验异常, bizId: {}, tableId: {}, error: {}", bizId, tableId, e.getMessage(), e);
            return RuleValidationResult.error("规则校验异常: " + e.getMessage());
        }
    }
    
    /**
     * 校验单个表的基础规则和自定义规则（优化版本）
     */
    private RuleValidationResult validateSingleTableWithCache(
            Long bizId, 
            Long tableId, 
            Map<String, Object> data,
            Map<Long, ModelTableDO> tableCache,
            Map<Long, List<ModelFieldDO>> fieldCache,
            Map<Long, List<RuleConfigDO>> ruleCache) {
        
        // 从缓存获取表信息
        ModelTableDO table = tableCache.get(tableId);
        if (table == null) {
            return RuleValidationResult.error("表不存在: " + tableId);
        }
        
        // 从缓存获取字段信息
        List<ModelFieldDO> fields = fieldCache.getOrDefault(tableId, Collections.emptyList());
        
        // 1. 执行基础规则校验（使用Drools）
        log.debug("开始执行基础规则校验, tableId: {}", tableId);
        RuleValidationResult baseResult = executeBaseValidation(table, fields, data);
        if (!baseResult.isValid()) {
            log.warn("基础规则校验失败, tableId: {}, error: {}", tableId, baseResult.getErrorMessage());
            return baseResult;
        }
        
        // 2. 执行自定义规则校验（基于配置）
        log.debug("开始执行自定义规则校验, bizId: {}, tableId: {}", bizId, tableId);
        RuleValidationResult customResult = executeCustomValidationWithCache(bizId, tableId, fields, data, ruleCache);
        if (!customResult.isValid()) {
            log.warn("自定义规则校验失败, bizId: {}, tableId: {}, error: {}", bizId, tableId, customResult.getErrorMessage());
            return customResult;
        }
        
        log.debug("规则校验通过, bizId: {}, tableId: {}", bizId, tableId);
        return RuleValidationResult.success();
    }

    @Override
    public Map<String, Object> executeCalculationRules(Long tableId, Map<String, Object> data) {
        log.debug("开始执行单表计算规则, tableId: {}", tableId);

        try {
            // 单表计算逻辑
            Map<String, Object> result = new HashMap<>(data);

            if (tableId != null) {
                executeCalculationForTable(null, tableId, result);
            }

            return result;

        } catch (Exception e) {
            log.error("计算规则异常, tableId: {}, error: {}", tableId, e.getMessage(), e);
            return data;
        }
    }

    @Override
    public Map<String, Object> executeCalculationRules(Long bizId, Long tableId, Map<String, Object> data) {
        log.info("开始执行计算规则, bizId: {}, tableId: {}, 输入数据: {}", bizId, tableId, data.keySet());
        
        try {
            // 创建结果副本
            Map<String, Object> result = new HashMap<>(data);
            
            if (tableId != null) {
                // 执行表级计算规则
                result = executeCalculationForTable(bizId, tableId, result);
                log.info("表级计算完成, 结果: {}", result.keySet());
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("计算规则异常, bizId: {}, tableId: {}, error: {}", bizId, tableId, e.getMessage(), e);
            return data;
        }
    }
    
    @Override
    public Map<String, Object> executeBusinessCalculationRules(Long bizId, Map<String, Object> data) {
        log.info("开始执行业务计算规则, bizId: {}", bizId);
        try {
            // 如果是树形结构数据，需要递归处理
            if (data.containsKey("table_id") && data.containsKey("data")) {
                return executeTreeCalculationRules(bizId, data);
            }
            
            // 业务级计算逻辑
            Map<String, Object> result = new HashMap<>(data);
            
            if (bizId != null) {
                // 通过bizId获取所有关联的表ID进行计算
                List<BizRelationDO> bizRelations = bizRelationMapper.selectByBizId(bizId);
                for (BizRelationDO relation : bizRelations) {
                    executeCalculationForTable(bizId, relation.getTableId(), result);
                }
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("业务计算规则异常, bizId: {}, error: {}", bizId, e.getMessage(), e);
            return data;
        }
    }
    
    /**
     * 执行树形结构的计算规则
     */
    private Map<String, Object> executeTreeCalculationRules(Long bizId, Map<String, Object> treeData) {
        // 提取全局动态参数
        Map<String, Object> globalDynamicParams = extractGlobalDynamicParams(treeData);
        log.info("计算规则使用全局动态参数: {}", globalDynamicParams.keySet());
        
        return executeTreeCalculationRulesWithParams(bizId, treeData, globalDynamicParams);
    }

    /**
     * 递归执行计算规则（带动态参数）
     */
    private Map<String, Object> executeTreeCalculationRulesWithParams(Long bizId, Map<String, Object> treeData, 
                                                                     Map<String, Object> globalDynamicParams) {
        Map<String, Object> result = new HashMap<>(treeData);
        
        // 获取当前层级的表ID
        Long currentTableId = Long.parseLong(treeData.get("table_id").toString());
        
        // 获取当前层级的数据列表
        List<Map<String, Object>> dataList = (List<Map<String, Object>>) result.get("data");
        
        if (dataList != null && !dataList.isEmpty()) {
            for (Map<String, Object> dataRecord : dataList) {
                // 提取并计算业务数据
                Map<String, Object> businessData = extractBusinessData(dataRecord);
                
                // 合并全局动态参数
                Map<String, Object> mergedData = new HashMap<>(businessData);
                mergedData.putAll(globalDynamicParams);
                
                Map<String, Object> calculatedData = executeCalculationForTable(bizId, currentTableId, mergedData);
                
                // 将计算结果合并回原数据（排除动态参数，避免污染子节点）
                for (Map.Entry<String, Object> entry : calculatedData.entrySet()) {
                    String key = entry.getKey();
                    if (!Set.of("children", "table_id", "data", "path").contains(key) && 
                        !globalDynamicParams.containsKey(key)) {
                        dataRecord.put(key, entry.getValue());
                    }
                }
                
                // 递归处理子级数据
                if (dataRecord.containsKey("children")) {
                    List<Map<String, Object>> children = (List<Map<String, Object>>) dataRecord.get("children");
                    if (children != null && !children.isEmpty()) {
                        for (int i = 0; i < children.size(); i++) {
                            Map<String, Object> child = children.get(i);
                            if (child.containsKey("table_id") && child.containsKey("data")) {
                                children.set(i, executeTreeCalculationRulesWithParams(bizId, child, globalDynamicParams));
                            }
                        }
                    }
                }
            }
        }
        
        return result;
    }
    
    /**
     * 为指定表执行计算规则
     */
    private Map<String, Object> executeCalculationForTable(Long bizId, Long tableId, Map<String, Object> data) {
        log.info("执行表级计算规则, bizId: {}, tableId: {}", bizId, tableId);
        
        Map<String, Object> result = new HashMap<>(data);
        
        // 获取表和字段信息
        ModelTableDO table = modelTableMapper.selectById(tableId);
        if (table == null) {
            log.warn("表不存在: {}", tableId);
            return result;
        }
        
        List<ModelFieldDO> fields = modelFieldMapper.selectList(
            LambdaQueryWrapperX.<ModelFieldDO>lambdaQueryX()
                .eq(ModelFieldDO::getTableId, tableId)
                .orderByAsc(ModelFieldDO::getSort)
        );
        
        log.info("找到{}个字段", fields.size());
        
        // 1. 执行基础计算规则
        executeBaseCalculation(table, fields, result);
        
        // 2. 执行自定义计算规则
        executeCustomCalculation(bizId, tableId, fields, result);
        
        log.info("表级计算完成, 最终结果: {}", result.keySet());
        return result;
    }
    
    /**
     * 执行基础校验规则（使用Drools）
     */
    private RuleValidationResult executeBaseValidation(ModelTableDO table, List<ModelFieldDO> fields, Map<String, Object> data) {
        KieSession session = knowledgeBase.newKieSession();
        
        try {
            RuleValidationResult result = RuleValidationResult.success();
            
            // 插入事实对象
            session.insert(table);
            session.insert(data);
            session.insert(result);
            
            // 系统字段列表
            Set<String> systemFields = Set.of(
                "createdDt", "updatedDt", "createdBy", "updatedBy",
                "createTime", "updateTime", "creator", "updater",
                "created_dt", "updated_dt", "created_by", "updated_by"
            );
            
            for (ModelFieldDO field : fields) {
                // 排除主键字段和系统字段
                if (field.getIsPrimaryKey() == 1 || systemFields.contains(field.getFieldCode())) {
                    continue;
                }
                session.insert(field);
            }
            
            // 执行基础规则
            session.fireAllRules();
            
            return result;
            
        } finally {
            session.dispose();
        }
    }
    
    /**
     * 执行自定义校验规则（基于配置，支持bizId维度）
     */
    private RuleValidationResult executeCustomValidation(Long bizId, Long tableId, List<ModelFieldDO> fields, Map<String, Object> data) {
        // 构建查询条件
        LambdaQueryWrapperX<RuleConfigDO> queryWrapper = LambdaQueryWrapperX.<RuleConfigDO>lambdaQueryX()
                .eq(RuleConfigDO::getRuleType, "VALIDATION")
                .eq(RuleConfigDO::getEnabled, true);
        
        // 添加bizId或tableId条件
        if (bizId != null) {
            queryWrapper.and(wrapper -> 
                wrapper.eq(RuleConfigDO::getBizId, bizId)
                       .or()
                       .isNull(RuleConfigDO::getBizId)
            );
        }
        
        queryWrapper.and(wrapper -> 
            wrapper.eq(RuleConfigDO::getTableId, tableId)
                   .or()
                   .isNull(RuleConfigDO::getTableId)
        );
        
        queryWrapper.isNull(RuleConfigDO::getFieldId)
                   .orderByAsc(RuleConfigDO::getPriority);
        
        // 获取表级自定义规则
        List<RuleConfigDO> tableRules = ruleConfigMapper.selectList(queryWrapper);
        
        // 执行表级规则
        for (RuleConfigDO rule : tableRules) {
            RuleValidationResult result = executeCustomRule(rule, data, null);
            if (!result.isValid()) {
                return result;
            }
        }
        
        // 获取字段级自定义规则
        for (ModelFieldDO field : fields) {
            LambdaQueryWrapperX<RuleConfigDO> fieldQueryWrapper = LambdaQueryWrapperX.<RuleConfigDO>lambdaQueryX()
                    .eq(RuleConfigDO::getRuleType, "VALIDATION")
                    .eq(RuleConfigDO::getFieldId, field.getFieldId())
                    .eq(RuleConfigDO::getEnabled, true);
            
            // 添加bizId条件
            if (bizId != null) {
                fieldQueryWrapper.and(wrapper -> 
                    wrapper.eq(RuleConfigDO::getBizId, bizId)
                           .or()
                           .isNull(RuleConfigDO::getBizId)
                );
            }
            
            fieldQueryWrapper.orderByAsc(RuleConfigDO::getPriority);
            
            List<RuleConfigDO> fieldRules = ruleConfigMapper.selectList(fieldQueryWrapper);
            
            for (RuleConfigDO rule : fieldRules) {
                RuleValidationResult result = executeCustomRule(rule, data, field);
                if (!result.isValid()) {
                    return result;
                }
            }
        }
        
        return RuleValidationResult.success();
    }
    
    /**
     * 执行基础计算规则（使用Drools）
     */
    private void executeBaseCalculation(ModelTableDO table, List<ModelFieldDO> fields, Map<String, Object> data) {
        KieSession session = knowledgeBase.newKieSession();
        
        try {
            // 插入事实对象
            session.insert(table);
            session.insert(data);
            session.setGlobal("currentTime", LocalDateTime.now());
            
            for (ModelFieldDO field : fields) {
                session.insert(field);
            }
            
            // 执行基础计算规则
            session.fireAllRules();
            
        } finally {
            session.dispose();
        }
    }
    
    /**
     * 执行自定义计算规则（基于配置）
     */
    private void executeCustomCalculation(Long bizId, Long tableId, List<ModelFieldDO> fields, Map<String, Object> data) {
        log.info("开始执行自定义计算规则, bizId: {}, tableId: {}", bizId, tableId);
        
        // 获取计算规则
        List<RuleConfigDO> calcRules = ruleConfigMapper.selectList(
            LambdaQueryWrapperX.<RuleConfigDO>lambdaQueryX()
                .eq(RuleConfigDO::getRuleType, "CALCULATION")
                .eq(RuleConfigDO::getEnabled, true)
                .orderByAsc(RuleConfigDO::getPriority)
        );
        
        log.info("找到{}条计算规则", calcRules.size());
        
        for (RuleConfigDO rule : calcRules) {
            log.info("检查规则: {}, bizId匹配: {}, tableId匹配: {}", 
                rule.getRuleName(), 
                rule.getBizId() == null || Objects.equals(rule.getBizId(), bizId),
                rule.getTableId() == null || Objects.equals(rule.getTableId(), tableId));
            
            // 检查规则是否适用于当前业务和表
            if ((rule.getBizId() == null || Objects.equals(rule.getBizId(), bizId)) &&
                (rule.getTableId() == null || Objects.equals(rule.getTableId(), tableId))) {
                
                log.info("执行规则: {}", rule.getRuleName());
                executeCustomCalculationRule(rule, data, fields);
            }
        }
        
        log.info("自定义计算规则执行完成, 数据: {}", data.keySet());
    }
    
    /**
     * 执行自定义规则
     */
    private RuleValidationResult executeCustomRule(RuleConfigDO rule, Map<String, Object> data, ModelFieldDO field) {
        try {
            // 解析规则条件
            Map<String, Object> condition = JSON.parseObject(rule.getRuleCondition());
            if (!checkRuleCondition(condition, data, field)) {
                return RuleValidationResult.success();
            }
            
            // 解析规则动作
            Map<String, Object> action = JSON.parseObject(rule.getRuleAction());
            
            // 执行校验动作
            String actionType = action.containsKey("type") ? (String) action.get("type") : "";
            switch (actionType) {
                case "REGEX_VALIDATE":
                    return validateRegex(action, data, field);
                case "RANGE_VALIDATE":
                    return validateRange(action, data, field);
                case "ENUM_VALIDATE":
                    return validateEnum(action, data, field);
                case "NUMERIC_COMPARE":
                    return validateNumericCompare(rule, data, field);
                case "CROSS_TABLE_NUMERIC_COMPARE":
                    return validateCrossTableNumericCompare(rule, data, field);
                case "CUSTOM_VALIDATE":
                    return validateCustom(action, data, field);
                default:
                    return RuleValidationResult.success();
            }
            
        } catch (Exception e) {
            log.error("执行自定义规则失败: {}", rule.getRuleName(), e);
            return RuleValidationResult.error("规则执行异常: " + rule.getRuleName());
        }
    }

    /**
     * 执行数字比较校验（同表内字段比较）
     */
    private RuleValidationResult validateNumericCompare(RuleConfigDO rule, Map<String, Object> data, ModelFieldDO field) {
        try {
            Map<String, Object> action = JsonUtilX.parseObject(rule.getRuleAction(), Map.class);
            
            String sourceField = (String) action.get("sourceField");
            String targetField = (String) action.get("targetField");
            String operator = (String) action.get("operator");
            String message = (String) action.get("message");
            Boolean allowNull = (Boolean) action.getOrDefault("allowNull", true);
            
            Object sourceValue = data.get(sourceField);
            Object targetValue = data.get(targetField);
            
            if (sourceValue == null || targetValue == null) {
                return allowNull ? RuleValidationResult.success() : 
                    RuleValidationResult.error("必填字段不能为空: " + (sourceValue == null ? sourceField : targetField));
            }
            
            BigDecimal sourceNumber = convertToNumber(sourceValue);
            BigDecimal targetNumber = convertToNumber(targetValue);
            
            boolean isValid = compareNumbers(sourceNumber, targetNumber, operator);
            
            if (!isValid) {
                String errorMessage = formatNumericErrorMessage(message, sourceNumber, targetNumber, operator, 
                    sourceField, targetField);
                return RuleValidationResult.error(errorMessage);
            }
            
            return RuleValidationResult.success();
            
        } catch (Exception e) {
            log.error("数字比较校验失败", e);
            return RuleValidationResult.error("数字比较校验失败: " + e.getMessage());
        }
    }

    /**
     * 执行跨表数字比较校验（支持动态参数）
     */
    private RuleValidationResult validateCrossTableNumericCompare(RuleConfigDO rule, Map<String, Object> data, ModelFieldDO field) {
        try {
            Map<String, Object> action = JsonUtilX.parseObject(rule.getRuleAction(), Map.class);

            String sourceField = (String) action.get("sourceField");           // 源数字字段
            String operator = (String) action.get("operator");                 // 比较操作符
            String message = (String) action.get("message");                   // 错误消息模板
            Boolean allowNull = (Boolean) action.getOrDefault("allowNull", true);

            // 获取源数据
            Object sourceValue = data.get(sourceField);
            if (sourceValue == null && !allowNull) {
                return RuleValidationResult.error("必填字段不能为空: " + sourceField);
            }

            BigDecimal targetNumber;
            
            // 检查是否使用动态参数
            String dynamicParam = (String) action.get("dynamicParam");
            if (dynamicParam != null) {
                log.info("使用动态参数: {}, 数据中的值: {}, 所有数据字段: {}", 
                        dynamicParam, data.get(dynamicParam), data.keySet());
                
                // 直接从data中获取动态参数值
                Object dynamicValue = data.get(dynamicParam);
                if (dynamicValue == null && !allowNull) {
                    return RuleValidationResult.error("动态参数值不能为空: " + dynamicParam);
                }
                targetNumber = convertToNumber(dynamicValue);
                
                log.info("动态参数校验: {} {} {}", dynamicParam, operator, targetNumber);
            } else {
                // 使用跨表查询模式的逻辑...
                targetNumber = BigDecimal.ZERO;
            }

            // 执行比较
            BigDecimal sourceNumber = convertToNumber(sourceValue);
            boolean isValid = compareNumbers(sourceNumber, targetNumber, operator);
            if (!isValid) {
                String formattedMessage = message
                    .replace("${sourceValue}", sourceNumber.toString())
                    .replace("${targetValue}", targetNumber.toString());
                return RuleValidationResult.error(formattedMessage);
            }

            return RuleValidationResult.success();

        } catch (Exception e) {
            log.error("跨表数字比较校验失败", e);
            return RuleValidationResult.error("跨表数字比较校验失败: " + e.getMessage());
        }
    }

    /**
     * 转换为数字类型
     */
    private BigDecimal convertToNumber(Object value) {
        if (value == null) {
            return BigDecimal.ZERO;
        }
        if (value instanceof BigDecimal) {
            return (BigDecimal) value;
        }
        if (value instanceof Number) {
            return BigDecimal.valueOf(((Number) value).doubleValue());
        }
        return new BigDecimal(value.toString());
    }

    /**
     * 执行数字比较
     */
    private boolean compareNumbers(BigDecimal source, BigDecimal target, String operator) {
        switch (operator.toUpperCase()) {
            case ">":
            case "GT":
                return source.compareTo(target) > 0;
            case ">=":
            case "GTE":
                return source.compareTo(target) >= 0;
            case "<":
            case "LT":
                return source.compareTo(target) < 0;
            case "<=":
            case "LTE":
                return source.compareTo(target) <= 0;
            case "=":
            case "==":
            case "EQ":
                return source.compareTo(target) == 0;
            case "!=":
            case "<>":
            case "NE":
                return source.compareTo(target) != 0;
            default:
                throw new IllegalArgumentException("不支持的比较操作符: " + operator);
        }
    }

    /**
     * 格式化数字比较错误消息
     */
    private String formatNumericErrorMessage(String template, BigDecimal sourceValue, BigDecimal targetValue,
                                           String operator, String sourceField, String targetField) {
        if (template == null || template.isEmpty()) {
            return String.format("%s(%s) %s %s(%s) 校验失败",
                    sourceField, sourceValue, operator, targetField, targetValue);
        }

        // 支持占位符替换
        return template
                .replace("${sourceValue}", sourceValue.toString())
                .replace("${targetValue}", targetValue.toString())
                .replace("${operator}", operator)
                .replace("${sourceField}", sourceField)
                .replace("${targetField}", targetField);
    }

    /**
     * 枚举值校验
     */
    private RuleValidationResult validateEnum(Map<String, Object> action, Map<String, Object> data, ModelFieldDO field) {
        String fieldCode = field != null ? field.getFieldCode() : (String) action.get("fieldCode");
        List<Object> allowedValues = (List<Object>) action.get("allowedValues");
        String message = (String) action.get("message");
        
        Object value = data.get(fieldCode);
        if (value != null && allowedValues != null) {
            Object enumValue = (Object)value;
            if (!allowedValues.contains(enumValue)) {
                return RuleValidationResult.error(message != null ? message :
                    fieldCode + "的值必须是" + allowedValues + "之一");
            }
        }
        
        return RuleValidationResult.success();
    }
    
    /**
     * 执行单个自定义计算规则
     */
    private void executeCustomCalculationRule(RuleConfigDO rule, Map<String, Object> data, List<ModelFieldDO> fields) {
        try {
            log.info("执行规则: {}, 条件: {}, 动作: {}", rule.getRuleName(), rule.getRuleCondition(), rule.getRuleAction());
            
            Map<String, Object> condition = JSON.parseObject(rule.getRuleCondition(), Map.class);
            Map<String, Object> action = JSON.parseObject(rule.getRuleAction(), Map.class);
            
            // 检查条件是否满足
            if (!checkRuleCondition(condition, data, null)) {
                log.info("规则条件不满足: {}", rule.getRuleName());
                return;
            }
            
            log.info("规则条件满足，执行动作: {}", action);
            
            // 执行计算动作
            String actionType = (String) action.get("type");
            switch (actionType) {
                case "ENUM_TRANSLATE":
                    translateEnumValue(action, data);
                    log.info("枚举翻译完成，当前数据: {}", data);
                    break;
                case "SET_VALUE":
                    setFieldValue(action, data);
                    break;
                case "CALCULATE_FORMULA":
                    calculateFormula(action, data);
                    break;
                case "COPY_VALUE":
                    copyFieldValue(action, data);
                    break;
                default:
                    log.warn("未知的动作类型: {}", actionType);
            }
            
        } catch (Exception e) {
            log.error("执行自定义计算规则失败: {}, error: {}", rule.getRuleName(), e.getMessage(), e);
        }
    }
    
    /**
     * 枚举值翻译
     */
    private void translateEnumValue(Map<String, Object> action, Map<String, Object> data) {
        String sourceField = (String) action.get("sourceField");
        String targetField = (String) action.get("targetField");
        Map<String, String> enumMapping = (Map<String, String>) action.get("enumMapping");
        
        log.info("枚举翻译 - 源字段: {}, 目标字段: {}, 当前数据: {}", sourceField, targetField, data.keySet());
        
        Object sourceValue = data.get(sourceField);
        log.info("源字段 {} 的值: {}", sourceField, sourceValue);
        
        if (sourceValue != null && enumMapping != null) {
            String translatedValue = enumMapping.get(sourceValue.toString());
            log.info("翻译映射: {} -> {}", sourceValue, translatedValue);
            
            if (translatedValue != null) {
                data.put(targetField, translatedValue);
                log.info("成功设置目标字段 {} = {}, 当前数据: {}", targetField, translatedValue, data.keySet());
            } else {
                log.warn("未找到枚举值 {} 的翻译", sourceValue);
            }
        } else {
            log.warn("源字段值为空或枚举映射为空 - sourceValue: {}, enumMapping: {}", sourceValue, enumMapping);
        }
    }
    
    /**
     * 检查规则条件
     */
    private boolean checkRuleCondition(Map<String, Object> condition, Map<String, Object> data, ModelFieldDO field) {
        String conditionType = (String) condition.get("type");
        
        switch (conditionType) {
            case "FIELD_NOT_EMPTY":
                String fieldCode = (String) condition.get("fieldCode");
                Object value = data.get(fieldCode);
                return value != null && !value.toString().trim().isEmpty();
                
            case "FIELD_EQUALS":
                String targetField = (String) condition.get("fieldCode");
                Object expectedValue = condition.get("value");
                Object actualValue = data.get(targetField);
                return expectedValue != null && expectedValue.equals(actualValue);
                
            case "FIELD_TYPE":
                if (field != null) {
                    String expectedType = (String) condition.get("fieldType");
                    return expectedType.equals(field.getFieldType());
                }
                return false;
                
            default:
                return true;
        }
    }
    
    /**
     * 正则表达式校验
     */
    private RuleValidationResult validateRegex(Map<String, Object> action, Map<String, Object> data, ModelFieldDO field) {
        String fieldCode = field != null ? field.getFieldCode() : (String) action.get("fieldCode");
        String pattern = (String) action.get("pattern");
        String message = (String) action.get("message");
        
        Object value = data.get(fieldCode);
        if (value != null && !Pattern.matches(pattern, value.toString())) {
            return RuleValidationResult.error(message);
        }
        
        return RuleValidationResult.success();
    }
    
    /**
     * 范围校验
     */
    private RuleValidationResult validateRange(Map<String, Object> action, Map<String, Object> data, ModelFieldDO field) {
        String fieldCode = field != null ? field.getFieldCode() : (String) action.get("fieldCode");
        Object minValue = action.get("minValue");
        Object maxValue = action.get("maxValue");
        String message = (String) action.get("message");
        
        Object value = data.get(fieldCode);
        if (value != null) {
            try {
                BigDecimal numValue = new BigDecimal(value.toString());
                if (minValue != null && numValue.compareTo(new BigDecimal(minValue.toString())) < 0) {
                    return RuleValidationResult.error(message);
                }
                if (maxValue != null && numValue.compareTo(new BigDecimal(maxValue.toString())) > 0) {
                    return RuleValidationResult.error(message);
                }
            } catch (NumberFormatException e) {
                return RuleValidationResult.error("数值格式错误");
            }
        }
        
        return RuleValidationResult.success();
    }
    
    /**
     * 自定义校验
     */
    private RuleValidationResult validateCustom(Map<String, Object> action, Map<String, Object> data, ModelFieldDO field) {
        // 可以扩展更复杂的自定义校验逻辑
        return RuleValidationResult.success();
    }
    
    /**
     * 设置字段值
     */
    private void setFieldValue(Map<String, Object> action, Map<String, Object> data) {
        String fieldCode = (String) action.get("fieldCode");
        Object value = action.get("value");
        data.put(fieldCode, value);
    }
    
    /**
     * 公式计算
     */
    private void calculateFormula(Map<String, Object> action, Map<String, Object> data) {
        String targetField = (String) action.get("targetField");
        String formula = (String) action.get("formula");
        
        // 简单的公式计算实现，可以扩展为更复杂的表达式引擎
        if (formula.contains("+")) {
            String[] parts = formula.split("\\+");
            BigDecimal result = BigDecimal.ZERO;
            for (String part : parts) {
                Object value = data.get(part.trim());
                if (value != null) {
                    result = result.add(new BigDecimal(value.toString()));
                }
            }
            data.put(targetField, result);
        }
    }
    
    /**
     * 复制字段值
     */
    private void copyFieldValue(Map<String, Object> action, Map<String, Object> data) {
        String sourceField = (String) action.get("sourceField");
        String targetField = (String) action.get("targetField");
        Object value = data.get(sourceField);
        if (value != null) {
            data.put(targetField, value);
        }
    }
    
    /**
     * 树形结构数据校验的入口方法（优化版本）
     */
    private RuleValidationResult validateTreeStructureDataWithCache(Long bizId, Map<String, Object> treeData) {
        // 提取根级别的动态参数
        Map<String, Object> globalDynamicParams = extractGlobalDynamicParams(treeData);
        log.info("提取到全局动态参数: {}", globalDynamicParams.keySet());
        
        // 预加载所有需要的表和字段信息
        Map<Long, ModelTableDO> tableCache = new HashMap<>();
        Map<Long, List<ModelFieldDO>> fieldCache = new HashMap<>();
        Map<Long, List<RuleConfigDO>> ruleCache = new HashMap<>();
        
        // 收集所有表ID
        Set<Long> allTableIds = collectAllTableIds(treeData);
        
        // 预加载缓存数据
        preloadCacheData(allTableIds, tableCache, fieldCache, ruleCache);
        
        // 开始递归校验，传递动态参数
        return validateTreeStructureDataWithCache(bizId, treeData, tableCache, fieldCache, ruleCache, globalDynamicParams);
    }
    
    /**
     * 提取全局动态参数
     */
    private Map<String, Object> extractGlobalDynamicParams(Map<String, Object> rootData) {
        Map<String, Object> dynamicParams = new HashMap<>();
        
        // 从根数据中提取动态参数（非树形结构字段）
        for (Map.Entry<String, Object> entry : rootData.entrySet()) {
            String key = entry.getKey();
            // 排除树形结构的标准字段
            if (!Set.of("table_id", "data", "children", "path").contains(key)) {
                dynamicParams.put(key, entry.getValue());
            }
        }
        
        return dynamicParams;
    }

    /**
     * 递归校验树形结构数据（带缓存和动态参数）
     */
    private RuleValidationResult validateTreeStructureDataWithCache(Long bizId, Map<String, Object> treeData,
                                                                   Map<Long, ModelTableDO> tableCache,
                                                                   Map<Long, List<ModelFieldDO>> fieldCache,
                                                                   Map<Long, List<RuleConfigDO>> ruleCache,
                                                                   Map<String, Object> globalDynamicParams) {
        if (treeData == null || treeData.isEmpty()) {
            return RuleValidationResult.success();
        }

        // 获取当前层级的表ID
        Long currentTableId = Long.parseLong(treeData.get("table_id").toString());
        
        // 获取当前层级的数据列表
        List<Map<String, Object>> dataList = (List<Map<String, Object>>) treeData.get("data");
        
        if (dataList != null && !dataList.isEmpty()) {
            for (Map<String, Object> dataRecord : dataList) {
                // 提取业务数据
                Map<String, Object> businessData = extractBusinessData(dataRecord);
                
                // 合并全局动态参数到业务数据中
                Map<String, Object> mergedData = new HashMap<>(businessData);
                mergedData.putAll(globalDynamicParams);
                
                log.info("表[{}]校验数据字段: {}, 包含动态参数: {}", 
                        currentTableId, businessData.keySet(), globalDynamicParams.keySet());
                
                // 执行当前表的规则校验
                RuleValidationResult result = validateTableDataWithCache(
                    bizId, currentTableId, mergedData, tableCache, fieldCache, ruleCache);
                
                if (!result.isValid()) {
                    return result;
                }
                
                // 递归校验子级数据
                if (dataRecord.containsKey("children")) {
                    List<Map<String, Object>> children = (List<Map<String, Object>>) dataRecord.get("children");
                    if (children != null && !children.isEmpty()) {
                        for (Map<String, Object> child : children) {
                            if (child.containsKey("table_id") && child.containsKey("data")) {
                                RuleValidationResult childResult = validateTreeStructureDataWithCache(
                                    bizId, child, tableCache, fieldCache, ruleCache, globalDynamicParams);
                                if (!childResult.isValid()) {
                                    return childResult;
                                }
                            }
                        }
                    }
                }
            }
        }
        
        return RuleValidationResult.success();
    }
    
    /**
     * 收集树形数据中的所有表ID
     */
    private Set<Long> collectAllTableIds(Map<String, Object> treeData) {
        Set<Long> tableIds = new HashSet<>();
        
        if (treeData.containsKey("table_id")) {
            tableIds.add(Long.parseLong(treeData.get("table_id").toString()));
        }
        
        List<Map<String, Object>> dataList = (List<Map<String, Object>>) treeData.get("data");
        if (dataList != null) {
            for (Map<String, Object> dataRecord : dataList) {
                if (dataRecord.containsKey("children")) {
                    List<Map<String, Object>> children = (List<Map<String, Object>>) dataRecord.get("children");
                    if (children != null) {
                        for (Map<String, Object> child : children) {
                            if (child.containsKey("table_id") && child.containsKey("data")) {
                                tableIds.addAll(collectAllTableIds(child));
                            }
                        }
                    }
                }
            }
        }
        
        return tableIds;
    }
    
    /**
     * 使用缓存执行自定义校验
     */
    private RuleValidationResult executeCustomValidationWithCache(
            Long bizId, 
            Long tableId, 
            List<ModelFieldDO> fields, 
            Map<String, Object> data,
            Map<Long, List<RuleConfigDO>> ruleCache) {
        
        // 获取表级规则（tableId对应的规则 + 全局规则）
        List<RuleConfigDO> tableRules = new ArrayList<>();
        tableRules.addAll(ruleCache.getOrDefault(tableId, Collections.emptyList()));
        tableRules.addAll(ruleCache.getOrDefault(0L, Collections.emptyList())); // 全局规则
        
        // 过滤表级规则（fieldId为null）
        List<RuleConfigDO> filteredTableRules = tableRules.stream()
            .filter(rule -> rule.getFieldId() == null)
            .filter(rule -> bizId == null || rule.getBizId() == null || Objects.equals(rule.getBizId(), bizId))
            .sorted(Comparator.comparing(RuleConfigDO::getPriority))
            .toList();
        
        // 执行表级规则
        for (RuleConfigDO rule : filteredTableRules) {
            RuleValidationResult result = executeCustomRule(rule, data, null);
            if (!result.isValid()) {
                return result;
            }
        }
        
        // 执行字段级规则
        for (ModelFieldDO field : fields) {
            List<RuleConfigDO> fieldRules = tableRules.stream()
                .filter(rule -> Objects.equals(rule.getFieldId(), field.getFieldId()))
                .filter(rule -> bizId == null || rule.getBizId() == null || Objects.equals(rule.getBizId(), bizId))
                .sorted(Comparator.comparing(RuleConfigDO::getPriority))
                .toList();
            
            for (RuleConfigDO rule : fieldRules) {
                RuleValidationResult result = executeCustomRule(rule, data, field);
                if (!result.isValid()) {
                    return result;
                }
            }
        }
        
        return RuleValidationResult.success();
    }
    
    /**
     * 从数据记录中提取业务数据，排除结构属性和系统字段
     */
    private Map<String, Object> extractBusinessData(Map<String, Object> dataRecord) {
        Map<String, Object> businessData = new HashMap<>();
        
        // 排除结构属性和系统字段
        Set<String> excludeKeys = Set.of(
            "children", "table_id", "data",
            // 系统字段（OperateDO相关）
            "createdDt", "updatedDt", "createdBy", "updatedBy",
            "createTime", "updateTime", "creator", "updater",
            "created_dt", "updated_dt", "created_by", "updated_by"
        );
        
        for (Map.Entry<String, Object> entry : dataRecord.entrySet()) {
            if (!excludeKeys.contains(entry.getKey())) {
                businessData.put(entry.getKey(), entry.getValue());
            }
        }
        
        return businessData;
    }

    /**
     * 执行校验规则（支持动态参数）
     * 场景① 通过接口获取预算限额
     * BigDecimal budgetLimit = getBudgetLimitFromApi(userId);
     *
     * // 构建动态参数
     * Map<String, Object> dynamicParams = new HashMap<>();
     * dynamicParams.put("api_budget_limit", budgetLimit);
     *
     * // 执行校验
     * RuleValidationResult result = ruleEngineService.validateDataWithDynamicParams(
     *     bizId, tableId, formData, dynamicParams);
     *     ---------------------------------------------
     * 场景② 执行Python脚本计算最高分
     * BigDecimal maxScore = executeScriptToCalculateMaxScore(examId, studentLevel);
     *
     * Map<String, Object> dynamicParams = new HashMap<>();
     * dynamicParams.put("calculated_max_score", maxScore);
     *
     * // 执行校验
     * RuleValidationResult result = ruleEngineService.validateDataWithDynamicParams(
     *     bizId, tableId, examData, dynamicParams);
     */
    @Override
    public RuleValidationResult validateDataWithDynamicParams(Long bizId, Long tableId, Map<String, Object> data, Map<String, Object> dynamicParams) {
        log.info("执行数据校验, bizId: {}, tableId: {}, 动态参数: {}", bizId, tableId, dynamicParams != null ? dynamicParams.keySet() : "无");
        
        // 合并动态参数到数据中
        Map<String, Object> mergedData = new HashMap<>(data);
        if (dynamicParams != null) {
            mergedData.putAll(dynamicParams);
        }
        
        return validateData(bizId, tableId, mergedData);
    }

    /**
     * 简化的数据校验方法
     */
    private RuleValidationResult validateData(Long bizId, Long tableId, Map<String, Object> data) {
        try {
            // 如果是树形结构数据，需要递归校验
            if (data.containsKey("table_id") && data.containsKey("data")) {
                return validateTreeStructureDataWithCache(bizId, data);
            }
            
            // 单表数据校验
            if (tableId != null) {
                return validateSingleTable(bizId, tableId, data);
            } else if (bizId != null) {
                return validateBusinessRules(bizId, null, data);
            } else {
                return RuleValidationResult.error("bizId和tableId不能同时为空");
            }
            
        } catch (Exception e) {
            log.error("数据校验异常, bizId: {}, tableId: {}, error: {}", bizId, tableId, e.getMessage(), e);
            return RuleValidationResult.error("数据校验异常: " + e.getMessage());
        }
    }

    /**
     * 单表校验方法
     */
    private RuleValidationResult validateSingleTable(Long bizId, Long tableId, Map<String, Object> data) {
        // 获取表信息
        ModelTableDO table = modelTableMapper.selectById(tableId);
        if (table == null) {
            return RuleValidationResult.error("表不存在: " + tableId);
        }
        
        // 获取字段信息
        List<ModelFieldDO> fields = modelFieldMapper.selectList(
            LambdaQueryWrapperX.<ModelFieldDO>lambdaQueryX()
                .eq(ModelFieldDO::getTableId, tableId)
                .orderByAsc(ModelFieldDO::getSort)
        );
        
        // 1. 执行基础规则校验
        RuleValidationResult baseResult = executeBaseValidation(table, fields, data);
        if (!baseResult.isValid()) {
            return baseResult;
        }
        
        // 2. 执行自定义规则校验
        RuleValidationResult customResult = executeCustomValidation(bizId, tableId, fields, data);
        if (!customResult.isValid()) {
            return customResult;
        }
        
        return RuleValidationResult.success();
    }

    /**
     * 预加载缓存数据
     */
    private void preloadCacheData(Set<Long> allTableIds, 
                                 Map<Long, ModelTableDO> tableCache,
                                 Map<Long, List<ModelFieldDO>> fieldCache,
                                 Map<Long, List<RuleConfigDO>> ruleCache) {
        
        log.info("开始预加载缓存数据，表ID数量: {}", allTableIds.size());
        
        // 预加载表信息
        if (!allTableIds.isEmpty()) {
            List<ModelTableDO> tables = modelTableMapper.selectBatchIds(allTableIds);
            for (ModelTableDO table : tables) {
                tableCache.put(table.getTableId(), table);
            }
            log.info("预加载表信息完成，数量: {}", tables.size());
        }
        
        // 预加载字段信息
        for (Long tableId : allTableIds) {
            List<ModelFieldDO> fields = modelFieldMapper.selectList(
                LambdaQueryWrapperX.<ModelFieldDO>lambdaQueryX()
                    .eq(ModelFieldDO::getTableId, tableId)
                    .orderByAsc(ModelFieldDO::getSort)
            );
            fieldCache.put(tableId, fields);
        }
        log.info("预加载字段信息完成");
        
        // 预加载规则信息（包括表级规则和字段级规则）
        Set<Long> allRuleTableIds = new HashSet<>(allTableIds);
        allRuleTableIds.add(0L); // 添加全局规则
        
        for (Long tableId : allRuleTableIds) {
            List<RuleConfigDO> rules = ruleConfigMapper.selectList(
                LambdaQueryWrapperX.<RuleConfigDO>lambdaQueryX()
                    .eq(RuleConfigDO::getTableId, tableId)
                    .eq(RuleConfigDO::getEnabled, true)
                    .orderByAsc(RuleConfigDO::getPriority)
            );
            ruleCache.put(tableId, rules);
        }
        log.info("预加载规则信息完成");
    }

    /**
     * 使用缓存校验单个表的数据
     */
    private RuleValidationResult validateTableDataWithCache(Long bizId, Long tableId, Map<String, Object> data,
                                                           Map<Long, ModelTableDO> tableCache,
                                                           Map<Long, List<ModelFieldDO>> fieldCache,
                                                           Map<Long, List<RuleConfigDO>> ruleCache) {
        
        // 从缓存获取表信息
        ModelTableDO table = tableCache.get(tableId);
        if (table == null) {
            log.warn("表不存在: {}", tableId);
            return RuleValidationResult.success();
        }
        
        // 从缓存获取字段信息
        List<ModelFieldDO> fields = fieldCache.getOrDefault(tableId, Collections.emptyList());
        
        log.info("开始校验表[{}]数据，字段数量: {}, 数据字段: {}", 
                table.getTableName(), fields.size(), data.keySet());
        
        // 执行自定义校验规则
        RuleValidationResult customResult = executeCustomValidationWithCache(bizId, tableId, fields, data, ruleCache);
        if (!customResult.isValid()) {
            return customResult;
        }

        // 系统字段列表
        Set<String> systemFields = Set.of(
                "createdDt", "updatedDt", "createdBy", "updatedBy",
                "createTime", "updateTime", "creator", "updater",
                "created_dt", "updated_dt", "created_by", "updated_by"
        );


        // 执行字段级基础校验
        for (ModelFieldDO field : fields) {
            // 排除主键字段和系统字段
            if (field.getIsPrimaryKey() == 1 || systemFields.contains(field.getFieldCode())) {
                continue;
            }
            RuleValidationResult fieldResult = validateFieldData(field, data);
            if (!fieldResult.isValid()) {
                return fieldResult;
            }
        }
        
        return RuleValidationResult.success();
    }

    /**
     * 校验单个字段数据
     */
    private RuleValidationResult validateFieldData(ModelFieldDO field, Map<String, Object> data) {
        String fieldCode = field.getFieldCode();
        Object value = data.get(fieldCode);
        // 主键不校验
        if (field.getIsPrimaryKey() != null && field.getIsPrimaryKey() == 1) {
            return RuleValidationResult.success();
        }
        // 必填校验
        if (field.getIsNullable() != null && field.getIsNullable() == 1) {
            if (value == null || (value instanceof String && ((String) value).trim().isEmpty())) {
                return RuleValidationResult.error(field.getFieldName() + "不能为空");
            }
        }
        
        // 长度校验
        Integer leng = field.getLeng();
        if (value != null && leng != null && leng > 0) {
            String strValue = value.toString();
            if (strValue.length() > leng) {
                return RuleValidationResult.error(field.getFieldName() + "长度不能超过" + leng);
            }
        }
        
        return RuleValidationResult.success();
    }
}
