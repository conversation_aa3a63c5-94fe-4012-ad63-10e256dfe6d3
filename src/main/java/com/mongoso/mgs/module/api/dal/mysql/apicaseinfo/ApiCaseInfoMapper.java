package com.mongoso.mgs.module.api.dal.mysql.apicaseinfo;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.api.controller.admin.apicaseinfo.vo.ApiCaseInfoPageReqVO;
import com.mongoso.mgs.module.api.controller.admin.apicaseinfo.vo.ApiCaseInfoQueryReqVO;
import com.mongoso.mgs.module.api.dal.db.apicaseinfo.ApiCaseInfoDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 用例管理 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ApiCaseInfoMapper extends BaseMapperX<ApiCaseInfoDO> {

    default PageResult<ApiCaseInfoDO> selectPage(ApiCaseInfoPageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<ApiCaseInfoDO>lambdaQueryX()
                .eqIfPresent(ApiCaseInfoDO::getItemId, reqVO.getItemId())
                .eqIfPresent(ApiCaseInfoDO::getIsDefault, reqVO.getIsDefault())
                .likeIfPresent(ApiCaseInfoDO::getCaseName, reqVO.getCaseName())
                .eqIfPresent(ApiCaseInfoDO::getApiUrl, reqVO.getApiUrl())
                .eqIfPresent(ApiCaseInfoDO::getEnvUrl, reqVO.getEnvUrl())
                .likeIfPresent(ApiCaseInfoDO::getEnvName, reqVO.getEnvName())
                .eqIfPresent(ApiCaseInfoDO::getRequestType, reqVO.getRequestType())
                .eqIfPresent(ApiCaseInfoDO::getRequest, reqVO.getRequest())
                .eqIfPresent(ApiCaseInfoDO::getResponse, reqVO.getResponse())
                .betweenIfPresent(ApiCaseInfoDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .orderByDesc(ApiCaseInfoDO::getCreatedDt));
    }




    default List<ApiCaseInfoDO> selectList(ApiCaseInfoQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<ApiCaseInfoDO>lambdaQueryX()
                .eqIfPresent(ApiCaseInfoDO::getItemId, reqVO.getItemId())
                .likeIfPresent(ApiCaseInfoDO::getCaseName, reqVO.getCaseName())
                .eqIfPresent(ApiCaseInfoDO::getApiUrl, reqVO.getApiUrl())
                .eqIfPresent(ApiCaseInfoDO::getEnvUrl, reqVO.getEnvUrl())
                .likeIfPresent(ApiCaseInfoDO::getEnvName, reqVO.getEnvName())
                .eqIfPresent(ApiCaseInfoDO::getRequestType, reqVO.getRequestType())
                .eqIfPresent(ApiCaseInfoDO::getRequest, reqVO.getRequest())
                .eqIfPresent(ApiCaseInfoDO::getResponse, reqVO.getResponse())
                .eqIfPresent(ApiCaseInfoDO::getIsDefault, reqVO.getIsDefault())
                .eqIfPresent(ApiCaseInfoDO::getCreatedBy, reqVO.getCreatedBy())
                .betweenIfPresent(ApiCaseInfoDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .eqIfPresent(ApiCaseInfoDO::getUpdatedBy, reqVO.getUpdatedBy())
                .betweenIfPresent(ApiCaseInfoDO::getUpdatedDt, reqVO.getStartUpdatedDt(), reqVO.getEndUpdatedDt())
                    .orderByDesc(ApiCaseInfoDO::getCreatedDt));
    }

    default int setDefault(Long id, Long itemId) {
        update(new LambdaUpdateWrapper<ApiCaseInfoDO>().eq(ApiCaseInfoDO::getItemId, itemId).set(ApiCaseInfoDO::getIsDefault, false));
        if(0L != id){
            update(new LambdaUpdateWrapper<ApiCaseInfoDO>().eq(ApiCaseInfoDO::getId, id).set(ApiCaseInfoDO::getIsDefault, true));
        }
        return 1;
    }

}