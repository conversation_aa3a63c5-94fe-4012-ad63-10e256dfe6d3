package com.mongoso.mgs.module.api.controller.admin.apicaseinfo.vo;

import lombok.*;

import jakarta.validation.constraints.*;
import java.io.Serializable;

  

/**
 * 用例管理 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class ApiCaseInfoBaseVO implements Serializable {

    /** 主键id */
    private Long id;

    /** 接口ID */
    @NotNull(message = "接口ID不能为空")
    private Long itemId;

    /** 用例名称 */
    private String caseName;

    /** 接口URL */
    private String apiUrl;

    /** 环境URL */
    private String envUrl;

    /** 环境名称 */
    private String envName;

    /** 请求类型(0-get 1-post) */
    private Short requestType;

    /** 请求信息 */
    private String request;

    /** 响应信息 */
    private String response;

    /** 是否默认 */
    private Boolean isDefault;

}
