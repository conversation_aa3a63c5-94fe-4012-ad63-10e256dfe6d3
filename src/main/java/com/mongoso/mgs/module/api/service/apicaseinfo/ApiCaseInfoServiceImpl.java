package com.mongoso.mgs.module.api.service.apicaseinfo;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.common.util.ObjUtilX;
import com.mongoso.mgs.framework.common.util.StrUtilX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.api.controller.admin.apicaseinfo.vo.*;
import com.mongoso.mgs.module.api.dal.db.apicaseinfo.ApiCaseInfoDO;
import com.mongoso.mgs.module.api.dal.mysql.apicaseinfo.ApiCaseInfoMapper;
import com.mongoso.mgs.module.model.dal.db.item.ItemDO;
import com.mongoso.mgs.module.model.dal.mysql.item.ItemMapper;
import com.mongoso.mgs.module.model.service.item.ItemServiceImpl;
import com.mongoso.mgs.module.project.dal.db.projectapienv.ProjectApiEnvDO;
import com.mongoso.mgs.module.project.dal.mysql.projectapienv.ProjectApiEnvMapper;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.*;
// import static com.mongoso.mgs.module.api.enums.ErrorCodeConstants.*;


/**
 * 用例管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ApiCaseInfoServiceImpl implements ApiCaseInfoService {

    @Resource
    private ApiCaseInfoMapper caseInfoMapper;
    @Resource
    private ItemMapper itemMapper;
    @Resource
    private ItemServiceImpl itemService;
    @Resource
    private ProjectApiEnvMapper apiEnvMapper;

    @Override
    public Long apiCaseInfoAdd(ApiCaseInfoAditReqVO reqVO) {
        // 插入
        ApiCaseInfoDO caseInfo = BeanUtilX.copy(reqVO, ApiCaseInfoDO::new);
        caseInfoMapper.insert(caseInfo);
        // 返回
        return caseInfo.getId();
    }

    @Override
    public Long apiCaseInfoEdit(ApiCaseInfoAditReqVO reqVO) {
        // 校验存在
        this.apiCaseInfoValidateExists(reqVO.getId());
        // 更新
        ApiCaseInfoDO caseInfo = BeanUtilX.copy(reqVO, ApiCaseInfoDO::new);
        caseInfoMapper.updateById(caseInfo);
        // 返回
        return caseInfo.getId();
    }

    @Override
    public void apiCaseInfoDel(Long id) {
        // 校验存在
        this.apiCaseInfoValidateExists(id);
        // 删除
        caseInfoMapper.deleteById(id);
    }

    private ApiCaseInfoDO apiCaseInfoValidateExists(Long id) {
        ApiCaseInfoDO caseInfo = caseInfoMapper.selectById(id);
        if (caseInfo == null) {
            // throw exception(CASE_INFO_NOT_EXISTS);
            throw new BizException("5001", "用例管理不存在");
        }
        return caseInfo;
    }

    @Override
    public ApiCaseInfoRespVO apiCaseInfoDetail(Long id) {
        ApiCaseInfoDO data = caseInfoMapper.selectById(id);
        return BeanUtilX.copy(data, ApiCaseInfoRespVO::new);
    }

    @Override
    public List<ApiCaseInfoRespVO> apiCaseInfoList(ApiCaseInfoQueryReqVO reqVO) {
        List<ApiCaseInfoRespVO> list = new ArrayList<>();
        List<ApiCaseInfoDO> data = caseInfoMapper.selectList(reqVO);
        //组装一个默认接口
        ApiCaseInfoDO defaultCase = new ApiCaseInfoDO();
        defaultCase.setId(0L);
        defaultCase.setItemId(reqVO.getItemId());
        defaultCase.setCaseName("默认接口");
        if(ObjUtilX.isNotEmpty(data)){// 有保存过用例，并且有默认值
            Long l = caseInfoMapper.selectCount(LambdaQueryWrapperX.<ApiCaseInfoDO>lambdaQueryX()
                    .eq(ApiCaseInfoDO::getIsDefault, true));
            if(l > 0) {
                defaultCase.setIsDefault(false);
            } else {
                defaultCase.setIsDefault(true);
            }
        } else {
            defaultCase.setIsDefault(true);
        }
        defaultCase.setRequestType((short)1);//默认post请求
        ApiCaseInfoRespVO defaultCaseParams = new ApiCaseInfoRespVO();
        BeanUtils.copyProperties(defaultCase, defaultCaseParams);
        generateCommonsCase(reqVO, defaultCaseParams);
        if (StrUtilX.isEmpty(defaultCaseParams.getRequest())) {
            defaultCaseParams.setRequest("{}");
        }
        list.add(defaultCaseParams);

        list.addAll(BeanUtilX.copy(data, ApiCaseInfoRespVO::new));
        return list;
    }

    private void generateCommonsCase(ApiCaseInfoQueryReqVO params, ApiCaseInfoRespVO resultInfo) {
        String environmentUrl;
        ItemDO apiDO = itemMapper.selectById(params.getItemId());
        if (Objects.isNull(apiDO)) {
            throw new BizException("5001", "找不到接口信息");
        }
        Long projectId = apiDO.getProjectId();
        List<ProjectApiEnvDO> envList = apiEnvMapper.selectListByProjectId(projectId);
        if (ObjUtilX.isNotEmpty(envList)) {
            environmentUrl = envList.get(0).getEnvUrl();
            resultInfo.setEnvUrl(environmentUrl);
        } else {
            throw new BizException("5001", "请先设置项目环境配置");
        }

        Map resultMap = new LinkedHashMap();
        itemService.generateInterfaceDataJason("0", resultMap, apiDO.getContent(), 0);
        String interfaceUrl = resultMap.get("interfaceUrl") != null ? resultMap.get("interfaceUrl").toString() : null;
        Map<String, String>  dataMap = (Map<String, String>) resultMap.get("data");
        resultInfo.setApiUrl(interfaceUrl);
        if (dataMap != null) {
            Map requetMap = new HashMap();
            requetMap.put("body", dataMap);
            requetMap.put("headers", new Object());
            requetMap.put("params", new Object());
            List<Map<String, Object>> files = new ArrayList<>();
            for (Map.Entry<String, String> entry : dataMap.entrySet()) {
                Map<String, Object> map = new HashMap<>();
                map.put("type", 0 );
                map.put("key", entry.getKey());
                map.put("value", entry.getValue());
                files.add(map);
            }
            requetMap.put("files", files);
            String jsonString = JSON.toJSONString(requetMap, SerializerFeature.DisableCircularReferenceDetect);
            resultInfo.setRequest(jsonString);
        }
    }

    @Override
    public PageResult<ApiCaseInfoRespVO> apiCaseInfoPage(ApiCaseInfoPageReqVO reqVO) {
        PageResult<ApiCaseInfoDO> data = caseInfoMapper.selectPage(reqVO);
        return BeanUtilX.copy(data, ApiCaseInfoRespVO::new);
    }

    @Override
    public Integer saveDefaultCase(ApiCaseInfoPrimaryReqVO reqVO) {
        //ApiCaseInfoDO apiCaseInfoDO = this.apiCaseInfoValidateExists(reqVO.getId());
        return caseInfoMapper.setDefault(reqVO.getId(), reqVO.getItemId());
    }

}
