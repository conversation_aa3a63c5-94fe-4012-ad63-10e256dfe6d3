package com.mongoso.mgs.module.api.service.apicaseinfo;

import java.util.*;
import jakarta.validation.*;
import com.mongoso.mgs.module.api.controller.admin.apicaseinfo.vo.*;
import com.mongoso.mgs.module.api.dal.db.apicaseinfo.ApiCaseInfoDO;
import com.mongoso.mgs.framework.common.domain.PageResult;
import jakarta.validation.constraints.NotNull;

/**
 * 用例管理 Service 接口
 *
 * <AUTHOR>
 */
public interface ApiCaseInfoService {

    /**
     * 创建用例管理
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long apiCaseInfoAdd(@Valid ApiCaseInfoAditReqVO reqVO);

    /**
     * 更新用例管理
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long apiCaseInfoEdit(@Valid ApiCaseInfoAditReqVO reqVO);

    /**
     * 删除用例管理
     *
     * @param id 编号
     */
    void apiCaseInfoDel(Long id);

    /**
     * 获得用例管理信息
     *
     * @param id 编号
     * @return 用例管理信息
     */
    ApiCaseInfoRespVO apiCaseInfoDetail(Long id);

    /**
     * 获得用例管理列表
     *
     * @param reqVO 查询条件
     * @return 用例管理列表
     */
    List<ApiCaseInfoRespVO> apiCaseInfoList(@Valid ApiCaseInfoQueryReqVO reqVO);

    /**
     * 获得用例管理分页
     *
     * @param reqVO 查询条件
     * @return 用例管理分页
     */
    PageResult<ApiCaseInfoRespVO> apiCaseInfoPage(@Valid ApiCaseInfoPageReqVO reqVO);

    Integer saveDefaultCase(@Valid ApiCaseInfoPrimaryReqVO reqVO);
}
