package com.mongoso.mgs.module.api.dal.db.apicaseinfo;

import lombok.*;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;

/**
 * 用例管理 DO
 *
 * <AUTHOR>
 */
@TableName(value = "lowcode.sys_api_case_info", autoResultMap = true)
//@KeySequence("sys_api_case_info_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApiCaseInfoDO extends OperateDO {

    /** 主键id */
        @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /** 接口ID */
    private Long itemId;

    /** 用例名称 */
    private String caseName;

    /** 接口URL */
    private String apiUrl;

    /** 环境URL */
    private String envUrl;

    /** 环境名称 */
    private String envName;

    /** 请求类型(0-get 1-post) */
    private Short requestType;

    /** 请求信息 */
    private String request;

    /** 响应信息 */
    private String response;

    /** 是否默认 */
    private Boolean isDefault;


}
