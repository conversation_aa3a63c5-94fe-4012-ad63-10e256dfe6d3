package com.mongoso.mgs.module.api.controller.admin.apicaseinfo.vo;

import lombok.*;

import com.mongoso.mgs.framework.common.domain.PageParam;


    
 import org.springframework.format.annotation.DateTimeFormat;
 
import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
  


/**
 * 用例管理 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ApiCaseInfoPageReqVO extends PageParam {

    /** 接口ID */
    private Long itemId;

    /** 用例名称 */
    private String caseName;

    /** 接口URL */
    private String apiUrl;

    /** 环境URL */
    private String envUrl;

    /** 环境名称 */
    private String envName;

    /** 请求类型(0-get 1-post) */
    private Short requestType;

    /** 请求信息 */
    private String request;

    /** 响应信息 */
    private String response;

    /** 是否默认 */
    private Boolean isDefault;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

}
