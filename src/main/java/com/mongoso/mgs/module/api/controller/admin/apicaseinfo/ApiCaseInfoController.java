package com.mongoso.mgs.module.api.controller.admin.apicaseinfo;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.api.controller.admin.apicaseinfo.vo.*;
import com.mongoso.mgs.module.api.service.apicaseinfo.ApiCaseInfoService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.mongoso.mgs.framework.common.domain.ResultX.success;

/**
 * 用例管理 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api")
@Validated
public class ApiCaseInfoController {

    @Resource
    private ApiCaseInfoService caseInfoService;

    @OperateLog("用例管理添加或编辑")
    @PostMapping("/apiCaseInfoAdit")
    @PreAuthorize("@ss.hasPermission('apiCaseInfo:adit')")
    public ResultX<Long> apiCaseInfoAdit(@Valid @RequestBody ApiCaseInfoAditReqVO reqVO) {
        return success(reqVO.getId() == null
                            ? caseInfoService.apiCaseInfoAdd(reqVO)
                            : caseInfoService.apiCaseInfoEdit(reqVO));
    }
    @OperateLog("用例管理保存默认")
    @PostMapping("/saveDefaultCase")
    @PreAuthorize("@ss.hasPermission('apiCaseInfo:adit')")
    public ResultX<Integer> saveDefaultCase(@Valid @RequestBody ApiCaseInfoPrimaryReqVO reqVO) {
        return success(caseInfoService.saveDefaultCase(reqVO));
    }

    @OperateLog("用例管理删除")
    @PostMapping("/apiCaseInfoDel")
    @PreAuthorize("@ss.hasPermission('apiCaseInfo:delete')")
    public ResultX<Boolean> apiCaseInfoDel(@Valid @RequestBody ApiCaseInfoPrimaryReqVO reqVO) {
        caseInfoService.apiCaseInfoDel(reqVO.getId());
        return success(true);
    }

    @OperateLog("用例管理详情")
    @PostMapping("/apiCaseInfoDetail")
    @PreAuthorize("@ss.hasPermission('apiCaseInfo:query')")
    public ResultX<ApiCaseInfoRespVO> apiCaseInfoDetail(@Valid @RequestBody ApiCaseInfoPrimaryReqVO reqVO) {
        return success(caseInfoService.apiCaseInfoDetail(reqVO.getId()));
    }

    @OperateLog("用例管理列表")
    @PostMapping("/apiCaseInfoList")
    @PreAuthorize("@ss.hasPermission('apiCaseInfo:query')")
    public ResultX<List<ApiCaseInfoRespVO>> apiCaseInfoList(@Valid @RequestBody ApiCaseInfoQueryReqVO reqVO) {
        return success(caseInfoService.apiCaseInfoList(reqVO));
    }

    @OperateLog("用例管理分页")
    @PostMapping("/apiCaseInfoPage")
    @PreAuthorize("@ss.hasPermission('apiCaseInfo:query')")
    public ResultX<PageResult<ApiCaseInfoRespVO>> apiCaseInfoPage(@Valid @RequestBody ApiCaseInfoPageReqVO reqVO) {
        return success(caseInfoService.apiCaseInfoPage(reqVO));
    }

}
