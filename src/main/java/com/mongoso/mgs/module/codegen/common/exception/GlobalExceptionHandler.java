package com.mongoso.mgs.module.codegen.common.exception;//package com.mongoso.mgs.module.codegen.common.exception;
//
//import com.mongoso.mgs.module.codegen.dal.pojo.ResultX;
//import lombok.AllArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.validation.BindException;
//import org.springframework.validation.FieldError;
//import org.springframework.web.HttpRequestMethodNotSupportedException;
//import org.springframework.web.bind.MethodArgumentNotValidException;
//import org.springframework.web.bind.MissingServletRequestParameterException;
//import org.springframework.web.bind.annotation.ExceptionHandler;
//import org.springframework.web.bind.annotation.RestControllerAdvice;
//import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
//import org.springframework.web.servlet.NoHandlerFoundException;
//
//import jakarta.servlet.http.HttpServletRequest;
//import jakarta.validation.ConstraintViolation;
//import jakarta.validation.ConstraintViolationException;
//import jakarta.validation.ValidationException;
//
//import static com.mongoso.mgs.module.codegen.common.enums.GlobalErrorCodeConstants.*;
//
//
///**
// * 全局异常处理器，将 Exception 翻译成 ResultX + 对应的异常编号
// *
// * <AUTHOR>
// */
//@RestControllerAdvice
//@AllArgsConstructor
//@Slf4j
//public class GlobalExceptionHandler {
//
//    /**
//     * 处理所有异常，主要是提供给 Filter 使用
//     * 因为 Filter 不走 SpringMVC 的流程，但是我们又需要兜底处理异常，所以这里提供一个全量的异常处理过程，保持逻辑统一。
//     *
//     * @param request 请求
//     * @param ex 异常
//     * @return 通用返回
//     */
//    public ResultX<?> allExceptionHandler(HttpServletRequest request, Throwable ex) {
//        if (ex instanceof MissingServletRequestParameterException) {
//            return missingServletRequestParameterExceptionHandler((MissingServletRequestParameterException) ex);
//        }
//        if (ex instanceof MethodArgumentTypeMismatchException) {
//            return methodArgumentTypeMismatchExceptionHandler((MethodArgumentTypeMismatchException) ex);
//        }
//        if (ex instanceof MethodArgumentNotValidException) {
//            return methodArgumentNotValidExceptionExceptionHandler((MethodArgumentNotValidException) ex);
//        }
//        if (ex instanceof BindException) {
//            return bindExceptionHandler((BindException) ex);
//        }
//        if (ex instanceof ConstraintViolationException) {
//            return constraintViolationExceptionHandler((ConstraintViolationException) ex);
//        }
//        if (ex instanceof ValidationException) {
//            return validationException((ValidationException) ex);
//        }
//        if (ex instanceof NoHandlerFoundException) {
//            return noHandlerFoundExceptionHandler(request, (NoHandlerFoundException) ex);
//        }
//        if (ex instanceof HttpRequestMethodNotSupportedException) {
//            return httpRequestMethodNotSupportedExceptionHandler((HttpRequestMethodNotSupportedException) ex);
//        }
//        if (ex instanceof BizException) {
//            return serviceExceptionHandler((BizException) ex);
//        }
//        return defaultExceptionHandler(ex);
//    }
//
//    /**
//     * 处理 SpringMVC 请求参数缺失
//     *
//     * 例如说，接口上设置了 @RequestParam("xx") 参数，结果并未传递 xx 参数
//     */
//    @ExceptionHandler(MissingServletRequestParameterException.class)
//    public ResultX<?> missingServletRequestParameterExceptionHandler(MissingServletRequestParameterException ex) {
//        log.warn("[missingServletRequestParameterExceptionHandler]", ex);
//        return ResultX.error(BAD_REQUEST.getCode(), String.format("请求参数缺失:%s", ex.getParameterName()));
//    }
//
//    /**
//     * 处理 SpringMVC 请求参数类型错误
//     *
//     * 例如说，接口上设置了 @RequestParam("xx") 参数为 Integer，结果传递 xx 参数类型为 String
//     */
//    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
//    public ResultX<?> methodArgumentTypeMismatchExceptionHandler(MethodArgumentTypeMismatchException ex) {
//        log.warn("[missingServletRequestParameterExceptionHandler]", ex);
//        return ResultX.error(BAD_REQUEST.getCode(), String.format("请求参数类型错误:%s", ex.getMessage()));
//    }
//
//    /**
//     * 处理 SpringMVC 参数校验不正确
//     */
//    @ExceptionHandler(MethodArgumentNotValidException.class)
//    public ResultX<?> methodArgumentNotValidExceptionExceptionHandler(MethodArgumentNotValidException ex) {
//        log.warn("[methodArgumentNotValidExceptionExceptionHandler]", ex);
//        FieldError fieldError = ex.getBindingResult().getFieldError();
//        assert fieldError != null; // 断言，避免告警
//        return ResultX.error(BAD_REQUEST.getCode(), String.format("请求参数不正确:%s", fieldError.getDefaultMessage()));
//    }
//
//    /**
//     * 处理 SpringMVC 参数绑定不正确，本质上也是通过 Validator 校验
//     */
//    @ExceptionHandler(BindException.class)
//    public ResultX<?> bindExceptionHandler(BindException ex) {
//        log.warn("[handleBindException]", ex);
//        FieldError fieldError = ex.getFieldError();
//        assert fieldError != null; // 断言，避免告警
//        return ResultX.error(BAD_REQUEST.getCode(), String.format("请求参数不正确:%s", fieldError.getDefaultMessage()));
//    }
//
//    /**
//     * 处理 Validator 校验不通过产生的异常
//     */
//    @ExceptionHandler(ConstraintViolationException.class)
//    public ResultX<?> constraintViolationExceptionHandler(ConstraintViolationException ex) {
//        log.warn("[constraintViolationExceptionHandler]", ex);
//        ConstraintViolation<?> constraintViolation = ex.getConstraintViolations().iterator().next();
//        return ResultX.error(BAD_REQUEST.getCode(), String.format("请求参数不正确:%s", constraintViolation.getMessage()));
//    }
//
//    /**
//     * 处理 Dubbo Consumer 本地参数校验时，抛出的 ValidationException 异常
//     */
//    @ExceptionHandler(ValidationException.class)
//    public ResultX<?> validationException(ValidationException ex) {
//        log.warn("[constraintViolationExceptionHandler]", ex);
//        // 无法拼接明细的错误信息，因为 Dubbo Consumer 抛出 ValidationException 异常时，是直接的字符串信息，且人类不可读
//        return ResultX.error(BAD_REQUEST);
//    }
//
//    /**
//     * 处理 SpringMVC 请求地址不存在
//     *
//     * 注意，它需要设置如下两个配置项：
//     * 1. spring.mvc.throw-exception-if-no-handler-found 为 true
//     * 2. spring.mvc.static-path-pattern 为 /statics/**
//     */
//    @ExceptionHandler(NoHandlerFoundException.class)
//    public ResultX<?> noHandlerFoundExceptionHandler(HttpServletRequest req, NoHandlerFoundException ex) {
//        log.warn("[noHandlerFoundExceptionHandler]", ex);
//        return ResultX.error(NOT_FOUND.getCode(), String.format("请求地址不存在:%s", ex.getRequestURL()));
//    }
//
//    /**
//     * 处理 SpringMVC 请求方法不正确
//     *
//     * 例如说，A 接口的方法为 GET 方式，结果请求方法为 POST 方式，导致不匹配
//     */
//    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
//    public ResultX<?> httpRequestMethodNotSupportedExceptionHandler(HttpRequestMethodNotSupportedException ex) {
//        log.warn("[httpRequestMethodNotSupportedExceptionHandler]", ex);
//        return ResultX.error(METHOD_NOT_ALLOWED.getCode(), String.format("请求方法不正确:%s", ex.getMessage()));
//    }
//
//    /**
//     * 处理业务异常 ServiceException
//     *
//     * 例如说，商品库存不足，用户手机号已存在。
//     */
//    @ExceptionHandler(BizException.class)
//    public ResultX<?> serviceExceptionHandler(BizException ex) {
//        log.info("[serviceExceptionHandler]", ex);
//        return ResultX.error(ex.getCode(), ex.getMessage());
//    }
//
//    /**
//     * 处理系统异常，兜底处理所有的一切
//     */
//    @ExceptionHandler(Exception.class)
//    public ResultX<?> defaultExceptionHandler(Throwable ex) {
//        log.error("[defaultExceptionHandler]", ex);
//        // 返回 ERROR ResultX
//        return ResultX.error(INTERNAL_SERVER_ERROR.getCode(), INTERNAL_SERVER_ERROR.getMsg());
//    }
//
//}
