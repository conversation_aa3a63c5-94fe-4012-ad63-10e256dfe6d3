package com.mongoso.mgs.module.codegen.controller.admin.codegen.vo.column;


import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
* 代码生成字段定义 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class CodegenColumnBaseVO implements Serializable {


    @NotNull(message = "表编号不能为空")
    private String tableId;

    @NotNull(message = "字段名不能为空")
    private String columnName;

    @NotNull(message = "字段类型不能为空")
    private String dataType;

    @NotNull(message = "字段描述不能为空")
    private String columnComment;

    @NotNull(message = "是否允许为空不能为空")
    private Boolean nullable;

    @NotNull(message = "是否主键不能为空")
    private Boolean primaryKey;

    @NotNull(message = "是否自增不能为空")
    private String autoIncrement;

    @NotNull(message = "排序不能为空")
    private Integer ordinalPosition;

    @NotNull(message = "Java 属性类型不能为空")
    private String javaType;

    @NotNull(message = "Java 属性名不能为空")
    private String javaField;

    private String javaFieldUpper;

    private String dictType;// 字典类型

    private String example;// 数据示例

    @NotNull(message = "是否为 Adit 添加更新操作的字段不能为空")
    private Boolean aditOperation;

    @NotNull(message = "是否为 Page 查询操作的字段不能为空")
    private Boolean pageOperation;

    @NotNull(message = "List 查询操作的条件类型不能为空")
    private String listOperationCondition;

    @NotNull(message = "是否为 List 查询操作的返回字段不能为空")
    private Boolean listOperationResult;

}
