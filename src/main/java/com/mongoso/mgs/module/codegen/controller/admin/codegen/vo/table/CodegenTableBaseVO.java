package com.mongoso.mgs.module.codegen.controller.admin.codegen.vo.table;

import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
* 代码生成 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class CodegenTableBaseVO implements Serializable {

    @NotNull(message = "导入类型不能为空")
    private Integer scene;

    @NotNull(message = "表名称不能为空")
    private String tableName;

    @NotNull(message = "表描述不能为空")
    private String tableComment;

    private String remark;

    @NotNull(message = "模块名不能为空")
    private String moduleName;

    @NotNull(message = "业务名不能为空")
    private String businessName;

    @NotNull(message = "类名称不能为空")
    private String className;

    @NotNull(message = "类描述不能为空")
    private String classComment;

    @NotNull(message = "作者不能为空")
    private String author;

    @NotNull(message = "模板类型不能为空")
    private Integer templateType;

}
