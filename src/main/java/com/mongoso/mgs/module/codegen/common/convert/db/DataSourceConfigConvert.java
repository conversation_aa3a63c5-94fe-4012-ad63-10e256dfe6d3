package com.mongoso.mgs.module.codegen.common.convert.db;

import com.mongoso.mgs.module.codegen.controller.admin.db.vo.DataSourceConfigCreateReqVO;
import com.mongoso.mgs.module.codegen.controller.admin.db.vo.DataSourceConfigRespVO;
import com.mongoso.mgs.module.codegen.controller.admin.db.vo.DataSourceConfigUpdateReqVO;
import com.mongoso.mgs.module.codegen.dal.db.config.DataSourceConfigDO;

import java.util.ArrayList;
import java.util.List;

public class DataSourceConfigConvert {

    public static DataSourceConfigDO convert(DataSourceConfigCreateReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        DataSourceConfigDO dataSourceConfigDO = new DataSourceConfigDO();

        dataSourceConfigDO.setName( bean.getName() );
        dataSourceConfigDO.setUrl( bean.getUrl() );
        dataSourceConfigDO.setUsername( bean.getUsername() );
        dataSourceConfigDO.setPassword( bean.getPassword() );
        dataSourceConfigDO.setDbType( bean.getDbType() );

        return dataSourceConfigDO;
    }

    public static DataSourceConfigDO convert(DataSourceConfigUpdateReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        DataSourceConfigDO dataSourceConfigDO = new DataSourceConfigDO();

        dataSourceConfigDO.setId( bean.getId() );
        dataSourceConfigDO.setName( bean.getName() );
        dataSourceConfigDO.setUrl( bean.getUrl() );
        dataSourceConfigDO.setUsername( bean.getUsername() );
        dataSourceConfigDO.setPassword( bean.getPassword() );
        dataSourceConfigDO.setDbType( bean.getDbType() );

        return dataSourceConfigDO;
    }

    public static DataSourceConfigRespVO convert(DataSourceConfigDO bean) {
        if ( bean == null ) {
            return null;
        }

        DataSourceConfigRespVO dataSourceConfigRespVO = new DataSourceConfigRespVO();

        dataSourceConfigRespVO.setName( bean.getName() );
        dataSourceConfigRespVO.setUrl( bean.getUrl() );
        dataSourceConfigRespVO.setUsername( bean.getUsername() );
        //dataSourceConfigRespVO.setPassword( bean.getPassword() );// 移除密码显示
        dataSourceConfigRespVO.setPassword( null );
        dataSourceConfigRespVO.setDbType( bean.getDbType() );
        if ( bean.getId() != null ) {
            dataSourceConfigRespVO.setId( bean.getId().intValue() );
        }
        dataSourceConfigRespVO.setCreateTime( bean.getCreateTime() );

        return dataSourceConfigRespVO;
    }

    public static List<DataSourceConfigRespVO> convertList(List<DataSourceConfigDO> list) {
        if ( list == null ) {
            return null;
        }

        List<DataSourceConfigRespVO> list1 = new ArrayList<DataSourceConfigRespVO>( list.size() );
        for ( DataSourceConfigDO dataSourceConfigDO : list ) {
            list1.add( convert( dataSourceConfigDO ) );
        }

        return list1;
    }
}
