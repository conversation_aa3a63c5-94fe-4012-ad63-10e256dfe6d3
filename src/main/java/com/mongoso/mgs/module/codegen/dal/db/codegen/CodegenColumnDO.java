package com.mongoso.mgs.module.codegen.dal.db.codegen;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mongoso.mgs.module.codegen.dal.GenBaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 代码生成 column 字段定义
 *
 * <AUTHOR>
 */
@TableName(value = "lowcode.a_codegen_column", autoResultMap = true)
//@KeySequence("infra_codegen_column_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class CodegenColumnDO extends GenBaseDO {


    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 表编号
     * <p>
     * 关联 {@link CodegenTableDO#getTableId()}
     */
    private String tableId;

    // ========== 表相关字段 ==========

    /**
     * 字段名
     */
    private String columnName;
    /**
     * 数据库字段类型
     */
    private String dataType;
    /**
     * 字段描述
     */
    private String columnComment;
    /**
     * 是否允许为空
     */
    private Boolean nullable;
    /**
     * 是否主键
     */
    private Boolean primaryKey;
    /**
     * 是否自增
     */
    private Boolean autoIncrement;
    /**
     * 排序
     */
    private Integer ordinalPosition;

    // ========== Java 相关字段 ==========

    /**
     * Java 属性类型
     * <p>
     * 例如说 String、Boolean 等等
     */
    private String javaType;
    /**
     * Java 属性名
     */
    private String javaField;
    /**
     * Java 大写属性名
     */
    private String javaFieldUpper;
    /**
     * 数据示例，主要用于生成 Swagger 注解的 example 字段
     */
    private String example;

    // ========== CRUD 相关字段 ==========

    /**
     * 是否为 adit 添加更新操作的字段
     */
    private Boolean aditOperation;
    /**
     * 是否为 Page 查询操作的字段
     */
    private Boolean pageOperation;
    /**
     * List 查询操作的条件类型
     * <p>
     */
    private String listOperationCondition;
    /**
     * 是否为 List 查询操作的返回字段
     */
    private Boolean listOperationResult;

}
