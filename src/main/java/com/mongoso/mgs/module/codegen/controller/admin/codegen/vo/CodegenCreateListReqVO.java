package com.mongoso.mgs.module.codegen.controller.admin.codegen.vo;

import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.util.List;

@Data
public class CodegenCreateListReqVO {

    @NotNull(message = "数据源配置的编号不能为空")
    private Long dataSourceConfigId;

    @NotNull(message = "项目id不能为空")
    private Long projectId;

    @NotNull(message = "表名数组不能为空")
    private List<String> tableNames;

}
