package com.mongoso.mgs.module.codegen.controller.admin.db;

import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.codegen.common.convert.db.DataSourceConfigConvert;
import com.mongoso.mgs.module.codegen.controller.admin.db.vo.DataSourceConfigCreateReqVO;
import com.mongoso.mgs.module.codegen.controller.admin.db.vo.DataSourceConfigRespVO;
import com.mongoso.mgs.module.codegen.controller.admin.db.vo.DataSourceConfigUpdateReqVO;
import com.mongoso.mgs.module.codegen.dal.db.config.DataSourceConfigDO;
import com.mongoso.mgs.module.codegen.dal.pojo.ResultX;
import com.mongoso.mgs.module.codegen.service.db.DataSourceConfigService;
import com.mongoso.mgs.module.model.dal.db.modeltable.ModelTableDO;
import com.mongoso.mgs.module.model.dal.mysql.modeltable.ModelTableMapper;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

import static com.mongoso.mgs.module.codegen.dal.pojo.ResultX.success;


@RestController
@RequestMapping("/infra/data-source-config")
@Validated
public class DataSourceConfigController {

    @Resource
    private DataSourceConfigService dataSourceConfigService;
    @Resource
    private ModelTableMapper tableMapper;

    @PostMapping("/init")
    public ResultX<Integer> fullInitializeDataSources() {
        dataSourceConfigService.fullInitializeDataSources();
        return success(1);
    }

    @PostMapping("/create")
    //public ResultX<Long> createDataSourceConfig(@Valid @RequestBody DataSourceConfigCreateReqVO createReqVO) {
    public ResultX<Long> createDataSourceConfig(@Valid @RequestBody DataSourceConfigUpdateReqVO updateReqVO) {
        return success(dataSourceConfigService.createDataSourceConfig(updateReqVO));
    }

    @RequestMapping("/update")
    public ResultX<Boolean> updateDataSourceConfig(@Valid @RequestBody DataSourceConfigUpdateReqVO updateReqVO) {
        dataSourceConfigService.updateDataSourceConfig(updateReqVO);
        return success(true);
    }

    @RequestMapping("/delete")
    public ResultX<Boolean> deleteDataSourceConfig(@RequestParam("id") Long id) {
        //查询是否被项目引用
        Long count = tableMapper.selectCount(LambdaQueryWrapperX.<ModelTableDO>lambdaQueryX()
                .eq(ModelTableDO::getDataSourceConfigId, id));
        if(count > 0){
            throw new BizException("5001","该数据源被项目引用，无法删除");
        }
        dataSourceConfigService.deleteDataSourceConfig(id);
        return success(true);
    }

    @RequestMapping("/get")
    public ResultX<DataSourceConfigRespVO> getDataSourceConfig(@RequestParam("id") Long id) {
        DataSourceConfigDO dataSourceConfig = dataSourceConfigService.getDataSourceConfig(id);
        return success(DataSourceConfigConvert.convert(dataSourceConfig));
//        return success(BeanUtilX.copy(dataSourceConfig, DataSourceConfigRespVO::new));
    }

    @RequestMapping("/list")
    public ResultX<List<DataSourceConfigRespVO>> getDataSourceConfigList(@RequestParam(value = "name", required = false, defaultValue = "") String name) {
        List<DataSourceConfigDO> list = dataSourceConfigService.getDataSourceConfigList(name);
        return success(DataSourceConfigConvert.convertList(list));
        //return success(BeanUtilX.copyList(list, DataSourceConfigRespVO::new));
    }

}
