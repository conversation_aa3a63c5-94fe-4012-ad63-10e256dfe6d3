package com.mongoso.mgs.module.codegen.controller.admin.codegen;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ZipUtil;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.module.codegen.common.convert.codegen.CodegenConvert;
import com.mongoso.mgs.module.codegen.common.util.ServletUtils;
import com.mongoso.mgs.module.codegen.controller.admin.codegen.vo.CodegenCreateListReqVO;
import com.mongoso.mgs.module.codegen.controller.admin.codegen.vo.CodegenDetailRespVO;
import com.mongoso.mgs.module.codegen.controller.admin.codegen.vo.CodegenPreviewRespVO;
import com.mongoso.mgs.module.codegen.controller.admin.codegen.vo.CodegenUpdateReqVO;
import com.mongoso.mgs.module.codegen.controller.admin.codegen.vo.table.CodegenTablePageReqVO;
import com.mongoso.mgs.module.codegen.controller.admin.codegen.vo.table.CodegenTableRespVO;
import com.mongoso.mgs.module.codegen.controller.admin.codegen.vo.table.DatabaseTableRespVO;
import com.mongoso.mgs.module.codegen.dal.db.codegen.CodegenColumnDO;
import com.mongoso.mgs.module.codegen.dal.db.codegen.CodegenTableDO;
import com.mongoso.mgs.module.codegen.dal.pojo.ResultX;
import com.mongoso.mgs.module.codegen.service.codegen.CodegenService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.mongoso.mgs.module.codegen.dal.pojo.ResultX.success;

@RestController
@RequestMapping("/infra/codegen")
@Validated
public class CodegenController {

    @Resource
    private CodegenService codegenService;

    @RequestMapping("/db/table/list")
    public ResultX<List<DatabaseTableRespVO>> getDatabaseTableList(
            @RequestParam(value = "dataSourceConfigId") Long dataSourceConfigId,
            @RequestParam(value = "projectId") Long projectId,
            @RequestParam(value = "opType", required = false) Integer opType,
            @RequestParam(value = "exclude", required = false) Integer exclude,
            @RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "comment", required = false) String comment) {
        return success(codegenService.getDatabaseTableList(dataSourceConfigId, projectId, opType, exclude, name, comment));
    }

    @RequestMapping("/table/page")
    public ResultX<PageResult<CodegenTableRespVO>> getCodeGenTablePage(@Valid @RequestBody CodegenTablePageReqVO pageReqVO) {
        PageResult<CodegenTableDO> pageResult = codegenService.getCodegenTablePage(pageReqVO);

        return success(CodegenConvert.convertPage(pageResult));
//        return success(BeanUtilX.copyPage(pageResult, CodegenTableRespVO::new));
    }

    @RequestMapping("/detail")
    public ResultX<CodegenDetailRespVO> getCodegenDetail(@RequestParam("tableId") String tableId) {
        CodegenTableDO table = codegenService.getCodegenTablePage(tableId);
        List<CodegenColumnDO> columns = codegenService.getCodegenColumnListByTableId(tableId);

        // 拼装返回
        CodegenDetailRespVO respVO = new CodegenDetailRespVO();
//        respVO.setTable(BeanUtilX.copy(table, CodegenTableRespVO::new));
//        respVO.setColumns(BeanUtilX.copyList(columns, CodegenColumnRespVO::new));
        respVO.setTable(CodegenConvert.convert(table));
        respVO.setColumns(CodegenConvert.convertList02(columns));
        return success(respVO);
    }

    @RequestMapping("/create-list")
    public ResultX<List<String>> createCodegenList(@Valid @RequestBody CodegenCreateListReqVO reqVO) {
        return success(codegenService.createCodegenList(reqVO));
    }

    @RequestMapping("/update")
    public ResultX<Boolean> updateCodegen(@Valid @RequestBody CodegenUpdateReqVO updateReqVO) {
        codegenService.updateCodegen(updateReqVO);
        return success(true);
    }

    @RequestMapping("/sync-from-db")
    public ResultX<Boolean> syncCodegenFromDB(@RequestParam("tableId") String tableId) {
        codegenService.syncCodegenFromDB(tableId);
        return success(true);
    }

    @RequestMapping("/delete")
    public ResultX<Boolean> deleteCodegen(@RequestParam("tableId") String tableId) {
        codegenService.deleteCodegen(tableId);
        return success(true);
    }

    @RequestMapping("/preview")
    public ResultX<List<CodegenPreviewRespVO>> previewCodegen(@RequestParam("tableId") String tableId) {
        Map<String, String> codes = codegenService.generationCodes(tableId);

        List<CodegenPreviewRespVO> collect = codes.entrySet().stream().map(entry -> {
            CodegenPreviewRespVO respVO = new CodegenPreviewRespVO();
            respVO.setFilePath(entry.getKey());
            respVO.setCode(entry.getValue());
            return respVO;
        }).collect(Collectors.toList());
        return success(collect);
    }

    @RequestMapping("/download")
    public void downloadCodegen(@RequestParam("tableId") String tableId,
                                HttpServletResponse response) throws IOException {
        // 生成代码
        Map<String, String> codes = codegenService.generationCodes(tableId);
        // 构建 zip 包
        String[] paths = codes.keySet().toArray(new String[0]);
        ByteArrayInputStream[] ins = codes.values().stream().map(IoUtil::toUtf8Stream).toArray(ByteArrayInputStream[]::new);
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ZipUtil.zip(outputStream, paths, ins);
        // 输出
        ServletUtils.writeAttachment(response, "codegen.zip", outputStream.toByteArray());
    }

    @RequestMapping("/batchDownload")
    public void batchDownload(@RequestParam("tableIdList") List<String> tableIdList,
                                HttpServletResponse response) throws IOException {

        // 创建一个 Map 用于存储所有表的代码
        Map<String, String> allCodes = new HashMap<>();

        for (String tableId : tableIdList) {
            // 生成代码
            Map<String, String> codes = codegenService.generationCodes(tableId);

            // 将生成的代码加入到总的 Map 中
            allCodes.putAll(codes);
        }

        // 构建 zip 包
        String[] paths = allCodes.keySet().toArray(new String[0]);
        ByteArrayInputStream[] ins = allCodes.values().stream()
                .map(IoUtil::toUtf8Stream)
                .toArray(ByteArrayInputStream[]::new);
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ZipUtil.zip(outputStream, paths, ins);

        // 输出
        ServletUtils.writeAttachment(response, "codegen.zip", outputStream.toByteArray());
    }

}
