package com.mongoso.mgs.module.codegen.common.util;

import org.springframework.beans.BeanUtils;
import org.springframework.util.SerializationUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.function.Supplier;


/**
 * <AUTHOR>
 * @date 2023/9/27 17:40
 *
 * 重新封装bean拷贝的，如果有接口有性能要求，别使用这个拷贝。
 *
 * 注意：拷贝的对象需要实现【Serializable】接口，不然会报错
 *
 */
public class BeanUtilX {


    /**
     * 对象转换
     * @param source 原对象
     * @param supplier 目标对象的构造方法
     * @return
     * @param <S>
     * @param <T>
     */
    public static <S, T> T copy(S source, Supplier<T> supplier) {
        T t = supplier.get();
        if (null == source || null == t) {
            return null;
//            throw exception("405", "转换对象不能为空");
        }
        BeanUtils.copyProperties(source,t);
        return t;
    }

    /**
     * 对象转换
     * @param source 原对象
     * @param target 目标对象
     * @return
     * @param <S>
     * @param <T>
     */
    public static <S, T> T copy(S source, T target) {
        if (null == source || null == target) {
            return null;
        }
        BeanUtils.copyProperties(source,target);
        return target;
    }

    /**
     * 对象转换，列表
     * @param sources 原对象列表
     * @param supplier 目标对象的构造方法
     * @return
     * @param <S>
     * @param <T>
     */
    public static <S, T> List<T> copyList(List<S> sources, Supplier<T> supplier) {
        if (null == sources || null == supplier.get()) {
            return Collections.emptyList();
        }

        List<T> list = new ArrayList<>();
        for (S source : sources) {
            byte[] data = SerializationUtils.serialize(supplier.get());
            T target1 = (T) SerializationUtils.deserialize(data);
            copy(source,target1);
            list.add(target1);
        }
        return list;
    }

    /**
     * 对象转换，列表
     * @param sources 原对象列表
     * @param target 目标对象
     * @return
     * @param <S>
     * @param <T>
     */
    public static <S, T> List<T> copyList(List<S> sources, T target) {
        if (null == sources || null == target) {
            return Collections.emptyList();
        }

        List<T> list = new ArrayList<>();
        for (S source : sources) {
            byte[] data = SerializationUtils.serialize(target);
            T target1 = (T) SerializationUtils.deserialize(data);
            copy(source,target1);
            list.add(target1);
        }
        return list;
    }


}


