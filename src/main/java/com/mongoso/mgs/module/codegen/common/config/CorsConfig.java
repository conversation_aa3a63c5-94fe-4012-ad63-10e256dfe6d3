package com.mongoso.mgs.module.codegen.common.config;

import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;

import jakarta.servlet.Filter;

/**
 * 网关的自定义过滤器，解决跨域问题，使用reactive包下的UrlBasedCorsConfigurationSource
 *mongoso-server-demo-dynamic
 */
@Configuration
public class CorsConfig {

    // 单应用跨域
    @Bean
    public CorsFilter corsFilter() {
        CorsConfiguration config = new CorsConfiguration();
        config.addAllowedOrigin("*");
        config.setAllowCredentials(true);
        config.addAllowedMethod("*");
        config.addAllowedHeader("*");
        UrlBasedCorsConfigurationSource configSource = new UrlBasedCorsConfigurationSource();
        configSource.registerCorsConfiguration("/**", config);
        return new CorsFilter(configSource);
    }

    //@Bean
    //public FilterRegistrationBean<CorsFilter> corsFilterBean() {
    //    // 创建 CorsConfiguration 对象
    //    CorsConfiguration config = new CorsConfiguration();
    //    config.setAllowCredentials(true);
    //    config.addAllowedOriginPattern("*"); // 设置访问源地址
    //    config.addAllowedHeader("*"); // 设置访问源请求头
    //    config.addAllowedMethod("*"); // 设置访问源请求方法
    //    // 创建 UrlBasedCorsConfigurationSource 对象
    //    UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
    //    source.registerCorsConfiguration("/**", config); // 对接口配置跨域设置
    //    return createFilterBean(new CorsFilter(source), Integer.MAX_VALUE);
    //}

    public static <T extends Filter> FilterRegistrationBean<T> createFilterBean(T filter, Integer order) {
        FilterRegistrationBean<T> bean = new FilterRegistrationBean<>(filter);
        bean.setOrder(order);
        return bean;
    }


}

