package com.mongoso.mgs.module.codegen.controller.admin.db.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import jakarta.validation.constraints.NotNull;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DataSourceConfigUpdateReqVO extends DataSourceConfigBaseVO {

    //@NotNull(message = "主键编号不能为空")
    private Long id;

    @NotNull(message = "密码不能为空")
    private String password;

}
