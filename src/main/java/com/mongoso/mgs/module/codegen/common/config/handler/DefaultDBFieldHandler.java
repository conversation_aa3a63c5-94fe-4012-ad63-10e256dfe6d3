package com.mongoso.mgs.module.codegen.common.config.handler;


import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.mongoso.mgs.module.codegen.dal.GenBaseDO;
import org.apache.ibatis.reflection.MetaObject;

import java.util.Date;
import java.util.Objects;

/**
 * 通用参数填充实现类
 *
 * 如果没有显式的对通用参数进行赋值，这里会对通用参数进行填充、赋值
 *
 * <AUTHOR>
 */
public class DefaultDBFieldHandler implements MetaObjectHandler {

    @Override
    public void insertFill(MetaObject metaObject) {
        if (Objects.nonNull(metaObject) && metaObject.getOriginalObject() instanceof GenBaseDO) {
            GenBaseDO baseDO = (GenBaseDO) metaObject.getOriginalObject();

            Date current = new Date();
            // 创建时间为空，则以当前时间为插入时间
            if (Objects.isNull(baseDO.getCreateTime())) {
                baseDO.setCreateTime(current);
            }
            // 更新时间为空，则以当前时间为更新时间
            if (Objects.isNull(baseDO.getUpdateTime())) {
                baseDO.setUpdateTime(current);
            }
            baseDO.setDeleted(false);
            baseDO.setCreator(System.getProperty("user.name"));
            baseDO.setUpdater(System.getProperty("user.name"));
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        // 更新时间为空，则以当前时间为更新时间
        setFieldValByName("updateTime", new Date(), metaObject);
    }
}
