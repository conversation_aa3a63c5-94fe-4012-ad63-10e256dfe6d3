package com.mongoso.mgs.module.codegen.dal.db.config;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mongoso.mgs.module.codegen.dal.GenBaseDO;
import lombok.Data;

/**
 * 数据源配置
 *
 * <AUTHOR>
 */
@TableName(value = "lowcode.a_codegen_data_source_config", autoResultMap = true)
@KeySequence("a_codegen_data_source_config_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
public class DataSourceConfigDO extends GenBaseDO {

    /**
     * 主键编号 - Master 数据源
     */
    public static final Long ID_MASTER = 0L;
    public static final String DB_NME = "平台默认库";

    /**
     * 主键编号
     */
    @TableId(type = IdType.INPUT)
    private Long id;
    /**
     * 连接名
     */
    private String name;

    /**
     * 数据源连接
     */
    private String url;
    /**
     * 用户名
     */
    private String username;
    /**
     * 密码
     */
    //@TableField(typeHandler = EncryptTypeHandler.class)
    private String password;

    private String dbType;

}
