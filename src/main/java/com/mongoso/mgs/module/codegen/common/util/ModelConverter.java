package com.mongoso.mgs.module.codegen.common.util;

import com.mongoso.mgs.module.model.controller.admin.modeltable.vo.ModelTableJsonReqVO;
import com.mongoso.mgs.module.model.dal.db.modelfield.ModelFieldDO;
import com.mongoso.mgs.module.model.dal.db.modeltable.ModelTableDO;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONArray;

import java.util.ArrayList;
import java.util.List;

/**
 * 用来将listify的思维导图JSON搞成model,
 */
public class ModelConverter {
    public void saveToDatabase(ModelTableJsonReqVO reqVO) {
        // 创建 ModelTableDO 对象
        ModelTableDO modelTableDO = new ModelTableDO();
        modelTableDO.setTableName(reqVO.getItemName()); // Level 1 的 itemName

        // 处理 level:2 中的第一个元素（children）
        if (reqVO.getChildren() != null && reqVO.getChildren().size() > 0) {
            ModelTableJsonReqVO.Child level2Child1 = reqVO.getChildren().get(0);
//            modelTableDO.setTableCode(level2Child1.getEnglishName()); // Level 2 第一个元素的 englishName

            // 处理 level:3 中的第一元素，并假设只有一个
            List<ModelTableJsonReqVO.Child> level3Children = level2Child1.getChildren();
            if (level3Children != null && level3Children.size() == 1) {
                ModelTableJsonReqVO.Child level3Child = level3Children.get(0);

                // 设置 ModelTableDO 的必要字段
                modelTableDO.setTableCode(level3Child.getItemName());
                // 根据需要设置其他字段
                // modelTableDO.setOtherField(value);
            }
        }

        // 处理 level:2 中的第二个元素（children）
        if (reqVO.getChildren().size() > 1) {
            ModelTableJsonReqVO.Child level2Child2 = reqVO.getChildren().get(1);
            List<ModelFieldDO> modelFieldDOList = new ArrayList<>();

            // 处理 level:3 中的所有元素
            for (ModelTableJsonReqVO.Child level3Child : level2Child2.getChildren()) {
                ModelFieldDO modelFieldDO = new ModelFieldDO();
                modelFieldDO.setFieldCode(level3Child.getEnglishName()); // 将 English name 映射到 field code
                modelFieldDO.setFieldName(level3Child.getItemName()); // 将 Item name 映射到 field name
                modelFieldDO.setRemark(level3Child.getCompositeName()); // Composite name 映射到 remark
                //modelFieldDO.setIsPrimaryKey(level3Child.getIsPrimaryKey()?1:0); // Primary key 标记
                modelFieldDO.setIsPrimaryKey(level3Child.getIsPrimaryKey()); // Primary key 标记
                modelFieldDO.setFieldType(level3Child.getFieldType()); // 数据类型
                if(level3Child.getHasLength()) {
                    modelFieldDO.setLeng(level3Child.getDataLength()); // 长度
                }
                if(level3Child.getHasPoint()) {
                    modelFieldDO.setFieldPrecision(level3Child.getDataPoint()); // 精度
                }

                // 处理 level:4 children 列表
                List<JSONObject> jsonFields = new ArrayList<>();
                List<ModelTableJsonReqVO.Child> level4Children = level3Child.getChildren();
                for (ModelTableJsonReqVO.Child level4Child : level4Children) {
                    JSONObject jsonField = new JSONObject();
                    jsonField.put("fieldName", level4Child.getItemName());
                    jsonField.put("remark", level4Child.getRemark());
                    jsonField.put("fieldCode", level4Child.getEnglishName());

                    jsonFields.add(jsonField); // 将 JSON 字段添加到列表
                }

                modelFieldDO.setJsonFields(jsonFields); // 设置 JSON 字段
                modelFieldDOList.add(modelFieldDO); // 将 ModelFieldDO 添加到列表
            }

            // 将 ModelFieldDO 列表设置到 ModelTableDO 中
//            modelTableDO.setModelFieldDOList(modelFieldDOList); // 假设 ModelTableDO 有此方法
        }

        // 执行数据库保存操作，例如：
        // database.save(modelTableDO);
    }

    public void saveToDatabase(List<JSONObject> reqVOList) {
        List<ModelFieldDO> modelFields = new ArrayList<>();

        for (JSONObject reqVO : reqVOList) {
            // 处理顶层节点（level 1）
            JSONArray children = reqVO.getJSONArray("children");
            if (children != null) {
                for (int i = 0; i < children.size(); i++) {
                    JSONObject level2Child = children.getJSONObject(i);
                    ModelFieldDO fieldDO = new ModelFieldDO();
                    fieldDO.setFieldCode(level2Child.getString("englishName"));
                    fieldDO.setFieldName(level2Child.getString("itemName"));
                    fieldDO.setFieldType(level2Child.getString("dataType"));
                    fieldDO.setRemark(level2Child.getString("compositeName"));

                    // 处理 level 3 children
                    JSONArray level3Children = level2Child.getJSONArray("children");
                    if (level3Children != null) {
                        List<JSONObject> jsonFields = new ArrayList<>();
                        for (int j = 0; j < level3Children.size(); j++) {
                            JSONObject level3Child = level3Children.getJSONObject(j);
                            JSONObject fieldJson = new JSONObject();
                            fieldJson.put("fieldCode", level3Child.getString("englishName"));
                            fieldJson.put("fieldName", level3Child.getString("itemName"));
                            fieldJson.put("dataType", level3Child.getString("dataType"));
                            fieldJson.put("remark", level3Child.getString("remark"));

                            // 处理 level 4 children
                            JSONArray level4Children = level3Child.getJSONArray("children");
                            if (level4Children != null && !level4Children.isEmpty()) {
                                JSONArray level4Array = new JSONArray();
                                for (int k = 0; k < level4Children.size(); k++) {
                                    JSONObject level4Child = level4Children.getJSONObject(k);
                                    JSONObject level4Json = new JSONObject();
                                    level4Json.put("itemName", level4Child.getString("itemName"));
                                    level4Json.put("remark", level4Child.getString("remark"));
                                    level4Json.put("englishName", level4Child.getString("englishName"));
                                    level4Array.add(level4Json);
                                }
                                fieldJson.put("level4Fields", level4Array); // 添加 level 4 字段
                            }

                            jsonFields.add(fieldJson);
                        }
                        fieldDO.setJsonFields(jsonFields); // 保存 level 3 fields 到 ModelFieldDO
                    }

                    modelFields.add(fieldDO);
                }
            }
        }

        // 执行数据库保存操作，例如：
        // database.save(modelFields);
    }

    public static ModelTableJsonReqVO convertToModelTableJsonReqVO(ModelTableDO modelTable, List<ModelFieldDO> modelFields) {
        ModelTableJsonReqVO reqVO = new ModelTableJsonReqVO();

        // 填充根节点信息
        reqVO.setMindType("root"); // 根据需要设置
        reqVO.setEnglishName(modelTable.getTableCode());
        reqVO.setItemName(modelTable.getTableName());
        reqVO.setCompositeName(modelTable.getRemark());
        reqVO.setLevel(1);
        reqVO.setExpanded(true);
        reqVO.setReadonly(false);

        // 创建 children 列表
        List<ModelTableJsonReqVO.Child> children = new ArrayList<>();

        for (ModelFieldDO field : modelFields) {
            ModelTableJsonReqVO.Child child = new ModelTableJsonReqVO.Child();
            child.setEnglishName(field.getFieldCode());
            child.setItemName(field.getFieldName());
            child.setCompositeName(field.getRemark());
            child.setFieldType(field.getFieldType());
            child.setDataLength(field.getLeng() != null ? Integer.valueOf(String.valueOf(field.getLeng())) : null);
            //child.setIsPrimaryKey(field.getIsPrimaryKey() == 1); // 假设 1 表示是主键
            child.setIsPrimaryKey(field.getIsPrimaryKey()); // 假设 1 表示是主键

            // 根据需要设置其他属性
            child.setLevel(2);
            child.setUnexpandable(false); // 假设默认可展开
            child.setEditing(false);
            child.setDragging(false);
            child.setExpanded(false);
            child.setReadonly(false);

            // 处理 jsonFields
            addJsonFieldsToChildren(field.getJsonFields(), child);

            children.add(child);
        }

        reqVO.setChildren(children);

        return reqVO;
    }

    private static void addJsonFieldsToChildren(List<JSONObject> jsonFields, ModelTableJsonReqVO.Child parentChild) {
        if (jsonFields == null || jsonFields.isEmpty()) {
            return;
        }

        List<ModelTableJsonReqVO.Child> nestedChildren = new ArrayList<>();

        for (JSONObject jsonField : jsonFields) {
            // 创建新的 Child 对象
            ModelTableJsonReqVO.Child nestedChild = new ModelTableJsonReqVO.Child();
            nestedChild.setEnglishName(jsonField.getString("fieldCode")); // 假设 JSON 中有 fieldCode 字段
            nestedChild.setItemName(jsonField.getString("fieldName"));     // 假设 JSON 中有 fieldName 字段
            nestedChild.setCompositeName(jsonField.getString("remark"));   // 假设 JSON 中有 remark 字段
            nestedChild.setFieldType(jsonField.getString("dataType"));      // 假设 JSON 中有 dataType 字段
            nestedChild.setDataLength((Integer) jsonField.getOrDefault("dataLength", "0"));  // 假设 JSON 中有 dataLength 字段
//            nestedChild.setIsPrimaryKey(jsonField.getBooleanValue("isPrimaryKey")); // 假设 JSON 中有 isPrimaryKey 字段

            // 如果该字段还有子字段，递归处理
            if (jsonField.containsKey("children")) {
                JSONArray childArray = jsonField.getJSONArray("children");
                if (childArray != null && !childArray.isEmpty()) {
                    List<JSONObject> childJsonFields = childArray.toJavaList(JSONObject.class);
                    addJsonFieldsToChildren(childJsonFields, nestedChild);
                }
            }

            nestedChildren.add(nestedChild);
        }

        parentChild.setChildren(nestedChildren);
    }
}
