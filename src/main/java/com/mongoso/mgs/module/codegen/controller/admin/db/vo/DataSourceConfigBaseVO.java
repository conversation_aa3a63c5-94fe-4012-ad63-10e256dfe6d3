package com.mongoso.mgs.module.codegen.controller.admin.db.vo;

import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
* 数据源配置 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class DataSourceConfigBaseVO implements Serializable {

    @NotNull(message = "数据源名称不能为空")
    private String name;

    @NotNull(message = "数据源连接不能为空")
    private String url;

    @NotNull(message = "用户名不能为空")
    private String username;

    private String dbType;
}
