package com.mongoso.mgs.module.codegen.common.config.handler;


import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * MyBaits 配置类
 *
 * <AUTHOR>
 */
@Configuration
public class MybatisAutoConfiguration {

//    @Bean
//    public MybatisPlusInterceptor mybatisPlusInterceptor() {
//        MybatisPlusInterceptor mybatisPlusInterceptor = new MybatisPlusInterceptor();
//        mybatisPlusInterceptor.addInnerInterceptor(new PaginationInnerInterceptor()); // 分页插件
//        return mybatisPlusInterceptor;
//    }

//    @Bean
//    public MetaObjectHandler defaultMetaObjectHandler(){
//        return new DefaultDBFieldHandler(); // 自动填充参数类
//    }

}
