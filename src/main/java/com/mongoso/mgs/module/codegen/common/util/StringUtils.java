package com.mongoso.mgs.module.codegen.common.util;

import com.mongoso.mgs.framework.common.util.StrUtilX;

import java.io.UnsupportedEncodingException;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
*
* <AUTHOR>
* @datetime 2018-1-3 20:28:04
* @version 1.0.0
* @copyright Shenzhen Mongoso Technology Co., Ltd.
*/
public class StringUtils {
	public static boolean isEmpty(String str){
		if(str != null && !str.trim().equals("")) {
			return false;
		}
		return true;
	}

	public static boolean isEmpty(Object obj){
		if(obj != null && !obj.toString().equals("")) {
			return false;
		}
		return true;
	}
	
	/**
	 * list转string，以逗号隔开
	 * @param list
	 * @return
     */
	public static String listToString(List<String> list) {
		if (null == list || list.size() <= 0) {
			return null;
		}

		StringBuffer sb = new StringBuffer();
		for (String str : list) {
			sb.append(str).append(",");
		}
		return sb.substring(0,sb.length()-1);
	}

	public static String toUtf8String(String s){
		StringBuffer sb = new StringBuffer();
		for (int i=0;i<s.length();i++){
			char c = s.charAt(i);
			if (c >= 0 && c <= 255){sb.append(c);}
			else{
				byte[] b;
				try { b = Character.toString(c).getBytes("utf-8");}
				catch (Exception ex) {
					System.out.println(ex);
					b = new byte[0];
				}
				for (int j = 0; j < b.length; j++) {
					int k = b[j];
					if (k < 0) k += 256;
					sb.append("%" + Integer.toHexString(k).toUpperCase());
				}
			}
		}
		return sb.toString();
	}

	static final int GB_SP_DIFF = 160;
	// 存放国标一级汉字不同读音的起始区位码
	static final int[] secPosValueList = { 1601, 1637, 1833, 2078, 2274, 2302, 2433, 2594, 2787, 3106, 3212, 3472, 3635, 3722, 3730, 3858, 4027, 4086, 4390, 4558, 4684, 4925, 5249, 5600 };
	// 存放国标一级汉字不同读音的起始区位码对应读音
	static final char[] firstLetter = { 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'w', 'x', 'y', 'z' };

	/**
	 *  获取字符传首字母
	 * @param characters
	 * @return
	 */
	public static String getSpells(String characters) {
		StringBuffer buffer = new StringBuffer();
		for (int i = 0; i < characters.length(); i++) {
			char ch = characters.charAt(i);
			// 判断是否为汉字，如果左移7为为0就不是汉字，否则是汉字
			if ((ch >> 7) == 0) {
				buffer.append(String.valueOf(ch));
			} else {
				char spell = getFirstLetter(ch);
				buffer.append(String.valueOf(spell));
			}
		}
		return buffer.toString();
	}
	// 获取一个汉字的首字母
	public static Character getFirstLetter(char ch) {
		byte[] uniCode = null;
		try {
			uniCode = String.valueOf(ch).getBytes("GBK");
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
			return null;
		}
		if (uniCode[0] < 128 && uniCode[0] > 0) { // 非汉字
			return null;
		} else {
			return convert(uniCode);
		}
	}

	/**
	 * 获取一个汉字的拼音首字母。 GB码两个字节分别减去160，转换成10进制码组合就可以得到区位码
	 * 例如汉字“你”的GB码是0xC4/0xE3，分别减去0xA0（160）就是0x24/0x43
	 * 0x24转成10进制就是36，0x43是67，那么它的区位码就是3667，在对照表中读音为‘n’
	 */
	static char convert(byte[] bytes) {
		char result = '-';
		int secPosValue = 0;
		int i;
		for (i = 0; i < bytes.length; i++) {
			bytes[i] -= GB_SP_DIFF;
		}
		secPosValue = bytes[0] * 100 + bytes[1];
		for (i = 0; i < 23; i++) {
			if (secPosValue >= secPosValueList[i]
					&& secPosValue < secPosValueList[i + 1]) {
				result = firstLetter[i];
				break;
			}
		}
		return result;
	}

	/**

	 * 判断字符串是否为数字
	 *
	 * @param str
	 * @return
	 */
	public static boolean isNumeric(String str) {
		Pattern pattern=Pattern.compile("^(([1-9]{1}\\d*)|([0]{1}))(\\.(\\d){0,2})?$");
		Matcher isNum = pattern.matcher(str);
		if (!isNum.matches()) {
			return false;
		}
		return true;
	}


	/**
	 * 计算字符串大小
	 *
	 * @return 1GB
	 * */
	public static String getStrSize(String str) {
		int GB = 1024 * 1024 * 1024;//定义GB的计算常量
		int MB = 1024 * 1024;//定义MB的计算常量
		int KB = 1024;//定义KB的计算常量
		try {
			// 加载文件
			int size = str.getBytes("UTF-8").length;
			// 格式化小数
			DecimalFormat df = new DecimalFormat("0.00");
			String resultSize = "";
			if (size / GB >= 1) {
				//如果当前Byte的值大于等于1GB
				resultSize = df.format(size / (float) GB) + "GB";
			} else if (size / MB >= 1) {
				//如果当前Byte的值大于等于1MB
				resultSize = df.format(size / (float) MB) + "MB";
			} else if (size / KB >= 1) {
				//如果当前Byte的值大于等于1KB
				resultSize = df.format(size / (float) KB) + "KB";
			} else {
				resultSize = size + "B";
			}
			return resultSize;
		} catch (Exception e) {
			return null;
		}
	}

	/**
	 *   字符串杠转驼峰
	 * @param englishName
	 * @return
	 */
	public static String strBarToHump(String englishName){
		if(StringUtils.isEmpty(englishName)){
			return "";
		}
		String en = "";
		String names [] = englishName.split("_");
		for(int j=0; j< names.length; j++){
			if(StrUtilX.isEmpty(names[j])){
				continue;
			}
			if(j == 0){
				en = names[j];
			}else{
				en = en + names[j].substring(0, 1).toUpperCase() + names[j].substring(1, names[j].length());
			}
		}
		return en;
	}


	// 方法：转义单引号
	public static String escapeSingleQuotes(String input) {
		if (input != null) {
			input.replace("''", "'");
			return input.replace("'", "''"); // 替换单引号为两个单引号
		}
		return input;
	}

	/**
	 *   字符串驼峰转杠
	 * @param englishName
	 * @return
	 */
	public static String strHumpToBar(String englishName){
		if(StringUtils.isEmpty(englishName)){
			return "";
		}
		String en = "";
		for(int j=0; j< englishName.length(); j++){
			char c = englishName.charAt(j);
			if (!Character.isLowerCase(c) && !englishName.substring(j,j+1).toLowerCase().equals("_")){
				en = en + "_" + englishName.substring(j,j+1).toLowerCase();
			}else{
				en = en + englishName.substring(j,j+1);
			}
		}
		return en;
	}

	public static String turn(String str) {
		while (str.indexOf("\n") != -1) {
			str = str.substring(0, str.indexOf("\n")) + "<br>"
					+ str.substring(str.indexOf("\n") + 1);
		}
		return str;
	}

	/**
	 * 判断字符串中是否包含中文
	 * @param str
	 * 待校验字符串
	 * @return 是否为中文
	 * @warn 不能校验是否为中文标点符号
	 */
	public static boolean isContainChinese(String str) {
		Pattern p = Pattern.compile("[\u4e00-\u9fa5]");
		Matcher m = p.matcher(str);
		if (m.find()) {
			return true;
		}
		return false;
	}

	/**
	 * 匹配以("fieldName":")开头,以(")结尾的字符串，并且存储到分组中
	 * 正则表达式匹配字段值
	 * 不包含空值
	 * (?<=(href=")) 表示 匹配以(href=")开头的字符串，并且捕获(存储)到分组中
	 * (?=(">)) 表示 匹配以(">)结尾的字符串，并且捕获(存储)到分组中
	 *
	 * @param jsonStr
	 * @param fieldName
	 * @return
	 */
	public static List<String> getFieldListFromJsonStr(String jsonStr, String fieldName) {
		List<String> fieldValues = new ArrayList<>();
		String regex = "(?<=(\"" + fieldName + "\":\")).*?(?=(\"))";
		Pattern pattern = Pattern.compile(regex);
		Matcher matcher = pattern.matcher(jsonStr);
		while (matcher.find()) {
			if (!StringUtils.isEmpty(matcher.group().trim())) {
				fieldValues.add(matcher.group().trim());
			}
		}
		return fieldValues;
	}

	/**
	 * 验证邮箱
	 *
	 * @param email
	 * @return
	 */
	public static boolean checkEmail(String email) {
		boolean flag = false;
		try {
			String check = "^([a-z0-9A-Z]+[-|_|\\.]?)+[a-z0-9A-Z]@([a-z0-9A-Z]+(-[a-z0-9A-Z]+)?\\.)+[a-zA-Z]{2,}$";
			Pattern regex = Pattern.compile(check);
			Matcher matcher = regex.matcher(email);
			flag = matcher.matches();
		} catch (Exception e) {
			flag = false;
		}
		return flag;
	}
}
