package com.mongoso.mgs.module.codegen.controller.admin.codegen.vo;

import com.mongoso.mgs.module.codegen.controller.admin.codegen.vo.column.CodegenColumnRespVO;
import com.mongoso.mgs.module.codegen.controller.admin.codegen.vo.table.CodegenTableRespVO;
import lombok.Data;

import java.util.List;

@Data
public class CodegenDetailRespVO {

    private CodegenTableRespVO table;// 表定义

    private List<CodegenColumnRespVO> columns;// 字段定义

}
