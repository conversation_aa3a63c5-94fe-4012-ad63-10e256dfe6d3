package com.mongoso.mgs.module.codegen.service.db;

import com.mongoso.mgs.module.codegen.controller.admin.db.vo.DataSourceConfigCreateReqVO;
import com.mongoso.mgs.module.codegen.controller.admin.db.vo.DataSourceConfigUpdateReqVO;
import com.mongoso.mgs.module.codegen.dal.db.config.DataSourceConfigDO;

import jakarta.validation.Valid;
import java.sql.Connection;
import java.util.List;

/**
 * 数据源配置 Service 接口
 *
 * <AUTHOR>
 */
public interface DataSourceConfigService {

    void fullInitializeDataSources();

    Connection getConnectionFromConfig(Long configId);

    /**
     * 创建数据源配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createDataSourceConfig(@Valid DataSourceConfigUpdateReqVO createReqVO);

    /**
     * 更新数据源配置
     *
     * @param updateReqVO 更新信息
     */
    void updateDataSourceConfig(@Valid DataSourceConfigUpdateReqVO updateReqVO);

    /**
     * 删除数据源配置
     *
     * @param id 编号
     */
    void deleteDataSourceConfig(Long id);

    /**
     * 获得数据源配置
     *
     * @param id 编号
     * @return 数据源配置
     */
    DataSourceConfigDO getDataSourceConfig(Long id);

    /**
     * 获得数据源配置列表
     *
     * @return 数据源配置列表
     */
    List<DataSourceConfigDO> getDataSourceConfigList(String name);

}
