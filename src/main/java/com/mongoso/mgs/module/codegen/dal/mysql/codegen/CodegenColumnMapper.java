package com.mongoso.mgs.module.codegen.dal.mysql.codegen;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.module.codegen.dal.db.codegen.CodegenColumnDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface CodegenColumnMapper extends BaseMapperX<CodegenColumnDO> {

    default List<CodegenColumnDO> selectListByTableId(String tableId) {
        return selectList(Wrappers.<CodegenColumnDO>lambdaQuery()
                .eq(CodegenColumnDO::getTableId, tableId)
                .orderByAsc(CodegenColumnDO::getId));
    }

    default void deleteListByTableId(String tableId) {
        delete(Wrappers.<CodegenColumnDO>lambdaQuery()
                .eq(CodegenColumnDO::getTableId, tableId));
    }

}
