package com.mongoso.mgs.module.codegen.service.db;

import com.baomidou.dynamic.datasource.creator.DataSourceProperty;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.generator.config.DataSourceConfig;
import com.mongoso.mgs.common.util.DatabaseUtil;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.ObjUtilX;
import com.mongoso.mgs.module.codegen.common.convert.db.DataSourceConfigConvert;
import com.mongoso.mgs.module.codegen.common.util.JdbcUtils;
import com.mongoso.mgs.module.codegen.controller.admin.db.vo.DataSourceConfigUpdateReqVO;
import com.mongoso.mgs.module.codegen.dal.db.config.DataSourceConfigDO;
import com.mongoso.mgs.module.codegen.dal.mysql.db.DataSourceConfigMapper;
import com.mongoso.mgs.module.enums.DBTypeEnum;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.sql.DataSource;
import java.sql.Connection;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.mongoso.mgs.module.codegen.common.enums.ErrorCodeConstants.DATA_SOURCE_CONFIG_NOT_EXISTS;
import static com.mongoso.mgs.module.codegen.common.enums.ErrorCodeConstants.DATA_SOURCE_CONFIG_NOT_OK;
import static com.mongoso.mgs.module.codegen.common.util.BizExceptionUtilX.exception;

/**
 * 数据源配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DataSourceConfigServiceImpl implements DataSourceConfigService {

    @Resource
    private DataSourceConfigMapper dataSourceConfigMapper;

    @Resource
    private DynamicDataSourceProperties dynamicDataSourceProperties;
    //private WebFrameworkUtilX webFrameworkUtils;

    @Resource
    private DataSourceManager dataSourceManager;

    @Resource
    private DatabaseUtil databaseUtil;

    public DataSourceConfigServiceImpl(DataSourceManager dataSourceManager) {
        this.dataSourceManager = dataSourceManager;
    }

    // 全量初始化数据源
    @Override
    public void fullInitializeDataSources() {
        List<DataSourceConfigDO> configs = dataSourceConfigMapper.selectList(); // 假设有方法获取所有配置
        dataSourceManager.initializeDataSources(configs);
    }

    @Override
    public Connection getConnectionFromConfig(Long configId) {
        //DataSource dataSource = dataSourceManager.getDataSource(configId);
        //if (dataSource == null) {
        //    fullInitializeDataSources();
        //    throw new BizException(5001, "未找到数据库配置，已重新初始化数据源，请重试");
        //}
        //try {
        //    return dataSource.getConnection();
        //} catch (SQLException e) {
        //    throw new BizException(5001, "连接数据库失败:" + e.getMessage());
        //}

         //从数据库中获取连接信息
        try {
            DataSourceConfigDO config = dataSourceConfigMapper.selectById(configId);

            if (config != null) {
                // 使用 MyBatis Plus Generator 解析表结构，这里用Mybatis配置的多数据源
                DataSourceConfig dataSourceConfig = new DataSourceConfig.Builder(config.getUrl(), config.getUsername(),
                        config.getPassword()).build();

                // 获取并返回数据库连接
                return dataSourceConfig.getConn();

                // 手动使用 Druid 多数据源 目前只支持PG和MYSQL
                //String driverClass = "";
                //if(config.getUrl().startsWith("jdbc:postgresql")){
                //    driverClass = "org.postgresql.Driver";
                //}else{
                //    driverClass = "com.mysql.cj.jdbc.Driver";
                //}
                //DruidDataSource druidDataSource = new DruidDataSource();
                //druidDataSource.setDriverClassName(driverClass);
                //druidDataSource.setUrl(config.getUrl());
                //druidDataSource.setUsername(config.getUsername());
                //druidDataSource.setPassword(config.getPassword());
                //
                //// 配置连接池参数
                //druidDataSource.setInitialSize(5);
                //druidDataSource.setMinIdle(5);
                //druidDataSource.setMaxActive(20);
                //druidDataSource.setMaxWait(60000);
                //
                //return druidDataSource.getConnection();
            } else {
                throw new BizException(5001, "未找到数据库配置");
            }
        } catch (Exception e) {
            throw new BizException(5001, "连接数据库失败:" + e.getMessage());
        }
    }

    @Override
    public Long createDataSourceConfig(DataSourceConfigUpdateReqVO createReqVO) {

        //校验
        validDBType(createReqVO);
        // 校验连接有效性
        DataSourceConfigDO dataSourceConfig = DataSourceConfigConvert.convert(createReqVO);
        checkConnectionOK(dataSourceConfig);
        Date now = new Date();
        dataSourceConfig.setCreateTime(now);
        dataSourceConfig.setUpdateTime(now);
        // 插入
        dataSourceConfigMapper.insert(dataSourceConfig);

        // 新增数据源到管理器
        DataSource newDataSource = dataSourceManager.createDataSource(dataSourceConfig);
        dataSourceManager.addDataSource(dataSourceConfig.getId(), newDataSource);

        // 返回
        return dataSourceConfig.getId();
    }

    @Override
    public void updateDataSourceConfig(DataSourceConfigUpdateReqVO updateReqVO) {

        validateDataSourceConfigExists(updateReqVO.getId());
        // 校验存在
        validDBType(updateReqVO);

        // 校验连接有效性
        //databaseUtil.getCon(updateReqVO.getId());
        DataSourceConfigDO updateObj = DataSourceConfigConvert.convert(updateReqVO);
        checkConnectionOK(updateObj);

        //LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        // 更新
        updateObj.setUpdateTime(new Date());
        //updateObj.setUpdater(loginUser.getUserName());
        updateObj.setUpdater("管理员");
        dataSourceConfigMapper.updateById(updateObj);

        // 更新数据源到管理器
        DataSource newDataSource = dataSourceManager.createDataSource(updateObj);
        dataSourceManager.updateDataSource(updateObj.getId(), newDataSource);
    }

    private void validDBType(DataSourceConfigUpdateReqVO updateReqVO) {
        if(updateReqVO.getUrl().toLowerCase().contains(DBTypeEnum.PG.getValue())){
            updateReqVO.setDbType(DBTypeEnum.PG.getValue());
        } else if(updateReqVO.getUrl().toLowerCase().contains(DBTypeEnum.MYSQL.getValue())){
            updateReqVO.setDbType(DBTypeEnum.MYSQL.getValue());
        } else {
            throw new BizException(5001, "不支持的数据库类型，目前只支持PG和MYSQL");
        }
    }

    @Override
    public void deleteDataSourceConfig(Long id) {
        // 校验存在
        validateDataSourceConfigExists(id);
        // 删除
        dataSourceConfigMapper.deleteById(id);

        // 从管理器中删除数据源
        dataSourceManager.removeDataSource(id);
    }

    private void validateDataSourceConfigExists(Long id) {
        if (dataSourceConfigMapper.selectById(id) == null) {
            throw exception(DATA_SOURCE_CONFIG_NOT_EXISTS);
        }
    }

    @Override
    public DataSourceConfigDO getDataSourceConfig(Long id) {
        // 如果 id 为 0，默认为 master 的数据源
       if (Objects.equals(id, DataSourceConfigDO.ID_MASTER)) {
           return buildMasterDataSourceConfig();
       }
        // 从 DB 中读取
        DataSourceConfigDO dataSourceConfigDO = dataSourceConfigMapper.selectById(id);
        return dataSourceConfigDO;
    }

    @Override
    public List<DataSourceConfigDO> getDataSourceConfigList(String name) {
        QueryWrapper qw = new QueryWrapper<DataSourceConfigDO>();
        if(ObjUtilX.isNotEmpty(name)){
            qw.like("name", name);
        }

       List<DataSourceConfigDO> result = dataSourceConfigMapper.selectList(qw);
        // 补充 master 数据源
       //result.add(0, buildMasterDataSourceConfig());
       return result;
    }

    private void checkConnectionOK(DataSourceConfigDO config) {
        boolean success = JdbcUtils.isConnectionOK(config.getUrl(), config.getUsername(), config.getPassword());
        if (!success) {
            throw exception(DATA_SOURCE_CONFIG_NOT_OK);
        }
    }

   private DataSourceConfigDO buildMasterDataSourceConfig() {
       String primary = dynamicDataSourceProperties.getPrimary();
       DataSourceProperty dataSourceProperty = dynamicDataSourceProperties.getDatasource().get(primary);

       DataSourceConfigDO dataSourceConfigDO = new DataSourceConfigDO();
       dataSourceConfigDO.setId(DataSourceConfigDO.ID_MASTER);
       //dataSourceConfigDO.setName(primary);
       dataSourceConfigDO.setName(DataSourceConfigDO.DB_NME);
       dataSourceConfigDO.setUrl(dataSourceProperty.getUrl());
       dataSourceConfigDO.setUsername(dataSourceProperty.getUsername());
       dataSourceConfigDO.setPassword(dataSourceProperty.getPassword());
       return dataSourceConfigDO;
   }

}
