package com.mongoso.mgs.module.codegen.controller.admin.config;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.module.codegen.common.enums.ErrorCodeConstants;
import com.mongoso.mgs.module.codegen.common.util.BizExceptionUtilX;
import com.mongoso.mgs.module.codegen.common.util.ExcelUtils;
import com.mongoso.mgs.module.codegen.controller.admin.config.vo.*;
import com.mongoso.mgs.module.codegen.dal.db.config.ConfigDO;
import com.mongoso.mgs.module.codegen.dal.pojo.ResultX;
import com.mongoso.mgs.module.codegen.service.config.ConfigService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.mongoso.mgs.module.codegen.dal.pojo.ResultX.success;

/**
 * 这个目前没用
 */
@RestController
@RequestMapping("/infra/config")
@Validated
public class ConfigController {

    @Resource
    private ConfigService configService;

    @PostMapping("/create")
    public ResultX<Long> createConfig(@Valid @RequestBody ConfigCreateReqVO reqVO) {
        return success(configService.createConfig(reqVO));
    }

    @RequestMapping("/update")
    public ResultX<Boolean> updateConfig(@Valid @RequestBody ConfigUpdateReqVO reqVO) {
        configService.updateConfig(reqVO);
        return success(true);
    }

    @RequestMapping("/delete")
    public ResultX<Boolean> deleteConfig(@RequestParam("id") Long id) {
        configService.deleteConfig(id);
        return success(true);
    }

    @RequestMapping(value = "/get")
    public ResultX<ConfigRespVO> getConfig(@RequestParam("id") Long id) {
        return success(BeanUtilX.copy(configService.getConfig(id), ConfigRespVO::new));
    }

    @RequestMapping(value = "/get-value-by-key")
    public ResultX<String> getConfigKey(@RequestParam("key") String key) {
        ConfigDO config = configService.getConfigByKey(key);
        if (config == null) {
            return null;
        }
        if (!config.getVisible()) {
            throw BizExceptionUtilX.exception(ErrorCodeConstants.CONFIG_GET_VALUE_ERROR_IF_VISIBLE);
        }
        return success(config.getValue());
    }

    @RequestMapping("/page")
    public ResultX<PageResult<ConfigRespVO>> getConfigPage(@Valid ConfigPageReqVO reqVO) {
        PageResult<ConfigDO> page = configService.getConfigPage(reqVO);
//        return success(ConfigConvert.convertPage(page));
        return success(BeanUtilX.copyPage(page, ConfigRespVO::new));
    }

    @RequestMapping("/export")
    public void exportSysConfig(@Valid ConfigExportReqVO reqVO,
                                HttpServletResponse response) throws IOException {
        List<ConfigDO> list = configService.getConfigList(reqVO);
        // 拼接数据
        List<ConfigExcelVO> datas = BeanUtilX.copyList(list, ConfigExcelVO::new);
        // 输出
        ExcelUtils.write(response, "参数配置.xls", "数据", ConfigExcelVO.class, datas);
    }

}
