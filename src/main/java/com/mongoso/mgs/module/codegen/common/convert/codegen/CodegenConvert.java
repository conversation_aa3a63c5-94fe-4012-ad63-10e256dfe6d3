package com.mongoso.mgs.module.codegen.common.convert.codegen;

import com.baomidou.mybatisplus.generator.config.po.TableField;
import com.baomidou.mybatisplus.generator.config.po.TableInfo;
import com.baomidou.mybatisplus.generator.config.rules.IColumnType;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.module.codegen.controller.admin.codegen.vo.CodegenUpdateReqVO;
import com.mongoso.mgs.module.codegen.controller.admin.codegen.vo.column.CodegenColumnRespVO;
import com.mongoso.mgs.module.codegen.controller.admin.codegen.vo.table.CodegenTableRespVO;
import com.mongoso.mgs.module.codegen.controller.admin.codegen.vo.table.DatabaseTableRespVO;
import com.mongoso.mgs.module.codegen.dal.db.codegen.CodegenColumnDO;
import com.mongoso.mgs.module.codegen.dal.db.codegen.CodegenTableDO;
import org.apache.ibatis.type.JdbcType;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class CodegenConvert  {

    public static CodegenTableDO convert(TableInfo bean) {
        if ( bean == null ) {
            return null;
        }

        CodegenTableDO codegenTableDO = new CodegenTableDO();

        codegenTableDO.setTableName( bean.getName() );
        codegenTableDO.setTableComment( bean.getComment() );

        return codegenTableDO;
    }

    public static List<CodegenColumnDO> convertList(List<TableField> list) {
        if ( list == null ) {
            return null;
        }

        List<CodegenColumnDO> list1 = new ArrayList<CodegenColumnDO>( list.size() );
        for ( TableField tableField : list ) {
            list1.add( convert( tableField ) );
        }

        return list1;
    }

    public static CodegenColumnDO convert(TableField bean) {
        if ( bean == null ) {
            return null;
        }

        CodegenColumnDO codegenColumnDO = new CodegenColumnDO();

        codegenColumnDO.setColumnName( bean.getName() );

        String javaType = "";
        JdbcType jdbcType = beanMetaInfoJdbcType(bean);
        if(jdbcType.equals(JdbcType.OTHER)){
            // 其他类型如果是JSONB格式
            String dbType = bean.getMetaInfo().getTypeName();
            if (dbType.equalsIgnoreCase("jsonb")
                    || dbType.equalsIgnoreCase("json")) {

            }
            javaType = "JSONObject";
        }else{
            javaType = beanColumnTypeType( bean );
        }

        codegenColumnDO.setDataType( jdbcType.name() );
        codegenColumnDO.setColumnComment( bean.getComment() );
        codegenColumnDO.setNullable( beanMetaInfoNullable( bean ) );
        codegenColumnDO.setPrimaryKey( bean.isKeyFlag() );
        codegenColumnDO.setAutoIncrement( bean.isKeyIdentityFlag() );
        codegenColumnDO.setJavaType( javaType );
        codegenColumnDO.setJavaField( bean.getPropertyName() );

        return codegenColumnDO;
    }

    public static CodegenTableRespVO convert(CodegenTableDO bean) {
        if ( bean == null ) {
            return null;
        }

        CodegenTableRespVO codegenTableRespVO = new CodegenTableRespVO();

        codegenTableRespVO.setScene( bean.getScene() );
        codegenTableRespVO.setTableName( bean.getTableName() );
        codegenTableRespVO.setTableComment( bean.getTableComment() );
        codegenTableRespVO.setRemark( bean.getRemark() );
        codegenTableRespVO.setModuleName( bean.getModuleName() );
        codegenTableRespVO.setBusinessName( bean.getBusinessName() );
        codegenTableRespVO.setClassName( bean.getClassName() );
        codegenTableRespVO.setClassComment( bean.getClassComment() );
        codegenTableRespVO.setAuthor( bean.getAuthor() );
        codegenTableRespVO.setTemplateType( bean.getTemplateType() );
        codegenTableRespVO.setId( bean.getId() );
        if ( bean.getDataSourceConfigId() != null ) {
            codegenTableRespVO.setDataSourceConfigId( bean.getDataSourceConfigId().intValue() );
        }
        codegenTableRespVO.setCreateTime( bean.getCreateTime() );
        codegenTableRespVO.setUpdateTime( bean.getUpdateTime() );

        return codegenTableRespVO;
    }

    public static PageResult<CodegenTableRespVO> convertPage(PageResult<CodegenTableDO> page) {
        if ( page == null ) {
            return null;
        }

        PageResult<CodegenTableRespVO> pageResult = new PageResult<CodegenTableRespVO>();

        pageResult.setList( codegenTableDOListToCodegenTableRespVOList( page.getList() ) );
        pageResult.setTotalCount( page.getTotalCount() );

        return pageResult;
    }

    public static List<CodegenColumnRespVO> convertList02(List<CodegenColumnDO> list) {
        if ( list == null ) {
            return null;
        }

        List<CodegenColumnRespVO> list1 = new ArrayList<CodegenColumnRespVO>( list.size() );
        for ( CodegenColumnDO codegenColumnDO : list ) {
            list1.add( codegenColumnDOToCodegenColumnRespVO( codegenColumnDO ) );
        }

        return list1;
    }


    public static CodegenTableDO convert(CodegenUpdateReqVO.Table bean) {
        if ( bean == null ) {
            return null;
        }

        CodegenTableDO codegenTableDO = new CodegenTableDO();

        codegenTableDO.setId( bean.getId() );
        codegenTableDO.setScene( bean.getScene() );
        codegenTableDO.setTableName( bean.getTableName() );
        codegenTableDO.setTableComment( bean.getTableComment() );
        codegenTableDO.setRemark( bean.getRemark() );
        codegenTableDO.setModuleName( bean.getModuleName() );
        codegenTableDO.setBusinessName( bean.getBusinessName() );
        codegenTableDO.setClassName( bean.getClassName() );
        codegenTableDO.setClassComment( bean.getClassComment() );
        codegenTableDO.setAuthor( bean.getAuthor() );
        codegenTableDO.setTemplateType( bean.getTemplateType() );

        return codegenTableDO;
    }

    public static List<CodegenColumnDO> convertList03(List<CodegenUpdateReqVO.Column> columns) {
        if ( columns == null ) {
            return null;
        }

        List<CodegenColumnDO> list = new ArrayList<CodegenColumnDO>( columns.size() );
        for ( CodegenUpdateReqVO.Column column : columns ) {
            list.add( columnToCodegenColumnDO( column ) );
        }

        return list;
    }

    public static List<DatabaseTableRespVO> convertList04(List<TableInfo> list) {
        if ( list == null ) {
            return null;
        }

        List<DatabaseTableRespVO> list1 = new ArrayList<DatabaseTableRespVO>( list.size() );
        for ( TableInfo tableInfo : list ) {
            list1.add( tableInfoToDatabaseTableRespVO( tableInfo ) );
        }

        return list1;
    }

    private static JdbcType beanMetaInfoJdbcType(TableField tableField) {
        if ( tableField == null ) {
            return null;
        }
        TableField.MetaInfo metaInfo = tableField.getMetaInfo();
        if ( metaInfo == null ) {
            return null;
        }
        JdbcType jdbcType = metaInfo.getJdbcType();
        if ( jdbcType == null ) {
            return null;
        }
        return jdbcType;
    }

    private static Boolean beanMetaInfoNullable(TableField tableField) {
        if ( tableField == null ) {
            return null;
        }
        TableField.MetaInfo metaInfo = tableField.getMetaInfo();
        if ( metaInfo == null ) {
            return null;
        }
        return metaInfo.isNullable();
    }

    private static String beanColumnTypeType(TableField tableField) {
        if ( tableField == null ) {
            return null;
        }
        IColumnType columnType = tableField.getColumnType();
        if ( columnType == null ) {
            return null;
        }
        String type = columnType.getType();
        if ( type == null ) {
            return null;
        }
        return type;
    }

    protected static List<CodegenTableRespVO> codegenTableDOListToCodegenTableRespVOList(List<CodegenTableDO> list) {
        if ( list == null ) {
            return null;
        }

        List<CodegenTableRespVO> list1 = new ArrayList<CodegenTableRespVO>( list.size() );
        for ( CodegenTableDO codegenTableDO : list ) {
            list1.add( convert( codegenTableDO ) );
        }

        return list1;
    }

    protected static CodegenColumnRespVO codegenColumnDOToCodegenColumnRespVO(CodegenColumnDO codegenColumnDO) {
        if ( codegenColumnDO == null ) {
            return null;
        }

        CodegenColumnRespVO codegenColumnRespVO = new CodegenColumnRespVO();

        codegenColumnRespVO.setTableId( codegenColumnDO.getTableId() );
        codegenColumnRespVO.setColumnName( codegenColumnDO.getColumnName() );
        codegenColumnRespVO.setDataType( codegenColumnDO.getDataType() );
        codegenColumnRespVO.setColumnComment( codegenColumnDO.getColumnComment() );
        codegenColumnRespVO.setNullable( codegenColumnDO.getNullable() );
        codegenColumnRespVO.setPrimaryKey( codegenColumnDO.getPrimaryKey() );
        if ( codegenColumnDO.getAutoIncrement() != null ) {
            codegenColumnRespVO.setAutoIncrement( String.valueOf( codegenColumnDO.getAutoIncrement() ) );
        }
        codegenColumnRespVO.setOrdinalPosition( codegenColumnDO.getOrdinalPosition() );
        codegenColumnRespVO.setJavaType( codegenColumnDO.getJavaType() );
        codegenColumnRespVO.setJavaField( codegenColumnDO.getJavaField() );
        codegenColumnRespVO.setExample( codegenColumnDO.getExample() );
        codegenColumnRespVO.setAditOperation( codegenColumnDO.getAditOperation() );
        codegenColumnRespVO.setPageOperation( codegenColumnDO.getPageOperation() );
        codegenColumnRespVO.setListOperationCondition( codegenColumnDO.getListOperationCondition() );
        codegenColumnRespVO.setListOperationResult( codegenColumnDO.getListOperationResult() );
        codegenColumnRespVO.setId( codegenColumnDO.getId() );
        codegenColumnRespVO.setCreateTime( codegenColumnDO.getCreateTime() );

        return codegenColumnRespVO;
    }

    protected static CodegenColumnDO columnToCodegenColumnDO(CodegenUpdateReqVO.Column column) {
        if ( column == null ) {
            return null;
        }

        CodegenColumnDO codegenColumnDO = new CodegenColumnDO();

        codegenColumnDO.setId( column.getId() );
        codegenColumnDO.setTableId( column.getTableId() );
        codegenColumnDO.setColumnName( column.getColumnName() );
        codegenColumnDO.setDataType( column.getDataType() );
        codegenColumnDO.setColumnComment( column.getColumnComment() );
        codegenColumnDO.setNullable( column.getNullable() );
        codegenColumnDO.setPrimaryKey( column.getPrimaryKey() );
        if ( column.getAutoIncrement() != null ) {
            codegenColumnDO.setAutoIncrement( Boolean.parseBoolean( column.getAutoIncrement() ) );
        }
        codegenColumnDO.setOrdinalPosition( column.getOrdinalPosition() );
        codegenColumnDO.setJavaType( column.getJavaType() );
        codegenColumnDO.setJavaField( column.getJavaField() );
        codegenColumnDO.setExample( column.getExample() );
        codegenColumnDO.setAditOperation( column.getAditOperation() );
        codegenColumnDO.setPageOperation( column.getPageOperation() );
        codegenColumnDO.setListOperationCondition( column.getListOperationCondition() );
        codegenColumnDO.setListOperationResult( column.getListOperationResult() );

        //todo 补充基础字段
        codegenColumnDO.setCreateTime(new Date());
        codegenColumnDO.setCreator(System.getProperty("user.name"));
        codegenColumnDO.setUpdateTime(new Date());
        codegenColumnDO.setUpdater(System.getProperty("user.name"));
        return codegenColumnDO;
    }

    protected static DatabaseTableRespVO tableInfoToDatabaseTableRespVO(TableInfo tableInfo) {
        if ( tableInfo == null ) {
            return null;
        }

        DatabaseTableRespVO databaseTableRespVO = new DatabaseTableRespVO();

        databaseTableRespVO.setName( tableInfo.getName() );
        databaseTableRespVO.setComment( tableInfo.getComment() );

        return databaseTableRespVO;
    }
}
