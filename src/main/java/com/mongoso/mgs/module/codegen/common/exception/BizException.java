//package com.mongoso.mgs.module.codegen.common.exception;
//
//
//import com.mongoso.mgs.module.codegen.common.util.ErrorCode;
//import lombok.Data;
//import lombok.EqualsAndHashCode;
//
///**
// * 业务逻辑异常 Exception
// */
//@Data
//@EqualsAndHashCode(callSuper = true)
//public final class BizException extends RuntimeException {
//
//    /**
//     * 业务错误码
//     *
//     */
//    private Integer code;
//    /**
//     * 错误提示
//     */
//    private String message;
//
//    /**
//     * 空构造方法，避免反序列化问题
//     */
//    public BizException() {
//    }
//
//    public BizException(ErrorCode errorCode) {
//        this.code = errorCode.getCode();
//        this.message = errorCode.getMsg();
//    }
//
//    public BizException(Integer code, String message) {
//        this.code = code;
//        this.message = message;
//    }
//
//    public Integer getCode() {
//        return code;
//    }
//
//    public BizException setCode(Integer code) {
//        this.code = code;
//        return this;
//    }
//
//    @Override
//    public String getMessage() {
//        return message;
//    }
//
//    public BizException setMessage(String message) {
//        this.message = message;
//        return this;
//    }
//
//}
