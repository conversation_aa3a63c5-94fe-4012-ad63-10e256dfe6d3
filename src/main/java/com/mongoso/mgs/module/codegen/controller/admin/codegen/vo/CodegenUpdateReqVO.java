package com.mongoso.mgs.module.codegen.controller.admin.codegen.vo;

import com.mongoso.mgs.module.codegen.controller.admin.codegen.vo.column.CodegenColumnBaseVO;
import com.mongoso.mgs.module.codegen.controller.admin.codegen.vo.table.CodegenTableBaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.List;

@Data
public class CodegenUpdateReqVO {

    @Valid // 校验内嵌的字段
    @NotNull(message = "表定义不能为空")
    private Table table;

    @Valid // 校验内嵌的字段
    @NotNull(message = "字段定义不能为空")
    private List<Column> columns;

    @Data
    @EqualsAndHashCode(callSuper = true)
    @ToString(callSuper = true)
    @Valid
    public static class Table extends CodegenTableBaseVO {

        private String id;// 编号

    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    @ToString(callSuper = true)
    public static class Column extends CodegenColumnBaseVO {

        private String id; // 编号

    }

}
