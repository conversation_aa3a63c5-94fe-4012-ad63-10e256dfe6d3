package com.mongoso.mgs.module.codegen.service.codegen;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.generator.config.po.TableField;
import com.baomidou.mybatisplus.generator.config.po.TableInfo;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.ObjUtilX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.codegen.common.convert.codegen.CodegenConvert;
import com.mongoso.mgs.module.codegen.common.enums.codegen.CodegenSceneEnum;
import com.mongoso.mgs.module.codegen.common.util.CollectionUtils;
import com.mongoso.mgs.module.codegen.controller.admin.codegen.vo.CodegenCreateListReqVO;
import com.mongoso.mgs.module.codegen.controller.admin.codegen.vo.CodegenUpdateReqVO;
import com.mongoso.mgs.module.codegen.controller.admin.codegen.vo.table.CodegenTablePageReqVO;
import com.mongoso.mgs.module.codegen.controller.admin.codegen.vo.table.DatabaseTableRespVO;
import com.mongoso.mgs.module.codegen.dal.db.codegen.CodegenColumnDO;
import com.mongoso.mgs.module.codegen.dal.db.codegen.CodegenTableDO;
import com.mongoso.mgs.module.codegen.dal.mysql.codegen.CodegenColumnMapper;
import com.mongoso.mgs.module.codegen.dal.mysql.codegen.CodegenTableMapper;
import com.mongoso.mgs.module.codegen.service.codegen.inner.CodegenBuilder;
import com.mongoso.mgs.module.codegen.service.codegen.inner.CodegenEngine;
import com.mongoso.mgs.module.codegen.service.db.DatabaseTableService;
import com.mongoso.mgs.module.model.dal.db.modeltable.ModelTableDO;
import com.mongoso.mgs.module.model.dal.mysql.modeltable.ModelTableMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.BiPredicate;
import java.util.stream.Collectors;

import static com.mongoso.mgs.module.codegen.common.enums.ErrorCodeConstants.*;
import static com.mongoso.mgs.module.codegen.common.util.BizExceptionUtilX.exception;


/**
 * 代码生成 Service 实现类
 *
 * <AUTHOR>
 */
@Service
public class CodegenServiceImpl implements CodegenService {

    @Resource
    private DatabaseTableService databaseTableService;

    @Resource
    private ModelTableMapper modelTableMapper;
    @Resource
    private CodegenTableMapper codegenTableMapper;
    @Resource
    private CodegenColumnMapper codegenColumnMapper;

    @Resource
    private CodegenBuilder codegenBuilder;
    @Resource
    private CodegenEngine codegenEngine;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> createCodegenList(CodegenCreateListReqVO reqVO) {
        if(ObjUtilX.isEmpty(reqVO.getProjectId())){
            throw new BizException("5001", "项目id不能为空");
        }
        List<String> ids = new ArrayList<>(reqVO.getTableNames().size());
        // 遍历添加。虽然效率会低一点，但是没必要做成完全批量，因为不会这么大量
        reqVO.getTableNames().forEach(tableName -> ids.add(createCodegen(reqVO.getProjectId(), reqVO.getDataSourceConfigId(), tableName)));
        return ids;
    }

    public String createCodegen(Long projectId, Long dataSourceConfigId, String tableName) {
        // 从数据库中，获得数据库表结构
        TableInfo tableInfo = databaseTableService.getTable(dataSourceConfigId, tableName);
        // 导入
        return createCodegen0(projectId, dataSourceConfigId, tableInfo);
    }

    private String createCodegen0(Long projectId, Long dataSourceConfigId, TableInfo tableInfo) {
        // 校验导入的表和字段非空
        checkTableInfo(tableInfo);
        // 校验是否已经存在
        if (codegenTableMapper.selectByTableNameAndDataSourceConfigId(tableInfo.getName(),
                dataSourceConfigId) != null) {
            throw exception(CODEGEN_TABLE_EXISTS);
        }

        // 构建 CodegenTableDO 对象，插入到 DB 中
        CodegenTableDO table = codegenBuilder.buildTable(tableInfo);
        table.setProjectId(projectId);
        table.setDataSourceConfigId(dataSourceConfigId);
        table.setScene(CodegenSceneEnum.ADMIN.getScene()); // 默认配置下，使用管理后台的模板
        table.setAuthor(System.getProperty("user.name"));

        //todo 补充基础字段
        table.setCreateTime(new Date());
        table.setCreator(System.getProperty("user.name"));
        table.setUpdateTime(new Date());
        table.setUpdater(System.getProperty("user.name"));
        codegenTableMapper.insert(table);

        // 构建 CodegenColumnDO 数组，插入到 DB 中
        List<CodegenColumnDO> columns = codegenBuilder.buildColumns(table.getId(), tableInfo.getFields());
        // 如果没有主键，则使用第一个字段作为主键
        if (!tableInfo.isHavePrimaryKey()) {
            columns.get(0).setPrimaryKey(true);
        }
        codegenColumnMapper.insertBatch(columns);

        return table.getId();
    }

    private void checkTableInfo(TableInfo tableInfo) {
        if (tableInfo == null) {
            throw exception(CODEGEN_IMPORT_TABLE_NULL);
        }
        if (StrUtil.isEmpty(tableInfo.getComment())) {
            throw exception(CODEGEN_TABLE_INFO_TABLE_COMMENT_IS_NULL);
        }
        if (CollUtil.isEmpty(tableInfo.getFields())) {
            throw exception(CODEGEN_IMPORT_COLUMNS_NULL);
        }
        tableInfo.getFields().forEach(field -> {
            if (StrUtil.isEmpty(field.getComment())) {
                throw exception(CODEGEN_TABLE_INFO_COLUMN_COMMENT_IS_NULL, field.getName());
            }
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCodegen(CodegenUpdateReqVO updateReqVO) {
        // 校验是否已经存在
        if (codegenTableMapper.selectById(updateReqVO.getTable().getId()) == null) {
            throw exception(CODEGEN_TABLE_NOT_EXISTS);
        }

        // 更新 table 表定义
        CodegenTableDO updateTableObj = CodegenConvert.convert(updateReqVO.getTable());
//        CodegenTableDO updateTableObj = BeanUtilX.copy(updateReqVO.getTable(), CodegenTableDO::new);

        //todo 补充基础字段
        updateTableObj.setCreateTime(new Date());
        updateTableObj.setCreator(System.getProperty("user.name"));
        updateTableObj.setUpdateTime(new Date());
        updateTableObj.setUpdater(System.getProperty("user.name"));
        codegenTableMapper.updateById(updateTableObj);
        // 更新 column 字段定义
        List<CodegenColumnDO> updateColumnObjs = CodegenConvert.convertList03(updateReqVO.getColumns());
//        List<CodegenColumnDO> updateColumnObjs = BeanUtilX.copyList(updateReqVO.getColumns(), CodegenColumnDO::new);
        updateColumnObjs.forEach(updateColumnObj -> codegenColumnMapper.updateById(updateColumnObj));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncCodegenFromDB(String tableId) {
        // 校验是否已经存在
        CodegenTableDO table = codegenTableMapper.selectById(tableId);
        if (table == null) {
            throw exception(CODEGEN_TABLE_NOT_EXISTS);
        }
        // 从数据库中，获得数据库表结构
        TableInfo tableInfo = databaseTableService.getTable(table.getDataSourceConfigId(), table.getTableName());
        // 执行同步
        syncCodegen0(tableId, tableInfo);
    }

    private void syncCodegen0(String tableId, TableInfo tableInfo) {
        // 校验导入的表和字段非空
        checkTableInfo(tableInfo);
        List<TableField> tableFields = tableInfo.getFields();

        // 构建 CodegenColumnDO 数组，只同步新增的字段
        List<CodegenColumnDO> codegenColumns = codegenColumnMapper.selectListByTableId(tableId);
        Set<String> codegenColumnNames = CollectionUtils.convertSet(codegenColumns, CodegenColumnDO::getColumnName);

        //计算需要修改的字段，插入时重新插入，删除时将原来的删除
        BiPredicate<TableField, CodegenColumnDO> pr =
                (tableField, codegenColumn) -> tableField.getType().equals(codegenColumn.getDataType())
                        && tableField.getMetaInfo().isNullable() == codegenColumn.getNullable()
                        && tableField.isKeyFlag() == codegenColumn.getPrimaryKey()
                        && tableField.getComment().equals(codegenColumn.getColumnComment());
        Map<String, CodegenColumnDO> codegenColumnDOMap = CollectionUtils.convertMap(codegenColumns, CodegenColumnDO::getColumnName);
        //需要修改的字段
        Set<String> modifyFieldNames = tableFields.stream()
                .filter(tableField -> codegenColumnDOMap.get(tableField.getColumnName()) != null
                        && !pr.test(tableField, codegenColumnDOMap.get(tableField.getColumnName())))
                .map(TableField::getColumnName)
                .collect(Collectors.toSet());
        // 计算需要删除的字段
        Set<String> tableFieldNames = CollectionUtils.convertSet(tableFields, TableField::getName);
        Set<String> deleteColumnIds = codegenColumns.stream()
                .filter(column -> (!tableFieldNames.contains(column.getColumnName())) || modifyFieldNames.contains(column.getColumnName()))
                .map(CodegenColumnDO::getId).collect(Collectors.toSet());
        // 移除已经存在的字段
        tableFields.removeIf(column -> codegenColumnNames.contains(column.getColumnName()) && (!modifyFieldNames.contains(column.getColumnName())));
        if (CollUtil.isEmpty(tableFields) && CollUtil.isEmpty(deleteColumnIds)) {
            throw exception(CODEGEN_SYNC_NONE_CHANGE);
        }

        // 插入新增的字段
        List<CodegenColumnDO> columns = codegenBuilder.buildColumns(tableId, tableFields);
        codegenColumnMapper.insertBatch(columns);
        // 删除不存在的字段
        if (CollUtil.isNotEmpty(deleteColumnIds)) {
            codegenColumnMapper.deleteBatchIds(deleteColumnIds);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteCodegen(String tableId) {
        // 校验是否已经存在
        if (codegenTableMapper.selectById(tableId) == null) {
            throw exception(CODEGEN_TABLE_NOT_EXISTS);
        }

        // 删除 table 表定义
        codegenTableMapper.deleteById(tableId);
        // 删除 column 字段定义
        codegenColumnMapper.deleteListByTableId(tableId);
    }

    @Override
    public PageResult<CodegenTableDO> getCodegenTablePage(CodegenTablePageReqVO pageReqVO) {
        if(ObjUtilX.isEmpty(pageReqVO.getProjectId())){
            throw new BizException("5001", "项目id不能为空");
        }
        PageResult<CodegenTableDO> codegenTableDOPageResult = codegenTableMapper.selectPage(pageReqVO, LambdaQueryWrapperX.<CodegenTableDO>lambdaQueryX()
                .eq(CodegenTableDO::getProjectId, pageReqVO.getProjectId())
                .likeIfPresent(CodegenTableDO::getTableName, pageReqVO.getTableName())
                .likeIfPresent(CodegenTableDO::getTableComment, pageReqVO.getTableComment())
                .betweenIfPresent(CodegenTableDO::getCreateTime, pageReqVO.getCreateTime())
                .orderByDesc(CodegenTableDO::getCreateTime));

        return codegenTableDOPageResult;
    }

    @Override
    public CodegenTableDO getCodegenTablePage(String id) {
        return codegenTableMapper.selectById(id);
    }

    @Override
    public List<CodegenColumnDO> getCodegenColumnListByTableId(String tableId) {
        return codegenColumnMapper.selectListByTableId(tableId);
    }

    @Override
    public Map<String, String> generationCodes(String tableId) {
        // 校验是否已经存在
        CodegenTableDO table = codegenTableMapper.selectById(tableId);
        //todo 后续 根据 table.projectId 查询项目配置的基础路径 如 com.mongoso.mgs，然后再 execute
        if (table == null) {
            throw exception(CODEGEN_TABLE_NOT_EXISTS);
        }
        List<CodegenColumnDO> columns = codegenColumnMapper.selectListByTableId(tableId);
        if (CollUtil.isEmpty(columns)) {
            throw exception(CODEGEN_COLUMN_NOT_EXISTS);
        }

        // 执行生成
         return codegenEngine.execute(table, columns);
    }

    @Override
    public List<DatabaseTableRespVO> getDatabaseTableList(Long dataSourceConfigId,Long projectId, Integer opType,Integer exclude, String name, String comment) {
        List<TableInfo> tables = databaseTableService.getTableList(dataSourceConfigId, name, comment);

        if(ObjUtilX.isNotEmpty(opType) && opType == 1) { // 建模查询
            // 获取项目已经建模的表
            Set<String> existsTables = CollectionUtils.convertSet(
                    modelTableMapper.selectList(LambdaQueryWrapperX.<ModelTableDO>lambdaQueryX()
                            .eq(ModelTableDO::getProjectId, projectId)
                            .eq(ModelTableDO::getDataSourceConfigId, dataSourceConfigId)
                    ), ModelTableDO::getTableCode);

            // 如果是平台库，移除系统表
            if(dataSourceConfigId == 0L) {
                tables.removeIf(table -> table.getName().startsWith("sys_")
                        || table.getName().startsWith("a_codegen_")
                        || table.getName().startsWith("s_")
                );
            }
            List<DatabaseTableRespVO> result = new ArrayList<>();
            if(null != exclude && exclude == 1){
                // 移除在 Codegen 中，已经存在的
                tables.removeIf(table -> existsTables.contains(table.getName()));
                // 转换为响应对象
                result = CodegenConvert.convertList04(tables);
            }else {
                // 为每个表设置exist标志
                // 转换为响应对象
                result = CodegenConvert.convertList04(tables);
                for (DatabaseTableRespVO table : result) {
                    if (existsTables.contains(table.getName())) {
                        table.setExist(1); // 已存在
                    } else {
                        table.setExist(0); // 不存在
                    }
                }
            }
            return result;
        } else { // 代码生成器查询
            // 移除在 Codegen 中，已经存在的
            Set<String> existsTables = CollectionUtils.convertSet(
                    codegenTableMapper.selectListByDataSourceConfigId(dataSourceConfigId),
                    CodegenTableDO::getTableName);
            tables.removeIf(table -> existsTables.contains(table.getName()));

            // 直接转换并返回结果
            return CodegenConvert.convertList04(tables);
        }
    }

    // 旧的
    //@Override
    //public List<DatabaseTableRespVO> getDatabaseTableList(Long dataSourceConfigId, Integer opType, String name, String comment) {
    //    List<TableInfo> tables = databaseTableService.getTableList(dataSourceConfigId, name, comment);
    //
    //    // 移除已经生成的表
    //    if(ObjUtilX.isNotEmpty(opType) && opType == 1){//建模查询
    //        // 移除已经建模的
    //        Set<String> existsTables = CollectionUtils.convertSet(
    //                modelTableMapper.selectList(LambdaQueryWrapperX.<ModelTableDO>lambdaQueryX()
    //                        .eq(ModelTableDO::getDataSourceConfigId, dataSourceConfigId)
    //                ), ModelTableDO::getTableCode);
    //        if(dataSourceConfigId == 0L) {//如果是平台就移除系统表
    //            tables.removeIf(table -> existsTables.contains(table.getName())
    //                    || table.getName().startsWith("sys_")
    //                    || table.getName().startsWith("a_codegen_")
    //                    || table.getName().startsWith("s_")
    //            );
    //        }
    //    }else {//代码生成器查询
    //        // 移除在 Codegen 中，已经存在的
    //        Set<String> existsTables = CollectionUtils.convertSet(
    //                codegenTableMapper.selectListByDataSourceConfigId(dataSourceConfigId), CodegenTableDO::getTableName);
    //        tables.removeIf(table -> existsTables.contains(table.getName()));
    //    }
    //    return CodegenConvert.convertList04(tables);
    //}

}
