package com.mongoso.mgs.module.codegen.service.codegen.inner;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.generator.config.po.TableField;
import com.baomidou.mybatisplus.generator.config.po.TableInfo;
import com.google.common.collect.Sets;
import com.mongoso.mgs.module.codegen.common.convert.codegen.CodegenConvert;
import com.mongoso.mgs.module.codegen.common.enums.codegen.CodegenColumnHtmlTypeEnum;
import com.mongoso.mgs.module.codegen.common.enums.codegen.CodegenColumnListConditionEnum;
import com.mongoso.mgs.module.codegen.common.enums.codegen.CodegenTemplateTypeEnum;
import com.mongoso.mgs.module.codegen.dal.GenBaseDO;
import com.mongoso.mgs.module.codegen.dal.db.codegen.CodegenColumnDO;
import com.mongoso.mgs.module.codegen.dal.db.codegen.CodegenTableDO;
import org.springframework.stereotype.Component;

import java.util.*;

import static cn.hutool.core.text.CharSequenceUtil.*;

/**
 * 代码生成器的 Builder，负责：
 * 1. 将数据库的表 {@link TableInfo} 定义，构建成 {@link CodegenTableDO}
 * 2. 将数据库的列 {@link TableField} 构定义，建成 {@link CodegenColumnDO}
 */
@Component
public class CodegenBuilder {

    /**
     * 字段名与 {@link CodegenColumnListConditionEnum} 的默认映射
     * 注意，字段的匹配以后缀的方式
     */
    private static final Map<String, CodegenColumnListConditionEnum> COLUMN_LIST_OPERATION_CONDITION_MAPPINGS =
            MapUtil.<String, CodegenColumnListConditionEnum>builder()
                    .put("code", CodegenColumnListConditionEnum.LIKE)
                    .put("name", CodegenColumnListConditionEnum.LIKE)
                    .put("time", CodegenColumnListConditionEnum.BETWEEN)
                    .put("date", CodegenColumnListConditionEnum.BETWEEN)
                    .put("dt", CodegenColumnListConditionEnum.BETWEEN)
                    .build();

    /**
     * 字段名与 {@link CodegenColumnHtmlTypeEnum} 的默认映射
     * 注意，字段的匹配以后缀的方式
     */
    private static final Map<String, CodegenColumnHtmlTypeEnum> COLUMN_HTML_TYPE_MAPPINGS =
            MapUtil.<String, CodegenColumnHtmlTypeEnum>builder()
                    .put("status", CodegenColumnHtmlTypeEnum.RADIO)
                    .put("sex", CodegenColumnHtmlTypeEnum.RADIO)
                    .put("type", CodegenColumnHtmlTypeEnum.SELECT)
                    .put("image", CodegenColumnHtmlTypeEnum.UPLOAD_IMAGE)
                    .put("file", CodegenColumnHtmlTypeEnum.UPLOAD_FILE)
                    .put("content", CodegenColumnHtmlTypeEnum.EDITOR)
                    .put("description", CodegenColumnHtmlTypeEnum.EDITOR)
                    .put("demo", CodegenColumnHtmlTypeEnum.EDITOR)
                    .put("time", CodegenColumnHtmlTypeEnum.DATETIME)
                    .put("date", CodegenColumnHtmlTypeEnum.DATETIME)
                    .put("dt", CodegenColumnHtmlTypeEnum.DATETIME)
                    .build();

    /**
     * 多租户编号的字段名
     */
    public static final String TENANT_ID_FIELD = "tenantId";
    /**
     * {@link GenBaseDO} 的字段
     */
    public static final Set<String> BASE_DO_FIELDS = new HashSet<>();
    /**
     * 修改操作，不需要传递的字段
     */
    private static final Set<String> ADIT_OPERATION_EXCLUDE_COLUMN = Sets.newHashSet();
    /**
     * 分页操作的条件，不需要传递的字段
     */
    private static final Set<String> PAGE_OPERATION_EXCLUDE_COLUMN = Sets.newHashSet("id");
    /**
     * 列表操作的结果，不需要返回的字段
     */
    private static final Set<String> LIST_OPERATION_RESULT_EXCLUDE_COLUMN = Sets.newHashSet("id");

    static {
        Arrays.stream(ReflectUtil.getFields(ExcludeColumn.class)).forEach(field -> BASE_DO_FIELDS.add(field.getName()));
        BASE_DO_FIELDS.add(TENANT_ID_FIELD);
        // 处理 OPERATION 相关的字段
        ADIT_OPERATION_EXCLUDE_COLUMN.addAll(BASE_DO_FIELDS);
        PAGE_OPERATION_EXCLUDE_COLUMN.addAll(BASE_DO_FIELDS);
        PAGE_OPERATION_EXCLUDE_COLUMN.remove("createdDt"); // 创建时间，还是可能需要传递的
        LIST_OPERATION_RESULT_EXCLUDE_COLUMN.addAll(BASE_DO_FIELDS);
        LIST_OPERATION_RESULT_EXCLUDE_COLUMN.remove("createdDt"); // 创建时间，还是需要返回的
        LIST_OPERATION_RESULT_EXCLUDE_COLUMN.remove("updatedDt"); // 创建时间，还是需要返回的
        LIST_OPERATION_RESULT_EXCLUDE_COLUMN.remove("createdBy"); // 创建时间，还是需要返回的
        LIST_OPERATION_RESULT_EXCLUDE_COLUMN.remove("updatedBy"); // 创建时间，还是需要返回的
    }

    public CodegenTableDO buildTable(TableInfo tableInfo) {
        CodegenTableDO table = CodegenConvert.convert(tableInfo);
//        CodegenTableDO table = BeanUtilX.copy(tableInfo, CodegenTableDO::new);
        initTableDefault(table);
        return table;
    }

    /**
     * 初始化 Table 表的默认字段
     *
     * @param table 表定义
     */
    private void initTableDefault(CodegenTableDO table) {
        // 以 system_dept 举例子。moduleName 为 system、businessName 为 dept、className 为 Dept
        // 如果希望以 System 前缀，则可以手动在【代码生成 - 修改生成配置 - 基本信息】，将实体类名称改为 SystemDept 即可
        String tableName = table.getTableName().toLowerCase();
        // 第一步，第二个 _ 前缀的前面，去掉第一个下划线的后面，作为 module 名字；第二步，moduleName 必须小写；
        String substring = tableName.substring(tableName.indexOf("_") + 1);
        table.setModuleName(subBefore(substring, '_', false).toLowerCase());
        // 第一步，第一个 _ 前缀的后面，作为 module 名字; 第二步，可能存在多个 _ 的情况，转换成驼峰; 第三步，businessName 必须小写；
        table.setBusinessName(toCamelCase(subAfter(tableName, '_', false)).toLowerCase());
        // 驼峰 + 首字母大写；第一步，第一个 _ 前缀的后面，作为 class 名字；第二步，驼峰命名
        table.setClassName(upperFirst(toCamelCase(subAfter(tableName, '_', false))));
        // 去除结尾的表，作为类描述
        table.setClassComment(StrUtil.removeSuffixIgnoreCase(table.getTableComment(), "表"));
        table.setTemplateType(CodegenTemplateTypeEnum.CRUD.getType());
    }

    public List<CodegenColumnDO> buildColumns(String tableId, List<TableField> tableFields) {
        List<CodegenColumnDO> columns = CodegenConvert.convertList(tableFields);
        int index = 1;
        for (CodegenColumnDO column : columns) {
            column.setTableId(tableId);
            column.setOrdinalPosition(index++);
            column.setJavaFieldUpper(upperFirst(column.getJavaField())); // Java 大写属性名
            // 初始化 Column 列的默认字段
            processColumnOperation(column); // 处理 CRUD 相关的字段的默认值

            //todo 补充基础字段
            column.setCreateTime(new Date());
            column.setCreator(System.getProperty("user.name"));
            column.setUpdateTime(new Date());
            column.setUpdater(System.getProperty("user.name"));
        }
        Optional.ofNullable(columns.get(0)).ifPresent(item -> item.setNullable(true)); // 主键允许空
        return columns;
    }

    private void processColumnOperation(CodegenColumnDO column) {
        // 处理 aditOperation 字段
        column.setAditOperation(!ADIT_OPERATION_EXCLUDE_COLUMN.contains(column.getJavaField())); // 对于主键
        // 处理 pageOperation 字段
        column.setPageOperation(!PAGE_OPERATION_EXCLUDE_COLUMN.contains(column.getJavaField())
                && !column.getPrimaryKey()); // 对于主键，列表过滤不需要传递
        // 处理 listOperationCondition 字段
        COLUMN_LIST_OPERATION_CONDITION_MAPPINGS.entrySet().stream()
                .filter(entry -> StrUtil.endWithIgnoreCase(column.getJavaField(), entry.getKey()) && !column.getPrimaryKey())
                .findFirst().ifPresent(entry -> column.setListOperationCondition(entry.getValue().getCondition()));
        if (column.getListOperationCondition() == null) {
            column.setListOperationCondition(CodegenColumnListConditionEnum.EQ.getCondition());
        }
        // 处理 listOperationResult 字段
        column.setListOperationResult(!LIST_OPERATION_RESULT_EXCLUDE_COLUMN.contains(column.getJavaField()));
    }

}
