package com.mongoso.mgs.module.codegen.controller.admin.codegen.vo.table;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CodegenTableRespVO extends CodegenTableBaseVO {

    private String id; // 编号

    private Integer dataSourceConfigId;// 主键编号

    private Date createTime; // 创建时间

    private Date updateTime;// 更新时间

}
