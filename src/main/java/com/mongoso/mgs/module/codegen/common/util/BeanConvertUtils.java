package com.mongoso.mgs.module.codegen.common.util;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.cglib.beans.BeanCopier;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Supplier;

/**
 * Bean 转换高性能工具
 * 如果source或者targetSupplier只要有一个为null就返回null
 * 调用方如果把null进行转换，那就是想转换为null，为不为空应该由调用方自己负责
 * <AUTHOR>
 * @since 2023/1/30 17:13
 */
public class BeanConvertUtils {

    private BeanConvertUtils() {}

    /**
     * BeanCopier缓存(享元模式)
     */
    private static final Map<String, BeanCopier> BEAN_COPIER_MAP = new ConcurrentHashMap<>();

    /**
     * 将source对象的属性拷贝到target对象中去
     *
     * @param source source对象
     * @param target target对象
     */
    public static void copyProperties(Object source, Object target) {
        String cacheKey = source.getClass().toString().concat(target.getClass().toString());
        // 查看concurrentHashMap的实现方法介绍可以得知该方法为原子方法,保证了线程安全
        BeanCopier beanCopier = BEAN_COPIER_MAP.computeIfAbsent(cacheKey, k -> BeanCopier.create(source.getClass(), target.getClass(), false));
        beanCopier.copy(source, target, null);
    }

    public static <S, T> T convertTo(S sources, Supplier<T> targetSupplier) {
        return convertTo(sources, targetSupplier, null);
    }

    public static <S, T> T convertTo(S sources, Supplier<T> targetSupplier,ConvertCallBack<S,T> callBack) {
       if (sources == null){
           return null;
       }
        T target = targetSupplier.get();
       copyProperties(sources,target);

       if (callBack != null){
           callBack.callBack(sources,target);
       }
       return target;
    }

    public static <S, T> List<T> convertListTo(List<S> sources, Supplier<T> targetSupplier) {
        return convertListTo(sources, targetSupplier, null);
    }

    /**
     * 转换对象
     *
     * @param sources        源对象list
     * @param targetSupplier 目标对象供应方
     * @param callBack       回调方法
     * @param <S>            源对象类型
     * @param <T>            目标对象类型
     * @return 目标对象list
     */
    public static <S, T> List<T> convertListTo(List<S> sources, Supplier<T> targetSupplier, ConvertCallBack<S, T> callBack) {
        List<T> list = new ArrayList<>();
        for (S source : sources) {
            T target = targetSupplier.get();
            copyProperties(source, target);
            if (callBack != null) {
                callBack.callBack(source, target);
            }
            list.add(target);
        }
        return list;
    }

    public static <S, T> IPage<T> convertPageTo(IPage<S> sources, Supplier<T> targetSupplier) {
        return convertPageTo(sources, targetSupplier, null);
    }

    /**
     * 转换分页对象
     *
     * @param sources        源对象iPage
     * @param targetSupplier 目标对象供应方
     * @param callBack       回调方法
     * @param <S>            源对象类型
     * @param <T>            目标对象类型
     * @return 目标对象list
     */
    public static <S, T> IPage<T> convertPageTo(IPage<S> sources, Supplier<T> targetSupplier, ConvertCallBack<S, T> callBack) {
        return sources.convert(source -> {
            T target = targetSupplier.get();
            copyProperties(source, target);
            if (callBack != null) {
                callBack.callBack(source, target);
            }
            return target;
        });
    }

    /**
     * 回调接口
     *
     * @param <S> 源对象类型
     * @param <T> 目标对象类型
     */
    @FunctionalInterface
    public interface ConvertCallBack<S, T> {
        /**
         * 回调方法
         * @param t 目标对象
         * @param s 源对象
         */
        void callBack(S t, T s);
    }
}
