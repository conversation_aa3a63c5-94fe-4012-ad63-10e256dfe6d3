package com.mongoso.mgs.module.codegen.dal.pojo;


import com.fasterxml.jackson.annotation.JsonIgnore;
import com.mongoso.mgs.module.codegen.common.enums.GlobalErrorCodeConstants;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.module.codegen.common.exception.ServerException;
import com.mongoso.mgs.module.codegen.common.util.ErrorCode;
import lombok.Data;
import org.springframework.util.Assert;

import java.io.Serializable;
import java.util.Objects;

/**
 * 通用返回
 *
 * @param <T> 数据泛型
 */
@Data
public class ResultX<T> implements Serializable {

    /**
     * 错误码
     *
     */
    private String code;
    /**
     * 返回数据
     */
    private T data;
    /**
     * 错误提示，用户可阅读
     *

     */
    private String msg;

    private Integer result = 0;

    /**
     * 将传入的 result 对象，转换成另外一个泛型结果的对象
     *
     * 因为 A 方法返回的 ResultX 对象，不满足调用其的 B 方法的返回，所以需要进行转换。
     *
     * @param result 传入的 result 对象
     * @param <T> 返回的泛型
     * @return 新的 ResultX 对象
     */
    public static <T> ResultX<T> error(ResultX<?> result) {
        return error(result.getCode(), result.getMsg());
    }

    public static <T> ResultX<T> error(String code, String message) {
        Assert.isTrue(!GlobalErrorCodeConstants.SUCCESS.getCode().equals(code), "code 必须是错误的！");
        ResultX<T> result = new ResultX<>();
        result.code = code;
        result.msg = message;
        return result;
    }

    public static <T> ResultX<T> error(ErrorCode errorCode) {
        return error(errorCode.getCode(), errorCode.getMsg());
    }

    public static <T> ResultX<T> success(T data) {
        ResultX<T> result = new ResultX<>();
        result.code = GlobalErrorCodeConstants.SUCCESS.getCode();
        result.data = data;
        result.msg = "";
        result.result = 1;
        return result;
    }

    public static boolean isSuccess(String code) {
        return Objects.equals(code, GlobalErrorCodeConstants.SUCCESS.getCode());
    }

    @JsonIgnore // 避免 jackson 序列化
    public boolean isSuccess() {
        return isSuccess(code);
    }

    @JsonIgnore // 避免 jackson 序列化
    public boolean isError() {
        return !isSuccess();
    }

    // ========= 和 Exception 异常体系集成 =========

    /**
     * 判断是否有异常。如果有，则抛出 {@link BizException} 异常
     */
    public void checkError() throws BizException {
        if (isSuccess()) {
            return;
        }
        // 服务端异常
        if (GlobalErrorCodeConstants.isServerErrorCode(code)) {
            throw new ServerException(code, msg);
        }
        // 业务异常
        throw new BizException(code, msg);
    }

    /**
     * 判断是否有异常。如果有，则抛出 {@link BizException} 异常
     * 如果没有，则返回 {@link #data} 数据
     */
    @JsonIgnore // 避免 jackson 序列化
    public T getCheckedData() {
        checkError();
        return data;
    }

    public static <T> ResultX<T> error(BizException bizException) {
        return error(bizException.getCode(), bizException.getMessage());
    }

}
