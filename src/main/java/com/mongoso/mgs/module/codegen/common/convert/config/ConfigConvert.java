package com.mongoso.mgs.module.codegen.common.convert.config;

import com.mongoso.mgs.module.codegen.controller.admin.config.vo.ConfigCreateReqVO;
import com.mongoso.mgs.module.codegen.controller.admin.config.vo.ConfigExcelVO;
import com.mongoso.mgs.module.codegen.controller.admin.config.vo.ConfigRespVO;
import com.mongoso.mgs.module.codegen.controller.admin.config.vo.ConfigUpdateReqVO;
import com.mongoso.mgs.module.codegen.dal.db.config.ConfigDO;
import com.mongoso.mgs.framework.common.domain.PageResult;

import java.util.ArrayList;
import java.util.List;


public class ConfigConvert  {

    public static PageResult<ConfigRespVO> convertPage(PageResult<ConfigDO> page) {
        if ( page == null ) {
            return null;
        }

        PageResult<ConfigRespVO> pageResult = new PageResult<ConfigRespVO>();

        pageResult.setList( configDOListToConfigRespVOList( page.getList() ) );
        pageResult.setTotalCount( page.getTotalCount() );

        return pageResult;
    }

    public static ConfigRespVO convert(ConfigDO bean) {
        if ( bean == null ) {
            return null;
        }

        ConfigRespVO configRespVO = new ConfigRespVO();

        configRespVO.setKey( bean.getConfigKey() );
        configRespVO.setCategory( bean.getCategory() );
        configRespVO.setName( bean.getName() );
        configRespVO.setValue( bean.getValue() );
        configRespVO.setVisible( bean.getVisible() );
        configRespVO.setRemark( bean.getRemark() );
        configRespVO.setId( bean.getId() );
        configRespVO.setType( bean.getType() );
        configRespVO.setCreateTime( bean.getCreateTime() );

        return configRespVO;
    }

    public static ConfigDO convert(ConfigCreateReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        ConfigDO configDO = new ConfigDO();

        configDO.setConfigKey( bean.getKey() );
        configDO.setCategory( bean.getCategory() );
        configDO.setName( bean.getName() );
        configDO.setValue( bean.getValue() );
        configDO.setVisible( bean.getVisible() );
        configDO.setRemark( bean.getRemark() );

        return configDO;
    }

    public static ConfigDO convert(ConfigUpdateReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        ConfigDO configDO = new ConfigDO();

        configDO.setId( bean.getId() );
        configDO.setCategory( bean.getCategory() );
        configDO.setName( bean.getName() );
        configDO.setValue( bean.getValue() );
        configDO.setVisible( bean.getVisible() );
        configDO.setRemark( bean.getRemark() );

        return configDO;
    }

    public static List<ConfigExcelVO> convertList(List<ConfigDO> list) {
        if ( list == null ) {
            return null;
        }

        List<ConfigExcelVO> list1 = new ArrayList<ConfigExcelVO>( list.size() );
        for ( ConfigDO configDO : list ) {
            list1.add( configDOToConfigExcelVO( configDO ) );
        }

        return list1;
    }

    protected static List<ConfigRespVO> configDOListToConfigRespVOList(List<ConfigDO> list) {
        if ( list == null ) {
            return null;
        }

        List<ConfigRespVO> list1 = new ArrayList<ConfigRespVO>( list.size() );
        for ( ConfigDO configDO : list ) {
            list1.add( convert( configDO ) );
        }

        return list1;
    }

    protected static ConfigExcelVO configDOToConfigExcelVO(ConfigDO configDO) {
        if ( configDO == null ) {
            return null;
        }

        ConfigExcelVO configExcelVO = new ConfigExcelVO();

        configExcelVO.setId( configDO.getId() );
        configExcelVO.setName( configDO.getName() );
        configExcelVO.setValue( configDO.getValue() );
        configExcelVO.setType( configDO.getType() );
        configExcelVO.setRemark( configDO.getRemark() );
        configExcelVO.setCreateTime( configDO.getCreateTime() );

        return configExcelVO;
    }
}
