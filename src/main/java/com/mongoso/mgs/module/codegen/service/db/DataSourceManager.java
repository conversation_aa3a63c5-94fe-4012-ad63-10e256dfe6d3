package com.mongoso.mgs.module.codegen.service.db;

import com.alibaba.druid.pool.DruidDataSource;
import com.mongoso.mgs.module.codegen.dal.db.config.DataSourceConfigDO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/2/21
 * @description 搞一个数据源管理方法
 */
@Component
public class DataSourceManager {
    @Resource
    private DataSource dataSource;

    private Map<Long, DataSource> dataSourceMap = new HashMap<>();

    // 添加数据源
    public void addDataSource(Long id, DataSource config) {
        if (!dataSourceMap.containsKey(id)) {
            dataSourceMap.put(id, config);
        }
    }

    // 删除数据源
    public void removeDataSource(Long id) {
        if (dataSourceMap.containsKey(id)) {
            DataSource ds = dataSourceMap.remove(id);
            if (ds instanceof DruidDataSource) {
                ((DruidDataSource) ds).close(); // 关闭连接池
            }
        }
    }

    // 更新数据源
    public void updateDataSource(Long id, DataSource config) {
        if (dataSourceMap.containsKey(id)) {
            removeDataSource(id); // 先移除旧的数据源
            addDataSource(id, config); // 再添加新的数据源
        }
    }

    // 根据 ID 获取数据源
    public DataSource getDataSource(Long id) {
        if(id == 0L){
            return dataSource;
        }
        return dataSourceMap.get(id);
    }

    // 全量初始化数据源
    public void initializeDataSources(List<DataSourceConfigDO> configs) {
        dataSourceMap.clear(); // 清空当前的数据源映射
        for (DataSourceConfigDO config : configs) {
            DataSource dataSource = createDataSource(config);
            if (dataSource != null) {
                addDataSource(config.getId(), dataSource);
            }
        }
    }

    // 创建数据源的方法
    public DataSource createDataSource(DataSourceConfigDO config) {
        DruidDataSource druidDataSource = new DruidDataSource();
        druidDataSource.setDriverClassName(getDriverClass(config.getUrl()));
        druidDataSource.setUrl(config.getUrl());
        druidDataSource.setUsername(config.getUsername());
        druidDataSource.setPassword(config.getPassword());

        // 配置连接池参数
        druidDataSource.setInitialSize(5);
        druidDataSource.setMinIdle(5);
        druidDataSource.setMaxActive(20);
        druidDataSource.setMaxWait(60000);


        return druidDataSource;
    }

    private String getDriverClass(String url) {
        if (url.startsWith("jdbc:postgresql")) {
            return "org.postgresql.Driver";
        } else if (url.startsWith("jdbc:mysql")) {
            return "com.mysql.cj.jdbc.Driver";
        }
        throw new IllegalArgumentException("Unsupported database type");
    }
}
