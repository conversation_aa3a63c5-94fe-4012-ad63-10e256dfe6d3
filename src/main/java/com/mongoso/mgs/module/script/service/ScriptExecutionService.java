package com.mongoso.mgs.module.script.service;

import java.util.Map;

/**
 * 脚本执行服务
 */
public interface ScriptExecutionService {
    
    /**
     * 执行自定义脚本
     * @param bizId 业务ID
     * @param scriptId 脚本ID
     * @param params 脚本参数
     * @return 执行结果
     */
    Object executeScript(Long bizId, String scriptId, Map<String, Object> params);
    
    /**
     * 保存自定义脚本
     * @param bizId 业务ID
     * @param scriptId 脚本ID
     * @param scriptContent 脚本内容
     * @param scriptType 脚本类型 (JS/GROOVY)
     */
    void saveScript(Long bizId, String scriptId, String scriptContent, String scriptType);
    
    /**
     * 获取自定义脚本
     * @param bizId 业务ID
     * @param scriptId 脚本ID
     * @return 脚本内容
     */
    Map<String, Object> getScript(Long bizId, String scriptId);
}