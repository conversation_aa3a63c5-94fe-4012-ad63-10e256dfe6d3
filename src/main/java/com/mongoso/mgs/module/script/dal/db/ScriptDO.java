package com.mongoso.mgs.module.script.dal.db;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 自定义脚本 DO
 */
@TableName("lowcode.sys_lowcode_script")
@Data
@EqualsAndHashCode(callSuper = true)
public class ScriptDO extends OperateDO {

    @TableId
    private Long id;
    /**
     * 业务ID
     */
    private Long bizId;
    
    /**
     * 脚本ID
     */
    private String scriptId;
    
    /**
     * 脚本名称
     */
    private String scriptName;
    
    /**
     * 脚本内容
     */
    private String scriptContent;
    
    /**
     * 脚本类型 (JS/GROOVY/PYTHON)
     */
    private String scriptType;
    
    /**
     * 是否启用
     */
    private Boolean enabled;
}