package com.mongoso.mgs.module.script.controller.admin;

import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.module.script.controller.vo.ScriptExecuteReqVO;
import com.mongoso.mgs.module.script.controller.vo.ScriptSaveReqVO;
import com.mongoso.mgs.module.script.controller.vo.ScriptTestReqVO;
import com.mongoso.mgs.module.script.service.ScriptExecutionService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

import static com.mongoso.mgs.framework.common.domain.ResultX.success;

/**
 * 脚本管理 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/admin/script")
@Validated
@Slf4j
public class ScriptController {

    @Resource
    private ScriptExecutionService scriptExecutionService;

    @PostMapping("/save")
    @PreAuthorize("@ss.hasPermission('script:save')")
    public ResultX<Boolean> saveScript(@Valid @RequestBody ScriptSaveReqVO reqVO) {
        scriptExecutionService.saveScript(reqVO.getBizId(), reqVO.getScriptId(), 
                reqVO.getScriptContent(), reqVO.getScriptType());
        return success(true);
    }

    @GetMapping("/get")
    @PreAuthorize("@ss.hasPermission('script:query')")
    public ResultX<Map<String, Object>> getScript(
            @RequestParam Long bizId,
            @RequestParam String scriptId) {
        Map<String, Object> script = scriptExecutionService.getScript(bizId, scriptId);
        return success(script);
    }

    @PostMapping("/execute")
    @PreAuthorize("@ss.hasPermission('script:execute')")
    public ResultX<Object> executeScript(@Valid @RequestBody ScriptExecuteReqVO reqVO) {
        Object result = scriptExecutionService.executeScript(reqVO.getBizId(), 
                reqVO.getScriptId(), reqVO.getParams());
        return success(result);
    }

    @PostMapping("/test")
    @PreAuthorize("@ss.hasPermission('script:test')")
    public ResultX<Object> testScript(@Valid @RequestBody ScriptTestReqVO reqVO) {
        try {
            // 临时保存脚本用于测试
            String tempScriptId = "temp_test_" + System.currentTimeMillis();
            scriptExecutionService.saveScript(reqVO.getBizId(), tempScriptId, 
                    reqVO.getScriptContent(), reqVO.getScriptType());
            
            // 执行测试
            Object result = scriptExecutionService.executeScript(reqVO.getBizId(), 
                    tempScriptId, reqVO.getParams());
            
            return success(result);
        } catch (Exception e) {
            log.error("脚本测试失败: {}", e.getMessage(), e);
            return ResultX.error("5001", "脚本测试失败: " + e.getMessage());
        }
    }
}