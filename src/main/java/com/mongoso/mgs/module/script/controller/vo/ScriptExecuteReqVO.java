package com.mongoso.mgs.module.script.controller.vo;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.Map;

/**
 * 脚本执行请求VO
 */
@Data
public class ScriptExecuteReqVO {

    @NotNull(message = "业务ID不能为空")
    private Long bizId;

    @NotBlank(message = "脚本ID不能为空")
    private String scriptId;

    private Map<String, Object> params;
}