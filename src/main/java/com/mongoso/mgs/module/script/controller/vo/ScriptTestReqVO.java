package com.mongoso.mgs.module.script.controller.vo;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Map;

/**
 * 脚本测试请求VO
 */
@Data
public class ScriptTestReqVO {

    @NotNull(message = "业务ID不能为空")
    private Long bizId;

    @NotBlank(message = "脚本内容不能为空")
    private String scriptContent;

    @NotBlank(message = "脚本类型不能为空")
    private String scriptType; // JS/GROOVY/PYTHON

    private Map<String, Object> params;
}