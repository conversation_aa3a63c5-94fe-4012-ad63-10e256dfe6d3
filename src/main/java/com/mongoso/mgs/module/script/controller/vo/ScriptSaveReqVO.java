package com.mongoso.mgs.module.script.controller.vo;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 脚本保存请求VO
 */
@Data
public class ScriptSaveReqVO {

    @NotNull(message = "业务ID不能为空")
    private Long bizId;

    @NotBlank(message = "脚本ID不能为空")
    private String scriptId;

    private String scriptName;

    @NotBlank(message = "脚本内容不能为空")
    private String scriptContent;

    @NotBlank(message = "脚本类型不能为空")
    private String scriptType; // JS/GROOVY/PYTHON

    private String description;

    private Boolean enabled = true;
}