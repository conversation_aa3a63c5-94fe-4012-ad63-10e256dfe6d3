package com.mongoso.mgs.module.script.dal.mysql;

import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.script.dal.db.ScriptDO;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface ScriptMapper extends BaseMapperX<ScriptDO> {

    /**
     * 根据 bizId 和 scriptId 获取脚本
     *
     * @param bizId    业务 ID
     * @param scriptId 脚本 ID
     * @return 脚本
     */
    default ScriptDO getScriptByBizIdAndScriptId(Long bizId, String scriptId) {
        return selectOne(LambdaQueryWrapperX.<ScriptDO>lambdaQueryX()
                .eq(ScriptDO::getBizId, bizId)
                .eq(ScriptDO::getScriptId, scriptId)
                .last(" limit 1")
        );
    }
}