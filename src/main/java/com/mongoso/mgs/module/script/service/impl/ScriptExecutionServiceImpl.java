package com.mongoso.mgs.module.script.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mongoso.mgs.common.util.HttpRequestUtil;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.ObjUtilX;
import com.mongoso.mgs.framework.mybatis.core.util.IDUtilX;
import com.mongoso.mgs.module.script.dal.db.ScriptDO;
import com.mongoso.mgs.module.script.dal.mysql.ScriptMapper;
import com.mongoso.mgs.module.script.service.ScriptExecutionService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.SimpleBindings;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileWriter;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class ScriptExecutionServiceImpl implements ScriptExecutionService {

    @Resource
    private ScriptMapper scriptMapper;

    @Value("${remote.python.executeurl:}")
    private String pythonExecuteUrl;

    @Value("${remote.python.use-local:false}")
    private boolean useLocalPython;

    @Value("${remote.python.local-path:python}")
    private String pythonPath;

    @Override
    public Object executeScript(Long bizId, String scriptId, Map<String, Object> params) {
        log.info("执行自定义脚本, bizId: {}, scriptId: {}", bizId, scriptId);
        
        // 获取脚本内容
        ScriptDO script = scriptMapper.getScriptByBizIdAndScriptId(bizId, scriptId);
        if (script == null) {
            throw new BizException("5001", "脚本不存在: " + scriptId);
        }
        
        String scriptContent = script.getScriptContent();
        String scriptType = script.getScriptType();
        
        if (ObjUtilX.isEmpty(scriptContent)) {
            throw new BizException("5001", "脚本内容为空");
        }
        
        // 根据脚本类型执行
        switch (scriptType.toUpperCase()) {
            case "JS":
                return executeJavaScript(scriptContent, params);
            case "GROOVY":
                return executeGroovyScript(scriptContent, params);
            case "PYTHON":
                return executePythonScript(scriptContent, params);
            default:
                throw new BizException("5001", "不支持的脚本类型: " + scriptType);
        }
    }

    @Override
    public void saveScript(Long bizId, String scriptId, String scriptContent, String scriptType) {
        log.info("保存自定义脚本, bizId: {}, scriptId: {}", bizId, scriptId);
        
        ScriptDO script = scriptMapper.getScriptByBizIdAndScriptId(bizId, scriptId);
        if (script == null) {
            // 新增
            script = new ScriptDO();
            script.setId(IDUtilX.getId());
            script.setBizId(bizId);
            script.setScriptId(scriptId);
            script.setScriptContent(scriptContent);
            script.setScriptType(scriptType);
            scriptMapper.insert(script);
        } else {
            // 更新
            script.setScriptContent(scriptContent);
            script.setScriptType(scriptType);
            scriptMapper.updateById(script);
        }
    }

    @Override
    public Map<String, Object> getScript(Long bizId, String scriptId) {
        ScriptDO script = scriptMapper.getScriptByBizIdAndScriptId(bizId, scriptId);
        if (script == null) {
            return new HashMap<>();
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("scriptId", script.getScriptId());
        result.put("scriptContent", script.getScriptContent());
        result.put("scriptType", script.getScriptType());
        return result;
    }
    
    /**
     * 执行JavaScript脚本
     */
    private Object executeJavaScript(String scriptContent, Map<String, Object> params) {
        try {
            ScriptEngineManager manager = new ScriptEngineManager();
            ScriptEngine engine = manager.getEngineByName("nashorn");
            
            // 绑定参数
            SimpleBindings bindings = new SimpleBindings();
            bindings.putAll(params);
            
            // 执行脚本
            return engine.eval(scriptContent, bindings);
        } catch (Exception e) {
            log.error("执行JavaScript脚本失败: {}", e.getMessage(), e);
            throw new BizException("5001", "执行JavaScript脚本失败: " + e.getMessage());
        }
    }
    
    /**
     * 执行Groovy脚本
     */
    private Object executeGroovyScript(String scriptContent, Map<String, Object> params) {
        try {
            ScriptEngineManager manager = new ScriptEngineManager();
            ScriptEngine engine = manager.getEngineByName("groovy");
            
            // 绑定参数
            SimpleBindings bindings = new SimpleBindings();
            bindings.putAll(params);
            
            // 执行脚本
            return engine.eval(scriptContent, bindings);
        } catch (Exception e) {
            log.error("执行Groovy脚本失败: {}", e.getMessage(), e);
            throw new BizException("5001", "执行Groovy脚本失败: " + e.getMessage());
        }
    }
    
    /**
     * 执行Python脚本
     */
    private Object executePythonScript(String scriptContent, Map<String, Object> params) {
        if (useLocalPython) {
            return executePythonScriptLocal(scriptContent, params);
        } else {
            return executePythonScriptRemote(scriptContent, params);
        }
    }

    /**
     * 本地Python执行
     */
    private Object executePythonScriptLocal(String scriptContent, Map<String, Object> params) {
        try {
            // 首先检查Python环境
            if (!checkPythonEnvironment()) {
                throw new BizException("5001", "Python环境检查失败，请确认Python已正确安装并配置");
            }
            
            // 创建临时Python文件
            File tempScript = File.createTempFile("script_", ".py");
            tempScript.deleteOnExit();
            
            // 准备脚本内容，注入参数
            String fullScript = buildLocalPythonScript(scriptContent, params);
            
            // 写入脚本文件
            try (FileWriter writer = new FileWriter(tempScript, StandardCharsets.UTF_8)) {
                writer.write(fullScript);
            }
            
            // 增强日志：输出完整的脚本内容用于调试
            log.info("生成的Python脚本内容:\n{}", fullScript);
            log.info("Python路径: {}, 脚本文件: {}", pythonPath, tempScript.getAbsolutePath());
            
            // 构建执行命令 - 添加更多调试参数
            ProcessBuilder processBuilder = new ProcessBuilder(pythonPath, "-u", tempScript.getAbsolutePath());
            processBuilder.redirectErrorStream(false); // 分别处理标准输出和错误输出
            
            // 设置工作目录和环境变量
            processBuilder.directory(new File(System.getProperty("user.dir")));
            Map<String, String> env = processBuilder.environment();
            env.put("PYTHONIOENCODING", "utf-8");
            env.put("PYTHONUNBUFFERED", "1"); // 禁用Python输出缓冲
            env.put("PYTHONPATH", System.getProperty("user.dir"));
            
            log.info("执行本地Python脚本: {}", tempScript.getAbsolutePath());
            
            // 启动进程
            Process process = processBuilder.start();
            
            // 分别读取标准输出和错误输出
            StringBuilder output = new StringBuilder();
            StringBuilder errorOutput = new StringBuilder();
            
            // 使用线程分别读取输出流，避免阻塞
            Thread outputThread = new Thread(() -> {
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream(), StandardCharsets.UTF_8))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        output.append(line).append("\n");
                    }
                } catch (Exception e) {
                    log.error("读取Python标准输出失败: {}", e.getMessage());
                }
            });
            
            Thread errorThread = new Thread(() -> {
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getErrorStream(), StandardCharsets.UTF_8))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        errorOutput.append(line).append("\n");
                    }
                } catch (Exception e) {
                    log.error("读取Python错误输出失败: {}", e.getMessage());
                }
            });
            
            outputThread.start();
            errorThread.start();
            
            // 等待进程完成，设置超时时间
            boolean finished = process.waitFor(30, TimeUnit.SECONDS);
            if (!finished) {
                process.destroyForcibly();
                throw new BizException("5001", "Python脚本执行超时（30秒）");
            }
            
            // 等待读取线程完成
            outputThread.join(5000);
            errorThread.join(5000);
            
            int exitCode = process.exitValue();
            String result = output.toString().trim();
            String error = errorOutput.toString().trim();
            
            log.info("Python脚本执行完成，退出码: {}, 输出长度: {}, 错误长度: {}", 
                    exitCode, result.length(), error.length());
            
            // 详细记录输出和错误信息
            if (!result.isEmpty()) {
                log.info("Python脚本标准输出: {}", result);
            }
            if (!error.isEmpty()) {
                log.error("Python脚本错误输出: {}", error);
            }
            
            if (exitCode != 0) {
                String errorMsg = String.format("Python脚本执行失败，退出码: %d", exitCode);
                if (!error.isEmpty()) {
                    errorMsg += ", 错误信息: " + error;
                }
                if (!result.isEmpty()) {
                    errorMsg += ", 输出信息: " + result;
                }
                
                // 根据退出码提供更具体的错误信息
                errorMsg += getExitCodeDescription(exitCode);
                
                log.error(errorMsg);
                throw new BizException("5001", errorMsg);
            }
            
            // 解析返回结果
            return parseLocalPythonResult(result);
            
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            log.error("本地Python脚本执行异常: {}", e.getMessage(), e);
            throw new BizException("5001", "本地Python脚本执行失败: " + e.getMessage());
        }
    }

    /**
     * 构建本地Python脚本内容
     */
    private String buildLocalPythonScript(String scriptContent, Map<String, Object> params) {
        StringBuilder script = new StringBuilder();
        
        try {
            // 添加必要的导入
            script.append("#!/usr/bin/env python\n");
            script.append("# -*- coding: utf-8 -*-\n");
            script.append("import json\n");
            script.append("import sys\n");
            script.append("import os\n");
            script.append("import traceback\n\n");
            
            // 注入参数数据 - 修复null值问题
            script.append("# 注入参数数据\n");
            try {
                ObjectMapper mapper = new ObjectMapper();
                // 配置ObjectMapper处理null值
                String paramsJson = mapper.writeValueAsString(params);
                // 将Java的null转换为Python的None
                paramsJson = paramsJson.replace(":null", ":None")
                                     .replace("[null", "[None")
                                     .replace(",null", ",None")
                                     .replace("null,", "None,")
                                     .replace("null]", "None]")
                                     .replace("null}", "None}");

                script.append("input_data = ").append(paramsJson).append("\n");
                script.append("params = input_data\n\n");
            } catch (Exception e) {
                log.warn("序列化参数失败，使用空字典: {}", e.getMessage());
                script.append("input_data = {}\n");
                script.append("params = {}\n\n");
            }
            
            // 添加错误处理包装
            script.append("# 错误处理包装\n");
            script.append("try:\n");
            
            // 添加用户脚本（缩进）
            script.append("    # 用户脚本开始\n");
            String[] lines = scriptContent.split("\n");
            for (String line : lines) {
                script.append("    ").append(line).append("\n");
            }
            script.append("    # 用户脚本结束\n\n");
            
            // 修复函数调用 - 传递params参数
            if (!scriptContent.contains("mongo_main()")) {
                script.append("    # 自动调用主函数\n");
                script.append("    if 'mongo_main' in globals() and callable(mongo_main):\n");
                script.append("        result = mongo_main(params)\n");  // 传递params参数
                script.append("        if result is not None:\n");
                script.append("            print('__RESULT_START__')\n");
                script.append("            print(json.dumps(result, ensure_ascii=False))\n");
                script.append("            print('__RESULT_END__')\n");
                script.append("    else:\n");
                script.append("        print('__ERROR__: mongo_main function not found')\n");
            }
            
            // 添加异常处理
            script.append("except Exception as e:\n");
            script.append("    print('__ERROR__: ' + str(e))\n");
            script.append("    traceback.print_exc()\n");
            script.append("    sys.exit(1)\n");
            
            return script.toString();
            
        } catch (Exception e) {
            log.error("构建Python脚本失败: {}", e.getMessage(), e);
            throw new BizException("5001", "构建Python脚本失败: " + e.getMessage());
        }
    }

    /**
     * 解析本地Python执行结果
     */
    private Object parseLocalPythonResult(String output) {
        try {
            // 查找结果标记
            int startIndex = output.indexOf("__RESULT_START__");
            int endIndex = output.indexOf("__RESULT_END__");
            
            if (startIndex != -1 && endIndex != -1) {
                String resultJson = output.substring(startIndex + "__RESULT_START__".length(), endIndex).trim();
                return new ObjectMapper().readValue(resultJson, Object.class);
            }
            
            // 如果没有找到结果标记，检查是否有错误
            if (output.contains("__ERROR__")) {
                throw new BizException("5001", output);
            }
            
            // 尝试解析整个输出为JSON
            if (output.startsWith("{") || output.startsWith("[")) {
                return new ObjectMapper().readValue(output, Object.class);
            }
            
            // 返回原始输出
            return output;
            
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            log.warn("解析Python执行结果失败，返回原始输出: {}", e.getMessage());
            return output;
        }
    }

    /**
     * 远程Python执行（保留原有逻辑）
     */
    private Object executePythonScriptRemote(String scriptContent, Map<String, Object> params) {
        // 保留原有的远程执行逻辑
        try {
            if (ObjUtilX.isEmpty(pythonExecuteUrl)) {
                throw new BizException("5001", "Python执行URL未配置");
            }
            
            JSONObject pyParams = new JSONObject();
            pyParams.put("code", scriptContent);
            pyParams.put("params", params);

            String ps = pyParams.toJSONString();
            String rst = HttpRequestUtil.sendPost(pythonExecuteUrl, ps, null, null, null);
            JSONObject exeRst = JSONObject.parseObject(rst);
            
            log.info("调用Python执行服务: {}, 参数: {}, 结果: {}", pythonExecuteUrl, pyParams, exeRst);
            
            if(exeRst.get("status").equals("error")){
                String errorMsg = exeRst.getString("error");
                throw new BizException("5001", "Python脚本执行失败: " + errorMsg);
            }
            
            return exeRst.get("result");
            
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            log.error("执行Python脚本失败: {}", e.getMessage(), e);
            throw new BizException("5001", "执行Python脚本失败: " + e.getMessage());
        }
    }

    /**
     * 检查Python环境
     */
    private boolean checkPythonEnvironment() {
        try {
            ProcessBuilder pb = new ProcessBuilder(pythonPath, "--version");
            Process process = pb.start();
            boolean finished = process.waitFor(5, TimeUnit.SECONDS);
            if (!finished) {
                process.destroyForcibly();
                log.error("Python版本检查超时");
                return false;
            }
            
            int exitCode = process.exitValue();
            if (exitCode == 0) {
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                    String version = reader.readLine();
                    log.info("Python环境检查成功: {}", version);
                    return true;
                }
            } else {
                log.error("Python版本检查失败，退出码: {}", exitCode);
                return false;
            }
        } catch (Exception e) {
            log.error("Python环境检查异常: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 根据退出码提供描述信息
     */
    private String getExitCodeDescription(int exitCode) {
        switch (exitCode) {
            case 1:
                return " (通用错误)";
            case 2:
                return " (命令行参数错误)";
            case 126:
                return " (命令无法执行)";
            case 127:
                return " (命令未找到)";
            case 128:
                return " (无效的退出参数)";
            case 9009:
                return " (可能是脚本语法错误或模块导入失败)";
            default:
                return " (未知错误码)";
        }
    }
}
