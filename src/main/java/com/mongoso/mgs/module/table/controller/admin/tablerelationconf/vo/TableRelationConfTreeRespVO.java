package com.mongoso.mgs.module.table.controller.admin.tablerelationconf.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;


/**
 * 表关系配置 RespVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TableRelationConfTreeRespVO extends TableRelationConfBaseVO {

    /** 子节点 */
    List<TableRelationConfTreeRespVO> children;

    // 构造函数
    public TableRelationConfTreeRespVO() {
        this.children = new ArrayList<>(); // 初始化子节点列表
    }

    // 添加子节点的方法
    public void addChild(TableRelationConfTreeRespVO child) {
        this.children.add(child);
    }
}
