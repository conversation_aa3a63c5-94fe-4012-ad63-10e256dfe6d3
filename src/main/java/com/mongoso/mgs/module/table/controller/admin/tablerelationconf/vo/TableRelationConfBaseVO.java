package com.mongoso.mgs.module.table.controller.admin.tablerelationconf.vo;

import lombok.*;

import jakarta.validation.constraints.*;
import java.io.Serializable;

  

/**
 * 表关系配置 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class TableRelationConfBaseVO implements Serializable {

    /** 主键 */
    private Long id;

    /** 查询id */
    private Long queryId;

    /** 父表 */
    private String parentTable;

    /** 子表 */
    private String childTable;

    /** 父表字段 */
    private String parentCol;

    /** 子表字段 */
    private String childCol;

}
