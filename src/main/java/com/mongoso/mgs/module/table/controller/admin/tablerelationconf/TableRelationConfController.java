package com.mongoso.mgs.module.table.controller.admin.tablerelationconf;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.table.controller.admin.tablerelationconf.vo.*;
import com.mongoso.mgs.module.table.service.tablerelationconf.TableRelationConfService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.mongoso.mgs.framework.common.domain.ResultX.success;

/**
 * 表关系配置 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/table")
@Validated
public class TableRelationConfController {

    @Resource
    private TableRelationConfService relationConfService;

    /**
     * 添加或编辑表关系配置
     * @param reqVO
     * @return
     */
    @OperateLog("表关系配置添加或编辑")
    @PostMapping("/tableRelationConfAdit")
    @PreAuthorize("@ss.hasPermission('tableRelationConf:adit')")
    public ResultX<Long> tableRelationConfAdit(@Valid @RequestBody TableRelationConfAditReqVO reqVO) {
        return success(reqVO.getId() == null
                            ? relationConfService.tableRelationConfAdd(reqVO)
                            : relationConfService.tableRelationConfEdit(reqVO));
    }

    /**
     * 删除表关系配置
     * @param reqVO
     * @return
     */
    @OperateLog("表关系配置删除")
    @PostMapping("/tableRelationConfDel")
    @PreAuthorize("@ss.hasPermission('tableRelationConf:delete')")
    public ResultX<Boolean> tableRelationConfDel(@Valid @RequestBody TableRelationConfPrimaryReqVO reqVO) {
        relationConfService.tableRelationConfDel(reqVO.getId());
        return success(true);
    }

    /**
     * 表关系配置详情
     * @param reqVO
     * @return
     */
    @OperateLog("表关系配置详情")
    @PostMapping("/tableRelationConfDetail")
    @PreAuthorize("@ss.hasPermission('tableRelationConf:query')")
    public ResultX<TableRelationConfRespVO> tableRelationConfDetail(@Valid @RequestBody TableRelationConfPrimaryReqVO reqVO) {
        return success(relationConfService.tableRelationConfDetail(reqVO.getId()));
    }

    /**
     * 表关系配置列表
     * @param reqVO
     * @return
     */
    @OperateLog("表关系配置列表")
    @PostMapping("/tableRelationConfList")
    @PreAuthorize("@ss.hasPermission('tableRelationConf:query')")
    public ResultX<List<TableRelationConfRespVO>> tableRelationConfList(@Valid @RequestBody TableRelationConfQueryReqVO reqVO) {
        return success(relationConfService.tableRelationConfList(reqVO));
    }

    /**
     * 表关系配置分页
     * @param reqVO
     * @return
     */
    @OperateLog("表关系配置分页")
    @PostMapping("/tableRelationConfPage")
    @PreAuthorize("@ss.hasPermission('tableRelationConf:query')")
    public ResultX<PageResult<TableRelationConfRespVO>> tableRelationConfPage(@Valid @RequestBody TableRelationConfPageReqVO reqVO) {
        return success(relationConfService.tableRelationConfPage(reqVO));
    }

    /**
     * 表关系配置分页
     * @param reqVO
     * @return
     */
    @OperateLog("表关系配置分页")
    @PostMapping("/tableRelationConfTree")
    @PreAuthorize("@ss.hasPermission('tableRelationConf:query')")
    public ResultX<List<TableRelationConfTreeRespVO>> tableRelationConfTree(@Valid @RequestBody TableRelationConfQueryTreeReqVO reqVO) {
        return success(relationConfService.getTableRelationsByQueryId(reqVO.getQueryId()));
    }

}
