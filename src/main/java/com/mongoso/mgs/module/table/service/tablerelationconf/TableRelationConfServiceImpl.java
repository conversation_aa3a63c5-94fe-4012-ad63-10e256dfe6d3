package com.mongoso.mgs.module.table.service.tablerelationconf;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.common.util.ObjUtilX;
import com.mongoso.mgs.module.table.controller.admin.tablerelationconf.vo.*;
import com.mongoso.mgs.module.table.dal.db.tablerelationconf.TableRelationConfDO;
import com.mongoso.mgs.module.table.dal.mysql.tablerelationconf.TableRelationConfMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
// import static com.mongoso.mgs.module.table.enums.ErrorCodeConstants.*;


/**
 * 表关系配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TableRelationConfServiceImpl implements TableRelationConfService {

    @Resource
    private TableRelationConfMapper relationConfMapper;

    @Override
    public Long tableRelationConfAdd(TableRelationConfAditReqVO reqVO) {
        // 插入
        TableRelationConfDO relationConf = BeanUtilX.copy(reqVO, TableRelationConfDO::new);
        relationConfMapper.insert(relationConf);
        // 返回
        return relationConf.getId();
    }

    @Override
    public Long tableRelationConfEdit(TableRelationConfAditReqVO reqVO) {
        // 校验存在
        this.tableRelationConfValidateExists(reqVO.getId());
        // 更新
        TableRelationConfDO relationConf = BeanUtilX.copy(reqVO, TableRelationConfDO::new);
        relationConfMapper.updateById(relationConf);
        // 返回
        return relationConf.getId();
    }

    /**************** 不拆分节点 *************************/
    public List<TableRelationConfTreeRespVO> getTableRelationsByQueryId(Long queryId) {
        // 获取根节点和关系
        List<TableRelationConfDO> rootTable = relationConfMapper.getRootTable(queryId);
        if(ObjUtilX.isEmpty(rootTable)){
            return new ArrayList<>();
        }
        List<TableRelationConfTreeRespVO> rootRst = new ArrayList<>();
        // 获取根节点和关系
        List<TableRelationConfTreeRespVO> rootNodes = relationConfMapper.getRootTableAndRelation(queryId);

        for (TableRelationConfDO tempRoot : rootTable) {
            TableRelationConfTreeRespVO root = BeanUtilX.copy(tempRoot, TableRelationConfTreeRespVO::new);
            root.setChildTable(root.getParentTable());
            for (TableRelationConfTreeRespVO rootNode : rootNodes) {
                if(root.getChildTable().equals(rootNode.getParentTable())){
                    buildTree(queryId, rootNode);
                    root.addChild(rootNode);
                }
            }
            rootRst.add(root);
        }
        return rootRst; // 返回根节点列表
    }

    private void buildTree(Long queryId, TableRelationConfTreeRespVO parentNode) {
        // 获取当前节点的子节点
        List<TableRelationConfDO> childrens = relationConfMapper.selectListByQueryIdAndParentTable(queryId, parentNode.getChildTable());
        //List<TableRelationConfDO> childrens = childTableMap.get(parentNode.getChildTable());

        for (TableRelationConfDO child : childrens) {
            TableRelationConfTreeRespVO childNode = new TableRelationConfTreeRespVO();
            childNode.setId(child.getId());
            childNode.setParentTable(child.getParentTable());
            childNode.setChildTable(child.getChildTable());
            childNode.setParentCol(child.getParentCol());
            childNode.setChildCol(child.getChildCol());
            childNode.setQueryId(child.getQueryId());

            // 递归构建子节点的树结构
            buildTree(queryId, childNode);

            // 将子节点添加到父节点的子节点列表中
            parentNode.addChild(childNode);
        }
    }

    /**************** 拆分节点 *************************/
    //public List<TableRelationConfTreeRespVO> getTableRelationsByQueryId(Long queryId) {
    //    // 获取根节点和关系
    //    List<TableRelationConfTreeRespVO> rootNodes = relationConfMapper.getRootTableAndRelation(queryId);
    //    if (rootNodes.isEmpty()) {
    //        return new ArrayList<>();
    //    }
    //
    //    // 构建树形结构
    //    for (TableRelationConfTreeRespVO rootNode : rootNodes) {
    //        buildTree(queryId, rootNode);
    //    }
    //
    //    // 返回根节点列表
    //    return rootNodes;
    //}
    //
    //private void buildTree(Long queryId, TableRelationConfTreeRespVO parentNode) {
    //    // 获取当前父节点对应的所有子关系
    //    List<TableRelationConfDO> childrenRelations = relationConfMapper.selectListByQueryIdAndParentTable(queryId, parentNode.getChildTable());
    //
    //    for (TableRelationConfDO relation : childrenRelations) {
    //        // 构建子节点
    //        TableRelationConfTreeRespVO childNode = new TableRelationConfTreeRespVO();
    //
    //        // 设定子节点属性
    //        childNode.setId(relation.getId());
    //        childNode.setParentTable(relation.getParentTable());
    //        childNode.setChildTable(relation.getChildTable());
    //        childNode.setParentCol(relation.getParentCol());
    //        childNode.setChildCol(relation.getChildCol());
    //        childNode.setQueryId(relation.getQueryId());
    //
    //        // 将子节点添加到父节点的子节点列表中
    //        parentNode.addChild(childNode);
    //
    //        // 递归构建子节点的树结构
    //        buildTree(queryId, childNode);
    //    }
    //}

    @Override
    public void tableRelationConfDel(Long id) {
        // 校验存在
        this.tableRelationConfValidateExists(id);
        // 删除
        relationConfMapper.deleteById(id);
    }

    private TableRelationConfDO tableRelationConfValidateExists(Long id) {
        TableRelationConfDO relationConf = relationConfMapper.selectById(id);
        if (relationConf == null) {
            // throw exception(RELATION_CONF_NOT_EXISTS);
            throw new BizException("5001", "表关系配置不存在");
        }
        return relationConf;
    }

    @Override
    public TableRelationConfRespVO tableRelationConfDetail(Long id) {
        TableRelationConfDO data = relationConfMapper.selectById(id);
        return BeanUtilX.copy(data, TableRelationConfRespVO::new);
    }

    @Override
    public List<TableRelationConfRespVO> tableRelationConfList(TableRelationConfQueryReqVO reqVO) {
        List<TableRelationConfDO> data = relationConfMapper.selectList(reqVO);
        return BeanUtilX.copy(data, TableRelationConfRespVO::new);
    }

    @Override
    public PageResult<TableRelationConfRespVO> tableRelationConfPage(TableRelationConfPageReqVO reqVO) {
        PageResult<TableRelationConfDO> data = relationConfMapper.selectPage(reqVO);
        return BeanUtilX.copy(data, TableRelationConfRespVO::new);
    }

}
