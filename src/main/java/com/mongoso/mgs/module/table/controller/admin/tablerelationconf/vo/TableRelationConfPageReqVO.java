package com.mongoso.mgs.module.table.controller.admin.tablerelationconf.vo;

import lombok.*;

import com.mongoso.mgs.framework.common.domain.PageParam;


    


/**
 * 表关系配置 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TableRelationConfPageReqVO extends PageParam {

    /** 查询id */
    private Long queryId;

    /** 父表 */
    private String parentTable;

    /** 子表 */
    private String childTable;

    /** 父表字段 */
    private String parentCol;

    /** 子表字段 */
    private String childCol;

}
