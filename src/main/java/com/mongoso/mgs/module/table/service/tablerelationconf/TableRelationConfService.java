package com.mongoso.mgs.module.table.service.tablerelationconf;

import java.util.*;
import jakarta.validation.*;
import com.mongoso.mgs.module.table.controller.admin.tablerelationconf.vo.*;
import com.mongoso.mgs.module.table.dal.db.tablerelationconf.TableRelationConfDO;
import com.mongoso.mgs.framework.common.domain.PageResult;

/**
 * 表关系配置 Service 接口
 *
 * <AUTHOR>
 */
public interface TableRelationConfService {

    /**
     * 创建表关系配置
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long tableRelationConfAdd(@Valid TableRelationConfAditReqVO reqVO);

    /**
     * 更新表关系配置
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long tableRelationConfEdit(@Valid TableRelationConfAditReqVO reqVO);

    /**
     * 删除表关系配置
     *
     * @param id 编号
     */
    void tableRelationConfDel(Long id);

    /**
     * 获得表关系配置信息
     *
     * @param id 编号
     * @return 表关系配置信息
     */
    TableRelationConfRespVO tableRelationConfDetail(Long id);

    /**
     * 获得表关系配置列表
     *
     * @param reqVO 查询条件
     * @return 表关系配置列表
     */
    List<TableRelationConfRespVO> tableRelationConfList(@Valid TableRelationConfQueryReqVO reqVO);

    /**
     * 获得表关系配置分页
     *
     * @param reqVO 查询条件
     * @return 表关系配置分页
     */
    PageResult<TableRelationConfRespVO> tableRelationConfPage(@Valid TableRelationConfPageReqVO reqVO);

    /**
     * 返回树形结构
     *
     * @param queryId
     * @return
     */
    List<TableRelationConfTreeRespVO> getTableRelationsByQueryId(Long queryId);
}
