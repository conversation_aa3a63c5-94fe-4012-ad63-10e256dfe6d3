package com.mongoso.mgs.module.table.dal.db.tablerelationconf;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 表关系配置 DO
 *
 * <AUTHOR>
 */
@TableName(value = "lowcode.sys_table_relation_conf", autoResultMap = true)
//@KeySequence("sys_table_relation_conf_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TableRelationConfDO {

    /** 主键 */
        @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /** 查询id */
    private Long queryId;

    /** 父表 */
    private String parentTable;

    /** 子表 */
    private String childTable;

    /** 父表字段 */
    private String parentCol;

    /** 子表字段 */
    private String childCol;


}
