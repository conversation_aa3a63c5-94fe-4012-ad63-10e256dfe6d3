package com.mongoso.mgs.module.table.dal.mysql.tablerelationconf;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.table.controller.admin.tablerelationconf.vo.TableRelationConfPageReqVO;
import com.mongoso.mgs.module.table.controller.admin.tablerelationconf.vo.TableRelationConfQueryReqVO;
import com.mongoso.mgs.module.table.controller.admin.tablerelationconf.vo.TableRelationConfTreeRespVO;
import com.mongoso.mgs.module.table.dal.db.tablerelationconf.TableRelationConfDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 表关系配置 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TableRelationConfMapper extends BaseMapperX<TableRelationConfDO> {

    default PageResult<TableRelationConfDO> selectPage(TableRelationConfPageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<TableRelationConfDO>lambdaQueryX()
                .eqIfPresent(TableRelationConfDO::getQueryId, reqVO.getQueryId())
                .eqIfPresent(TableRelationConfDO::getParentTable, reqVO.getParentTable())
                .eqIfPresent(TableRelationConfDO::getChildTable, reqVO.getChildTable())
                .eqIfPresent(TableRelationConfDO::getParentCol, reqVO.getParentCol())
                .eqIfPresent(TableRelationConfDO::getChildCol, reqVO.getChildCol())
                .orderByDesc(TableRelationConfDO::getId));
    }




    default List<TableRelationConfDO> selectList(TableRelationConfQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<TableRelationConfDO>lambdaQueryX()
                .eqIfPresent(TableRelationConfDO::getQueryId, reqVO.getQueryId())
                .eqIfPresent(TableRelationConfDO::getParentTable, reqVO.getParentTable())
                .eqIfPresent(TableRelationConfDO::getChildTable, reqVO.getChildTable())
                .eqIfPresent(TableRelationConfDO::getParentCol, reqVO.getParentCol())
                .eqIfPresent(TableRelationConfDO::getChildCol, reqVO.getChildCol())
                    .orderByDesc(TableRelationConfDO::getId));
    }
    default List<TableRelationConfDO> selectListByQueryId(Long queryId) {
        return selectList(LambdaQueryWrapperX.<TableRelationConfDO>lambdaQueryX()
                .eq(TableRelationConfDO::getQueryId, queryId));
    }

    /**
     * 获取根节点表名，去重
     * @param queryId
     * @return
     */
    List<TableRelationConfDO> getRootTable(Long queryId);

    /**
     * 获取根节点表和关系
     * @param queryId
     * @return
     */
    List<TableRelationConfTreeRespVO> getRootTableAndRelation(Long queryId);

    default List<TableRelationConfDO> selectListByQueryIdAndParentTable(Long queryId, String parentTable){
        return selectList(LambdaQueryWrapperX.<TableRelationConfDO>lambdaQueryX()
                .eq(TableRelationConfDO::getQueryId, queryId)
                .eq(TableRelationConfDO::getParentTable, parentTable));
    }
}