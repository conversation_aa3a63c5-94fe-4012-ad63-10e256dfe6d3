package com.mongoso.mgs.module.project.dal.db.projectmodelfield;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;
import com.mongoso.mgs.framework.typehandler.JsonbTypeHandler;
import lombok.*;

import java.util.List;

/**
 * 项目图形建模初始化字段 DO
 *
 * <AUTHOR>
 */
@TableName(value = "lowcode.sys_project_model_field", autoResultMap = true)
//@KeySequence("sys_project_model_field_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectModelFieldDO extends OperateDO {

    /** 主键id */
        @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /** 模型字段表中文名 */
    private String fieldName;

    /** 模型字段表实名 */
    private String fieldCode;

    /** 数据类型 */
    private String fieldType;

    /** 长度 */
    private Integer leng;

    /** 精度 */
    private Integer fieldPrecision;


    /** 是否为空 */
    private Integer isNullable;

    /** 是否为主键 */
    private Integer isPrimaryKey;

    /** 默认值 */
    private String defaultVal;

    /** 排序 */
    private Integer sort;

    /** 属性类型，0：系统字段，1：常用字段 */
    private Integer propType;

    /** 备注描述 */
    private String remark;

    /** 业务类型 */
    private Integer bizType;

    /** 字段编码 */
    private String fieldNo;

}
