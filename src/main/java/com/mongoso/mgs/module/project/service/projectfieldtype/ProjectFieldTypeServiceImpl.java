package com.mongoso.mgs.module.project.service.projectfieldtype;

import com.mongoso.mgs.common.util.DatabaseUtil;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.common.util.ObjUtilX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.enums.DBTypeEnum;
import com.mongoso.mgs.module.model.dal.db.modeltable.ModelTableDO;
import com.mongoso.mgs.module.model.dal.mysql.modeltable.ModelTableMapper;
import com.mongoso.mgs.module.model.service.modeltable.ModelTableService;
import com.mongoso.mgs.module.project.controller.admin.projectfieldtype.vo.ProjectFieldTypeAditReqVO;
import com.mongoso.mgs.module.project.controller.admin.projectfieldtype.vo.ProjectFieldTypePageReqVO;
import com.mongoso.mgs.module.project.controller.admin.projectfieldtype.vo.ProjectFieldTypeQueryReqVO;
import com.mongoso.mgs.module.project.controller.admin.projectfieldtype.vo.ProjectFieldTypeRespVO;
import com.mongoso.mgs.module.project.dal.db.projectfieldtype.ProjectFieldTypeDO;
import com.mongoso.mgs.module.project.dal.mysql.projectfieldtype.ProjectFieldTypeMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;
// import static com.mongoso.mgs.module.project.enums.ErrorCodeConstants.*;


/**
 * 项目图形建模字段类型 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ProjectFieldTypeServiceImpl implements ProjectFieldTypeService {

    @Resource
    private ProjectFieldTypeMapper fieldTypeMapper;
    @Resource
    private ModelTableMapper tableMapper;
    @Resource
    private DatabaseUtil dbUtil;
    @Resource
    private ModelTableService tableService;

    @Override
    public Long projectFieldTypeAdd(ProjectFieldTypeAditReqVO reqVO) {
        // 插入
        ProjectFieldTypeDO fieldType = BeanUtilX.copy(reqVO, ProjectFieldTypeDO::new);
        fieldType.setDbType(tableService.modelTableDbType(reqVO.getProjectId()));
        fieldTypeMapper.insert(fieldType);
        // 返回
        return fieldType.getId();
    }

    @Override
    public Long projectFieldTypeEdit(ProjectFieldTypeAditReqVO reqVO) {
        // 校验存在
        this.projectFieldTypeValidateExists(reqVO.getId());
        // 更新
        ProjectFieldTypeDO fieldType = BeanUtilX.copy(reqVO, ProjectFieldTypeDO::new);
        fieldTypeMapper.updateById(fieldType);
        // 返回
        return fieldType.getId();
    }

    @Override
    public void projectFieldTypeDel(Long id) {
        // 校验存在
        this.projectFieldTypeValidateExists(id);
        // 删除
        fieldTypeMapper.deleteById(id);
    }

    private ProjectFieldTypeDO projectFieldTypeValidateExists(Long id) {
        ProjectFieldTypeDO fieldType = fieldTypeMapper.selectById(id);
        if (fieldType == null) {
            // throw exception(FIELD_TYPE_NOT_EXISTS);
            throw new BizException("5001", "项目图形建模字段类型不存在");
        }
        return fieldType;
    }

    @Override
    public ProjectFieldTypeRespVO projectFieldTypeDetail(Long id) {
        ProjectFieldTypeDO data = fieldTypeMapper.selectById(id);
        return BeanUtilX.copy(data, ProjectFieldTypeRespVO::new);
    }

    @Override
    public List<ProjectFieldTypeRespVO> projectFieldTypeList(ProjectFieldTypeQueryReqVO reqVO) {
        List<ProjectFieldTypeDO> data = fieldTypeMapper.selectList(reqVO);
        return BeanUtilX.copy(data, ProjectFieldTypeRespVO::new);
    }

    @Override
    public PageResult<ProjectFieldTypeRespVO> projectFieldTypePage(ProjectFieldTypePageReqVO reqVO) {
        PageResult<ProjectFieldTypeDO> data = fieldTypeMapper.selectPage(reqVO);
        return BeanUtilX.copy(data, ProjectFieldTypeRespVO::new);
    }

    @Override
    public ProjectFieldTypeRespVO projectFieldTypeInfo(Long projectId) {
//        ModelTableDO project = tableMapper.selectById(projectId);
//        if(ObjUtilX.isEmpty(project)){
//            throw new BizException("5001", "项目不存在");
//        }
        // 如果查不到数据，就去 项目为0 的初始化数据
        ProjectFieldTypeDO fieldTypeDO = fieldTypeMapper.selectOne(LambdaQueryWrapperX.<ProjectFieldTypeDO>lambdaQueryX()
                .eq(ProjectFieldTypeDO::getDbType, DBTypeEnum.PG)
                .last("limit 1")
        );
//        if(ObjUtilX.isEmpty(fieldTypeDO)){
//            // 如果查不到就查默认初始化的数据
//            String dbType = dbUtil.getDbTypeDynamic(project.getDataSourceConfigId());
//            fieldTypeDO = fieldTypeMapper.selectOne(LambdaQueryWrapperX.<ProjectFieldTypeDO>lambdaQueryX()
////                    .eq(ProjectFieldTypeDO::getProjectId, 0)
//                    .eq(ProjectFieldTypeDO::getDbType, dbType.toLowerCase())
//            );
//            fieldTypeDO.setId(null);
//        }
        return BeanUtilX.copy(fieldTypeDO, ProjectFieldTypeRespVO::new);
    }

}
