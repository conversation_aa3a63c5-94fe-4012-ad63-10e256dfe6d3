package com.mongoso.mgs.module.project.service.projectapiparams;

import java.util.*;
import jakarta.validation.*;
import com.mongoso.mgs.module.project.controller.admin.projectapiparams.vo.*;
import com.mongoso.mgs.module.project.dal.db.projectapiparams.ProjectApiParamsDO;
import com.mongoso.mgs.framework.common.domain.PageResult;

/**
 * 公共参数 Service 接口
 *
 * <AUTHOR>
 */
public interface ProjectApiParamsService {

    /**
     * 创建公共参数
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long projectApiParamsAdd(@Valid ProjectApiParamsAditReqVO reqVO);

    /**
     * 更新公共参数
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long projectApiParamsEdit(@Valid ProjectApiParamsAditReqVO reqVO);

    /**
     * 删除公共参数
     *
     * @param id 编号
     */
    void projectApiParamsDel(Long id);

    /**
     * 获得公共参数信息
     *
     * @param id 编号
     * @return 公共参数信息
     */
    ProjectApiParamsRespVO projectApiParamsDetail(Long id);

    ProjectApiParamsRespVO projectApiParamsDetailByProjectId(Long projectId);

    /**
     * 获得公共参数列表
     *
     * @param reqVO 查询条件
     * @return 公共参数列表
     */
    List<ProjectApiParamsRespVO> projectApiParamsList(@Valid ProjectApiParamsQueryReqVO reqVO);

    /**
     * 获得公共参数分页
     *
     * @param reqVO 查询条件
     * @return 公共参数分页
     */
    PageResult<ProjectApiParamsRespVO> projectApiParamsPage(@Valid ProjectApiParamsPageReqVO reqVO);

}
