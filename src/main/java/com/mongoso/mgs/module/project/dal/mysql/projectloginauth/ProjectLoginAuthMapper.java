package com.mongoso.mgs.module.project.dal.mysql.projectloginauth;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.project.controller.admin.projectloginauth.vo.ProjectLoginAuthPageReqVO;
import com.mongoso.mgs.module.project.controller.admin.projectloginauth.vo.ProjectLoginAuthQueryReqVO;
import com.mongoso.mgs.module.project.dal.db.projectloginauth.ProjectLoginAuthDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 授权配置 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ProjectLoginAuthMapper extends BaseMapperX<ProjectLoginAuthDO> {

    default PageResult<ProjectLoginAuthDO> selectPage(ProjectLoginAuthPageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<ProjectLoginAuthDO>lambdaQueryX()
                .eqIfPresent(ProjectLoginAuthDO::getProjectId, reqVO.getProjectId())
                .eqIfPresent(ProjectLoginAuthDO::getLoginApiCode, reqVO.getLoginApiCode())
                .likeIfPresent(ProjectLoginAuthDO::getLoginFaildCode, reqVO.getLoginFaildCode())
                .eqIfPresent(ProjectLoginAuthDO::getLoginAuthKey, reqVO.getLoginAuthKey())
                .eqIfPresent(ProjectLoginAuthDO::getLoginAuthVal, reqVO.getLoginAuthVal())
                .betweenIfPresent(ProjectLoginAuthDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .orderByDesc(ProjectLoginAuthDO::getCreatedDt));
    }




    default List<ProjectLoginAuthDO> selectList(ProjectLoginAuthQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<ProjectLoginAuthDO>lambdaQueryX()
                .eqIfPresent(ProjectLoginAuthDO::getProjectId, reqVO.getProjectId())
                .eqIfPresent(ProjectLoginAuthDO::getLoginApiCode, reqVO.getLoginApiCode())
                .likeIfPresent(ProjectLoginAuthDO::getLoginFaildCode, reqVO.getLoginFaildCode())
                .eqIfPresent(ProjectLoginAuthDO::getLoginAuthKey, reqVO.getLoginAuthKey())
                .eqIfPresent(ProjectLoginAuthDO::getLoginAuthVal, reqVO.getLoginAuthVal())
                .eqIfPresent(ProjectLoginAuthDO::getCreatedBy, reqVO.getCreatedBy())
                .betweenIfPresent(ProjectLoginAuthDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .eqIfPresent(ProjectLoginAuthDO::getUpdatedBy, reqVO.getUpdatedBy())
                .betweenIfPresent(ProjectLoginAuthDO::getUpdatedDt, reqVO.getStartUpdatedDt(), reqVO.getEndUpdatedDt())
                    .orderByDesc(ProjectLoginAuthDO::getCreatedDt));
    }


}