package com.mongoso.mgs.module.project.dal.db.projectloginauth;

import lombok.*;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;

/**
 * 授权配置 DO
 *
 * <AUTHOR>
 */
@TableName(value = "lowcode.sys_project_login_auth", autoResultMap = true)
//@KeySequence("sys_project_login_auth_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectLoginAuthDO extends OperateDO {

    /** 接口登录信息ID */
        @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /** 项目ID */
    private Long projectId;

    //登录接口 API文档的ID
    private Long itemId;

    /** 登录接口任务ID */
    private Long loginApiCode;

    @TableField(exist = false)
    private String loginApiName;

    /** 登录时效编码 */
    private String loginFaildCode;
    private String loginFaildKey;

    /** 登录认证字段名 */
    private String loginAuthKey;

    /** 登录认证字段值 */
    private String loginAuthVal;


}
