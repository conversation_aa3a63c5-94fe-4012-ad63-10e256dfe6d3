package com.mongoso.mgs.module.project.dal.mysql.projectapiparams;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.project.controller.admin.projectapiparams.vo.ProjectApiParamsPageReqVO;
import com.mongoso.mgs.module.project.controller.admin.projectapiparams.vo.ProjectApiParamsQueryReqVO;
import com.mongoso.mgs.module.project.dal.db.projectapiparams.ProjectApiParamsDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 公共参数 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ProjectApiParamsMapper extends BaseMapperX<ProjectApiParamsDO> {

    default PageResult<ProjectApiParamsDO> selectPage(ProjectApiParamsPageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<ProjectApiParamsDO>lambdaQueryX()
                .eqIfPresent(ProjectApiParamsDO::getProjectId, reqVO.getProjectId())
                .eqIfPresent(ProjectApiParamsDO::getHeaderParams, reqVO.getHeaderParams())
                .eqIfPresent(ProjectApiParamsDO::getCommonParams, reqVO.getCommonParams())
                .orderByDesc(ProjectApiParamsDO::getId));
    }




    default List<ProjectApiParamsDO> selectList(ProjectApiParamsQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<ProjectApiParamsDO>lambdaQueryX()
                .eqIfPresent(ProjectApiParamsDO::getProjectId, reqVO.getProjectId())
                .eqIfPresent(ProjectApiParamsDO::getHeaderParams, reqVO.getHeaderParams())
                .eqIfPresent(ProjectApiParamsDO::getCommonParams, reqVO.getCommonParams())
                    .orderByDesc(ProjectApiParamsDO::getId));
    }

    default ProjectApiParamsDO selectOneByProjectId(Long projectId) {
        return selectOne(LambdaQueryWrapperX.<ProjectApiParamsDO>lambdaQueryX()
                .eq(ProjectApiParamsDO::getProjectId, projectId).last("limit 1"));
    }


}