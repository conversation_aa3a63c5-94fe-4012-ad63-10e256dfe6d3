package com.mongoso.mgs.module.project.controller.admin.projectapiparams.vo;

import lombok.*;

import com.mongoso.mgs.framework.common.domain.PageParam;


    
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONObject;

import java.util.List;


/**
 * 公共参数 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ProjectApiParamsPageReqVO extends PageParam {

    /** 项目Id */
    private Long projectId;

    /** heder参数 */
    private List<JSONObject> headerParams;

    /** 普通参数 */
    private List<JSONObject> commonParams;

}
