package com.mongoso.mgs.module.project.service.projectloginauth;

import java.util.*;
import jakarta.validation.*;
import com.mongoso.mgs.module.project.controller.admin.projectloginauth.vo.*;
import com.mongoso.mgs.module.project.dal.db.projectloginauth.ProjectLoginAuthDO;
import com.mongoso.mgs.framework.common.domain.PageResult;

/**
 * 授权配置 Service 接口
 *
 * <AUTHOR>
 */
public interface ProjectLoginAuthService {

    /**
     * 创建授权配置
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long projectLoginAuthAdd(@Valid ProjectLoginAuthAditReqVO reqVO);

    /**
     * 更新授权配置
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long projectLoginAuthEdit(@Valid ProjectLoginAuthAditReqVO reqVO);

    /**
     * 删除授权配置
     *
     * @param id 编号
     */
    void projectLoginAuthDel(Long id);

    /**
     * 获得授权配置信息
     *
     * @param id 编号
     * @return 授权配置信息
     */
    ProjectLoginAuthRespVO projectLoginAuthDetail(Long id);

    /**
     * 获得授权配置列表
     *
     * @param reqVO 查询条件
     * @return 授权配置列表
     */
    List<ProjectLoginAuthRespVO> projectLoginAuthList(@Valid ProjectLoginAuthQueryReqVO reqVO);

    /**
     * 获得授权配置分页
     *
     * @param reqVO 查询条件
     * @return 授权配置分页
     */
    PageResult<ProjectLoginAuthRespVO> projectLoginAuthPage(@Valid ProjectLoginAuthPageReqVO reqVO);

}
