package com.mongoso.mgs.module.project.controller.admin.projectmodelfield.vo;

import lombok.*;

import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.util.List;


import com.alibaba.fastjson.JSONObject;

/**
 * 项目图形建模初始化字段 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class ProjectModelFieldBaseVO implements Serializable {

    /** 主键id */
    private Long id;

    /** 模型字段表中文名 */
    @NotEmpty(message = "模型字段表中文名不能为空")
    private String fieldName;

    /** 模型字段表实名 */
    @NotEmpty(message = "模型字段表实名不能为空")
    private String fieldCode;

    /** 字段类型 */
    @NotEmpty(message = "数据类型不能为空")
    private String fieldType;

    /** 长度 */
    private Integer leng;

    /** 精度 */
    private Integer fieldPrecision;


    /** 是否为空 */
    @NotNull(message = "是否必填不能为空")
    private Integer isNullable;

    /** 是否为主键 */
    @NotNull(message = "是否为主键不能为空")
    private Integer isPrimaryKey;

    /** 默认值 */
    private String defaultVal;

    /** 排序 */
    @NotNull(message = "排序不能为空")
    private Integer sort;

    /** 属性类型，0：系统，1：用户 */
    @NotNull(message = "属性类型，0：系统字段，1：常用字段不能为空")
    private Integer propType;

    /** 备注描述 */
    private String remark;

    /** 业务类型 */
    private Integer bizType;

    /** 字段编码 */
    private String fieldNo;

}
