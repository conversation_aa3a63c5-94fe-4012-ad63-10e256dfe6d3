package com.mongoso.mgs.module.project.controller.admin.projectapienv.vo;

import lombok.*;

import jakarta.validation.constraints.*;
import java.io.Serializable;

  

/**
 * 环境配置 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class ProjectApiEnvBaseVO implements Serializable {

    /** 环境ID */
    private Long id;

    /** 项目Id */
    @NotEmpty(message = "项目Id不能为空")
    private Long projectId;

    /** 环境名称 */
    private String envName;

    /** 环境地址 */
    private String envUrl;

    /** 是否默认(0-否 1-是) */
    @NotNull(message = "是否默认(0-否 1-是)不能为空")
    private Boolean isDefault;

}
