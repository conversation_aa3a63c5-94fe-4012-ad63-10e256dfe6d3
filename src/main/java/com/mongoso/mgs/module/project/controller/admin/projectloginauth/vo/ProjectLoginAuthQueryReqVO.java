package com.mongoso.mgs.module.project.controller.admin.projectloginauth.vo;

import lombok.*;

    
 import org.springframework.format.annotation.DateTimeFormat;
 
import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
  


/**
 * 授权配置 QueryReqVO
 *
 * <AUTHOR>
 */
@Data
public class ProjectLoginAuthQueryReqVO {

    /** 项目ID */
    private Long projectId;

    /** 登录接口任务ID */
    private Long loginApiCode;

    /** 登录时效编码 */
    private String loginFaildCode;

    /** 登录认证字段名 */
    private String loginAuthKey;

    /** 登录认证字段值 */
    private String loginAuthVal;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startUpdatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endUpdatedDt;

}
