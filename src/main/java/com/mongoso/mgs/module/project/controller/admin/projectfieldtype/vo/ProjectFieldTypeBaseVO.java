package com.mongoso.mgs.module.project.controller.admin.projectfieldtype.vo;

import lombok.*;

import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.util.List;


import com.alibaba.fastjson.JSONObject;

/**
 * 项目图形建模字段类型 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class ProjectFieldTypeBaseVO implements Serializable {

    /** 主键id */
    private Long id;

    /** 项目id */
    @NotNull(message = "项目id不能为空")
    private Long projectId;

    /** 字段类型列表 */
    private List<JSONObject> fieldTypeList;

    /** 数据库类型 */
    private String dbType;

}
