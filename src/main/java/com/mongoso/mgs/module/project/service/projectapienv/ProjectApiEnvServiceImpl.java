package com.mongoso.mgs.module.project.service.projectapienv;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.common.util.ObjUtilX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.model.dal.db.item.ItemDO;
import com.mongoso.mgs.module.model.dal.mysql.item.ItemMapper;
import com.mongoso.mgs.module.project.controller.admin.projectapienv.vo.*;
import com.mongoso.mgs.module.project.dal.db.projectapienv.ProjectApiEnvDO;
import com.mongoso.mgs.module.project.dal.db.projectloginauth.ProjectLoginAuthDO;
import com.mongoso.mgs.module.project.dal.mysql.projectapienv.ProjectApiEnvMapper;
import com.mongoso.mgs.module.project.dal.mysql.projectloginauth.ProjectLoginAuthMapper;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
// import static com.mongoso.mgs.module.project.enums.ErrorCodeConstants.*;


/**
 * 环境配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ProjectApiEnvServiceImpl implements ProjectApiEnvService {

    @Resource
    private ProjectApiEnvMapper apiEnvMapper;
    @Resource
    private ProjectLoginAuthMapper authMapper;
    @Autowired
    private ItemMapper itemMapper;

    @Override
    public Long projectApiEnvAdd(ProjectApiEnvAditReqVO reqVO) {
        // 插入
        ProjectApiEnvDO apiEnv = BeanUtilX.copy(reqVO, ProjectApiEnvDO::new);
        apiEnvMapper.insert(apiEnv);
        // 返回
        return apiEnv.getId();
    }

    @Override
    public Long projectApiEnvEdit(ProjectApiEnvAditReqVO reqVO) {
        // 校验存在
        this.projectApiEnvValidateExists(reqVO.getId());
        // 更新
        ProjectApiEnvDO apiEnv = BeanUtilX.copy(reqVO, ProjectApiEnvDO::new);
        apiEnvMapper.updateById(apiEnv);
        // 返回
        return apiEnv.getId();
    }

    @Override
    public void projectApiEnvDel(Long id) {
        // 校验存在
        this.projectApiEnvValidateExists(id);
        // 删除
        apiEnvMapper.deleteById(id);
    }

    private ProjectApiEnvDO projectApiEnvValidateExists(Long id) {
        ProjectApiEnvDO apiEnv = apiEnvMapper.selectById(id);
        if (apiEnv == null) {
            // throw exception(API_ENV_NOT_EXISTS);
            throw new BizException("5001", "环境配置不存在");
        }
        return apiEnv;
    }

    @Override
    public ProjectApiEnvRespVO projectApiEnvDetail(Long id) {
        ProjectApiEnvDO data = apiEnvMapper.selectById(id);
        return BeanUtilX.copy(data, ProjectApiEnvRespVO::new);
    }

    @Override
    public List<ProjectApiEnvRespVO> projectApiEnvList(ProjectApiEnvQueryReqVO reqVO) {
        List<ProjectApiEnvDO> data = apiEnvMapper.selectList(reqVO);
        return BeanUtilX.copy(data, ProjectApiEnvRespVO::new);
    }

    @Override
    public PageResult<ProjectApiEnvRespVO> projectApiEnvPage(ProjectApiEnvPageReqVO reqVO) {
        PageResult<ProjectApiEnvDO> data = apiEnvMapper.selectPage(reqVO);
        return BeanUtilX.copy(data, ProjectApiEnvRespVO::new);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long projectApiEnvSave(ProjectApiEnvAditListReqVO reqVO) {
        if(ObjUtilX.isEmpty(reqVO.getEnvironmentList())){
            throw new BizException("5001", "环境配置不能为空");
        }else if(reqVO.getEnvironmentList().size() > 10){
            throw new BizException("5001", "环境配置不能超过10个");
        }
        // 保存配置
        List<ProjectApiEnvDO> existingEnvironments = apiEnvMapper.selectList(new LambdaQueryWrapperX<ProjectApiEnvDO>()
                .eq(ProjectApiEnvDO::getProjectId, reqVO.getProjectId()));

        Set<Long> incomingIds = reqVO.getEnvironmentList().stream()
                .takeWhile(a -> Objects.nonNull(a.getId()))  // 取出非空 ID 的项，直到遇到第一个为空的
                .map(ProjectApiEnvAditReqVO::getId)      // 收集非空 ID
                .collect(Collectors.toSet());

        existingEnvironments.stream()
                .filter(env -> !incomingIds.contains(env.getId()))
                .forEach(env -> apiEnvMapper.deleteById(env.getId()));

        reqVO.getEnvironmentList().forEach(item -> {
            ProjectApiEnvDO apiEnv = BeanUtilX.copy(item, ProjectApiEnvDO::new);
            apiEnv.setProjectId(reqVO.getProjectId());
            if(Objects.isNull(item.getId())){
                //插入
                apiEnvMapper.insert(apiEnv);
            } else {
                //更新
                apiEnvMapper.updateById(apiEnv);
            }
        });
        if(ObjUtilX.isNotEmpty(reqVO.getAutoLoginInfo())){
            ProjectLoginAuthDO authDO = authMapper.selectOne(new LambdaQueryWrapperX<ProjectLoginAuthDO>()
                    .eq(ProjectLoginAuthDO::getProjectId, reqVO.getProjectId()));
            if(ObjUtilX.isNotEmpty(authDO)){
                authDO.setItemId(reqVO.getAutoLoginInfo().getItemId());
                authDO.setLoginApiCode(reqVO.getAutoLoginInfo().getLoginApiCode());
                authDO.setLoginFaildCode(reqVO.getAutoLoginInfo().getLoginFaildCode());
                authDO.setLoginFaildKey(reqVO.getAutoLoginInfo().getLoginFaildKey());
                authDO.setLoginAuthKey(reqVO.getAutoLoginInfo().getLoginAuthKey());
                authDO.setLoginAuthVal(reqVO.getAutoLoginInfo().getLoginAuthVal());
                authMapper.updateById(authDO);
            }else {
                ProjectLoginAuthDO authInsert = BeanUtilX.copy(reqVO.getAutoLoginInfo(), ProjectLoginAuthDO::new);
                authInsert.setProjectId(reqVO.getProjectId());
                authMapper.insert(authInsert);
            }
        }
        return 1L;
    }

    @Override
    public ProjectApiEnvDetailRespVO projectApiEnvDetailList(Long projectId) {
        ProjectApiEnvDetailRespVO result = new ProjectApiEnvDetailRespVO();
        // 域名配置
        List<ProjectApiEnvDO> envList = apiEnvMapper.selectList(new LambdaQueryWrapperX<ProjectApiEnvDO>()
                .eq(ProjectApiEnvDO::getProjectId, projectId));
        result.setEnvironmentList(envList);
        // 登录配置
        ProjectLoginAuthDO authDO = authMapper.selectOne(new LambdaQueryWrapperX<ProjectLoginAuthDO>()
                .eq(ProjectLoginAuthDO::getProjectId, projectId));
        result.setProjectId(projectId);
        // 接口名称
        if(ObjUtilX.isNotEmpty(authDO)) {
            ItemDO itemDO = itemMapper.selectById(authDO.getItemId());
            if(ObjUtilX.isNotEmpty(itemDO)) {
                authDO.setLoginApiName(itemDO.getItemName());
            }
            result.setAutoLoginInfo(authDO);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void projectApiEnvSetDefault(ProjectApiEnvPrimaryReqVO reqVO) {
        if(ObjUtilX.isEmpty(reqVO.getProjectId())){
            throw new BizException("5001", "项目id不能为空");
        }

        apiEnvMapper.update(new LambdaUpdateWrapper<ProjectApiEnvDO>()
                .eq(ProjectApiEnvDO::getProjectId, reqVO.getProjectId())
                .set(ProjectApiEnvDO::getIsDefault, false)
        );

        apiEnvMapper.update(new LambdaUpdateWrapper<ProjectApiEnvDO>()
                .eq(ProjectApiEnvDO::getId, reqVO.getId())
                .set(ProjectApiEnvDO::getIsDefault, true)
        );
    }

}
