package com.mongoso.mgs.module.project.controller.admin.projectmodelfield.vo;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * 项目图形建模初始化字段 AditReqVO
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
public class ProjectModelFieldSaveReqVO implements Serializable {

    /** 项目id */
    @NotNull(message = "业务类型不能为空")
    private Integer bizType;

    //@NotEmpty(message = "字段列表不能为空")
    /** 字段列表 */
    List<ProjectModelFieldAditReqVO> fieldList;
}
