package com.mongoso.mgs.module.project.dal.mysql.projectfieldtype;

import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.module.project.dal.db.projectfieldtype.ProjectFieldTypeDO;
import org.apache.ibatis.annotations.Mapper;
import com.mongoso.mgs.module.project.controller.admin.projectfieldtype.vo.*;

/**
 * 项目图形建模字段类型 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ProjectFieldTypeMapper extends BaseMapperX<ProjectFieldTypeDO> {

    default PageResult<ProjectFieldTypeDO> selectPage(ProjectFieldTypePageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<ProjectFieldTypeDO>lambdaQueryX()
                .eqIfPresent(ProjectFieldTypeDO::getProjectId, reqVO.getProjectId())
                .eqIfPresent(ProjectFieldTypeDO::getFieldTypeList, reqVO.getFieldTypeList())
                .eqIfPresent(ProjectFieldTypeDO::getDbType, reqVO.getDbType())
                .betweenIfPresent(ProjectFieldTypeDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .orderByDesc(ProjectFieldTypeDO::getCreatedDt));
    }




    default List<ProjectFieldTypeDO> selectList(ProjectFieldTypeQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<ProjectFieldTypeDO>lambdaQueryX()
                .eqIfPresent(ProjectFieldTypeDO::getProjectId, reqVO.getProjectId())
                .eqIfPresent(ProjectFieldTypeDO::getFieldTypeList, reqVO.getFieldTypeList())
                .eqIfPresent(ProjectFieldTypeDO::getDbType, reqVO.getDbType())
                .eqIfPresent(ProjectFieldTypeDO::getCreatedBy, reqVO.getCreatedBy())
                .betweenIfPresent(ProjectFieldTypeDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .eqIfPresent(ProjectFieldTypeDO::getUpdatedBy, reqVO.getUpdatedBy())
                .betweenIfPresent(ProjectFieldTypeDO::getUpdatedDt, reqVO.getStartUpdatedDt(), reqVO.getEndUpdatedDt())
                    .orderByDesc(ProjectFieldTypeDO::getCreatedDt));
    }


}