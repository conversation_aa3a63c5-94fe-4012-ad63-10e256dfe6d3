package com.mongoso.mgs.module.project.service.projectloginauth;

import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.mongoso.mgs.module.project.controller.admin.projectloginauth.vo.*;
import com.mongoso.mgs.module.project.dal.db.projectloginauth.ProjectLoginAuthDO;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.module.project.dal.mysql.projectloginauth.ProjectLoginAuthMapper;

// import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exception;
import com.mongoso.mgs.framework.common.exception.BizException;
// import static com.mongoso.mgs.module.project.enums.ErrorCodeConstants.*;


/**
 * 授权配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ProjectLoginAuthServiceImpl implements ProjectLoginAuthService {

    @Resource
    private ProjectLoginAuthMapper loginAuthMapper;

    @Override
    public Long projectLoginAuthAdd(ProjectLoginAuthAditReqVO reqVO) {
        // 插入
        ProjectLoginAuthDO loginAuth = BeanUtilX.copy(reqVO, ProjectLoginAuthDO::new);
        loginAuthMapper.insert(loginAuth);
        // 返回
        return loginAuth.getId();
    }

    @Override
    public Long projectLoginAuthEdit(ProjectLoginAuthAditReqVO reqVO) {
        // 校验存在
        this.projectLoginAuthValidateExists(reqVO.getId());
        // 更新
        ProjectLoginAuthDO loginAuth = BeanUtilX.copy(reqVO, ProjectLoginAuthDO::new);
        loginAuthMapper.updateById(loginAuth);
        // 返回
        return loginAuth.getId();
    }

    @Override
    public void projectLoginAuthDel(Long id) {
        // 校验存在
        this.projectLoginAuthValidateExists(id);
        // 删除
        loginAuthMapper.deleteById(id);
    }

    private ProjectLoginAuthDO projectLoginAuthValidateExists(Long id) {
        ProjectLoginAuthDO loginAuth = loginAuthMapper.selectById(id);
        if (loginAuth == null) {
            // throw exception(LOGIN_AUTH_NOT_EXISTS);
            throw new BizException("5001", "授权配置不存在");
        }
        return loginAuth;
    }

    @Override
    public ProjectLoginAuthRespVO projectLoginAuthDetail(Long id) {
        ProjectLoginAuthDO data = loginAuthMapper.selectById(id);
        return BeanUtilX.copy(data, ProjectLoginAuthRespVO::new);
    }

    @Override
    public List<ProjectLoginAuthRespVO> projectLoginAuthList(ProjectLoginAuthQueryReqVO reqVO) {
        List<ProjectLoginAuthDO> data = loginAuthMapper.selectList(reqVO);
        return BeanUtilX.copy(data, ProjectLoginAuthRespVO::new);
    }

    @Override
    public PageResult<ProjectLoginAuthRespVO> projectLoginAuthPage(ProjectLoginAuthPageReqVO reqVO) {
        PageResult<ProjectLoginAuthDO> data = loginAuthMapper.selectPage(reqVO);
        return BeanUtilX.copy(data, ProjectLoginAuthRespVO::new);
    }

}
