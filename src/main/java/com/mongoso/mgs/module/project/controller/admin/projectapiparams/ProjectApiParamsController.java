package com.mongoso.mgs.module.project.controller.admin.projectapiparams;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.project.controller.admin.projectapiparams.vo.*;
import com.mongoso.mgs.module.project.service.projectapiparams.ProjectApiParamsService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.mongoso.mgs.framework.common.domain.ResultX.success;

/**
 * 公共参数 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/project")
@Validated
public class ProjectApiParamsController {

    @Resource
    private ProjectApiParamsService apiParamsService;

    @OperateLog("公共参数添加或编辑")
    @PostMapping("/projectApiParamsAdit")
    @PreAuthorize("@ss.hasPermission('projectApiParams:adit')")
    public ResultX<Long> projectApiParamsAdit(@Valid @RequestBody ProjectApiParamsAditReqVO reqVO) {
        return success(reqVO.getId() == null
                            ? apiParamsService.projectApiParamsAdd(reqVO)
                            : apiParamsService.projectApiParamsEdit(reqVO));
    }

    @OperateLog("公共参数删除")
    @PostMapping("/projectApiParamsDel")
    @PreAuthorize("@ss.hasPermission('projectApiParams:delete')")
    public ResultX<Boolean> projectApiParamsDel(@Valid @RequestBody ProjectApiParamsPrimaryReqVO reqVO) {
        apiParamsService.projectApiParamsDel(reqVO.getId());
        return success(true);
    }

    @OperateLog("公共参数详情")
    @PostMapping("/projectApiParamsDetail")
    @PreAuthorize("@ss.hasPermission('projectApiParams:query')")
    public ResultX<ProjectApiParamsRespVO> projectApiParamsDetail(@Valid @RequestBody ProjectApiParamsPrimaryReqVO reqVO) {
        return success(apiParamsService.projectApiParamsDetail(reqVO.getId()));
    }

    @OperateLog("公共参数详情")
    @PostMapping("/projectApiParamsDetailByProjectId")
    @PreAuthorize("@ss.hasPermission('projectApiParams:query')")
    public ResultX<ProjectApiParamsRespVO> projectApiParamsDetailByProjectId(@Valid @RequestBody ProjectApiParamsQueryReqVO reqVO) {
        return success(apiParamsService.projectApiParamsDetailByProjectId(reqVO.getProjectId()));
    }

    @OperateLog("公共参数列表")
    @PostMapping("/projectApiParamsList")
    @PreAuthorize("@ss.hasPermission('projectApiParams:query')")
    public ResultX<List<ProjectApiParamsRespVO>> projectApiParamsList(@Valid @RequestBody ProjectApiParamsQueryReqVO reqVO) {
        return success(apiParamsService.projectApiParamsList(reqVO));
    }

    @OperateLog("公共参数分页")
    @PostMapping("/projectApiParamsPage")
    @PreAuthorize("@ss.hasPermission('projectApiParams:query')")
    public ResultX<PageResult<ProjectApiParamsRespVO>> projectApiParamsPage(@Valid @RequestBody ProjectApiParamsPageReqVO reqVO) {
        return success(apiParamsService.projectApiParamsPage(reqVO));
    }

}
