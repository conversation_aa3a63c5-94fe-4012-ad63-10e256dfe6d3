package com.mongoso.mgs.module.project.controller.admin.projectapiparams.vo;

import lombok.*;

import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.util.List;


import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONObject;

/**
 * 公共参数 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class ProjectApiParamsBaseVO implements Serializable {

    /** 主键Id */
    private Long id;

    /** 项目Id */
    @NotNull(message = "项目Id不能为空")
    private Long projectId;

    /** heder参数 */
    private List<JSONObject> headerParams;

    /** 普通参数 */
    private List<JSONObject> commonParams;

}
