package com.mongoso.mgs.module.project.controller.admin.projectloginauth.vo;

import lombok.*;

import jakarta.validation.constraints.*;
import java.io.Serializable;

  

/**
 * 授权配置 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class ProjectLoginAuthBaseVO implements Serializable {

    /** 接口登录信息ID */
    private Long id;

    /** 项目ID */
    @NotNull(message = "项目ID不能为空")
    private Long projectId;

    //登录接口 API文档的ID
    private Long itemId;

    /** 登录接口任务ID */
    private Long loginApiCode;

    /** 登录时效编码 */
    private String loginFaildCode;
    private String loginFaildKey;

    /** 登录认证字段名 */
    private String loginAuthKey;

    /** 登录认证字段值 */
    private String loginAuthVal;

}
