package com.mongoso.mgs.module.project.controller.admin.projectapienv.vo;

import com.mongoso.mgs.module.project.dal.db.projectapienv.ProjectApiEnvDO;
import com.mongoso.mgs.module.project.dal.db.projectloginauth.ProjectLoginAuthDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * 环境配置 详情返回
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ProjectApiEnvDetailRespVO extends ProjectApiEnvBaseVO {

    //"接口命名规范 0-驼峰 1-下划线"
    //private int interfaceFieldStyle;
    private Long projectId;

    // "接口环境列表"
    List<ProjectApiEnvDO> environmentList;

    // 接口登录信息
    ProjectLoginAuthDO autoLoginInfo;
}
