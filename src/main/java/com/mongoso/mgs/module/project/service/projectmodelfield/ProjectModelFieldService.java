package com.mongoso.mgs.module.project.service.projectmodelfield;

import java.util.*;
import jakarta.validation.*;
import com.mongoso.mgs.module.project.controller.admin.projectmodelfield.vo.*;
import com.mongoso.mgs.module.project.dal.db.projectmodelfield.ProjectModelFieldDO;
import com.mongoso.mgs.framework.common.domain.PageResult;

/**
 * 项目图形建模初始化字段 Service 接口
 *
 * <AUTHOR>
 */
public interface ProjectModelFieldService {

    /**
     * 创建项目图形建模初始化字段
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long projectModelFieldAdd(@Valid ProjectModelFieldAditReqVO reqVO);

    /**
     * 更新项目图形建模初始化字段
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long projectModelFieldEdit(@Valid ProjectModelFieldAditReqVO reqVO);

    /**
     * 删除项目图形建模初始化字段
     *
     * @param id 编号
     */
    void projectModelFieldDel(Long id);

    /**
     * 获得项目图形建模初始化字段信息
     *
     * @param id 编号
     * @return 项目图形建模初始化字段信息
     */
    ProjectModelFieldRespVO projectModelFieldDetail(Long id);

    /**
     * 获得项目图形建模初始化字段列表
     *
     * @param reqVO 查询条件
     * @return 项目图形建模初始化字段列表
     */
    List<ProjectModelFieldRespVO> projectModelFieldList(@Valid ProjectModelFieldQueryReqVO reqVO);

    /**
     * 获得项目图形建模初始化字段分页
     *
     * @param reqVO 查询条件
     * @return 项目图形建模初始化字段分页
     */
    PageResult<ProjectModelFieldRespVO> projectModelFieldPage(@Valid ProjectModelFieldPageReqVO reqVO);

    Boolean projectModelFieldSave(@Valid ProjectModelFieldSaveReqVO reqVO);
}
