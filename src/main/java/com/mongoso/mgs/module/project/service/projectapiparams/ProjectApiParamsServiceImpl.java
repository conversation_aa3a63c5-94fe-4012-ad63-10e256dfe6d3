package com.mongoso.mgs.module.project.service.projectapiparams;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.common.util.ObjUtilX;
import com.mongoso.mgs.module.project.controller.admin.projectapiparams.vo.ProjectApiParamsAditReqVO;
import com.mongoso.mgs.module.project.controller.admin.projectapiparams.vo.ProjectApiParamsPageReqVO;
import com.mongoso.mgs.module.project.controller.admin.projectapiparams.vo.ProjectApiParamsQueryReqVO;
import com.mongoso.mgs.module.project.controller.admin.projectapiparams.vo.ProjectApiParamsRespVO;
import com.mongoso.mgs.module.project.dal.db.projectapiparams.ProjectApiParamsDO;
import com.mongoso.mgs.module.project.dal.mysql.projectapiparams.ProjectApiParamsMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
// import static com.mongoso.mgs.module.project.enums.ErrorCodeConstants.*;


/**
 * 公共参数 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ProjectApiParamsServiceImpl implements ProjectApiParamsService {

    @Resource
    private ProjectApiParamsMapper apiParamsMapper;

    @Override
    public Long projectApiParamsAdd(ProjectApiParamsAditReqVO reqVO) {
        // 插入
        ProjectApiParamsDO apiParams = BeanUtilX.copy(reqVO, ProjectApiParamsDO::new);
        apiParamsMapper.insert(apiParams);
        // 返回
        return apiParams.getId();
    }

    @Override
    public Long projectApiParamsEdit(ProjectApiParamsAditReqVO reqVO) {
        // 校验存在
        this.projectApiParamsValidateExists(reqVO.getId());
        // 更新
        ProjectApiParamsDO apiParams = BeanUtilX.copy(reqVO, ProjectApiParamsDO::new);
        apiParamsMapper.updateById(apiParams);
        // 返回
        return apiParams.getId();
    }

    @Override
    public void projectApiParamsDel(Long id) {
        // 校验存在
        this.projectApiParamsValidateExists(id);
        // 删除
        apiParamsMapper.deleteById(id);
    }

    private ProjectApiParamsDO projectApiParamsValidateExists(Long id) {
        ProjectApiParamsDO apiParams = apiParamsMapper.selectById(id);
        if (apiParams == null) {
            // throw exception(API_PARAMS_NOT_EXISTS);
            throw new BizException("5001", "公共参数不存在");
        }
        return apiParams;
    }

    @Override
    public ProjectApiParamsRespVO projectApiParamsDetail(Long id) {
        ProjectApiParamsDO data = apiParamsMapper.selectById(id);
        return BeanUtilX.copy(data, ProjectApiParamsRespVO::new);
    }

    @Override
    public ProjectApiParamsRespVO projectApiParamsDetailByProjectId(Long projectId) {
        ProjectApiParamsDO data = apiParamsMapper.selectOneByProjectId(projectId);
        if(ObjUtilX.isEmpty(data)){
            JSONObject json = new JSONObject();
            json.put("key", "");
            json.put("value", "");
            json.put("description", "");
            json.put("selected", 0);
            List<JSONObject> list = new ArrayList<>();
            list.add(json);
            ProjectApiParamsRespVO emptyResp = new ProjectApiParamsRespVO();
            emptyResp.setHeaderParams(list);
            emptyResp.setCommonParams(list);
            return emptyResp;
        } else {
            return BeanUtilX.copy(data, ProjectApiParamsRespVO::new);
        }
    }

    @Override
    public List<ProjectApiParamsRespVO> projectApiParamsList(ProjectApiParamsQueryReqVO reqVO) {
        List<ProjectApiParamsDO> data = apiParamsMapper.selectList(reqVO);
        return BeanUtilX.copy(data, ProjectApiParamsRespVO::new);
    }

    @Override
    public PageResult<ProjectApiParamsRespVO> projectApiParamsPage(ProjectApiParamsPageReqVO reqVO) {
        PageResult<ProjectApiParamsDO> data = apiParamsMapper.selectPage(reqVO);
        return BeanUtilX.copy(data, ProjectApiParamsRespVO::new);
    }

}
