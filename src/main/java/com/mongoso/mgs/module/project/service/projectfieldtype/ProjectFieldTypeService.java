package com.mongoso.mgs.module.project.service.projectfieldtype;

import java.util.*;
import jakarta.validation.*;
import com.mongoso.mgs.module.project.controller.admin.projectfieldtype.vo.*;
import com.mongoso.mgs.module.project.dal.db.projectfieldtype.ProjectFieldTypeDO;
import com.mongoso.mgs.framework.common.domain.PageResult;

/**
 * 项目图形建模字段类型 Service 接口
 *
 * <AUTHOR>
 */
public interface ProjectFieldTypeService {

    /**
     * 创建项目图形建模字段类型
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long projectFieldTypeAdd(@Valid ProjectFieldTypeAditReqVO reqVO);

    /**
     * 更新项目图形建模字段类型
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long projectFieldTypeEdit(@Valid ProjectFieldTypeAditReqVO reqVO);

    /**
     * 删除项目图形建模字段类型
     *
     * @param id 编号
     */
    void projectFieldTypeDel(Long id);

    /**
     * 获得项目图形建模字段类型信息
     *
     * @param id 编号
     * @return 项目图形建模字段类型信息
     */
    ProjectFieldTypeRespVO projectFieldTypeDetail(Long id);

    /**
     * 获得项目图形建模字段类型列表
     *
     * @param reqVO 查询条件
     * @return 项目图形建模字段类型列表
     */
    List<ProjectFieldTypeRespVO> projectFieldTypeList(@Valid ProjectFieldTypeQueryReqVO reqVO);

    /**
     * 获得项目图形建模字段类型分页
     *
     * @param reqVO 查询条件
     * @return 项目图形建模字段类型分页
     */
    PageResult<ProjectFieldTypeRespVO> projectFieldTypePage(@Valid ProjectFieldTypePageReqVO reqVO);

    ProjectFieldTypeRespVO projectFieldTypeInfo(Long projectId);
}
