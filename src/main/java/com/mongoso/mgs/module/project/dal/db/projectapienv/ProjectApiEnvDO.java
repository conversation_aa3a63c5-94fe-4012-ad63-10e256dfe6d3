package com.mongoso.mgs.module.project.dal.db.projectapienv;

import lombok.*;

import com.baomidou.mybatisplus.annotation.*;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;

/**
 * 环境配置 DO
 *
 * <AUTHOR>
 */
@TableName(value = "lowcode.sys_project_api_env", autoResultMap = true)
//@KeySequence("sys_project_api_env_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectApiEnvDO {

    /** 环境ID */
        @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /** 项目Id */
    private Long projectId;

    /** 环境名称 */
    private String envName;

    /** 环境地址 */
    private String envUrl;

    /** 是否默认(0-否 1-是) */
    private Boolean isDefault;


}
