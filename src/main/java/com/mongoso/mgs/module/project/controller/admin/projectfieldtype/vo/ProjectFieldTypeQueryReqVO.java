package com.mongoso.mgs.module.project.controller.admin.projectfieldtype.vo;

import lombok.*;

    
import com.alibaba.fastjson.JSONObject;
 import org.springframework.format.annotation.DateTimeFormat;
 
import java.time.LocalDateTime;
import java.util.List;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
  


/**
 * 项目图形建模字段类型 QueryReqVO
 *
 * <AUTHOR>
 */
@Data
public class ProjectFieldTypeQueryReqVO {

    /** 项目id */
    private Long projectId;

    /** 字段类型列表 */
    private List<JSONObject> fieldTypeList;

    /** 数据库类型 */
    private String dbType;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startUpdatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endUpdatedDt;

}
