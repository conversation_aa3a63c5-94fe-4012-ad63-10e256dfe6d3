package com.mongoso.mgs.module.project.dal.db.projectapiparams;

import com.mongoso.mgs.framework.typehandler.JsonbTypeHandler;
import lombok.*;
import com.alibaba.fastjson.JSONObject;

import com.baomidou.mybatisplus.annotation.*;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;

import java.util.List;

/**
 * 公共参数 DO
 *
 * <AUTHOR>
 */
@TableName(value = "lowcode.sys_project_api_params", autoResultMap = true)
//@KeySequence("sys_project_api_params_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectApiParamsDO {

    /** 主键Id */
        @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /** 项目Id */
    private Long projectId;

    /** heder参数 */
    @TableField(typeHandler = JsonbTypeHandler.class,fill = FieldFill.INSERT)
    private List<JSONObject> headerParams;

    /** 普通参数 */
    @TableField(typeHandler = JsonbTypeHandler.class,fill = FieldFill.INSERT)
    private List<JSONObject> commonParams;


}
