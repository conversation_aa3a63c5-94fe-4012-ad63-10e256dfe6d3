package com.mongoso.mgs.module.project.controller.admin.projectapienv.vo;

import lombok.*;

import com.mongoso.mgs.framework.common.domain.PageParam;


    


/**
 * 环境配置 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ProjectApiEnvPageReqVO extends PageParam {

    /** 项目Id */
    private Long projectId;

    /** 环境名称 */
    private String envName;

    /** 环境地址 */
    private String envUrl;

    /** 是否默认(0-否 1-是) */
    private Boolean isDefault;

}
