package com.mongoso.mgs.module.project.service.projectmodelfield;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.common.util.ObjUtilX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.codegen.common.util.StringUtils;
import com.mongoso.mgs.module.model.controller.admin.modelfieldalter.vo.ModelFieldAlterAditReqVO;
import com.mongoso.mgs.module.model.controller.admin.modelfieldconf.vo.ModelFieldConfAditReqVO;
import com.mongoso.mgs.module.model.controller.admin.modelfieldconf.vo.ModelFieldTransReqVO;
import com.mongoso.mgs.module.model.service.modelfieldconf.ModelFieldConfService;
import com.mongoso.mgs.module.project.controller.admin.projectmodelfield.vo.*;
import com.mongoso.mgs.module.project.dal.db.projectmodelfield.ProjectModelFieldDO;
import com.mongoso.mgs.module.project.dal.mysql.projectmodelfield.ProjectModelFieldMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.List;
// import static com.mongoso.mgs.module.project.enums.ErrorCodeConstants.*;


/**
 * 项目图形建模初始化字段 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class ProjectModelFieldServiceImpl implements ProjectModelFieldService {

    @Resource
    private ProjectModelFieldMapper projectModelFieldMapper;
    @Resource
    private ModelFieldConfService confService;

    @Override
    public Long projectModelFieldAdd(ProjectModelFieldAditReqVO reqVO) {
        // 插入
        ProjectModelFieldDO modelField = BeanUtilX.copy(reqVO, ProjectModelFieldDO::new);

        //设置关键属性字段
        setAttr(reqVO, modelField);
        projectModelFieldMapper.insert(modelField);

        //新增翻译字段
        ModelFieldConfAditReqVO modelFieldConfAditReqVO = addConf(modelField);
        modelFieldConfAditReqVO.setId(modelField.getId());
        try {
            confService.modelFieldConfAdd(modelFieldConfAditReqVO);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        // 返回
        return modelField.getId();
    }

    @Override
    public Long projectModelFieldEdit(ProjectModelFieldAditReqVO reqVO) {
        // 校验存在
        this.projectModelFieldValidateExists(reqVO.getId());
        // 更新
        ProjectModelFieldDO modelField = BeanUtilX.copy(reqVO, ProjectModelFieldDO::new);

        //设置关键属性字段
        setAttr(reqVO, modelField);
        modelField.setFieldNo(null);//不能更新自增字段
        projectModelFieldMapper.updateById(modelField);

        //修改翻译字段
        ModelFieldConfAditReqVO modelFieldConfAditReqVO = addConf(modelField);
        modelFieldConfAditReqVO.setId(modelField.getId());
        try {
            confService.modelFieldConfAdd(modelFieldConfAditReqVO);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        // 返回
        return modelField.getId();
    }

    /**
     * 解析keyAttr的值，用逗号分隔：0:选中了主键 1选中了必填，空两个都没有选中
     * @param reqVO
     * @param modelField
     */
    private static void setAttr(ProjectModelFieldAditReqVO reqVO, ProjectModelFieldDO modelField) {
        //解析keyAttr的值，用逗号分隔：0:选中了主键 1选中了必填，空两个都没有选中
        if(ObjUtilX.isNotEmpty(reqVO.getKeyAttr())){
            String[] keyAttr = reqVO.getKeyAttr().split(",");
            if(keyAttr.length > 0){
                //表示只选中了一个
                int value = Integer.parseInt(keyAttr[0]);
                if (value == 0){
                    modelField.setIsPrimaryKey(1);
                    modelField.setIsNullable(0);
                }else if (value == 1){
                    modelField.setIsPrimaryKey(0);
                    modelField.setIsNullable(1);
                }else {
                    modelField.setIsPrimaryKey(0);
                    modelField.setIsNullable(0);
                }

            }
            if(keyAttr.length > 1){
                //表示两个都选中了
                modelField.setIsPrimaryKey(1);
                modelField.setIsNullable(1);
            }

        }else {
            //表示没有传递
            modelField.setIsPrimaryKey(0);
            modelField.setIsNullable(0);
        }
    }

    @Override
    public void projectModelFieldDel(Long id) {
        // 校验存在
        this.projectModelFieldValidateExists(id);
        // 删除
        projectModelFieldMapper.deleteById(id);
    }

    private ProjectModelFieldDO projectModelFieldValidateExists(Long id) {
        ProjectModelFieldDO modelField = projectModelFieldMapper.selectById(id);
        if (modelField == null) {
            // throw exception(MODEL_FIELD_NOT_EXISTS);
            throw new BizException("5001", "项目图形建模初始化字段不存在");
        }
        return modelField;
    }

    @Override
    public ProjectModelFieldRespVO projectModelFieldDetail(Long id) {
        ProjectModelFieldDO data = projectModelFieldMapper.selectById(id);
        ProjectModelFieldRespVO modelFieldRespVO = BeanUtilX.copy(data, ProjectModelFieldRespVO::new);
        //组装keyAttr的值返回
        if(data.getIsPrimaryKey() == 1 && data.getIsNullable() == 1){
            modelFieldRespVO.setKeyAttr("0,1");
        }else if(data.getIsPrimaryKey() == 1){
            modelFieldRespVO.setKeyAttr("0");
        }else if(data.getIsNullable() == 1){
            modelFieldRespVO.setKeyAttr("1");
        }else {
            modelFieldRespVO.setKeyAttr("");
        }
        //根据业务类型回写业务类型名称
        if(data.getBizType() == 0){
            modelFieldRespVO.setBizTypeDictName("单据主表");
        }else if(data.getBizType() == 1){
            modelFieldRespVO.setBizTypeDictName("单据明细表");
        }else if (data.getBizType() == 2){
            modelFieldRespVO.setBizTypeDictName("单表");
        }else if (data.getBizType() == 3){
            modelFieldRespVO.setBizTypeDictName("树结构");
        }
        return modelFieldRespVO;
    }

    @Override
    public List<ProjectModelFieldRespVO> projectModelFieldList(ProjectModelFieldQueryReqVO reqVO) {
        List<ProjectModelFieldDO> data = projectModelFieldMapper.selectList(reqVO);
        return BeanUtilX.copy(data, ProjectModelFieldRespVO::new);
    }

    @Override
    public PageResult<ProjectModelFieldRespVO> projectModelFieldPage(ProjectModelFieldPageReqVO reqVO) {
        PageResult<ProjectModelFieldDO> data = projectModelFieldMapper.selectPage(reqVO);
        return BeanUtilX.copy(data, ProjectModelFieldRespVO::new);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean projectModelFieldSave(ProjectModelFieldSaveReqVO reqVO) {
         //先删除
        projectModelFieldMapper.delete(LambdaQueryWrapperX.<ProjectModelFieldDO>lambdaQueryX()
                .eq(ProjectModelFieldDO::getBizType, reqVO.getBizType()));

        if(ObjUtilX.isNotEmpty(reqVO.getFieldList())) {
            List<ModelFieldConfAditReqVO> fieldConf = new ArrayList<>();
            List<ProjectModelFieldDO> fieldList = new ArrayList<>(reqVO.getFieldList().size());
            //重新新增当前传递过来的字段
            for (ProjectModelFieldAditReqVO req : reqVO.getFieldList()) {
                ProjectModelFieldDO modelField = BeanUtilX.copy(req, ProjectModelFieldDO::new);
                modelField.setBizType(reqVO.getBizType());
                fieldList.add(modelField);

                fieldConf.add(addConf(modelField));
            }
            //增加翻译配置
            if(ObjUtilX.isNotEmpty(fieldConf)){
                ModelFieldTransReqVO req = new ModelFieldTransReqVO();
                req.setFields(fieldConf);
                //req.setDataSourceConfigId(exists.getDataSourceConfigId());
                confService.modelFieldTransSave(req);
            }
            return projectModelFieldMapper.insertBatch(fieldList);
        }else{
            return true;
        }
    }

    private static ModelFieldConfAditReqVO addConf(ProjectModelFieldDO field) {
        ModelFieldConfAditReqVO tempConf = new ModelFieldConfAditReqVO();
        tempConf.setFieldCode(field.getFieldCode());
        tempConf.setEnglishName(StringUtils.strBarToHump(field.getFieldCode()));
        tempConf.setFieldName(field.getFieldName());
        tempConf.setItemName(field.getFieldName());
        tempConf.setFieldType(field.getFieldType());
        tempConf.setFieldLength(field.getLeng());
        tempConf.setLeng(field.getLeng());
        tempConf.setFieldPrecision(field.getFieldPrecision());
        tempConf.setSort(field.getSort());
        tempConf.setIsNullable(field.getIsNullable());
        tempConf.setIsPrimaryKey(field.getIsPrimaryKey());
        tempConf.setPropType(field.getPropType());
        //fieldConf.add(tempConf);
        return tempConf;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void projectModelFieldBatchDel(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return;
        }

        // 批量删除字段
        projectModelFieldMapper.deleteBatchIds(ids);
    }

}
