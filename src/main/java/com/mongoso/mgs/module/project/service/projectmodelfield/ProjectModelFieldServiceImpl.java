package com.mongoso.mgs.module.project.service.projectmodelfield;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.common.util.ObjUtilX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.codegen.common.util.StringUtils;
import com.mongoso.mgs.module.model.controller.admin.modelfieldalter.vo.ModelFieldAlterAditReqVO;
import com.mongoso.mgs.module.model.controller.admin.modelfieldconf.vo.ModelFieldConfAditReqVO;
import com.mongoso.mgs.module.model.controller.admin.modelfieldconf.vo.ModelFieldTransReqVO;
import com.mongoso.mgs.module.model.service.modelfieldconf.ModelFieldConfService;
import com.mongoso.mgs.module.project.controller.admin.projectmodelfield.vo.*;
import com.mongoso.mgs.module.project.dal.db.projectmodelfield.ProjectModelFieldDO;
import com.mongoso.mgs.module.project.dal.mysql.projectmodelfield.ProjectModelFieldMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.List;
// import static com.mongoso.mgs.module.project.enums.ErrorCodeConstants.*;


/**
 * 项目图形建模初始化字段 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class ProjectModelFieldServiceImpl implements ProjectModelFieldService {

    @Resource
    private ProjectModelFieldMapper projectModelFieldMapper;
    @Resource
    private ModelFieldConfService confService;

    @Override
    public Long projectModelFieldAdd(ProjectModelFieldAditReqVO reqVO) {
        // 插入
        ProjectModelFieldDO modelField = BeanUtilX.copy(reqVO, ProjectModelFieldDO::new);
        projectModelFieldMapper.insert(modelField);

        //新增翻译字段
        ModelFieldConfAditReqVO modelFieldConfAditReqVO = addConf(modelField);
        modelFieldConfAditReqVO.setId(modelField.getId());
        try {
            confService.modelFieldConfAdd(modelFieldConfAditReqVO);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        // 返回
        return modelField.getId();
    }

    @Override
    public Long projectModelFieldEdit(ProjectModelFieldAditReqVO reqVO) {
        // 校验存在
        this.projectModelFieldValidateExists(reqVO.getId());
        // 更新
        ProjectModelFieldDO modelField = BeanUtilX.copy(reqVO, ProjectModelFieldDO::new);
        projectModelFieldMapper.updateById(modelField);

        //修改翻译字段
        ModelFieldConfAditReqVO modelFieldConfAditReqVO = addConf(modelField);
        modelFieldConfAditReqVO.setId(modelField.getId());
        try {
            confService.modelFieldConfAdd(modelFieldConfAditReqVO);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        // 返回
        return modelField.getId();
    }

    @Override
    public void projectModelFieldDel(Long id) {
        // 校验存在
        this.projectModelFieldValidateExists(id);
        // 删除
        projectModelFieldMapper.deleteById(id);
    }

    private ProjectModelFieldDO projectModelFieldValidateExists(Long id) {
        ProjectModelFieldDO modelField = projectModelFieldMapper.selectById(id);
        if (modelField == null) {
            // throw exception(MODEL_FIELD_NOT_EXISTS);
            throw new BizException("5001", "项目图形建模初始化字段不存在");
        }
        return modelField;
    }

    @Override
    public ProjectModelFieldRespVO projectModelFieldDetail(Long id) {
        ProjectModelFieldDO data = projectModelFieldMapper.selectById(id);
        return BeanUtilX.copy(data, ProjectModelFieldRespVO::new);
    }

    @Override
    public List<ProjectModelFieldRespVO> projectModelFieldList(ProjectModelFieldQueryReqVO reqVO) {
        List<ProjectModelFieldDO> data = projectModelFieldMapper.selectList(reqVO);
        return BeanUtilX.copy(data, ProjectModelFieldRespVO::new);
    }

    @Override
    public PageResult<ProjectModelFieldRespVO> projectModelFieldPage(ProjectModelFieldPageReqVO reqVO) {
        PageResult<ProjectModelFieldDO> data = projectModelFieldMapper.selectPage(reqVO);
        return BeanUtilX.copy(data, ProjectModelFieldRespVO::new);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean projectModelFieldSave(ProjectModelFieldSaveReqVO reqVO) {
         //先删除
        projectModelFieldMapper.delete(LambdaQueryWrapperX.<ProjectModelFieldDO>lambdaQueryX()
                .eq(ProjectModelFieldDO::getBizType, reqVO.getBizType()));

        if(ObjUtilX.isNotEmpty(reqVO.getFieldList())) {
            List<ModelFieldConfAditReqVO> fieldConf = new ArrayList<>();
            List<ProjectModelFieldDO> fieldList = new ArrayList<>(reqVO.getFieldList().size());
            //重新新增当前传递过来的字段
            for (ProjectModelFieldAditReqVO req : reqVO.getFieldList()) {
                ProjectModelFieldDO modelField = BeanUtilX.copy(req, ProjectModelFieldDO::new);
                modelField.setBizType(reqVO.getBizType());
                fieldList.add(modelField);

                fieldConf.add(addConf(modelField));
            }
            //增加翻译配置
            if(ObjUtilX.isNotEmpty(fieldConf)){
                ModelFieldTransReqVO req = new ModelFieldTransReqVO();
                req.setFields(fieldConf);
                //req.setDataSourceConfigId(exists.getDataSourceConfigId());
                confService.modelFieldTransSave(req);
            }
            return projectModelFieldMapper.insertBatch(fieldList);
        }else{
            return true;
        }
    }

    private static ModelFieldConfAditReqVO addConf(ProjectModelFieldDO field) {
        ModelFieldConfAditReqVO tempConf = new ModelFieldConfAditReqVO();
        tempConf.setFieldCode(field.getFieldCode());
        tempConf.setEnglishName(StringUtils.strBarToHump(field.getFieldCode()));
        tempConf.setFieldName(field.getFieldName());
        tempConf.setItemName(field.getFieldName());
        tempConf.setFieldType(field.getFieldType());
        tempConf.setFieldLength(field.getLeng());
        tempConf.setLeng(field.getLeng());
        tempConf.setFieldPrecision(field.getFieldPrecision());
        tempConf.setSort(field.getSort());
        tempConf.setIsNullable(field.getIsNullable());
        tempConf.setIsPrimaryKey(field.getIsPrimaryKey());
        tempConf.setPropType(field.getPropType());
        //fieldConf.add(tempConf);
        return tempConf;
    }

}
