package com.mongoso.mgs.module.project.dal.mysql.projectmodelfield;

import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.module.project.dal.db.projectmodelfield.ProjectModelFieldDO;
import org.apache.ibatis.annotations.Mapper;
import com.mongoso.mgs.module.project.controller.admin.projectmodelfield.vo.*;

/**
 * 项目图形建模初始化字段 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ProjectModelFieldMapper extends BaseMapperX<ProjectModelFieldDO> {

    default PageResult<ProjectModelFieldDO> selectPage(ProjectModelFieldPageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<ProjectModelFieldDO>lambdaQueryX()
                .eqIfPresent(ProjectModelFieldDO::getBizType, reqVO.getBizType())
                .likeIfPresent(ProjectModelFieldDO::getFieldName, reqVO.getFieldName())
                .likeIfPresent(ProjectModelFieldDO::getFieldCode, reqVO.getFieldCode())
                .eqIfPresent(ProjectModelFieldDO::getFieldType, reqVO.getFieldType())
                .eqIfPresent(ProjectModelFieldDO::getIsNullable, reqVO.getIsNullable())
                .eqIfPresent(ProjectModelFieldDO::getIsPrimaryKey, reqVO.getIsPrimaryKey())
                .eqIfPresent(ProjectModelFieldDO::getSort, reqVO.getSort())
                .eqIfPresent(ProjectModelFieldDO::getPropType, reqVO.getPropType())
                .eqIfPresent(ProjectModelFieldDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(ProjectModelFieldDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .orderByDesc(ProjectModelFieldDO::getCreatedDt));
    }




    default List<ProjectModelFieldDO> selectList(ProjectModelFieldQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<ProjectModelFieldDO>lambdaQueryX()
                .likeIfPresent(ProjectModelFieldDO::getFieldName, reqVO.getFieldName())
                .likeIfPresent(ProjectModelFieldDO::getFieldCode, reqVO.getFieldCode())
                .eqIfPresent(ProjectModelFieldDO::getFieldType, reqVO.getFieldType())
                .eqIfPresent(ProjectModelFieldDO::getBizType, reqVO.getBizType())
                .eqIfPresent(ProjectModelFieldDO::getIsNullable, reqVO.getIsNullable())
                .eqIfPresent(ProjectModelFieldDO::getIsPrimaryKey, reqVO.getIsPrimaryKey())
                .eqIfPresent(ProjectModelFieldDO::getSort, reqVO.getSort())
                .eqIfPresent(ProjectModelFieldDO::getPropType, reqVO.getPropType())
                .eqIfPresent(ProjectModelFieldDO::getRemark, reqVO.getRemark())
                .eqIfPresent(ProjectModelFieldDO::getCreatedBy, reqVO.getCreatedBy())
                .betweenIfPresent(ProjectModelFieldDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .eqIfPresent(ProjectModelFieldDO::getUpdatedBy, reqVO.getUpdatedBy())
                .betweenIfPresent(ProjectModelFieldDO::getUpdatedDt, reqVO.getStartUpdatedDt(), reqVO.getEndUpdatedDt())
                    .orderByAsc(ProjectModelFieldDO::getSort));
    }


}