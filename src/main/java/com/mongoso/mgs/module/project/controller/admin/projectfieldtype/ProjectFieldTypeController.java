package com.mongoso.mgs.module.project.controller.admin.projectfieldtype;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import jakarta.validation.*;
import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import static com.mongoso.mgs.framework.common.domain.ResultX.success;

import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.project.controller.admin.projectfieldtype.vo.*;
import com.mongoso.mgs.module.project.dal.db.projectfieldtype.ProjectFieldTypeDO;
import com.mongoso.mgs.module.project.service.projectfieldtype.ProjectFieldTypeService;

/**
 * 项目图形建模字段类型 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/project")
@Validated
public class ProjectFieldTypeController {

    @Resource
    private ProjectFieldTypeService fieldTypeService;

    @OperateLog("项目图形建模字段类型添加或编辑")
    @PostMapping("/projectFieldTypeAdit")
    @PreAuthorize("@ss.hasPermission('projectFieldType:adit')")
    public ResultX<Long> projectFieldTypeAdit(@Valid @RequestBody ProjectFieldTypeAditReqVO reqVO) {
        return success(reqVO.getId() == null
                            ? fieldTypeService.projectFieldTypeAdd(reqVO)
                            : fieldTypeService.projectFieldTypeEdit(reqVO));
    }

    @OperateLog("项目图形建模字段类型删除")
    @PostMapping("/projectFieldTypeDel")
    @PreAuthorize("@ss.hasPermission('projectFieldType:delete')")
    public ResultX<Boolean> projectFieldTypeDel(@Valid @RequestBody ProjectFieldTypePrimaryReqVO reqVO) {
        fieldTypeService.projectFieldTypeDel(reqVO.getId());
        return success(true);
    }

    @OperateLog("项目图形建模字段类型详情")
    @PostMapping("/projectFieldTypeDetail")
    @PreAuthorize("@ss.hasPermission('projectFieldType:query')")
    public ResultX<ProjectFieldTypeRespVO> projectFieldTypeDetail(@Valid @RequestBody ProjectFieldTypePrimaryReqVO reqVO) {
        return success(fieldTypeService.projectFieldTypeDetail(reqVO.getId()));
    }

    @OperateLog("项目图形建模字段类型详情-根据项目查")
    @PostMapping("/projectFieldTypeInfo")
    @PreAuthorize("@ss.hasPermission('projectFieldType:query')")
    public ResultX<ProjectFieldTypeRespVO> projectFieldTypeInfo(@Valid @RequestBody ProjectFieldTypeQueryReqVO reqVO) {
        return success(fieldTypeService.projectFieldTypeInfo(reqVO.getProjectId()));
    }

    @OperateLog("项目图形建模字段类型列表")
    @PostMapping("/projectFieldTypeList")
    @PreAuthorize("@ss.hasPermission('projectFieldType:query')")
    public ResultX<List<ProjectFieldTypeRespVO>> projectFieldTypeList(@Valid @RequestBody ProjectFieldTypeQueryReqVO reqVO) {
        return success(fieldTypeService.projectFieldTypeList(reqVO));
    }

    @OperateLog("项目图形建模字段类型分页")
    @PostMapping("/projectFieldTypePage")
    @PreAuthorize("@ss.hasPermission('projectFieldType:query')")
    public ResultX<PageResult<ProjectFieldTypeRespVO>> projectFieldTypePage(@Valid @RequestBody ProjectFieldTypePageReqVO reqVO) {
        return success(fieldTypeService.projectFieldTypePage(reqVO));
    }

}
