package com.mongoso.mgs.module.project.dal.db.projectfieldtype;

import lombok.*;
import java.time.LocalDateTime;
import java.util.List;

import com.alibaba.fastjson.JSONObject;
import com.mongoso.mgs.framework.typehandler.JsonbTypeHandler;

import com.baomidou.mybatisplus.annotation.*;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;

/**
 * 项目图形建模字段类型 DO
 *
 * <AUTHOR>
 */
@TableName(value = "lowcode.sys_project_field_type", autoResultMap = true)
//@KeySequence("sys_project_field_type_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectFieldTypeDO extends OperateDO {

    /** 主键id */
        @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /** 项目id */
    private Long projectId;

    /** 字段类型列表 */
    @TableField(typeHandler = JsonbTypeHandler.class,fill = FieldFill.INSERT)
    private List<JSONObject> fieldTypeList;

    /** 数据库类型 */
    private String dbType;


}
