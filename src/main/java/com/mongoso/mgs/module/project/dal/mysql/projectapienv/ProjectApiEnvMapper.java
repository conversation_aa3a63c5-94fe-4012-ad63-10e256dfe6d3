package com.mongoso.mgs.module.project.dal.mysql.projectapienv;

import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.module.project.dal.db.projectapienv.ProjectApiEnvDO;
import org.apache.ibatis.annotations.Mapper;
import com.mongoso.mgs.module.project.controller.admin.projectapienv.vo.*;

/**
 * 环境配置 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ProjectApiEnvMapper extends BaseMapperX<ProjectApiEnvDO> {

    default PageResult<ProjectApiEnvDO> selectPage(ProjectApiEnvPageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<ProjectApiEnvDO>lambdaQueryX()
                .eqIfPresent(ProjectApiEnvDO::getProjectId, reqVO.getProjectId())
                .likeIfPresent(ProjectApiEnvDO::getEnvName, reqVO.getEnvName())
                .eqIfPresent(ProjectApiEnvDO::getEnvUrl, reqVO.getEnvUrl())
                .eqIfPresent(ProjectApiEnvDO::getIsDefault, reqVO.getIsDefault())
                .orderByDesc(ProjectApiEnvDO::getId));
    }




    default List<ProjectApiEnvDO> selectList(ProjectApiEnvQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<ProjectApiEnvDO>lambdaQueryX()
                .eqIfPresent(ProjectApiEnvDO::getProjectId, reqVO.getProjectId())
                .likeIfPresent(ProjectApiEnvDO::getEnvName, reqVO.getEnvName())
                .eqIfPresent(ProjectApiEnvDO::getEnvUrl, reqVO.getEnvUrl())
                .eqIfPresent(ProjectApiEnvDO::getIsDefault, reqVO.getIsDefault())
                    .orderByDesc(ProjectApiEnvDO::getId));
    }

    default List<ProjectApiEnvDO> selectListByProjectId(Long projectId) {
        return selectList(LambdaQueryWrapperX.<ProjectApiEnvDO>lambdaQueryX()
                .eq(ProjectApiEnvDO::getProjectId, projectId)
        );
    }

}