package com.mongoso.mgs.module.project.controller.admin.projectmodelfield.vo;

import lombok.*;

    
import com.alibaba.fastjson.JSONObject;
 import org.springframework.format.annotation.DateTimeFormat;
 
import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
  


/**
 * 项目图形建模初始化字段 QueryReqVO
 *
 * <AUTHOR>
 */
@Data
public class ProjectModelFieldQueryReqVO {

    /** 模型字段表中文名 */
    private String fieldName;

    /** 模型字段表实名 */
    private String fieldCode;

    /** 字段类型 */
    private String fieldType;

    /** 是否为空 */
    private Integer isNullable;

    /** 是否为主键 */
    private Integer isPrimaryKey;

    /** 是否自增 */
    private Integer isAutoIncrease;

    /** 排序 */
    private Integer sort;

    /** 属性类型，0：系统，1：用户 */
    private Integer propType;


    /** 备注描述 */
    private String remark;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startUpdatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endUpdatedDt;
    /** 业务类型 */
    private Integer bizType;

    /** 字段编码 */
    private String fieldNo;
}
