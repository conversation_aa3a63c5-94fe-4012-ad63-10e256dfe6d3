package com.mongoso.mgs.module.project.controller.admin.projectapienv.vo;

import com.mongoso.mgs.module.project.controller.admin.projectloginauth.vo.ProjectLoginAuthAditReqVO;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * 环境配置 AditReqVO
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
public class ProjectApiEnvAditListReqVO {

    //"接口命名规范 0-驼峰 1-下划线"
    //private int interfaceFieldStyle;

    /** 项目ID */
     private Long projectId;

    /** 接口环境列表 */
    List<ProjectApiEnvAditReqVO> environmentList;

    /** 接口登录信息 */
    ProjectLoginAuthAditReqVO autoLoginInfo;
}
