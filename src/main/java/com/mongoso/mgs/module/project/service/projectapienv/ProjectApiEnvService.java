package com.mongoso.mgs.module.project.service.projectapienv;

import java.util.*;
import jakarta.validation.*;
import com.mongoso.mgs.module.project.controller.admin.projectapienv.vo.*;
import com.mongoso.mgs.module.project.dal.db.projectapienv.ProjectApiEnvDO;
import com.mongoso.mgs.framework.common.domain.PageResult;
import jakarta.validation.constraints.NotNull;

/**
 * 环境配置 Service 接口
 *
 * <AUTHOR>
 */
public interface ProjectApiEnvService {

    /**
     * 创建环境配置
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long projectApiEnvAdd(@Valid ProjectApiEnvAditReqVO reqVO);

    /**
     * 更新环境配置
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long projectApiEnvEdit(@Valid ProjectApiEnvAditReqVO reqVO);

    /**
     * 删除环境配置
     *
     * @param id 编号
     */
    void projectApiEnvDel(Long id);

    /**
     * 获得环境配置信息
     *
     * @param id 编号
     * @return 环境配置信息
     */
    ProjectApiEnvRespVO projectApiEnvDetail(Long id);

    /**
     * 获得环境配置列表
     *
     * @param reqVO 查询条件
     * @return 环境配置列表
     */
    List<ProjectApiEnvRespVO> projectApiEnvList(@Valid ProjectApiEnvQueryReqVO reqVO);

    /**
     * 获得环境配置分页
     *
     * @param reqVO 查询条件
     * @return 环境配置分页
     */
    PageResult<ProjectApiEnvRespVO> projectApiEnvPage(@Valid ProjectApiEnvPageReqVO reqVO);

    Long projectApiEnvSave(@Valid ProjectApiEnvAditListReqVO reqVO);

    ProjectApiEnvDetailRespVO projectApiEnvDetailList(Long projectId);

    void projectApiEnvSetDefault(ProjectApiEnvPrimaryReqVO req);
}
