package com.mongoso.mgs.module.project.controller.admin.projectapienv;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.project.controller.admin.projectapienv.vo.*;
import com.mongoso.mgs.module.project.service.projectapienv.ProjectApiEnvService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.mongoso.mgs.framework.common.domain.ResultX.success;

/**
 * 环境配置 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/project")
@Validated
public class ProjectApiEnvController {

    @Resource
    private ProjectApiEnvService apiEnvService;

    @OperateLog("环境保存")
    @PostMapping("/projectApiEnvSave")
    @PreAuthorize("@ss.hasPermission('projectApiEnv:adit')")
    public ResultX<Long> projectApiEnvSave(@Valid @RequestBody ProjectApiEnvAditListReqVO reqVO) {
        return success(apiEnvService.projectApiEnvSave(reqVO));
    }

    @OperateLog("环境配置添加或编辑")
    @PostMapping("/projectApiEnvAdit")
    @PreAuthorize("@ss.hasPermission('projectApiEnv:adit')")
    public ResultX<Long> projectApiEnvAdit(@Valid @RequestBody ProjectApiEnvAditReqVO reqVO) {
        return success(reqVO.getId() == null
                            ? apiEnvService.projectApiEnvAdd(reqVO)
                            : apiEnvService.projectApiEnvEdit(reqVO));
    }

    @OperateLog("环境配置删除")
    @PostMapping("/projectApiEnvDel")
    @PreAuthorize("@ss.hasPermission('projectApiEnv:delete')")
    public ResultX<Boolean> projectApiEnvDel(@Valid @RequestBody ProjectApiEnvPrimaryReqVO reqVO) {
        apiEnvService.projectApiEnvDel(reqVO.getId());
        return success(true);
    }

    @OperateLog("环境配置-设为默认")
    @PostMapping("/projectApiEnvSetDefault")
    @PreAuthorize("@ss.hasPermission('projectApiEnv:delete')")
    public ResultX<Boolean> projectApiEnvSetDefault(@Valid @RequestBody ProjectApiEnvPrimaryReqVO reqVO) {
        apiEnvService.projectApiEnvSetDefault(reqVO);
        return success(true);
    }

    @OperateLog("环境配置详情")
    @PostMapping("/projectApiEnvDetail")
    @PreAuthorize("@ss.hasPermission('projectApiEnv:query')")
    public ResultX<ProjectApiEnvRespVO> projectApiEnvDetail(@Valid @RequestBody ProjectApiEnvPrimaryReqVO reqVO) {
        return success(apiEnvService.projectApiEnvDetail(reqVO.getId()));
    }

    @OperateLog("环境配置详情")
    @PostMapping("/projectApiEnvDetailList")
    @PreAuthorize("@ss.hasPermission('projectApiEnv:query')")
    public ResultX<ProjectApiEnvDetailRespVO> projectApiEnvDetailList(@Valid @RequestBody ProjectApiEnvDetailListReqVO reqVO) {
        return success(apiEnvService.projectApiEnvDetailList(reqVO.getProjectId()));
    }

    @OperateLog("环境配置列表")
    @PostMapping("/projectApiEnvList")
    @PreAuthorize("@ss.hasPermission('projectApiEnv:query')")
    public ResultX<List<ProjectApiEnvRespVO>> projectApiEnvList(@Valid @RequestBody ProjectApiEnvQueryReqVO reqVO) {
        return success(apiEnvService.projectApiEnvList(reqVO));
    }

    @OperateLog("环境配置分页")
    @PostMapping("/projectApiEnvPage")
    @PreAuthorize("@ss.hasPermission('projectApiEnv:query')")
    public ResultX<PageResult<ProjectApiEnvRespVO>> projectApiEnvPage(@Valid @RequestBody ProjectApiEnvPageReqVO reqVO) {
        return success(apiEnvService.projectApiEnvPage(reqVO));
    }

}
