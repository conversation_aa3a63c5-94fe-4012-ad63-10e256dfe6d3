package com.mongoso.mgs.module.project.controller.admin.projectmodelfield;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.project.controller.admin.projectmodelfield.vo.*;
import com.mongoso.mgs.module.project.service.projectmodelfield.ProjectModelFieldService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.mongoso.mgs.framework.common.domain.ResultX.success;

/**
 * 字段管理 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/project")
@Validated
public class ProjectModelFieldController {

    @Resource
    private ProjectModelFieldService modelFieldService;

    @OperateLog("字段管理添加或编辑")
    @PostMapping("/projectModelFieldAdit")
    @PreAuthorize("@ss.hasPermission('projectModelField:adit')")
    public ResultX<Long> projectModelFieldAdit(@Valid @RequestBody ProjectModelFieldAditReqVO reqVO) {
        return success(reqVO.getId() == null
                            ? modelFieldService.projectModelFieldAdd(reqVO)
                            : modelFieldService.projectModelFieldEdit(reqVO));
    }
    @OperateLog("字段管理保存")
    @PostMapping("/projectModelFieldSave")
    @PreAuthorize("@ss.hasPermission('projectModelField:adit')")
    public ResultX<Boolean> projectModelFieldSave(@Valid @RequestBody ProjectModelFieldSaveReqVO reqVO) {
        return success(modelFieldService.projectModelFieldSave(reqVO));
    }

    @OperateLog("字段管理删除")
    @PostMapping("/projectModelFieldDel")
    @PreAuthorize("@ss.hasPermission('projectModelField:delete')")
    public ResultX<Boolean> projectModelFieldDel(@Valid @RequestBody ProjectModelFieldPrimaryReqVO reqVO) {
        modelFieldService.projectModelFieldDel(reqVO.getId());
        return success(true);
    }

    @OperateLog("字段管理批量删除")
    @PostMapping("/projectModelFieldBatchDel")
    @PreAuthorize("@ss.hasPermission('projectModelField:delete')")
    public ResultX<Boolean> projectModelFieldBatchDel(@Valid @RequestBody ProjectModelFieldBatchDeleteReqVO reqVO) {
        modelFieldService.projectModelFieldBatchDel(reqVO.getIds());
        return success(true);
    }

    @OperateLog("字段管理详情")
    @PostMapping("/projectModelFieldDetail")
    @PreAuthorize("@ss.hasPermission('projectModelField:query')")
    public ResultX<ProjectModelFieldRespVO> projectModelFieldDetail(@Valid @RequestBody ProjectModelFieldPrimaryReqVO reqVO) {
        return success(modelFieldService.projectModelFieldDetail(reqVO.getId()));
    }

    @OperateLog("字段管理列表")
    @PostMapping("/projectModelFieldList")
    @PreAuthorize("@ss.hasPermission('projectModelField:query')")
    public ResultX<List<ProjectModelFieldRespVO>> projectModelFieldList(@Valid @RequestBody ProjectModelFieldQueryReqVO reqVO) {
        return success(modelFieldService.projectModelFieldList(reqVO));
    }

    @OperateLog("字段管理分页")
    @PostMapping("/projectModelFieldPage")
    @PreAuthorize("@ss.hasPermission('projectModelField:query')")
    public ResultX<PageResult<ProjectModelFieldRespVO>> projectModelFieldPage(@Valid @RequestBody ProjectModelFieldPageReqVO reqVO) {
        return success(modelFieldService.projectModelFieldPage(reqVO));
    }

}
