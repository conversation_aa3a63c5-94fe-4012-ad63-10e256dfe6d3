package com.mongoso.mgs.module.project.controller.admin.projectloginauth;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import jakarta.validation.*;
import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import static com.mongoso.mgs.framework.common.domain.ResultX.success;

import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.project.controller.admin.projectloginauth.vo.*;
import com.mongoso.mgs.module.project.dal.db.projectloginauth.ProjectLoginAuthDO;
import com.mongoso.mgs.module.project.service.projectloginauth.ProjectLoginAuthService;

/**
 * 授权配置 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/project")
@Validated
public class ProjectLoginAuthController {

    @Resource
    private ProjectLoginAuthService loginAuthService;

    @OperateLog("授权配置添加或编辑")
    @PostMapping("/projectLoginAuthAdit")
    @PreAuthorize("@ss.hasPermission('projectLoginAuth:adit')")
    public ResultX<Long> projectLoginAuthAdit(@Valid @RequestBody ProjectLoginAuthAditReqVO reqVO) {
        return success(reqVO.getId() == null
                            ? loginAuthService.projectLoginAuthAdd(reqVO)
                            : loginAuthService.projectLoginAuthEdit(reqVO));
    }

    @OperateLog("授权配置删除")
    @PostMapping("/projectLoginAuthDel")
    @PreAuthorize("@ss.hasPermission('projectLoginAuth:delete')")
    public ResultX<Boolean> projectLoginAuthDel(@Valid @RequestBody ProjectLoginAuthPrimaryReqVO reqVO) {
        loginAuthService.projectLoginAuthDel(reqVO.getId());
        return success(true);
    }

    @OperateLog("授权配置详情")
    @PostMapping("/projectLoginAuthDetail")
    @PreAuthorize("@ss.hasPermission('projectLoginAuth:query')")
    public ResultX<ProjectLoginAuthRespVO> projectLoginAuthDetail(@Valid @RequestBody ProjectLoginAuthPrimaryReqVO reqVO) {
        return success(loginAuthService.projectLoginAuthDetail(reqVO.getId()));
    }

    @OperateLog("授权配置列表")
    @PostMapping("/projectLoginAuthList")
    @PreAuthorize("@ss.hasPermission('projectLoginAuth:query')")
    public ResultX<List<ProjectLoginAuthRespVO>> projectLoginAuthList(@Valid @RequestBody ProjectLoginAuthQueryReqVO reqVO) {
        return success(loginAuthService.projectLoginAuthList(reqVO));
    }

    @OperateLog("授权配置分页")
    @PostMapping("/projectLoginAuthPage")
    @PreAuthorize("@ss.hasPermission('projectLoginAuth:query')")
    public ResultX<PageResult<ProjectLoginAuthRespVO>> projectLoginAuthPage(@Valid @RequestBody ProjectLoginAuthPageReqVO reqVO) {
        return success(loginAuthService.projectLoginAuthPage(reqVO));
    }

}
