package com.mongoso.mgs.module.ping.controller.admin;

import com.mongoso.mgs.framework.common.domain.ResultX;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.security.PermitAll;

import static com.mongoso.mgs.framework.common.domain.ResultX.success;

@RestController
@RequestMapping
public class PingController {

    /**
     * jenkins调用的，验证服务启动状态
     * 心跳接口
     */
    @PermitAll
    @GetMapping("/ping")
    public ResultX<String> ping() {
        return success("pong");
    }

}
