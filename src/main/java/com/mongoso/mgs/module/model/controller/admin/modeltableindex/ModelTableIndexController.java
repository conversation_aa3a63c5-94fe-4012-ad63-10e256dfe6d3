package com.mongoso.mgs.module.model.controller.admin.modeltableindex;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import jakarta.validation.*;
import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import static com.mongoso.mgs.framework.common.domain.ResultX.success;

import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.model.controller.admin.modeltableindex.vo.*;
import com.mongoso.mgs.module.model.dal.db.modeltableindex.ModelTableIndexDO;
import com.mongoso.mgs.module.model.service.modeltableindex.ModelTableIndexService;

/**
 * 图形建模主表索引 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/model")
@Validated
public class ModelTableIndexController {

    @Resource
    private ModelTableIndexService tableIndexService;

    @OperateLog("图形建模主表索引添加或编辑")
    @PostMapping("/modelTableIndexAdit")
    @PreAuthorize("@ss.hasPermission('modelTableIndex:adit')")
    public ResultX<Long> modelTableIndexAdit(@Valid @RequestBody ModelTableIndexAditReqVO reqVO) {
        return success(reqVO.getId() == null
                            ? tableIndexService.modelTableIndexAdd(reqVO)
                            : tableIndexService.modelTableIndexEdit(reqVO));
    }

    @OperateLog("图形建模主表索引删除")
    @PostMapping("/modelTableIndexDel")
    @PreAuthorize("@ss.hasPermission('modelTableIndex:del')")
    public ResultX<Boolean> modelTableIndexDel(@Valid @RequestBody ModelTableIndexPrimaryReqVO reqVO) {
        tableIndexService.modelTableIndexDel(reqVO.getId());
        return success(true);
    }

    @OperateLog("图形建模主表索引详情")
    @PostMapping("/modelTableIndexDetail")
    @PreAuthorize("@ss.hasPermission('modelTableIndex:query')")
    public ResultX<ModelTableIndexRespVO> modelTableIndexDetail(@Valid @RequestBody ModelTableIndexPrimaryReqVO reqVO) {
        ModelTableIndexDO oldDO = tableIndexService.modelTableIndexDetail(reqVO.getId());
        return success(BeanUtilX.copy(oldDO, ModelTableIndexRespVO::new));
    }

    @OperateLog("图形建模主表索引列表")
    @PostMapping("/modelTableIndexList")
    @PreAuthorize("@ss.hasPermission('modelTableIndex:query')")
    public ResultX<List<ModelTableIndexRespVO>> modelTableIndexList(@Valid @RequestBody ModelTableIndexQueryReqVO reqVO) {
        List<ModelTableIndexDO> list = tableIndexService.modelTableIndexList(reqVO);
        return success(BeanUtilX.copyList(list, ModelTableIndexRespVO::new));
    }

    @OperateLog("图形建模主表索引分页")
    @PostMapping("/modelTableIndexPage")
    @PreAuthorize("@ss.hasPermission('modelTableIndex:query')")
    public ResultX<PageResult<ModelTableIndexRespVO>> modelTableIndexPage(@Valid @RequestBody ModelTableIndexPageReqVO reqVO) {
        PageResult<ModelTableIndexDO> pageResult = tableIndexService.modelTableIndexPage(reqVO);
        return success(BeanUtilX.copyPage(pageResult, ModelTableIndexRespVO::new));
    }

}
