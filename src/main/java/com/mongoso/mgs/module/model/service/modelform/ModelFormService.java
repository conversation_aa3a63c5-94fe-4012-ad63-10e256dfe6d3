package com.mongoso.mgs.module.model.service.modelform;

import java.util.*;
import jakarta.validation.*;
import com.mongoso.mgs.module.model.controller.admin.modelform.vo.*;
import com.mongoso.mgs.module.model.dal.db.modelform.ModelFormDO;
import com.mongoso.mgs.framework.common.domain.PageResult;

/**
 * 单据建模主 Service 接口
 *
 * <AUTHOR>
 */
public interface ModelFormService {

    /**
     * 创建单据建模主
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long modelFormAdd(@Valid ModelFormAditReqVO reqVO);

    /**
     * 更新单据建模主
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long modelFormEdit(@Valid ModelFormAditReqVO reqVO);

    /**
     * 删除单据建模主
     *
     * @param dataId 编号
     */
    void modelFormDelete(Long dataId);

    /**
     * 获得单据建模主信息
     *
     * @param dataId 编号
     * @return 单据建模主信息
     */
    ModelFormRespVO modelFormDetail(Long dataId);

    /**
     * 获得单据建模主列表
     *
     * @param reqVO 查询条件
     * @return 单据建模主列表
     */
    List<ModelFormRespVO> modelFormList(@Valid ModelFormQueryReqVO reqVO);

    /**
     * 获得单据建模主分页
     *
     * @param reqVO 查询条件
     * @return 单据建模主分页
     */
    PageResult<ModelFormRespVO> modelFormPage(@Valid ModelFormPageReqVO reqVO);

    /**
     * 批量删除单据建模主
     *
     * @param dataIds 单据建模ID列表
     */
    void modelFormBatchDelete(List<Long> dataIds);

}
