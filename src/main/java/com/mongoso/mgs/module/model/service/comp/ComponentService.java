package com.mongoso.mgs.module.model.service.comp;

import com.mongoso.mgs.module.model.controller.admin.comp.vo.ComponentBaseVO;
import com.mongoso.mgs.module.model.dal.db.comp.ComponentDO;

/**
 * <AUTHOR>
 * @date 2025/2/11
 * @description
 */
public interface ComponentService {

    /**
     *  保存项目组件
     * @param comp
     */
    Boolean saveProjectComp(ComponentBaseVO comp);

    /**
     *  查询项目组件
     */
    ComponentDO queryProjectComp();

}
