package com.mongoso.mgs.module.model.controller.admin.modelform.button.vo;

import lombok.*;

import com.mongoso.mgs.framework.common.domain.PageParam;


    
 import org.springframework.format.annotation.DateTimeFormat;
 
import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
  


/**
 * 单据建模按钮配置 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ModelFormButtonPageReqVO extends PageParam {

    /** 单据建模编码 */
    private String modelFormCode;

    /** 按钮类型 */
    private Integer buttonType;

    /** 按钮名称 */
    private String buttonName;

    /** 是否选中 */
    private Boolean isSelect;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

}
