package com.mongoso.mgs.module.model.dal.db.pageconfig;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mongoso.mgs.framework.mybatis.core.pojo.BaseDO;
import lombok.Data;

/**
 * 页面配置 DO
 *
 * <AUTHOR>
 */
@TableName("sys_page_config")
@Data
public class PageConfigDO extends BaseDO {

    /** id */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /** 父id */
    private Long parentId;

    private Long projectId;

    /** 名称 */
    private String name;

    /** 内容 */
    private String content;

    /** 路径标识 */
    private String routerPath;

    /** 是否显示在菜单里    枚举    0 否  1是 */
    private Integer isShow = 1;

    private Integer itemType;

    /** 排序号 **/
    private Long seq;
}
