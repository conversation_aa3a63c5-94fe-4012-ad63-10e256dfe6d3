package com.mongoso.mgs.module.model.dal.mysql.modelbiztable.detail;

import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.module.model.dal.db.modelbiztable.detail.ModelBizTableDetailDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.mongoso.mgs.module.model.controller.admin.modelbiztable.detail.vo.*;

/**
 * 子表明细 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ModelBizTableDetailMapper extends BaseMapperX<ModelBizTableDetailDO> {

    default PageResult<ModelBizTableDetailDO> selectPage(ModelBizTableDetailPageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<ModelBizTableDetailDO>lambdaQueryX()
                .eqIfPresent(ModelBizTableDetailDO::getTableId, reqVO.getTableId())
                .likeIfPresent(ModelBizTableDetailDO::getBizCode, reqVO.getBizCode())
                .likeIfPresent(ModelBizTableDetailDO::getTableCode, reqVO.getTableCode())
                .eqIfPresent(ModelBizTableDetailDO::getLevel, reqVO.getLevel())
                .eqIfPresent(ModelBizTableDetailDO::getTableType, reqVO.getTableType())
                .eqIfPresent(ModelBizTableDetailDO::getIsNullAble, reqVO.getIsNullAble())
                .eqIfPresent(ModelBizTableDetailDO::getParentDataId, reqVO.getParentDataId())
                .eqIfPresent(ModelBizTableDetailDO::getRemark, reqVO.getRemark())
                .orderByDesc(ModelBizTableDetailDO::getDataId));
    }




    default List<ModelBizTableDetailDO> selectList(ModelBizTableDetailQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<ModelBizTableDetailDO>lambdaQueryX()
                .eqIfPresent(ModelBizTableDetailDO::getDataId, reqVO.getDataId())
                .eqIfPresent(ModelBizTableDetailDO::getTableId, reqVO.getTableId())
                .likeIfPresent(ModelBizTableDetailDO::getBizCode, reqVO.getBizCode())
                .likeIfPresent(ModelBizTableDetailDO::getTableCode, reqVO.getTableCode())
                .eqIfPresent(ModelBizTableDetailDO::getLevel, reqVO.getLevel())
                .eqIfPresent(ModelBizTableDetailDO::getTableType, reqVO.getTableType())
                .eqIfPresent(ModelBizTableDetailDO::getIsNullAble, reqVO.getIsNullAble())
                .eqIfPresent(ModelBizTableDetailDO::getParentDataId, reqVO.getParentDataId())
                .eqIfPresent(ModelBizTableDetailDO::getRemark, reqVO.getRemark())
                .orderByDesc(ModelBizTableDetailDO::getDataId));
    }

    /**
     * 根据业务编码查询子表明细列表（连表查询获取表名称）
     *
     * @param bizCode 业务编码
     * @return 子表明细列表
     */
    List<ModelBizTableDetailRespVO> selectListWithTableNameByBizCode(@Param("bizCode") String bizCode);


}