package com.mongoso.mgs.module.model.controller.admin.modelform.nocopy;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import jakarta.validation.*;
import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;

import static com.mongoso.mgs.framework.common.domain.ResultX.success;

import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.model.controller.admin.modelform.nocopy.vo.*;
import com.mongoso.mgs.module.model.service.modelform.nocopy.ModelFormNoCopyService;

/**
 * 单据不可复制字段 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/model")
@Validated
public class ModelFormNoCopyController {

    @Resource
    private ModelFormNoCopyService formNoCopyService;

    @OperateLog("单据不可复制字段添加或编辑")
    @PostMapping("/modelFormNoCopyAdit")
    @PreAuthorize("@ss.hasPermission('modelFormNoCopy:adit')")
    public ResultX<Long> modelFormNoCopyAdit(@Valid @RequestBody ModelFormNoCopyAditReqVO reqVO) {
        return success(reqVO.getDataId() == null
                            ? formNoCopyService.modelFormNoCopyAdd(reqVO)
                            : formNoCopyService.modelFormNoCopyEdit(reqVO));
    }

    @OperateLog("单据不可复制字段删除")
    @PostMapping("/modelFormNoCopyDelete")
    @PreAuthorize("@ss.hasPermission('modelFormNoCopy:delete')")
    public ResultX<Boolean> modelFormNoCopyDelete(@Valid @RequestBody ModelFormNoCopyPrimaryReqVO reqVO) {
        formNoCopyService.modelFormNoCopyDelete(reqVO.getDataId());
        return success(true);
    }

    @OperateLog("单据不可复制字段详情")
    @PostMapping("/modelFormNoCopyDetail")
    @PreAuthorize("@ss.hasPermission('modelFormNoCopy:query')")
    public ResultX<ModelFormNoCopyRespVO> modelFormNoCopyDetail(@Valid @RequestBody ModelFormNoCopyPrimaryReqVO reqVO) {
        return success(formNoCopyService.modelFormNoCopyDetail(reqVO.getDataId()));
    }

    @OperateLog("单据不可复制字段列表")
    @PostMapping("/modelFormNoCopyList")
    @PreAuthorize("@ss.hasPermission('modelFormNoCopy:query')")
    public ResultX<List<ModelFormNoCopyRespVO>> modelFormNoCopyList(@Valid @RequestBody ModelFormNoCopyQueryReqVO reqVO) {
        return success(formNoCopyService.modelFormNoCopyList(reqVO));
    }

    @OperateLog("单据不可复制字段分页")
    @PostMapping("/modelFormNoCopyPage")
    @PreAuthorize("@ss.hasPermission('modelFormNoCopy:query')")
    public ResultX<PageResult<ModelFormNoCopyRespVO>> modelFormNoCopyPage(@Valid @RequestBody ModelFormNoCopyPageReqVO reqVO) {
        return success(formNoCopyService.modelFormNoCopyPage(reqVO));
    }

}
