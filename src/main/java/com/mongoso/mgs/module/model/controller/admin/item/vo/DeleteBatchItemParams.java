package com.mongoso.mgs.module.model.controller.admin.item.vo;

import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 批量删除item
 * daijinbiao
 * 2025-3-27 13:40:48
 */
@Data
public class DeleteBatchItemParams implements Serializable {
	
    private static final long serialVersionUID = 1L;

    @NotEmpty(message = "选择项不能为空")
    private List<Long> itemIds;
}
