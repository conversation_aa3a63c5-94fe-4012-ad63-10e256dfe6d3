package com.mongoso.mgs.module.model.service.modelbiztable.detail;

import java.util.*;
import jakarta.validation.*;
import com.mongoso.mgs.module.model.controller.admin.modelbiztable.detail.vo.*;
import com.mongoso.mgs.framework.common.domain.PageResult;

/**
 * 子表明细 Service 接口
 *
 * <AUTHOR>
 */
public interface ModelBizTableDetailService {

    /**
     * 创建子表明细
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long modelBizTableDetailAdd(@Valid ModelBizTableDetailAditReqVO reqVO);

    /**
     * 更新子表明细
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long modelBizTableDetailEdit(@Valid ModelBizTableDetailAditReqVO reqVO);

    /**
     * 删除子表明细
     *
     * @param dataId 编号
     */
    void modelBizTableDetailDelete(Long dataId);

    /**
     * 获得子表明细信息
     *
     * @param dataId 编号
     * @return 子表明细信息
     */
    ModelBizTableDetailRespVO modelBizTableDetailDetail(Long dataId);

    /**
     * 获得子表明细列表
     *
     * @param reqVO 查询条件
     * @return 子表明细列表
     */
    List<ModelBizTableDetailRespVO> modelBizTableDetailList(@Valid ModelBizTableDetailQueryReqVO reqVO);

    /**
     * 获得子表明细分页
     *
     * @param reqVO 查询条件
     * @return 子表明细分页
     */
    PageResult<ModelBizTableDetailRespVO> modelBizTableDetailPage(@Valid ModelBizTableDetailPageReqVO reqVO);

}
