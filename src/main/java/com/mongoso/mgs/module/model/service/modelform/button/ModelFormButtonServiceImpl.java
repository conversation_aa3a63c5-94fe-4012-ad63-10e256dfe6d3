package com.mongoso.mgs.module.model.service.modelform.button;

import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.mongoso.mgs.module.model.controller.admin.modelform.button.vo.*;
import com.mongoso.mgs.module.model.dal.db.modelform.button.ModelFormButtonDO;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.module.model.dal.mysql.modelform.button.ModelFormButtonMapper;

// import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exception;
import com.mongoso.mgs.framework.common.exception.BizException;
// import static com.mongoso.mgs.module.model.enums.ErrorCodeConstants.*;


/**
 * 单据建模按钮配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ModelFormButtonServiceImpl implements ModelFormButtonService {

    @Resource
    private ModelFormButtonMapper formButtonMapper;

    @Override
    public Long modelFormButtonAdd(ModelFormButtonAditReqVO reqVO) {
        // 插入
        ModelFormButtonDO formButton = BeanUtilX.copy(reqVO, ModelFormButtonDO::new);
        formButtonMapper.insert(formButton);
        // 返回
        return formButton.getDataId();
    }

    @Override
    public Long modelFormButtonEdit(ModelFormButtonAditReqVO reqVO) {
        // 校验存在
        this.modelFormButtonValidateExists(reqVO.getDataId());
        // 更新
        ModelFormButtonDO formButton = BeanUtilX.copy(reqVO, ModelFormButtonDO::new);
        formButtonMapper.updateById(formButton);
        // 返回
        return formButton.getDataId();
    }

    @Override
    public void modelFormButtonDelete(Long dataId) {
        // 校验存在
        this.modelFormButtonValidateExists(dataId);
        // 删除
        formButtonMapper.deleteById(dataId);
    }

    private ModelFormButtonDO modelFormButtonValidateExists(Long dataId) {
        ModelFormButtonDO formButton = formButtonMapper.selectById(dataId);
        if (formButton == null) {
            // throw exception(FORM_BUTTON_NOT_EXISTS);
            throw new BizException("5001", "单据建模按钮配置不存在");
        }
        return formButton;
    }

    @Override
    public ModelFormButtonRespVO modelFormButtonDetail(Long dataId) {
        ModelFormButtonDO data = formButtonMapper.selectById(dataId);
        return BeanUtilX.copy(data, ModelFormButtonRespVO::new);
    }

    @Override
    public List<ModelFormButtonRespVO> modelFormButtonList(ModelFormButtonQueryReqVO reqVO) {
        List<ModelFormButtonDO> data = formButtonMapper.selectList(reqVO);
        return BeanUtilX.copy(data, ModelFormButtonRespVO::new);
    }

    @Override
    public PageResult<ModelFormButtonRespVO> modelFormButtonPage(ModelFormButtonPageReqVO reqVO) {
        PageResult<ModelFormButtonDO> data = formButtonMapper.selectPage(reqVO);
        return BeanUtilX.copy(data, ModelFormButtonRespVO::new);
    }

    @Override
    public void modelFormButtonBatchAdd(List<ModelFormButtonDO> buttonDOList) {
        if (buttonDOList != null && !buttonDOList.isEmpty()) {
            formButtonMapper.insertBatch(buttonDOList);
        }
    }

    @Override
    public void modelFormButtonBatchDeleteByCode(String modelFormCode) {
        if (modelFormCode != null && !modelFormCode.trim().isEmpty()) {
            formButtonMapper.deleteByModelFormCode(modelFormCode);
        }
    }

}
