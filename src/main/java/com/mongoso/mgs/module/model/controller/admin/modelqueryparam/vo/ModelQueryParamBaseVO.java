package com.mongoso.mgs.module.model.controller.admin.modelqueryparam.vo;

import lombok.*;

import jakarta.validation.constraints.*;
import java.io.Serializable;

/**
 * 自定义查询参数 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class ModelQueryParamBaseVO implements Serializable {

    /** 主键ID */
    private Long id;

    /** 查询id */
    @NotNull(message = "查询id不能为空")
    private Long queryId;

    /** 参数英文名 */
    @NotEmpty(message = "参数英文名不能为空")
    private String paramCode;

    /** 参数中文名 */
    @NotEmpty(message = "参数中文名不能为空")
    private String paramName;

    /** 参数模式 */
    @NotNull(message = "参数模式不能为空")
    private Integer paramType;

    /** 数据类型 */
    private String dataType;

    /** 长度 */
    private Integer leng;

    /** 精度 */
    private Integer fieldPrecision;

    /** 数据源 */
    private String dataTable;
    private Long tableId;

    /** 数据列 */
    private String dataFields;
    private String dataFieldStr;

    /** 备注 */
    private String remark;

}
