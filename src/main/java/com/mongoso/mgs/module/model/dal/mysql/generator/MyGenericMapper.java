package com.mongoso.mgs.module.model.dal.mysql.generator;

import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/2/14
 * @description 验证一个通用的建模表实现的Mapper
 */

@Mapper
public interface MyGenericMapper {

   // 使用@Insert注解定义一个插入数据的SQL语句
   // 定义一个方法insertData，用于插入数据
   // @Param注解用于将方法参数传递给SQL语句中的占位符
   // tableCode: 表名
   // fields: 插入的字段名
   // values: 插入的字段值
    @Insert("INSERT INTO lowcode.${tableCode} (${fields}) VALUES (${values})")
    int insertData(@Param("tableCode") String tableCode, @Param("fields") String fields, @Param("values") String values);

    // 批量插入方法
    @Insert({
            "<script>",
            "INSERT INTO lowcode.${tableCode} (${fields}) VALUES ",
            "<foreach collection='valueList' item='item' separator=','>",
            "(#{item})",
            "</foreach>",
            "</script>"
    })
    int batchInsertData(@Param("tableCode") String tableCode, @Param("fields") String fields, @Param("valueList") List<String> valueList);


    // 使用@Select注解定义一个查询数据的SQL语句
    // 定义一个方法selectData，用于查询数据
    // @Param注解用于将方法参数传递给SQL语句中的占位符
    // tableCode: 表名
    // id: 查询的记录ID
    @Select("SELECT * FROM lowcode.${tableCode} WHERE ${pk} = #{id}")
    Map<String, Object> selectData(@Param("tableCode") String tableCode, @Param("pk") String pk, @Param("id") Long id);

    // 使用@Update注解定义一个更新数据的SQL语句
    // 定义一个方法updateData，用于更新数据
    // @Param注解用于将方法参数传递给SQL语句中的占位符
    // tableCode: 表名
    // id: 更新的记录ID
    // updateFields: 更新的字段和值
    @Update("UPDATE lowcode.${tableCode} SET ${updateFields} WHERE ${pk} = #{id}")
    int updateData(@Param("tableCode") String tableCode, @Param("pk") String pk, @Param("id") Long id, @Param("updateFields") String updateFields);

    // 使用@Delete注解定义一个删除数据的SQL语句
    // 定义一个方法deleteData，用于删除数据
    // @Param注解用于将方法参数传递给SQL语句中的占位符
    // tableCode: 表名
    // id: 删除的记录ID
    @Delete("DELETE FROM lowcode.${tableCode} WHERE ${pk} = #{id}")
    int deleteData(@Param("tableCode") String tableCode, @Param("pk") String pk, @Param("id") Long id);

    @Select({// 添加 ORDER BY，以符合 PostgreSQL 对 OFFSET 的要求
           """
           <script>
           SELECT * FROM lowcode.${tableCode}
           <if test='filter != null and filter != ""'>
           WHERE ${filter}
           </if>
           ORDER BY (SELECT NULL)
           LIMIT #{pageSize} OFFSET #{offset}
           </script>
           """
   })
   List<Map<String, Object>> selectPage(@Param("tableCode") String tableCode,
                                        @Param("filter") String filter,
                                        @Param("offset") int offset,
                                        @Param("pageSize") int pageSize);

   @Select({
           """
            <script>
            SELECT * FROM lowcode.${tableCode}
            <if test='filter != null and filter != ""'>
            WHERE ${filter}
            </if>
            </script>
            """
    })
    List<Map<String, Object>> selectAll(@Param("tableCode") String tableCode, @Param("filter") String filter);

   @Select({
           """
            <script>
            SELECT count(*) FROM lowcode.${tableCode}
            <if test='filter != null and filter != ""'>
            WHERE ${filter}
            </if>
            </script>
            """
    })
    Long selectCount(@Param("tableCode") String tableCode, @Param("filter") String filter);
}
