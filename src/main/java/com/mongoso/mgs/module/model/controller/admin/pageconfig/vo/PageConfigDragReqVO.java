package com.mongoso.mgs.module.model.controller.admin.pageconfig.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * @Author: Fashion.liu
 * @Date: 2024/12/11 15:58
 * @Description: 拖动页面配置
 */
@Data
public class PageConfigDragReqVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     *  拖动ID
     */
    private Long itemId;

    /**
     *  拖动目标ID
     */
    private Long destItemId;

    /**
     *  拖动类型 0-目标之前  1-目标之后
     */
    private Integer dragType;
}