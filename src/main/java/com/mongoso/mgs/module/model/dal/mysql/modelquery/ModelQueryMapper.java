package com.mongoso.mgs.module.model.dal.mysql.modelquery;

import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.module.model.dal.db.modelquery.ModelQueryDO;
import com.mongoso.mgs.module.model.dal.db.modeltable.ModelTableDO;
import org.apache.ibatis.annotations.Mapper;
import com.mongoso.mgs.module.model.controller.admin.modelquery.vo.*;

import jakarta.validation.constraints.NotEmpty;

/**
 * 自定义查询 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ModelQueryMapper extends BaseMapperX<ModelQueryDO> {

    default PageResult<ModelQueryDO> selectPage(ModelQueryPageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<ModelQueryDO>lambdaQueryX()
                .eqIfPresent(ModelQueryDO::getDataSourceConfigId, reqVO.getDataSourceConfigId())
                .eqIfPresent(ModelQueryDO::getQueryCode, reqVO.getQueryCode())
                .likeIfPresent(ModelQueryDO::getQueryName, reqVO.getQueryName())
                .eqIfPresent(ModelQueryDO::getQueryStatment, reqVO.getQueryStatment())
                .eqIfPresent(ModelQueryDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(ModelQueryDO::getCreatedDt, reqVO.getCreatedDt())
                .orderByDesc(ModelQueryDO::getCreatedDt));
    }

    default List<ModelQueryDO> selectList(ModelQueryQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<ModelQueryDO>lambdaQueryX()
                .eqIfPresent(ModelQueryDO::getQueryCode, reqVO.getQueryCode())
                .eqIfPresent(ModelQueryDO::getDataSourceConfigId, reqVO.getDataSourceConfigId())
                .likeIfPresent(ModelQueryDO::getQueryName, reqVO.getQueryName())
                .eqIfPresent(ModelQueryDO::getQueryStatment, reqVO.getQueryStatment())
                .eqIfPresent(ModelQueryDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(ModelQueryDO::getCreatedDt, reqVO.getCreatedDt())
                    .orderByDesc(ModelQueryDO::getCreatedDt));
    }

    default long selectCountByName(String queryName, Long queryId){
        return selectCount(LambdaQueryWrapperX.<ModelQueryDO>lambdaQueryX()
                .eq(ModelQueryDO::getQueryName, queryName)
                .neIfPresent(ModelQueryDO::getQueryId, queryId));
    }

    default long selectCountByCode(String queryCode, Long queryId){
        return selectCount(LambdaQueryWrapperX.<ModelQueryDO>lambdaQueryX()
                .eq(ModelQueryDO::getQueryCode, queryCode)
                .neIfPresent(ModelQueryDO::getQueryId, queryId));
    }
}