package com.mongoso.mgs.module.model.service.modelqueryresult;

import java.util.*;
import jakarta.validation.*;
import com.mongoso.mgs.module.model.controller.admin.modelqueryresult.vo.*;
import com.mongoso.mgs.module.model.dal.db.modelqueryresult.ModelQueryResultDO;
import com.mongoso.mgs.framework.common.domain.PageResult;

/**
 * 自定义查询结果 Service 接口
 *
 * <AUTHOR>
 */
public interface ModelQueryResultService {

    /**
     * 创建自定义查询结果
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long modelQueryResultAdd(@Valid ModelQueryResultAditReqVO reqVO);

    /**
     * 更新自定义查询结果
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long modelQueryResultEdit(@Valid ModelQueryResultAditReqVO reqVO);

    /**
     * 删除自定义查询结果
     *
     * @param id 编号
     */
    void modelQueryResultDel(Long id);

    /**
     * 获得自定义查询结果信息
     *
     * @param id 编号
     * @return 自定义查询结果信息
     */
    ModelQueryResultDO modelQueryResultDetail(Long id);

    /**
     * 获得自定义查询结果列表
     *
     * @param reqVO 查询条件
     * @return 自定义查询结果列表
     */
    List<ModelQueryResultDO> modelQueryResultList(@Valid ModelQueryResultQueryReqVO reqVO);

    /**
     * 获得自定义查询结果分页
     *
     * @param reqVO 查询条件
     * @return 自定义查询结果分页
     */
    PageResult<ModelQueryResultDO> modelQueryResultPage(@Valid ModelQueryResultPageReqVO reqVO);

}
