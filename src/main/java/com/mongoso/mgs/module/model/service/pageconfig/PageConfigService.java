package com.mongoso.mgs.module.model.service.pageconfig;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.module.model.controller.admin.pageconfig.vo.*;
import com.mongoso.mgs.module.model.dal.db.pageconfig.PageConfigDO;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 页面配置 Service 接口
 *
 * <AUTHOR>
 */
public interface PageConfigService {

    /**
     * 创建页面配置
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    PageConfigRespVO pageConfigAdd(@Valid PageConfigAditReqVO reqVO);

    /**
     * 更新页面配置
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    PageConfigRespVO pageConfigEdit(@Valid PageConfigAditReqVO reqVO);

    /**
     * 删除页面配置
     *
     * @param id 编号
     */
    void pageConfigDel(Long id);

    /**
     * 获得页面配置信息
     *
     * @param reqVO 请求参数
     * @return 页面配置信息
     */
    PageConfigDO pageConfigDetail(PageConfigDetailReqVO reqVO);

    /**
     * 获得页面配置列表
     *
     * @param reqVO 查询条件
     * @return 页面配置列表
     */
    List<PageConfigDO> pageConfigList(@Valid PageConfigQueryReqVO reqVO);

    /**
     * 获得页面配置树
     *
     * @param reqVO 查询条件
     * @return 页面配置列表
     */
    List<PageConfigRespVO> pageConfigTree(PageConfigQueryReqVO reqVO);

    /**
     * 获得页面配置分页
     *
     * @param reqVO 查询条件
     * @return 页面配置分页
     */
    PageResult<PageConfigDO> pageConfigPage(@Valid PageConfigPageReqVO reqVO);

    /**
     * 移动页面配置
     * @param reqVO 请求参数
     */
    void pageConfigMove(PageConfigMoveReqVO reqVO);

    /**
     * 拖动页面配置
     * @param reqVO 请求参数
     */
    void pageConfigDrag(PageConfigDragReqVO reqVO);

}
