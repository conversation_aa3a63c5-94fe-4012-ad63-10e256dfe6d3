package com.mongoso.mgs.module.model.dal.mysql.modelfunction;

import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.module.model.dal.db.modelfunction.ModelFunctionDO;
import org.apache.ibatis.annotations.Mapper;
import com.mongoso.mgs.module.model.controller.admin.modelfunction.vo.*;

/**
 * 自定义函数 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ModelFunctionMapper extends BaseMapperX<ModelFunctionDO> {

    default PageResult<ModelFunctionDO> selectPage(ModelFunctionPageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<ModelFunctionDO>lambdaQueryX()
                .likeIfPresent(ModelFunctionDO::getFunName, reqVO.getFunName())
                .eqIfPresent(ModelFunctionDO::getFunCode, reqVO.getFunCode())
                .eqIfPresent(ModelFunctionDO::getFunBody, reqVO.getFunBody())
                .eqIfPresent(ModelFunctionDO::getRunEnv, reqVO.getRunEnv())
                .eqIfPresent(ModelFunctionDO::getTimeout, reqVO.getTimeout())
                .eqIfPresent(ModelFunctionDO::getRemark, reqVO.getRemark())
                .eqIfPresent(ModelFunctionDO::getVersionNo, reqVO.getVersionNo())
                .eqIfPresent(ModelFunctionDO::getIsPublish, reqVO.getIsPublish())
                .eqIfPresent(ModelFunctionDO::getDirType, reqVO.getDirType())
                .eqIfPresent(ModelFunctionDO::getParentId, reqVO.getParentId())
                .eqIfPresent(ModelFunctionDO::getPropType, reqVO.getPropType())
                .betweenIfPresent(ModelFunctionDO::getCreatedDt, reqVO.getCreatedDt())
                .orderByDesc(ModelFunctionDO::getCreatedDt));
    }

    default List<ModelFunctionDO> selectList(ModelFunctionQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<ModelFunctionDO>lambdaQueryX()
                .likeIfPresent(ModelFunctionDO::getFunName, reqVO.getFunName())
                .eqIfPresent(ModelFunctionDO::getFunCode, reqVO.getFunCode())
                .eqIfPresent(ModelFunctionDO::getFunBody, reqVO.getFunBody())
                .eqIfPresent(ModelFunctionDO::getRunEnv, reqVO.getRunEnv())
                .eqIfPresent(ModelFunctionDO::getTimeout, reqVO.getTimeout())
                .eqIfPresent(ModelFunctionDO::getRemark, reqVO.getRemark())
                .eqIfPresent(ModelFunctionDO::getVersionNo, reqVO.getVersionNo())
                .eqIfPresent(ModelFunctionDO::getIsPublish, reqVO.getIsPublish())
                .eqIfPresent(ModelFunctionDO::getDirType, reqVO.getDirType())
                .eqIfPresent(ModelFunctionDO::getParentId, reqVO.getParentId())
                .eqIfPresent(ModelFunctionDO::getPropType, reqVO.getPropType())
                .betweenIfPresent(ModelFunctionDO::getCreatedDt, reqVO.getCreatedDt())
                    .orderByDesc(ModelFunctionDO::getCreatedDt));
    }

}