package com.mongoso.mgs.module.model.controller.admin.pageconfig.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 页面配置 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class PageConfigBaseVO implements Serializable {

    /** id */
    @JsonProperty(value = "itemId")
    private Long id;

    /** id */
    @JsonProperty(value = "parentItemId")
    private Long parentId = 0L;

    @NotNull(message = "项目id不能为空")
    @JsonProperty(value = "projectId")
    private Long projectId;

    /** 路由 */
    @JsonProperty(value = "itemName")
    private String name;

    /** 内容 */
    @JsonProperty(value = "itemContent")
    private String content;


    /** 路径标识 */
    @JsonProperty(value = "routerPath")
    private String routerPath;

    /** 是否显示在菜单里    枚举    0 否  1是 */
    @JsonProperty(value = "isShow")
    private Integer isShow = 1;
    private Integer itemType;
}
