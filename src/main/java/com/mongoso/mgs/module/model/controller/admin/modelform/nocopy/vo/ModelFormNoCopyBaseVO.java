package com.mongoso.mgs.module.model.controller.admin.modelform.nocopy.vo;

import lombok.*;

import jakarta.validation.constraints.*;
import java.io.Serializable;

  

/**
 * 单据不可复制字段 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class ModelFormNoCopyBaseVO implements Serializable {

    /** 单据建模编码 */
    private String modelFormCode;

    /** 业务建模编码 */
    private String modelBizCode;

    /** 表id */
    private Long tableId;

    /** 行号 */
    private Integer rowNo;

    /** 排序 */
    private Integer sort;

    /** 字段英文名称 */
    private String columnCode;

    /** 字段中文名称 */
    private String columnName;

    /** 备注 */
    private String remark;

    /** 主键id */
//    @NotNull(message = "主键id不能为空")
    private Long dataId;

}
