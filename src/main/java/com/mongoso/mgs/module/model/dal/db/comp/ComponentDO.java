package com.mongoso.mgs.module.model.dal.db.comp;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serializable;

/**
 * 前端组件
 *
 * <AUTHOR>
 */
@TableName(value = "sys_project_comp")
//@KeySequence("sys_model_field_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ComponentDO implements Serializable {

    /** 组件id */
    @TableId(type = IdType.ASSIGN_ID)
    private Long compId;

    /** 组件集合 */
    private String cmptList;
}
