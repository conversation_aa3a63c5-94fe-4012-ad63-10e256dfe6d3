package com.mongoso.mgs.module.model.service.modelfieldalter;

import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.mongoso.mgs.module.model.controller.admin.modelfieldalter.vo.*;
import com.mongoso.mgs.module.model.dal.db.modelfieldalter.ModelFieldAlterDO;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.module.model.dal.mysql.modelfieldalter.ModelFieldAlterMapper;

import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exception;
import static com.mongoso.mgs.module.model.enums.ErrorCodeConstants.*;

/**
 * 图形建模字段 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ModelFieldAlterServiceImpl implements ModelFieldAlterService {

    @Resource
    private ModelFieldAlterMapper fieldAlterMapper;

    @Override
    public Long modelFieldAlterAdd(ModelFieldAlterAditReqVO reqVO) {
        // 插入
        ModelFieldAlterDO fieldAlter = BeanUtilX.copy(reqVO, ModelFieldAlterDO::new);
        fieldAlterMapper.insert(fieldAlter);
        // 返回
        return fieldAlter.getId();
    }

    @Override
    public Long modelFieldAlterEdit(ModelFieldAlterAditReqVO reqVO) {
        // 校验存在
        this.modelFieldAlterValidateExists(reqVO.getId());
        // 更新
        ModelFieldAlterDO fieldAlter = BeanUtilX.copy(reqVO, ModelFieldAlterDO::new);
        fieldAlterMapper.updateById(fieldAlter);
        // 返回
        return fieldAlter.getId();
    }

    @Override
    public void modelFieldAlterDel(Long id) {
        // 校验存在
        this.modelFieldAlterValidateExists(id);
        // 删除
        fieldAlterMapper.deleteById(id);
    }

    private ModelFieldAlterDO modelFieldAlterValidateExists(Long id) {
        ModelFieldAlterDO fieldAlter = fieldAlterMapper.selectById(id);
        if (fieldAlter == null) {
            throw exception(FIELD_ALTER_NOT_EXISTS);
        }
        return fieldAlter;
    }

    @Override
    public ModelFieldAlterDO modelFieldAlterDetail(Long id) {
        return fieldAlterMapper.selectById(id);
    }

    @Override
    public List<ModelFieldAlterDO> modelFieldAlterList(ModelFieldAlterQueryReqVO reqVO) {
        return fieldAlterMapper.selectList(reqVO);
    }

    @Override
    public PageResult<ModelFieldAlterDO> modelFieldAlterPage(ModelFieldAlterPageReqVO reqVO) {
        return fieldAlterMapper.selectPage(reqVO);
    }

}
