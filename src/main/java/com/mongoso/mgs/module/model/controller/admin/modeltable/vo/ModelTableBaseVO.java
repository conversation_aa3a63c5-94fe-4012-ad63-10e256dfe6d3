package com.mongoso.mgs.module.model.controller.admin.modeltable.vo;

import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 图形建模主 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class ModelTableBaseVO implements Serializable {

    /** 模型表id */
    private Long tableId;

    /** 数据源id */
    private Long dataSourceConfigId = 0L;

    //@NotNull(message = "项目不能为空")
    /** 项目id */
    private Long projectId;

    /** 版本 */
    private String version;

    /** 图标 */
    private String icon;

    /** 模型表中文名 */
    @NotEmpty(message = "模型表中文名不能为空")
    private String tableName;

    /** 模型表实名 */
//    @NotEmpty(message = "模型表实名不能为空")
    private String tableCode;

    /** 备注描述 */
    private String remark;

    /** 升级标记 */
//    @NotNull(message = "升级标记不能为空")
    private Integer upgradeFlag = 0;

    /** 是否生成过 */
//    @NotNull(message = "是否生成过不能为空")
    private Integer isGen = 0;

    /** 序列 */
    private Integer seq;

    /** 类型 */
//    @NotNull(message = "类型不能为空")
    private Integer dirType = 1;

    /** 父节点 */
//    @NotNull(message = "父节点不能为空")
    private Long parentId;
    /** 属性类型，0：系统，1：用户 */
    private Integer propType;
}
