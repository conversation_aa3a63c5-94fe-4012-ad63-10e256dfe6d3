package com.mongoso.mgs.module.model.controller.admin.modelfield;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import jakarta.validation.*;
import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import static com.mongoso.mgs.framework.common.domain.ResultX.success;

import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.model.controller.admin.modelfield.vo.*;
import com.mongoso.mgs.module.model.dal.db.modelfield.ModelFieldDO;
import com.mongoso.mgs.module.model.service.modelfield.ModelFieldService;

/**
 * 图形建模字段 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/model")
@Validated
public class ModelFieldController {

    @Resource
    private ModelFieldService fieldService;

    @OperateLog("图形建模字段添加或编辑")
    @PostMapping("/modelFieldAdit")
    @PreAuthorize("@ss.hasPermission('modelField:adit')")
    public ResultX<Long> modelFieldAdit(@Valid @RequestBody ModelFieldAditReqVO reqVO) {
        return success(reqVO.getFieldId() == null
                            ? fieldService.modelFieldAdd(reqVO)
                            : fieldService.modelFieldEdit(reqVO));
    }

    @OperateLog("图形建模字段删除")
    @PostMapping("/modelFieldDel")
    @PreAuthorize("@ss.hasPermission('modelField:del')")
    public ResultX<Boolean> modelFieldDel(@Valid @RequestBody ModelFieldPrimaryReqVO reqVO) {
        fieldService.modelFieldDel(reqVO.getFieldId());
        return success(true);
    }

    @OperateLog("图形建模字段详情")
    @PostMapping("/modelFieldDetail")
    @PreAuthorize("@ss.hasPermission('modelField:query')")
    public ResultX<ModelFieldRespVO> modelFieldDetail(@Valid @RequestBody ModelFieldPrimaryReqVO reqVO) {
        ModelFieldDO oldDO = fieldService.modelFieldDetail(reqVO.getFieldId());
        return success(BeanUtilX.copy(oldDO, ModelFieldRespVO::new));
    }

    @OperateLog("图形建模字段列表")
    @PostMapping("/modelFieldList")
    @PreAuthorize("@ss.hasPermission('modelField:query')")
    public ResultX<List<ModelFieldRespVO>> modelFieldList(@Valid @RequestBody ModelFieldQueryReqVO reqVO) {
        List<ModelFieldDO> list = fieldService.modelFieldList(reqVO);
        return success(BeanUtilX.copyList(list, ModelFieldRespVO::new));
    }

    @OperateLog("图形建模字段分页")
    @PostMapping("/modelFieldPage")
    @PreAuthorize("@ss.hasPermission('modelField:query')")
    public ResultX<PageResult<ModelFieldRespVO>> modelFieldPage(@Valid @RequestBody ModelFieldPageReqVO reqVO) {
        PageResult<ModelFieldDO> pageResult = fieldService.modelFieldPage(reqVO);
        return success(BeanUtilX.copyPage(pageResult, ModelFieldRespVO::new));
    }

}
