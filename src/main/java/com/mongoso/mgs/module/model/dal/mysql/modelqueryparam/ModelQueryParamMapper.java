package com.mongoso.mgs.module.model.dal.mysql.modelqueryparam;

import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.module.model.dal.db.modelquery.ModelQueryDO;
import com.mongoso.mgs.module.model.dal.db.modelqueryparam.ModelQueryParamDO;
import org.apache.ibatis.annotations.Mapper;
import com.mongoso.mgs.module.model.controller.admin.modelqueryparam.vo.*;

import jakarta.validation.constraints.NotEmpty;

/**
 * 自定义查询参数 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ModelQueryParamMapper extends BaseMapperX<ModelQueryParamDO> {

    default PageResult<ModelQueryParamDO> selectPage(ModelQueryParamPageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<ModelQueryParamDO>lambdaQueryX()
                .eqIfPresent(ModelQueryParamDO::getQueryId, reqVO.getQueryId())
                .eqIfPresent(ModelQueryParamDO::getParamCode, reqVO.getParamCode())
                .likeIfPresent(ModelQueryParamDO::getParamName, reqVO.getParamName())
                .eqIfPresent(ModelQueryParamDO::getParamType, reqVO.getParamType())
                .eqIfPresent(ModelQueryParamDO::getDataType, reqVO.getDataType())
                .eqIfPresent(ModelQueryParamDO::getLeng, reqVO.getLeng())
                .eqIfPresent(ModelQueryParamDO::getFieldPrecision, reqVO.getFieldPrecision())
                .eqIfPresent(ModelQueryParamDO::getDataTable, reqVO.getDataTable())
                .eqIfPresent(ModelQueryParamDO::getDataFields, reqVO.getDataFields())
                .eqIfPresent(ModelQueryParamDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(ModelQueryParamDO::getCreatedDt, reqVO.getCreatedDt())
                .orderByDesc(ModelQueryParamDO::getCreatedDt));
    }

    default List<ModelQueryParamDO> selectList(ModelQueryParamQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<ModelQueryParamDO>lambdaQueryX()
                .eqIfPresent(ModelQueryParamDO::getQueryId, reqVO.getQueryId())
                .eqIfPresent(ModelQueryParamDO::getParamCode, reqVO.getParamCode())
                .likeIfPresent(ModelQueryParamDO::getParamName, reqVO.getParamName())
                .eqIfPresent(ModelQueryParamDO::getParamType, reqVO.getParamType())
                .eqIfPresent(ModelQueryParamDO::getDataType, reqVO.getDataType())
                .eqIfPresent(ModelQueryParamDO::getLeng, reqVO.getLeng())
                .eqIfPresent(ModelQueryParamDO::getFieldPrecision, reqVO.getFieldPrecision())
                .eqIfPresent(ModelQueryParamDO::getDataTable, reqVO.getDataTable())
                .eqIfPresent(ModelQueryParamDO::getDataFields, reqVO.getDataFields())
                .eqIfPresent(ModelQueryParamDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(ModelQueryParamDO::getCreatedDt, reqVO.getCreatedDt())
                    .orderByDesc(ModelQueryParamDO::getCreatedDt));
    }

    default long selectCountByCode(String paramCode, Long id){
        return selectCount(LambdaQueryWrapperX.<ModelQueryParamDO>lambdaQueryX()
                .eq(ModelQueryParamDO::getParamCode, paramCode)
                .neIfPresent(ModelQueryParamDO::getId, id));
    }
}