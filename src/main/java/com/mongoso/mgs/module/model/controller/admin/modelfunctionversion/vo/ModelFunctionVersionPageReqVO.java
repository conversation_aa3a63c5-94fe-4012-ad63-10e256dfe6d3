package com.mongoso.mgs.module.model.controller.admin.modelfunctionversion.vo;

import lombok.*;

import com.mongoso.mgs.framework.common.domain.PageParam;












import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 自定义报表版本 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ModelFunctionVersionPageReqVO extends PageParam {

    private Long funId;
    /** 版本号 */
    private Integer versionNo;

    /** 创建时间   */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createdDt;

}
