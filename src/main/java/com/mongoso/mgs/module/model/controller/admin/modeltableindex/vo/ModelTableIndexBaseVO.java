package com.mongoso.mgs.module.model.controller.admin.modeltableindex.vo;

import lombok.*;

import jakarta.validation.constraints.*;
import java.io.Serializable;

/**
 * 图形建模主表索引 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class ModelTableIndexBaseVO implements Serializable {

    /** id */
    private Long id;
    /**
     * 索引ID
     */
    private Long idxId;

    /**
     * 索引名称
     */
    private String idxName;

    /** 模型表id */
//    @NotNull(message = "模型表id不能为空")
    private Long tableId;

    /** 模型表实名 */
//    @NotEmpty(message = "模型表实名不能为空")
    private String tableCode;

    /** 是否生成过 */
//    @NotNull(message = "是否生成过不能为空")
    private Integer nonUnique = 1;

    /** 索引序号 */
//    @NotNull(message = "索引序号不能为空")
    private Integer idxSeq = 1;

    /** 字段id */
    private Long fieldId;
    /** 字段编码 */
    @NotEmpty(message = "字段编码不能为空")
    private String fieldCode;

    /** 排序类型(asc,desc) */
//    @NotEmpty(message = "排序类型(asc,desc,'')不能为空")
    private String sortType;

    /** 索引类型(betree,hash,'') */
//    @NotEmpty(message = "索引类型不能为空")
    private String idxType = "";
    /** 索引类型(betree,hash,'') */
    private String idxWay = "";

    /** 备注描述 */
    private String remark = "";

}
