package com.mongoso.mgs.module.model.controller.admin.modeltable.vo;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/2/6
 * @description
 */
@Data
public class UpdateTaskMindDetailParams {
    private static final long serialVersionUID = 1L;

    private Integer taskId;

    private Integer mindType = 0;

    private Integer apiType;

    private String apiUrl;

    private String itemContent;

    private String createUser;

    private Date createTime;
}
