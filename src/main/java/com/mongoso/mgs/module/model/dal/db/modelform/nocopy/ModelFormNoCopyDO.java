package com.mongoso.mgs.module.model.dal.db.modelform.nocopy;

import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;
import lombok.*;

import com.baomidou.mybatisplus.annotation.*;


/**
 * 单据不可复制字段 DO
 *
 * <AUTHOR>
 */
@TableName(value = "lowcode.sys_model_form_no_copy", autoResultMap = true)
//@KeySequence("sys_model_form_no_copy_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ModelFormNoCopyDO extends OperateDO {

    /** 单据建模编码 */
    private String modelFormCode;

    /** 业务建模编码 */
    private String modelBizCode;

    /** 表id */
    private Long tableId;

    /** 行号 */
    private Integer rowNo;

    /** 排序 */
    private Integer sort;

    /** 字段英文名称 */
    private String columnCode;

    /** 字段中文名称 */
    private String columnName;

    /** 备注 */
    private String remark;

    /** 主键id */
        @TableId(type = IdType.ASSIGN_ID)
    private Long dataId;


}
