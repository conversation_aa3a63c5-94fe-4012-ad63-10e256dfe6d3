package com.mongoso.mgs.module.model.service.pageconfig;

import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.module.model.controller.admin.pageconfig.vo.*;
import com.mongoso.mgs.module.model.dal.db.pageconfig.PageConfigDO;
import com.mongoso.mgs.module.model.dal.mysql.pageconfig.PageConfigMapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 页面配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PageConfigServiceImpl implements PageConfigService {

    @Resource
    private PageConfigMapper pageConfigMapper;

    @Override
    public PageConfigRespVO pageConfigAdd(PageConfigAditReqVO reqVO) {

//        Long count = pageConfigMapper.selectCount(reqVO.getName());
//        if(count > 0 ){
//            throw BizExceptionUtilX.exception(ErrorCodeConstants.PAGE_CONFIG_ROUTE_ALREADY_EXISTS);
//        }

        Long seq = pageConfigMapper.getMaxSeq(reqVO.getParentId(), reqVO.getProjectId());
        if(seq == null){
            seq = 1L;
        }else{
            seq++;
        }

        // 插入
        PageConfigDO pageConfig = BeanUtilX.copy(reqVO, PageConfigDO::new);
        pageConfig.setSeq(seq);
        pageConfigMapper.insert(pageConfig);
        // 返回
        return BeanUtilX.copy(pageConfig, PageConfigRespVO:: new);
    }

    @Override
    public PageConfigRespVO pageConfigEdit(PageConfigAditReqVO reqVO) {
        // 校验存在
        this.pageConfigValidateExists(reqVO.getId());
        // 更新
        PageConfigDO pageConfig = BeanUtilX.copy(reqVO, PageConfigDO::new);
        pageConfigMapper.updateById(pageConfig);
        // 返回
        return BeanUtilX.copy(pageConfig, PageConfigRespVO:: new);
    }

    @Override
    public void pageConfigDel(Long id) {
        // 校验存在
        this.pageConfigValidateExists(id);
        // 删除
        pageConfigMapper.deleteById(id);
    }

    private PageConfigDO pageConfigValidateExists(Long id) {
        PageConfigDO pageConfig = pageConfigMapper.selectById(id);
        if (pageConfig == null) {
            throw new BizException("5001", "页面配置不存在");
        }
        return pageConfig;
    }

    @Override
    public PageConfigDO pageConfigDetail(PageConfigDetailReqVO reqVO) {
        return pageConfigMapper.selectById(reqVO.getId());
    }

    @Override
    public List<PageConfigDO> pageConfigList(PageConfigQueryReqVO reqVO) {
        return pageConfigMapper.selectList(reqVO);
    }

    @Override
    public List<PageConfigRespVO> pageConfigTree(PageConfigQueryReqVO reqVO) {
        List<PageConfigRespVO> respVOList = BeanUtilX.copy(pageConfigMapper.selectList(reqVO), PageConfigRespVO :: new);

        List<PageConfigRespVO> newRespVOList = new ArrayList<>();
        respVOList.stream().map((parent) -> {
            if (parent.getParentId().equals(0L)) {
                newRespVOList.add(parent);
            }
            return parent;
        }).forEach((parent) -> {
            respVOList.stream().filter((child) -> (parent.getId().equals(child.getParentId()))).forEach((child) -> {
                parent.getChildren().add(child);
            });
        });
        return newRespVOList;
    }


    @Override
    public PageResult<PageConfigDO> pageConfigPage(PageConfigPageReqVO reqVO) {
        return pageConfigMapper.selectPage(reqVO);
    }

    @Override
    public void pageConfigMove(PageConfigMoveReqVO reqVO){

        PageConfigDO pageConfigDO = pageConfigMapper.selectById(reqVO.getItemId());
        pageConfigDO.setParentId(reqVO.getParentItemId());

        // 获取最大seq
        Long seq = pageConfigMapper.getMaxSeq(reqVO.getParentItemId(), pageConfigDO.getProjectId());
        if(seq == null){
            seq = 1L;
        }else{
            seq ++;
        }
        pageConfigDO.setSeq(seq);

        LoginUser loginUser =  WebFrameworkUtilX.getLoginUser();
        pageConfigDO.setUpdatedBy(loginUser.getFullUserName());
        pageConfigDO.setUpdatedDt(LocalDateTime.now());
        pageConfigMapper.updateById(pageConfigDO);

    }

    @Override
    public void pageConfigDrag(PageConfigDragReqVO reqVO){
        PageConfigDO destPageConfigDO = pageConfigMapper.selectById(reqVO.getDestItemId());
        // 更新排序
        if (reqVO.getDragType() == 0) {
            pageConfigMapper.updateSeqAddOne(destPageConfigDO.getParentId(), destPageConfigDO.getSeq() - 1, destPageConfigDO.getProjectId());
        } else {
            pageConfigMapper.updateSeqAddOne(destPageConfigDO.getParentId(), destPageConfigDO.getSeq(), destPageConfigDO.getProjectId());
        }

        //更新排序
        PageConfigDO pageConfigDO = pageConfigMapper.selectById(reqVO.getItemId());
        pageConfigDO.setParentId(destPageConfigDO.getParentId());
        Long seq = reqVO.getDragType() == 0 ? destPageConfigDO.getSeq() : destPageConfigDO.getSeq() + 1;
        LoginUser loginUser =  WebFrameworkUtilX.getLoginUser();
        pageConfigDO.setUpdatedBy(loginUser.getFullUserName());
        pageConfigDO.setUpdatedDt(LocalDateTime.now());
        pageConfigDO.setSeq(seq);
        pageConfigMapper.updateById(pageConfigDO);
    }

}
