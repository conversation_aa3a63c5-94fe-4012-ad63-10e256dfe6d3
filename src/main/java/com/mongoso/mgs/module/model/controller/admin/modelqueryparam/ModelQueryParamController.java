package com.mongoso.mgs.module.model.controller.admin.modelqueryparam;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import jakarta.validation.*;
import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import static com.mongoso.mgs.framework.common.domain.ResultX.success;

import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.model.controller.admin.modelqueryparam.vo.*;
import com.mongoso.mgs.module.model.dal.db.modelqueryparam.ModelQueryParamDO;
import com.mongoso.mgs.module.model.service.modelqueryparam.ModelQueryParamService;

/**
 * 自定义查询参数 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/model")
@Validated
public class ModelQueryParamController {

    @Resource
    private ModelQueryParamService queryParamService;

    @OperateLog("自定义查询参数添加或编辑")
    @PostMapping("/modelQueryParamAdit")
    @PreAuthorize("@ss.hasPermission('modelQueryParam:adit')")
    public ResultX<Long> modelQueryParamAdit(@Valid @RequestBody ModelQueryParamAditReqVO reqVO) {
        return success(reqVO.getId() == null
                            ? queryParamService.modelQueryParamAdd(reqVO)
                            : queryParamService.modelQueryParamEdit(reqVO));
    }

    @OperateLog("自定义查询参数删除")
    @PostMapping("/modelQueryParamDel")
    @PreAuthorize("@ss.hasPermission('modelQueryParam:del')")
    public ResultX<Boolean> modelQueryParamDel(@Valid @RequestBody ModelQueryParamPrimaryReqVO reqVO) {
        queryParamService.modelQueryParamDel(reqVO.getId());
        return success(true);
    }

    @OperateLog("自定义查询参数详情")
    @PostMapping("/modelQueryParamDetail")
    @PreAuthorize("@ss.hasPermission('modelQueryParam:query')")
    public ResultX<ModelQueryParamRespVO> modelQueryParamDetail(@Valid @RequestBody ModelQueryParamPrimaryReqVO reqVO) {
        ModelQueryParamDO oldDO = queryParamService.modelQueryParamDetail(reqVO.getId());
        return success(BeanUtilX.copy(oldDO, ModelQueryParamRespVO::new));
    }

    @OperateLog("自定义查询参数列表")
    @PostMapping("/modelQueryParamList")
    @PreAuthorize("@ss.hasPermission('modelQueryParam:query')")
    public ResultX<List<ModelQueryParamRespVO>> modelQueryParamList(@Valid @RequestBody ModelQueryParamQueryReqVO reqVO) {
        List<ModelQueryParamDO> list = queryParamService.modelQueryParamList(reqVO);
        return success(BeanUtilX.copyList(list, ModelQueryParamRespVO::new));
    }

    @OperateLog("自定义查询参数分页")
    @PostMapping("/modelQueryParamPage")
    @PreAuthorize("@ss.hasPermission('modelQueryParam:query')")
    public ResultX<PageResult<ModelQueryParamRespVO>> modelQueryParamPage(@Valid @RequestBody ModelQueryParamPageReqVO reqVO) {
        PageResult<ModelQueryParamDO> pageResult = queryParamService.modelQueryParamPage(reqVO);
        return success(BeanUtilX.copyPage(pageResult, ModelQueryParamRespVO::new));
    }

}
