package com.mongoso.mgs.module.model.dal.mysql.modelfieldconf;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.util.ObjUtilX;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.model.controller.admin.modelfieldconf.vo.ModelFieldConfPageReqVO;
import com.mongoso.mgs.module.model.controller.admin.modelfieldconf.vo.ModelFieldConfQueryReqVO;
import com.mongoso.mgs.module.model.dal.db.modelfieldconf.ModelFieldConfDO;
import com.mongoso.mgs.module.model.dal.db.modelquery.ModelQueryDO;
import jakarta.validation.constraints.NotEmpty;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 图形建模字段翻译配置 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ModelFieldConfMapper extends BaseMapperX<ModelFieldConfDO> {

    default PageResult<ModelFieldConfDO> selectPage(ModelFieldConfPageReqVO reqVO) {
        LambdaQueryWrapperX<ModelFieldConfDO> qw = LambdaQueryWrapperX.<ModelFieldConfDO>lambdaQueryX()
                .likeIfPresent(ModelFieldConfDO::getFieldName, reqVO.getFieldName())
                .likeIfPresent(ModelFieldConfDO::getFieldCode, reqVO.getFieldCode())
                .eqIfPresent(ModelFieldConfDO::getFieldType, reqVO.getFieldType())
                .eqIfPresent(ModelFieldConfDO::getLeng, reqVO.getLeng())
                .eqIfPresent(ModelFieldConfDO::getFieldPrecision, reqVO.getFieldPrecision())
                .eqIfPresent(ModelFieldConfDO::getIsNullable, reqVO.getIsNullable())
                .eqIfPresent(ModelFieldConfDO::getIsPrimaryKey, reqVO.getIsPrimaryKey())
                .eqIfPresent(ModelFieldConfDO::getDefaultVal, reqVO.getDefaultVal())
                .eqIfPresent(ModelFieldConfDO::getSort, reqVO.getSort())
                .eqIfPresent(ModelFieldConfDO::getPropType, reqVO.getPropType())
                .eqIfPresent(ModelFieldConfDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(ModelFieldConfDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .likeIfPresent(ModelFieldConfDO::getFieldTypeName, reqVO.getFieldTypeName())
                .eqIfPresent(ModelFieldConfDO::getJsonFields, reqVO.getJsonFields())
                .likeIfPresent(ModelFieldConfDO::getItemName, reqVO.getItemName())
                .likeIfPresent(ModelFieldConfDO::getEnglishName, reqVO.getEnglishName())
                .eqIfPresent(ModelFieldConfDO::getFieldLength, reqVO.getFieldLength())
                .orderByDesc(ModelFieldConfDO::getCreatedDt);

        if(ObjUtilX.isNotEmpty(reqVO.getFieldConf())) {
            qw.and(i -> i.or().like(ModelFieldConfDO::getFieldCode, reqVO.getFieldConf())
                    .or().like(ModelFieldConfDO::getEnglishName, reqVO.getFieldConf())
                    .or().like(ModelFieldConfDO::getFieldName, reqVO.getFieldConf())
            );
        }
        return selectPage(reqVO, qw);
    }



    //default PageResult<ModelFieldConfDO> selectPage(ModelFieldConfPageReqVO reqVO) {
    //    return jsonbSelectPage(reqVO, LambdaQueryWrapperX.<ModelFieldConfDO>lambdaQueryX()
    //            .likeIfPresent(ModelFieldConfDO::getFieldName, reqVO.getFieldName())
    //            .likeIfPresent(ModelFieldConfDO::getFieldCode, reqVO.getFieldCode())
    //            .eqIfPresent(ModelFieldConfDO::getFieldType, reqVO.getFieldType())
    //            .eqIfPresent(ModelFieldConfDO::getLeng, reqVO.getLeng())
    //            .eqIfPresent(ModelFieldConfDO::getFieldPrecision, reqVO.getFieldPrecision())
    //            .eqIfPresent(ModelFieldConfDO::getIsNullable, reqVO.getIsNullable())
    //            .eqIfPresent(ModelFieldConfDO::getIsPrimaryKey, reqVO.getIsPrimaryKey())
    //            .eqIfPresent(ModelFieldConfDO::getDefaultVal, reqVO.getDefaultVal())
    //            .eqIfPresent(ModelFieldConfDO::getSort, reqVO.getSort())
    //            .eqIfPresent(ModelFieldConfDO::getPropType, reqVO.getPropType())
    //            .eqIfPresent(ModelFieldConfDO::getRemark, reqVO.getRemark())
    //            .betweenIfPresent(ModelFieldConfDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
    //            .likeIfPresent(ModelFieldConfDO::getFieldTypeName, reqVO.getFieldTypeName())
    //            .eqIfPresent(ModelFieldConfDO::getJsonFields, reqVO.getJsonFields())
    //            .likeIfPresent(ModelFieldConfDO::getItemName, reqVO.getItemName())
    //            .likeIfPresent(ModelFieldConfDO::getEnglishName, reqVO.getEnglishName())
    //            .eqIfPresent(ModelFieldConfDO::getFieldLength, reqVO.getFieldLength())
    //            .orderByDesc(ModelFieldConfDO::getCreatedDt));
    //}

    default List<ModelFieldConfDO> selectList(ModelFieldConfQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<ModelFieldConfDO>lambdaQueryX()
                .likeIfPresent(ModelFieldConfDO::getFieldName, reqVO.getFieldName())
                .likeIfPresent(ModelFieldConfDO::getFieldCode, reqVO.getFieldCode())
                .eqIfPresent(ModelFieldConfDO::getFieldType, reqVO.getFieldType())
                .eqIfPresent(ModelFieldConfDO::getLeng, reqVO.getLeng())
                .eqIfPresent(ModelFieldConfDO::getFieldPrecision, reqVO.getFieldPrecision())
                .eqIfPresent(ModelFieldConfDO::getIsNullable, reqVO.getIsNullable())
                .eqIfPresent(ModelFieldConfDO::getIsPrimaryKey, reqVO.getIsPrimaryKey())
                .eqIfPresent(ModelFieldConfDO::getDefaultVal, reqVO.getDefaultVal())
                .eqIfPresent(ModelFieldConfDO::getSort, reqVO.getSort())
                .eqIfPresent(ModelFieldConfDO::getPropType, reqVO.getPropType())
                .eqIfPresent(ModelFieldConfDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(ModelFieldConfDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .likeIfPresent(ModelFieldConfDO::getFieldTypeName, reqVO.getFieldTypeName())
                .eqIfPresent(ModelFieldConfDO::getJsonFields, reqVO.getJsonFields())
                .likeIfPresent(ModelFieldConfDO::getItemName, reqVO.getItemName())
                .likeIfPresent(ModelFieldConfDO::getEnglishName, reqVO.getEnglishName())
                .eqIfPresent(ModelFieldConfDO::getFieldLength, reqVO.getFieldLength())
                .orderByDesc(ModelFieldConfDO::getCreatedDt));
    }

    default long selectCountByName(String name, Long id){
        return selectCount(LambdaQueryWrapperX.<ModelFieldConfDO>lambdaQueryX()
                .eq(ModelFieldConfDO::getFieldName, name)
                .neIfPresent(ModelFieldConfDO::getId, id));
    }

    default long selectCountByCode(String code, Long id){
        return selectCount(LambdaQueryWrapperX.<ModelFieldConfDO>lambdaQueryX()
                .eq(ModelFieldConfDO::getFieldCode, code)
                .neIfPresent(ModelFieldConfDO::getId, id));
    }

    default long selectCountByEnglishName(String code, Long id){
        return selectCount(LambdaQueryWrapperX.<ModelFieldConfDO>lambdaQueryX()
                .eq(ModelFieldConfDO::getEnglishName, code)
                .neIfPresent(ModelFieldConfDO::getId, id));
    }

    //default List<ModelFieldConfDO> selectList(ModelFieldConfQueryReqVO reqVO) {
    //    return jsonbSelectList(reqVO, LambdaQueryWrapperX.<ModelFieldConfDO>lambdaQueryX()
    //            .likeIfPresent(ModelFieldConfDO::getFieldName, reqVO.getFieldName())
    //            .likeIfPresent(ModelFieldConfDO::getFieldCode, reqVO.getFieldCode())
    //            .eqIfPresent(ModelFieldConfDO::getFieldType, reqVO.getFieldType())
    //            .eqIfPresent(ModelFieldConfDO::getLeng, reqVO.getLeng())
    //            .eqIfPresent(ModelFieldConfDO::getFieldPrecision, reqVO.getFieldPrecision())
    //            .eqIfPresent(ModelFieldConfDO::getIsNullable, reqVO.getIsNullable())
    //            .eqIfPresent(ModelFieldConfDO::getIsPrimaryKey, reqVO.getIsPrimaryKey())
    //            .eqIfPresent(ModelFieldConfDO::getDefaultVal, reqVO.getDefaultVal())
    //            .eqIfPresent(ModelFieldConfDO::getSort, reqVO.getSort())
    //            .eqIfPresent(ModelFieldConfDO::getPropType, reqVO.getPropType())
    //            .eqIfPresent(ModelFieldConfDO::getRemark, reqVO.getRemark())
    //            .betweenIfPresent(ModelFieldConfDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
    //            .likeIfPresent(ModelFieldConfDO::getFieldTypeName, reqVO.getFieldTypeName())
    //            .eqIfPresent(ModelFieldConfDO::getJsonFields, reqVO.getJsonFields())
    //            .likeIfPresent(ModelFieldConfDO::getItemName, reqVO.getItemName())
    //            .likeIfPresent(ModelFieldConfDO::getEnglishName, reqVO.getEnglishName())
    //            .eqIfPresent(ModelFieldConfDO::getFieldLength, reqVO.getFieldLength())
    //            .orderByDesc(ModelFieldConfDO::getCreatedDt));
    //}

}