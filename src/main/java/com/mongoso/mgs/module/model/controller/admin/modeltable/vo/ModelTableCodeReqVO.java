package com.mongoso.mgs.module.model.controller.admin.modeltable.vo;

import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * 图形建模主 PrimaryReqVO
 *
 * <AUTHOR>
 */
@Data
public class ModelTableCodeReqVO {

    /** 模型表实名 */
    @NotEmpty(message = "模型表实名不能为空")
    private String tableCode;

    /** 父节点 */
    private Long parentId;

    /** 数据源id */
    private Long dataSourceConfigId = 3L;
}
