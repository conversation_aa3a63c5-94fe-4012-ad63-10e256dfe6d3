package com.mongoso.mgs.module.model.controller.admin.modelfunctionhistory.vo;

import lombok.*;

import jakarta.validation.constraints.*;
import java.io.Serializable;

/**
 * 自定义函数历史 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class ModelFunctionHistoryBaseVO implements Serializable {

    /** 函数id   */
    private Long funId;

    /** 函数名称 */
    @NotEmpty(message = "函数名称不能为空")
    private String funName;

    /** 函数编号 */
    @NotEmpty(message = "函数编号不能为空")
    private String funCode;

    /** 函数主体 */
    private String funBody;

    /** 运行环境   */
    private String runEnv;

    /** 超时时间 */
    private Integer timeout;

    /** 备注   */
    private String remark;

    /** 版本号 */
    @NotNull(message = "版本号不能为空")
    private Integer versionNo;

    /** 是否发布 */
    @NotNull(message = "是否发布不能为空")
    private Integer isPublish;

    /** 是否锁定 */
    @NotNull(message = "是否锁定不能为空")
    private Integer isLock;

    /** 目录类型 */
    @NotNull(message = "目录类型不能为空")
    private Integer dirType;

    /** 父节点 */
    @NotNull(message = "父节点不能为空")
    private Long parentId;

    /** 属性类型，0：系统，1：用户 */
    @NotNull(message = "属性类型，0：系统，1：用户不能为空")
    private Integer propType;

    /** 数据id */
    @NotNull(message = "数据id不能为空")
    private Long dataId;

}
