package com.mongoso.mgs.module.model.controller.admin.modeltable.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 图形建模JSON对象接收
 *
 * <AUTHOR>
 */
@Data
public class ModelTableJsonReqVO implements Serializable {
    private String mindType;
    private String englishName;
    private String dataType;
    private String fieldType;
    private String fieldTypeName;
    private String compositeName;
    private Boolean touched;
    private Boolean copied;
    private Integer level;
    private Integer index;
    private Boolean unexpandable;
    private Boolean editing;
    private String itemName;
    private Boolean dragging;
    private Boolean expanded;
    private Integer startIndex;
    private Boolean readonly;
    private List<Child> children;
    private Boolean partSelected;
    private String sn;
    private Long tableId;


    @Data
    public static class Child implements Serializable {
        private String englishName;
        private String fieldCode;
        private Integer level;
        private Integer dataLength;
        private Integer dataPoint;
        private String dataType;
        private String fieldType;
        private String fieldTypeName;
        /** JSON类型，0：子表，1：主表 */
        private Integer jsonType = 0;
        private String defaultVal;
        private Integer required = 0;
        private Integer index;
        private String itemName;
        private String itemId;
        /** 索引类型(FULLTEXT,NORMAL,UNIQUE) */
        private String idxType;
        /**
         * 索引类型(betree,hash)
         */
        private String idxId;
        private String idxWay;
        private String sn;
        private List<Child> children; // 允许嵌套的子元素
        private Integer isPrimaryKey = 0;
        private String remark;

        private Boolean touched;
        private Boolean copied;
        private String compositeName;
        private Boolean unexpandable;
        private Boolean editing;
        private Boolean dragging;
        private Boolean expanded;
        private Boolean readonly;
        private Boolean partSelected;
        private Boolean inserting;
        private Boolean hasLength;
        private Boolean hasPoint;
        private Boolean isAutoIncrease;
        private Boolean isIndex;
    }
}
