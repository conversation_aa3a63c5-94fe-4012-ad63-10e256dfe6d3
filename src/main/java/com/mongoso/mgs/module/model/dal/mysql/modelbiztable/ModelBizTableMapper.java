package com.mongoso.mgs.module.model.dal.mysql.modelbiztable;

import java.util.*;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.module.model.dal.db.modelbiztable.ModelBizTableDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.mongoso.mgs.module.model.controller.admin.modelbiztable.vo.*;

/**
 * 主 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ModelBizTableMapper extends BaseMapperX<ModelBizTableDO> {

    default PageResult<ModelBizTableDO> selectPage(ModelBizTablePageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<ModelBizTableDO>lambdaQueryX()
                .eqIfPresent(ModelBizTableDO::getTableId, reqVO.getTableId())
                .likeIfPresent(ModelBizTableDO::getBizCode, reqVO.getBizCode())
                .eqIfPresent(ModelBizTableDO::getBizType, reqVO.getBizType())
                .likeIfPresent(ModelBizTableDO::getTableCode, reqVO.getTableCode())
                .likeIfPresent(ModelBizTableDO::getBizName, reqVO.getBizName())
                .eqIfPresent(ModelBizTableDO::getRemark, reqVO.getRemark())
                .orderByDesc(ModelBizTableDO::getDataId));
    }




    default List<ModelBizTableDO> selectList(ModelBizTableQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<ModelBizTableDO>lambdaQueryX()
                .eqIfPresent(ModelBizTableDO::getTableId, reqVO.getTableId())
                .likeIfPresent(ModelBizTableDO::getBizCode, reqVO.getBizCode())
                .eqIfPresent(ModelBizTableDO::getBizType, reqVO.getBizType())
                .likeIfPresent(ModelBizTableDO::getTableCode, reqVO.getTableCode())
                .likeIfPresent(ModelBizTableDO::getBizName, reqVO.getBizName())
                .eqIfPresent(ModelBizTableDO::getRemark, reqVO.getRemark())
                .eqIfPresent(ModelBizTableDO::getDataId, reqVO.getDataId())
                .orderByDesc(ModelBizTableDO::getDataId));
    }

    /**
     * 分页查询主表数据（连表查询获取表名称）
     *
     * @param page 分页参数
     * @param reqVO 查询条件
     * @return 分页结果
     */
    IPage<ModelBizTableRespVO> selectPageWithTableName(IPage<ModelBizTableRespVO> page, @Param("reqVO") ModelBizTablePageReqVO reqVO);


}