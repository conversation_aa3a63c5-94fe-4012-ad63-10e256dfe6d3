package com.mongoso.mgs.module.model.controller.admin.modeltable;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.mongoso.mgs.common.util.TableStructure;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.common.util.ObjUtilX;
import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.model.controller.admin.item.vo.QueryItemParams;
import com.mongoso.mgs.module.model.controller.admin.modeltable.vo.*;
import com.mongoso.mgs.module.model.dal.db.modeltable.ModelTableDO;
import com.mongoso.mgs.module.model.service.modeltable.ModelTableService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.sql.SQLException;
import java.util.List;

import static com.mongoso.mgs.framework.common.domain.ResultX.success;

/**
 * 图形建模主 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/model")
@Validated
public class ModelTableController {

    @Resource
    private ModelTableService tableService;

    @OperateLog("图形建模主添加或编辑")
    @PostMapping("/modelTableAdit")
    @PreAuthorize("@ss.hasPermission('modelTable:adit')")
    public ResultX<Long> modelTableAdit(@Valid @RequestBody ModelTableAditReqVO reqVO) throws SQLException {
        return success(reqVO.getTableId() == null
                ? tableService.modelTableAdd(reqVO)
                : tableService.modelTableEdit(reqVO));
    }
    @OperateLog("图形建模通过信息架构图导入")
    @PostMapping("/modelTableInit")
    @PreAuthorize("@ss.hasPermission('modelTable:adit')")
    public ResultX<Long> modelTableInit(@Valid @RequestBody QueryItemParams reqVO) throws SQLException {
        return success(tableService.modelTableInit(reqVO));
    }

    @OperateLog("图形建模主添加或编辑-思维导图的格式解析")
    @PostMapping("/modelTableAditNew")
    @PreAuthorize("@ss.hasPermission('modelTable:adit')")
    public ResultX<Long> modelTableAditNew(@Valid @RequestBody ModelTableListifyReqVO reqVO) throws SQLException {
        if(ObjUtilX.isEmpty(reqVO.getItemContent()) || "[]".equals(reqVO.getItemContent())) {
            throw new BizException("5001", "请输入内容");
        }
        if(ObjUtilX.isEmpty(reqVO.getTableId())) {
            throw new BizException("5001", "请选择表");
        }
        // 将 itemContent 字符串进一步解析为列表
        List<ModelTableJsonReqVO> itemContent = JSONArray.parseArray(reqVO.getItemContent(), ModelTableJsonReqVO.class);
        //return success(reqVO.getTableId() == null
        //        ? tableService.modelTableAddNew(itemContent.get(0))
        //        : tableService.modelTableEditNew(itemContent.get(0), reqVO.getTableId())
        //);
        // 目前只做编辑
        return success(tableService.modelTableEditNew(itemContent.get(0), reqVO.getTableId()));
    }

    @OperateLog("图形建模主删除")
    @PostMapping("/modelTableDel")
    @PreAuthorize("@ss.hasPermission('modelTable:del')")
    public ResultX<Boolean> modelTableDel(@Valid @RequestBody ModelTablePrimaryReqVO reqVO) {
        tableService.modelTableDel(reqVO.getTableId());
        return success(true);
    }

    @OperateLog("图形建模主批量删除")
    @PostMapping("/modelTableDelBatch")
    @PreAuthorize("@ss.hasPermission('modelTable:del')")
    public ResultX<Boolean> modelTableDelBatch(@Valid @RequestBody ModelTableDelBatchReqVO reqVO) {
        tableService.modelTableDelBatch(reqVO);
        return success(true);
    }

    @OperateLog("图形建模主修改父节点")
    @PostMapping("/modelTableDrag")
    @PreAuthorize("@ss.hasPermission('modelTable:adit')")
    public ResultX<Integer> modelTableDrag(@Valid @RequestBody ModelTableDragReqVO reqVO) {
        return success(tableService.modelTableDrag(reqVO));
    }

    @OperateLog(value="拖动文件")
    @RequestMapping(value = "/dragItem", method = RequestMethod.POST)
    public ResultX<Integer> drayItemInfo(@RequestBody ModelTableDragReqVO reqVO) {
        return success(tableService.dragItemInfo(reqVO));
    }

    @OperateLog("图形建模锁定")
    @PostMapping("/modelTableLock")
    @PreAuthorize("@ss.hasPermission('modelTable:adit')")
    public ResultX<Integer> modelTableLock(@Valid @RequestBody ModelTablePrimaryReqVO reqVO) {
        return success(tableService.modelTableLock(reqVO));
    }

    @OperateLog("图形建模-表格视图")
    @PostMapping("/modelTableDetail")
    @PreAuthorize("@ss.hasPermission('modelTable:query')")
    public ResultX<ModelTableRespVO> modelTableDetail(@Valid @RequestBody ModelTablePrimaryReqVO reqVO) {
        ModelTableRespVO oldDO = tableService.modelTableDetail(reqVO.getTableId());
        return success(oldDO);
    }
    @OperateLog("图形建模-表格视图")
    @PostMapping("/modelTableDbType")
    @PreAuthorize("@ss.hasPermission('modelTable:query')")
    public ResultX<String> modelTableDbType(@Valid @RequestBody ModelTablePrimaryReqVO reqVO) {
        String dbType = tableService.modelTableDbType(reqVO.getTableId());
        return success(dbType);
    }

    @OperateLog("图形建模-思维导图")
    @PostMapping("/modelTableDetailNew")
    @PreAuthorize("@ss.hasPermission('modelTable:query')")
    public ResultX<ModelTableListifyResVO> modelTableDetailNew(@Valid @RequestBody ModelTablePrimaryReqVO reqVO) {
        return success(tableService.modelTableDetailNew(reqVO.getTableId()));
    }

    @OperateLog("图形建模生成文档")
    @PostMapping("/modelTableDoc")
    @PreAuthorize("@ss.hasPermission('modelTable:query')")
    public ResultX<JSONObject> modelTableDoc(@Valid @RequestBody ModelTablePrimaryReqVO reqVO) {
        return success(tableService.modelTableDoc(reqVO.getTableId()));
    }

    @OperateLog("图形建模表结构同步")
    @PostMapping("/modelTableSync")
    @PreAuthorize("@ss.hasPermission('modelTable:adit')")
    public ResultX<TableStructure> modelTableSync(@Valid @RequestBody ModelTablePrimaryReqVO reqVO) {
//        TableStructure oldDO = tableService.modelTableSync(reqVO.getTableId());//如果下面的报错，就改回这个
        TableStructure oldDO = tableService.modelTableSyncNew(reqVO.getTableId());
        return success(oldDO);
    }

    @OperateLog("导入现有表")
    @PostMapping("/modelTableImport")
    @PreAuthorize("@ss.hasPermission('modelTable:adit')")
    public ResultX<TableStructure> modelTableImport(@Valid @RequestBody ModelTableCodeReqVO reqVO) {
        TableStructure oldDO = tableService.modelTableImport(reqVO);
        return success(oldDO);
    }

    @OperateLog("批量导入现有表")
    @PostMapping("/modelTableImportBatchOld")
    @PreAuthorize("@ss.hasPermission('modelTable:adit')")
    public ResultX<String> modelTableImportBatch(@Valid @RequestBody ModelTableImportListReqVO reqVO) {
        return success(tableService.modelTableImportBatch(reqVO));
    }

    @OperateLog("批量导入现有表(优化版)")
    @PostMapping("/modelTableImportBatch")
    @PreAuthorize("@ss.hasPermission('modelTable:adit')")
    public ResultX<String> modelTableImportBatchOptimized(@Valid @RequestBody ModelTableImportListReqVO reqVO) {
        return success(tableService.modelTableImportBatchOptimized(reqVO));
    }

    @OperateLog("图形建模数据条数")
    @PostMapping("/modelTableCount")
    @PreAuthorize("@ss.hasPermission('modelTable:query')")
    public ResultX<Integer> modelTableCount(@Valid @RequestBody ModelTablePrimaryReqVO reqVO) {
        return success(tableService.modelTableCount(reqVO.getTableId()));
    }

    @OperateLog("图形建模表清空数据")
    @PostMapping("/modelTableEmpty")
    @PreAuthorize("@ss.hasPermission('modelTable:del')")
    public ResultX<Boolean> modelTableEmpty(@Valid @RequestBody ModelTablePrimaryReqVO reqVO) {
        return success(tableService.modelTableEmpty(reqVO.getTableId()));
    }

    @OperateLog("图形建模主列表")
    @PostMapping("/modelTableList")
    @PreAuthorize("@ss.hasPermission('modelTable:query')")
    public ResultX<List<ModelTableRespVO>> modelTableList(@Valid @RequestBody ModelTableQueryReqVO reqVO) {
        List<ModelTableDO> list = tableService.modelTableList(reqVO);
        return success(BeanUtilX.copyList(list, ModelTableRespVO::new));
    }

    @OperateLog("图形建模主分页")
    @PostMapping("/modelTablePage")
    @PreAuthorize("@ss.hasPermission('modelTable:query')")
    public ResultX<PageResult<ModelTableRespVO>> modelTablePage(@Valid @RequestBody ModelTablePageReqVO reqVO) {
        return success(tableService.modelTablePage(reqVO));
    }

    @OperateLog("表Tree")
    @PostMapping("/modelTableTree")
    @PreAuthorize("@ss.hasPermission('modelTable:query')")
    public ResultX<List<ModelTableRespVO>> modelTableTree(@Valid @RequestBody ModelTableQueryReqVO reqVO) {
        return success(tableService.modelTableTree(reqVO));
    }
}
