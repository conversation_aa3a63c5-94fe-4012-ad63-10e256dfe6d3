package com.mongoso.mgs.module.model.service.dbfactory.creator;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.mongoso.mgs.common.util.ColumnInfo;
import com.mongoso.mgs.common.util.DatabaseUtil;
import com.mongoso.mgs.common.util.TableStructure;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.ObjUtilX;
import com.mongoso.mgs.framework.common.util.StrUtilX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.codegen.common.util.StringUtils;
import com.mongoso.mgs.module.enums.DBOPTypeEnum;
import com.mongoso.mgs.module.model.controller.admin.modelfield.vo.ModelFieldAditReqVO;
import com.mongoso.mgs.module.model.controller.admin.modeltableindex.vo.ModelTableIndexAditReqVO;
import com.mongoso.mgs.module.model.controller.admin.modeltableindex.vo.ModelTableIndexRespVO;
import com.mongoso.mgs.module.model.dal.db.modelfield.ModelFieldDO;
import com.mongoso.mgs.module.model.dal.db.modelquery.ModelQueryDO;
import com.mongoso.mgs.module.model.dal.db.modeltable.ModelTableDO;
import com.mongoso.mgs.module.model.dal.db.modeltableindex.ModelTableIndexDO;
import com.mongoso.mgs.module.model.dal.db.sqllog.SqlLogDO;
import com.mongoso.mgs.module.model.dal.mysql.modelfield.ModelFieldMapper;
import com.mongoso.mgs.module.model.dal.mysql.modeltableindex.ModelTableIndexMapper;
import com.mongoso.mgs.module.model.dal.mysql.sqllog.SqlLogMapper;
import com.mongoso.mgs.module.model.service.dbfactory.DBCreator;
import jakarta.annotation.Resource;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 *  pg 数据库操作类
 *  daijinbiao
 *  2024-10-12
 */
@Service
@Log4j2
public class PostgreSQLCreator implements DBCreator {

    @Resource
    private ModelFieldMapper fieldMapper;
    @Resource
    private ModelTableIndexMapper tableIndexMapper;
    @Resource
    private DatabaseUtil dbUtil;
    @Resource
    private SqlLogMapper sqlLogMapper;

    /******************** 如果要支持通用方法也多数据源 ************************/
    // 插入数据
    public int insertData(Long dataSourceId, String tableCode, String fields, String values) {
        String sql = String.format("INSERT INTO %s (%s) VALUES (%s)", tableCode, fields, values);
        try (Connection con = dbUtil.getCon(dataSourceId); Statement stmt = con.createStatement()) {
            return stmt.executeUpdate(sql);
        } catch (SQLException e) {
            throw new BizException("5001", "插入数据失败: " + e.getMessage());
        }
    }

    // 查询数据
    public Map<String, Object> selectData(Long dataSourceId, String tableCode, String pk, Long id) {
        String sql = String.format("SELECT * FROM %s WHERE %s = %d", tableCode, pk, id);
        try (Connection con = dbUtil.getCon(dataSourceId); Statement stmt = con.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {
            if (rs.next()) {
                return convertResultSetToMap(rs);
            }
        } catch (SQLException e) {
            throw new BizException("5001", "查询数据失败: " + e.getMessage());
        }
        return null;
    }

    // 更新数据
    public int updateData(Long dataSourceId, String tableCode, String pk, Long id, String updateFields) {
        String sql = String.format("UPDATE %s SET %s WHERE %s = %d", tableCode, updateFields, pk, id);
        try (Connection con = dbUtil.getCon(dataSourceId); Statement stmt = con.createStatement()) {
            return stmt.executeUpdate(sql);
        } catch (SQLException e) {
            throw new BizException("5001", "更新数据失败: " + e.getMessage());
        }
    }

    // 删除数据
    public int deleteData(Long dataSourceId, String tableCode, String pk, Long id) {
        String sql = String.format("DELETE FROM %s WHERE %s = %d", tableCode, pk, id);
        try (Connection con = dbUtil.getCon(dataSourceId); Statement stmt = con.createStatement()) {
            return stmt.executeUpdate(sql);
        } catch (SQLException e) {
            throw new BizException("5001", "删除数据失败: " + e.getMessage());
        }
    }

    // 分页查询
    public List<Map<String, Object>> selectPage(Long dataSourceId, String tableCode, String filter, int offset, int pageSize) {
        StringBuilder sql = new StringBuilder();
        sql.append(String.format("SELECT * FROM %s ", tableCode));
        if (filter != null) {
            sql.append("WHERE ").append(filter).append(" ");
        }
        sql.append("ORDER BY (SELECT NULL) LIMIT ").append(pageSize).append(" OFFSET ").append(offset);

        try (Connection con = dbUtil.getCon(dataSourceId); Statement stmt = con.createStatement();
             ResultSet rs = stmt.executeQuery(sql.toString())) {
            List<Map<String, Object>> resultList = new ArrayList<>();
            while (rs.next()) {
                resultList.add(convertResultSetToMap(rs));
            }
            return resultList;
        } catch (SQLException e) {
            throw new BizException("5001", "分页查询失败: " + e.getMessage());
        }
    }

    // 查询所有数据
    public List<Map<String, Object>> selectAll(Long dataSourceId, String tableCode, String filter) {
        StringBuilder sql = new StringBuilder();
        sql.append(String.format("SELECT * FROM %s ", tableCode));
        if (filter != null) {
            sql.append("WHERE ").append(filter).append(" ");
        }

        try (Connection con = dbUtil.getCon(dataSourceId); Statement stmt = con.createStatement();
             ResultSet rs = stmt.executeQuery(sql.toString())) {
            List<Map<String, Object>> resultList = new ArrayList<>();
            while (rs.next()) {
                resultList.add(convertResultSetToMap(rs));
            }
            return resultList;
        } catch (SQLException e) {
            throw new BizException("5001", "查询所有数据失败: " + e.getMessage());
        }
    }

    /*********************************************/


    @Override
    public void createTable(ModelTableDO tableDO, Statement stmt) throws SQLException {
        StringBuilder sql = genCreateTableSQL(tableDO);
        stmt.execute(sql.toString());
        System.out.println("PostgreSQL 表创建成功");
    }

    @Override
    public void createTableDynamic(ModelTableDO tableDO) {
        SqlLogDO sqlLog = new SqlLogDO(tableDO.getProjectId(), tableDO.getTableId(), tableDO.getTableCode(), "", DBOPTypeEnum.CREATE_TABLE.getType(), 1, DBOPTypeEnum.CREATE_TABLE.getDesc());

        try(Connection con = dbUtil.getCon(tableDO.getDataSourceConfigId());
            Statement stmt = con.createStatement()){
            StringBuilder sql = genCreateTableSQL(tableDO);
            String sqlStr = sql.toString();
            stmt.execute(sqlStr);
            sqlLog.setSql(sqlStr);
            log.info("PostgreSQL 表创建成功");
        } catch (SQLException e) {
            sqlLog.setSucStatus(0);
            sqlLog.setErrMgs(e.getMessage());
            // 捕获 SQLException 并提供特定错误信息
            throw new BizException("5002", "脚本执行失败，请检查原因: " + e.getMessage());
        } catch (Exception e) {
            sqlLog.setSucStatus(0);
            sqlLog.setErrMgs(e.getMessage());
            // 捕获其他异常
            throw new BizException("5001", "操作失败，请检查原因: " + e.getMessage());
        } finally {
            try {
                sqlLogMapper.insert(sqlLog);
            } catch (Exception e) {
                log.error("插入日志失败:" + e.getMessage());
            }
        }
    }


    @Override
    public StringBuilder getFullCode(ModelFieldDO field, StringBuilder sql) {
        sql.append("\"");
        sql.append(field.getFieldCode());
        sql.append("\" ");

        String upperStr = field.getFieldType().toUpperCase();
        if(upperStr.equals("JSONBARRAY")) {//特殊类型
            sql.append("JSONB");
        }else {
            sql.append(field.getFieldType());
        }
//        if (!upperStr.equals("DATE")
//                && !upperStr.equals("TIMESTAMP")
//                && !upperStr.equals("INT2")
//                && !upperStr.equals("INT4")
//                && !upperStr.equals("INT8")
//                && !upperStr.equals("TEXT")
//                && ObjUtilX.isNotEmpty(field.getLeng()) && field.getLeng() > 0) {
//            sql.append("(").append(field.getLeng());
//            if (ObjUtilX.isNotEmpty(field.getFieldPrecision()) && field.getFieldPrecision() > 0) {
//                sql.append(",").append(field.getFieldPrecision());
//            }
//            sql.append(")");
//        }

        if (upperStr.equals("VARCHAR") || upperStr.equals("CHAR") || upperStr.equals("NUMERIC")) {
            sql.append("(").append(ObjUtilX.isEmpty(field.getLeng()) || field.getLeng() == 0 ? 12 : field.getLeng());
            if (upperStr.equals("NUMERIC")) {
                sql.append(",").append(ObjUtilX.isEmpty(field.getFieldPrecision())
                        || field.getFieldPrecision() == 0 ? 2 : field.getFieldPrecision());
            }
            sql.append(")");
        }
        if(upperStr.equals("VARCHAR") || upperStr.equals("CHAR") || upperStr.equals("TEXT")) {
            sql.append(" COLLATE \"pg_catalog\".\"default\" ");
        }
        return sql;
    }

    public StringBuilder getFullType(ModelFieldDO field, StringBuilder sql) {
        sql.append(field.getFieldType());
        String upperStr = field.getFieldType().toUpperCase();
        if (!upperStr.equals("DATE")
                && !upperStr.equals("TIMESTAMP")
                && !upperStr.equals("INT2")
                && !upperStr.equals("INT4")
                && !upperStr.equals("INT8")
                && !upperStr.equals("TEXT")
                && ObjUtilX.isNotEmpty(field.getLeng()) && field.getLeng() > 0) {
            sql.append("(").append(field.getLeng());
            if (ObjUtilX.isNotEmpty(field.getFieldPrecision()) && field.getFieldPrecision() > 0) {
                sql.append(",").append(field.getFieldPrecision());
            }
            sql.append(")");
        }
        return sql;
    }

    //生成postgresql建表语法
    @Override
    public StringBuilder genCreateTableSQL(ModelTableDO tableDO) {
        StringBuilder sql = new StringBuilder();
        String prefix = dbUtil.getDBNameAllDynamic(tableDO.getDataSourceConfigId(), true);
        List<ModelFieldDO> fields = fieldMapper.selectList(LambdaQueryWrapperX.<ModelFieldDO>lambdaQueryX()
                .eqIfPresent(ModelFieldDO::getTableId, tableDO.getTableId())
                .orderByAsc(ModelFieldDO::getSort)
        );
        if (ObjUtilX.isEmpty(fields)) {
            throw new BizException("5001", "找不到模型表字段列表");
        }
        String lineSeparator = System.getProperty("line.separator");
        sql.append("-- ----------------------------").append(lineSeparator);
        sql.append("-- 表结构： ").append(tableDO.getTableCode()).append(lineSeparator);
        sql.append("-- ----------------------------").append(lineSeparator);
        sql.append("CREATE TABLE IF NOT EXISTS ");
        log.info("开始生成建表SQL:" + tableDO.getTableCode());
        sql.append(prefix).append(".\"").append(tableDO.getTableCode()).append("\" "); // 使用双引号来处理表名
        sql.append(" (").append(lineSeparator);
        List<String> primaryKeys = new ArrayList<>(); // 收集主键字段
        // 添加业务字段
        int primaryCount = 0;
        for (int i = 0; i < fields.size(); i++) {
            ModelFieldDO field = fields.get(i);
            if (ObjUtilX.isEmpty(field.getFieldName()) || ObjUtilX.isEmpty(field.getFieldCode())) {
                throw new BizException("5001", "请先完善字段信息再生成表结构");
            }
            // 拼接字段定义
            sql.append("  ");
            this.getFullCode(field, sql);
            if (field.getIsNullable() == 1 || field.getIsPrimaryKey() == 1) {
                sql.append(" NOT NULL");
            }
            String defVal = field.getDefaultVal();
            String fieldType = field.getFieldType();
            if (ObjUtilX.isNotEmpty(defVal)) {
                if (isNumeric(defVal)
                        || ("CURRENT_TIMESTAMP").equals(defVal)
                        || ("NULL").equals(defVal)
                        || defVal.contains("::")
                        //|| defVal.contains("::character")
                        //|| defVal.contains("::timestamp")
                        //|| defVal.contains("::jsonb")
                        //|| ("''::character varying").equals(defVal)
                        //|| ("NULL::character varying").equals(defVal)
                ) {
                    sql.append(" DEFAULT ").append(defVal);
                } else {
                    sql.append(" DEFAULT '").append(defVal).append("'");
                }
            }
            // 如果字段是主键，则加入到主键列表
            if (field.getIsPrimaryKey() == 1) {
                primaryCount++;
                primaryKeys.add("\"" + field.getFieldCode() + "\"");
            }
            sql.append(", ").append(lineSeparator);
        }
        if (primaryCount == 0) {
            throw new BizException("5001", "必须设置一个主键字段");
        } else if (primaryCount > 1) {
            throw new BizException("5001", "主键有且只能设置一个字段");
        }
        // 移除最后的逗号和空格
        //sql.setLength(sql.length() - 2);
        // 添加主键约束
        if (!primaryKeys.isEmpty()) {
            sql.append("  CONSTRAINT \"").append(tableDO.getTableCode()).append("_pkey\" PRIMARY KEY (").append(String.join(",", primaryKeys)).append(")").append(lineSeparator);
        }else{
            sql.setLength(sql.length() - 2);
        }
        sql.append("); ").append(lineSeparator);

        // 添加字段注释
        sql.append("-- ----------------------------").append(lineSeparator);
        sql.append("-- 字段注释： ").append(tableDO.getTableCode()).append(lineSeparator);
        sql.append("-- ----------------------------").append(lineSeparator);;
        for (ModelFieldDO field : fields) {
            if (ObjUtilX.isNotEmpty(field.getFieldName())) {
                //String remark = ObjUtilX.isNotEmpty(field.getRemark()) ? " " + StringUtils.escapeSingleQuotes(field.getRemark()) : "";
                //String remark = ObjUtilX.isNotEmpty(field.getRemark()) ? " " + field.getRemark() : "";
                sql.append("COMMENT ON COLUMN ");
                sql.append(prefix).append(".\"").append(tableDO.getTableCode()).append("\".\""); // 使用双引号来处理表名
                sql.append(field.getFieldCode());
                sql.append("\" IS '");
                //sql.append(field.getFieldName() + remark);
                sql.append(field.getFieldName());
                sql.append("'; ").append(lineSeparator);
            }
        }

        // 添加索引
        List<ModelTableIndexDO> indexDOS = tableIndexMapper.selectList(LambdaQueryWrapperX.<ModelTableIndexDO>lambdaQueryX()
                .eqIfPresent(ModelTableIndexDO::getTableId, tableDO.getTableId()));
        if (ObjUtilX.isNotEmpty(indexDOS)) {
            sql.append("-- ----------------------------").append(lineSeparator);
            sql.append("-- 表索引： ").append(tableDO.getTableCode()).append(lineSeparator);
            sql.append("-- ----------------------------").append(lineSeparator);
            Map<String, List<ModelTableIndexDO>> indexMap = indexDOS.stream().collect(Collectors.groupingBy(s -> s.getIdxName()));

            for (Map.Entry<String, List<ModelTableIndexDO>> entry : indexMap.entrySet()) {
                String key = entry.getKey();
                List<ModelTableIndexDO> value = entry.getValue();
                ModelTableIndexDO indexDO = value.get(0);
                if (indexDO.getIdxName().equals(tableDO.getTableCode() + "_pkey")) {//u_utility_config   u_utility_config_pkey
                    continue;
                }
                sql.append("CREATE ");
                if ("UNIQUE".equalsIgnoreCase(indexDO.getIdxType())) {
                    sql.append("UNIQUE ");
                }
                sql.append("INDEX \"").append(key).append("\" ON ").append(prefix).append(".\"").append(tableDO.getTableCode()).append("\"")
                        .append(" USING ")
                        .append(indexDO.getIdxWay())
                        .append(" (");
                for (ModelTableIndexDO idxField : value) {
                    sql.append("\"").append(idxField.getFieldCode()).append("\", ");
                }
                // 移除最后的逗号和空格
                sql.setLength(sql.length() - 2);
                sql.append("); ").append(lineSeparator);

                // 添加索引注释
                if (ObjUtilX.isNotEmpty(indexDO.getRemark())) {
                    sql.append("COMMENT ON INDEX ").append(prefix).append(".\"").append(key).append("\" IS '").append(StringUtils.escapeSingleQuotes(indexDO.getRemark())).append("'; ").append(lineSeparator);
                }
            }
        }

        // 添加表注释
        if (ObjUtilX.isNotEmpty(tableDO.getTableName())) {
            sql.append("COMMENT ON TABLE ").append(prefix).append(".\"").append(tableDO.getTableCode()).append("\" IS '")
                    .append(tableDO.getTableName()).append("'; ").append(lineSeparator);
        }

        log.info("生成建表SQL完毕:\n" + sql.toString());
        return sql;
    }

    @Override
    //    @Transactional(rollbackFor = Exception.class)
    public void dropField(ModelFieldDO change, ModelTableDO tableDO) {
        Long confId = tableDO.getDataSourceConfigId();
        String prefix = dbUtil.getDBNameAllDynamic(confId,true);
        // 删除数据库字段的逻辑
        StringBuilder sql = new StringBuilder("ALTER TABLE ");
        sql.append(prefix).append(".\"").append(tableDO.getTableCode()).append("\" "); // 使用双引号
        sql.append(" DROP COLUMN IF EXISTS ");
        sql.append("\"").append(change.getFieldCode()).append("\""); // 使用双引号
        sql.append(";");
        SqlLogDO sqlLog = new SqlLogDO(tableDO.getProjectId(), tableDO.getTableId(), tableDO.getTableCode(), "", DBOPTypeEnum.DEL_FIELD.getType(), 1, DBOPTypeEnum.DEL_FIELD.getDesc());

        try (Connection connection = dbUtil.getCon(confId);
             Statement stmt = connection.createStatement()) {
            String sqlStr = sql.toString();
            stmt.execute(sqlStr);
            sqlLog.setSql(sqlStr);
            log.info("执行表字段删除完毕:" + tableDO.getTableCode() + " - " + change.getFieldCode());
            //log.info("执行表字段删除完毕：" + sql);
        } catch (SQLException e) {
            sqlLog.setSucStatus(0);
            sqlLog.setErrMgs(e.getMessage());
            // 捕获 SQLException 并提供特定错误信息
            throw new BizException("5002", "数据库操作失败: " + e.getMessage());
        } catch (Exception e) {
            sqlLog.setSucStatus(0);
            sqlLog.setErrMgs(e.getMessage());
            log.error("执行表字段删除失败：" + sql);
            e.printStackTrace();
            throw new BizException("5001", "执行表字段删除失败：" + tableDO.getTableCode() + " - " + change.getFieldCode());
        } finally {
            try {
                sqlLogMapper.insert(sqlLog);
            } catch (Exception e) {
                log.error("插入日志失败:" + e.getMessage());
            }
        }
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED, rollbackFor = Exception.class)
    public String addField(ModelFieldDO change, ModelTableDO tableDO) {
        String sqlStr = "";
        Long confId = tableDO.getDataSourceConfigId();
        String prefix = dbUtil.getDBNameAllDynamic(confId,true);
        // 新增字段到数据库的逻辑
        StringBuilder sql = new StringBuilder("ALTER TABLE ");
        sql.append(prefix).append(".\"").append(tableDO.getTableCode()).append("\" "); // 使用双引号
        sql.append(" ADD COLUMN ");
        this.getFullCode(change, sql);

        // 判断是否为空
        if (ObjUtilX.isNotEmpty(change.getIsNullable()) && change.getIsNullable() == 1) { // 这里定义的事必填
            sql.append(" NOT NULL");
        }

        // 默认值
        if (ObjUtilX.isNotEmpty(change.getDefaultVal())) {
            sql.append(" DEFAULT '").append(change.getDefaultVal()).append("'");
        }
        sql.append(";");
        //sqlStr = sql.toString();
        SqlLogDO sqlLog = new SqlLogDO(tableDO.getProjectId(), tableDO.getTableId(), tableDO.getTableCode(), "", DBOPTypeEnum.ADD_FIELD.getType(), 1, DBOPTypeEnum.ADD_FIELD.getDesc());
        try (Connection connection = dbUtil.getCon(confId);
             Statement stmt = connection.createStatement()) {
            if(change.getNeedExe()) {
                stmt.execute(sql.toString());
            }

            // 如果是主键，则需要单独设置
            if (ObjUtilX.isNotEmpty(change.getIsPrimaryKey()) && change.getIsPrimaryKey() == 1) {
                String primaryKeySql = "ALTER TABLE " + prefix + ".\"" + tableDO.getTableCode() + "\" ADD CONSTRAINT "
                        + tableDO.getTableCode() + "_pkey PRIMARY KEY (\"" + change.getFieldCode() + "\");";
                if(change.getNeedExe()) {
                    stmt.execute(primaryKeySql);
                }
                //sqlStr.concat(";" + primaryKeySql);
                sql.append(primaryKeySql);
            }

            // 添加字段注释
            if (ObjUtilX.isNotEmpty(change.getFieldName())) {
                //判断是否有描述，有，则组装到表字段里面
                String remark = change.getFieldName();
                if (StrUtilX.isNotEmpty(change.getRemark())){
                    remark = remark +"["+change.getRemark()+"]";
                }
                String commentSql = "COMMENT ON COLUMN " + prefix + ".\"" + tableDO.getTableCode() + "\"."
                        + "\"" + change.getFieldCode() + "\" IS '" + remark + "';";
                if(change.getNeedExe()) {
                    stmt.execute(commentSql);
                }
                //sqlStr.concat(";" + commentSql);
                sql.append(commentSql);
            }
            sqlStr = sql.toString();
            sqlLog.setSql(sqlStr);

            log.info("执行表字段新增完毕:" + tableDO.getTableCode() + " - " + change.getFieldCode());
            log.info("执行表字段新增完毕：" + sql);
        } catch (SQLException e) {
            sqlLog.setSucStatus(0);
            sqlLog.setErrMgs(e.getMessage());
            log.error("执行表字段新增失败：" + sql);
            // 捕获 SQLException 并提供特定错误信息
            throw new BizException("5002", "数据库操作失败: " + e.getMessage());
        } catch (Exception e) {
            sqlLog.setSucStatus(0);
            sqlLog.setErrMgs(e.getMessage());
            log.error("执行表字段新增失败：" + sql);
            e.printStackTrace();
            throw new BizException("5001", "执行表字段新增失败：" + tableDO.getTableCode() + " - " + change.getFieldCode());
        } finally {
            insertLog(sqlLog);
        }
        return sqlStr;
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED, rollbackFor = Exception.class)
    public void modifyField(ModelFieldDO change, ModelFieldDO oldField, ModelTableDO tableDO) {
        Long confId = tableDO.getDataSourceConfigId();
        String prefix = dbUtil.getDBNameAllDynamic(confId, true);

        // 修改数据库字段的逻辑
        String alterTable = "ALTER TABLE " + prefix + ".\"" + tableDO.getTableCode() + "\" "; // 使用双引号处理表名
        StringBuilder sql = new StringBuilder();

        boolean needsAlter = false; // 标记是否需要执行 ALTER 操作

        // 如果有旧字段名且新旧名称不同，则使用 RENAME COLUMN 来更改字段名
        if (ObjUtilX.isNotEmpty(change.getOldFieldCode()) && !change.getOldFieldCode().equals(change.getFieldCode())) {
            sql.append(alterTable);
            sql.append("RENAME COLUMN \"").append(change.getOldFieldCode()).append("\" TO \"").append(change.getFieldCode()).append("\"; ");
            needsAlter = true;
        }

        // 添加类型定义对比
        if (!change.getFieldType().equals(oldField.getFieldType()) ||
                !change.getLeng().equals(oldField.getLeng()) ||
                !change.getFieldPrecision().equals(oldField.getFieldPrecision())) {

            sql.append(alterTable);
            sql.append("ALTER COLUMN \"").append(change.getFieldCode()).append("\" TYPE ");
            getFullType(change, sql);
            // 检查是否需要添加 USING 子句
            if (("int2".equalsIgnoreCase(change.getFieldType()) ||
                    "int4".equalsIgnoreCase(change.getFieldType()) ||
                    "int8".equalsIgnoreCase(change.getFieldType())) &&
                    "varchar".equalsIgnoreCase(oldField.getFieldType())) {
                sql.append(" USING \"").append(change.getFieldCode()).append("\"::").append(change.getFieldType()); // 添加 USING 子句
            }

            sql.append("; ");
            needsAlter = true;
        }

        // 检查是否需要设置 NOT NULL
        if (change.getIsNullable() == 1 && oldField.getIsNullable() == 0) { // 从 NOT NULL 到可空
            sql.append(alterTable);
            sql.append("ALTER COLUMN \"").append(change.getFieldCode()).append("\" SET NOT NULL; ");
            needsAlter = true;
        } else if (change.getIsNullable() == 0 && oldField.getIsNullable() == 1) { // 从可空到 NOT NULL
            sql.append(alterTable);
            sql.append("ALTER COLUMN \"").append(change.getFieldCode()).append("\" DROP NOT NULL; ");
            needsAlter = true;
        }

        // 设置默认值对比
        if (!areDefaultValuesEqual(change.getDefaultVal(), oldField.getDefaultVal())) {
            sql.append(alterTable);
            sql.append("ALTER COLUMN \"").append(change.getFieldCode()).append("\" ");
            // 如果默认值为空字符串，使用 DROP DEFAULT
            if (ObjUtilX.isEmpty(change.getDefaultVal())) {
                sql.append("DROP DEFAULT; ");
            } else {
                sql.append("SET DEFAULT '").append(change.getDefaultVal()).append("'; ");
            }
            needsAlter = true;
        }

        SqlLogDO sqlLog = new SqlLogDO(tableDO.getProjectId(), tableDO.getTableId(), tableDO.getTableCode(), "", DBOPTypeEnum.UPD_FIELD.getType(), 1, DBOPTypeEnum.UPD_FIELD.getDesc());

        try (Connection connection = dbUtil.getCon(confId);
             Statement stmt = connection.createStatement()) {

            // 执行 ALTER TABLE 的 SQL
            String sqlStr = sql.toString();
            if (needsAlter && change.getNeedExe()) {
                sqlLog.setSql(sqlStr);
                stmt.execute(sqlStr);
            }


            // 添加字段注释
            if (!Objects.equals(change.getFieldName(), oldField.getFieldName())
            || !Objects.equals(change.getRemark(), oldField.getRemark())) {
                //判断是否有描述，有，则组装到表字段里面
                String remark = change.getFieldName();
                if (StrUtilX.isNotEmpty(change.getRemark())){
                    remark = remark +"["+change.getRemark()+"]";
                }
                String commentSql = "COMMENT ON COLUMN " + prefix + ".\"" + tableDO.getTableCode() + "\"."
                        + "\"" + change.getFieldCode() + "\" IS '" + remark + "';";
                sqlStr = sqlStr.concat(";" + commentSql);
                if (change.getNeedExe()) {
                    sqlLog.setSql(sqlStr);
                    stmt.execute(commentSql);
                }
            }

            sqlLog.setSql(sqlStr);
            log.info("执行表字段修改完毕: " + tableDO.getTableCode() + " - " + change.getFieldCode());
        } catch (SQLException e) {
            sqlLog.setSucStatus(0);
            sqlLog.setErrMgs(e.getMessage());
            log.error("执行表字段修改失败：" + sql);
            throw new BizException("5002", "数据库操作失败: " + e.getMessage());
        } catch (Exception e) {
            sqlLog.setSucStatus(0);
            sqlLog.setErrMgs(e.getMessage());
            log.error("执行表字段修改失败：" + sql);
            e.printStackTrace();
            throw new BizException("5001", "执行表字段修改失败：" + tableDO.getTableCode() + " - " + change.getFieldCode());
        } finally {
            insertLog(sqlLog);
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    @Override
    public void insertLog(SqlLogDO sqlLog) {
        // 独立处理日志插入
        try {
            sqlLogMapper.insert(sqlLog);
        } catch (Exception e) {
            log.error("插入日志失败: " + e.getMessage());
        }
    }

    @Override
    public void delIndex(ModelTableDO table, String idxName) {
        String tableCode = table.getTableCode();
        Long confId = table.getDataSourceConfigId();
        String prefix = dbUtil.getDBNameAllDynamic(confId,true);
        // 删除 PostgreSQL 中的索引
        String sql = "DROP INDEX IF EXISTS " + prefix + ".\"" + idxName + "\";"; // 使用双引号
        dbUtil.exeSQL(table, sql, DBOPTypeEnum.DROP_INDEX);
        //SqlLogDO sqlLog = new SqlLogDO(table.getProjectId(), table.getTableCode(), sql, DBOPTypeEnum.DROP_INDEX.getType(), 1, DBOPTypeEnum.DROP_INDEX.getDesc());
        //try (Connection connection = dbUtil.getCon(confId);
        //     Statement stmt = connection.createStatement()) {
        //    stmt.execute(sql);
        //    sqlLog.setSql(sql);
        //
        //    log.info("成功删除索引: " + idxName + " 在表: " + tableCode);
        //} catch (SQLException e) {
        //    sqlLog.setSucStatus(0);
        //    sqlLog.setErrMgs(e.getMessage());
        //    // 捕获 SQLException 并提供特定错误信息
        //    throw new BizException("5002", "数据库操作失败: " + e.getMessage());
        //} catch (Exception e) {
        //    log.error("删除索引失败: " + idxName + " 在表: " + tableCode, e);
        //    throw new BizException("5001", "删除索引失败: " + idxName + " 在表: " + tableCode);
        //} finally {
        //    try {
        //        sqlLogMapper.insert(sqlLog);
        //    } catch (Exception e) {
        //        log.error("插入日志失败:" + e.getMessage());
        //    }
        //}
    }

    @Override
    public void renameIndex(ModelTableDO table, String oldIdxName, String idxName) {
        if(oldIdxName.equals(idxName)){
            return;
        }
        Long confId = table.getDataSourceConfigId();
        String prefix = dbUtil.getDBNameAllDynamic(confId,true);
        // 修改 PostgreSQL 中的索引名称
        String sql = "ALTER INDEX " + prefix + ".\"" + oldIdxName + "\" RENAME TO \"" + idxName + "\";"; // 使用双引号
        dbUtil.exeSQL(table, sql, DBOPTypeEnum.ALTER_INDEX);
    }

    @Override
    public void modifyIndex(ModelTableDO table, ModelTableIndexAditReqVO oldIndex, ModelTableIndexAditReqVO newIndex) {
        if (onlyNameChanged(oldIndex, newIndex)) {
            // 仅变更名称
            if (!oldIndex.getIdxName().equals(newIndex.getIdxName())) {
                Long confId = table.getDataSourceConfigId();
                String prefix = dbUtil.getDBNameAllDynamic(confId, true);
                // 修改索引名称
                String renameSql = "ALTER INDEX " + prefix + ".\"" + oldIndex.getIdxName() + "\" RENAME TO \"" + newIndex.getIdxName() + "\";";
                dbUtil.exeSQL(table, renameSql, DBOPTypeEnum.ALTER_INDEX);
            }
        } else {
            // 删除旧索引
            String dropSql = "DROP INDEX IF EXISTS " + dbUtil.getDBNameAllDynamic(table.getDataSourceConfigId(), true)
                    + ".\"" + oldIndex.getIdxName() + "\";";
            //dbUtil.exeSQL(table, dropSql, DBOPTypeEnum.ALTER_INDEX);

            // 创建新索引
            StringBuilder createSql = new StringBuilder();
            if ("UNIQUE".equalsIgnoreCase(newIndex.getIdxType())) {
                createSql.append("CREATE UNIQUE INDEX ");
            } else if ("FULLTEXT".equalsIgnoreCase(newIndex.getIdxType())) {
                createSql.append("CREATE INDEX ");
                // Note: Use appropriate index method for FULLTEXT based on PostgreSQL support
            } else {
                createSql.append("CREATE INDEX ");
            }

            createSql.append("\"").append(newIndex.getIdxName()).append("\" ON ");
            createSql.append(dbUtil.getDBNameAllDynamic(table.getDataSourceConfigId(), true))
                    .append(".\"").append(table.getTableCode()).append("\" (");

            // 处理 fields 列表来生成字段部分
            List<ModelFieldAditReqVO> fields = newIndex.getFields();
            for (int i = 0; i < fields.size(); i++) {
                if (i > 0) {
                    createSql.append(", ");
                }
                ModelFieldAditReqVO field = fields.get(i);
                createSql.append("\"").append(field.getFieldCode()).append("\" ");
                //if ("DESC".equalsIgnoreCase(field.getSortType())) {//todo 暂时没有这个排序属性，后续加
                //    createSql.append("DESC");
                //} else {
                //    createSql.append("ASC");
                //}
                createSql.append("ASC");
            }
            createSql.append(")");

            if (newIndex.getRemark() != null && !newIndex.getRemark().isEmpty()) {
                // Note: PostgreSQL requires a separate step for commenting
                // Example strategy: COMMENT ON INDEX can be separately executed
            }

            createSql.append(";");

            // 执行创建新索引的 SQL
            //dbUtil.exeSQL(table, createSql.toString(), DBOPTypeEnum.ALTER_INDEX);
            dbUtil.exeSQL(table, dropSql + "\n" + createSql, DBOPTypeEnum.ALTER_INDEX);
        }
    }


    @Override
    public void addIndex(List<ModelTableIndexAditReqVO> indexAdds, Map<String, ModelTableIndexDO> exsitIdxMap, Map<String, ModelFieldDO> exsitFieldCodeMap, ModelTableDO exists) {
        Long confId = exists.getDataSourceConfigId();
        String prefix = dbUtil.getDBNameAllDynamic(confId, true);
        SqlLogDO sqlLog = new SqlLogDO(exists.getProjectId(), exists.getTableId(), exists.getTableCode(), "", DBOPTypeEnum.ADD_INDEX.getType(), 1, DBOPTypeEnum.ADD_INDEX.getDesc());

        StringBuilder sqlSB = new StringBuilder();

        List<ModelTableIndexDO> allIndexDOList = new ArrayList<>();
        for (ModelTableIndexAditReqVO indexAdd : indexAdds) {
            if (ObjUtilX.isEmpty(indexAdd.getFields())) {
                throw new BizException("5001", "索引：" + indexAdd.getIdxName() + "，没有指定字段");
            }
            if (exists.getNeedExe() && exsitIdxMap.containsKey(indexAdd.getIdxName())) {
                throw new BizException("5001", "索引名【" + indexAdd.getIdxName() + "】已存在");
            }

            List<ModelTableIndexDO> indexDOList = new ArrayList<>(indexAdd.getFields().size());
            StringBuilder fieldStr = new StringBuilder();
            Long idxId = IdWorker.getId();
            int i = 1;

            for (ModelFieldAditReqVO field : indexAdd.getFields()) {
                if (!exsitFieldCodeMap.containsKey(field.getFieldCode())) {
                    throw new BizException("5001", "创建索引时【" + field.getFieldCode() + "】字段不存在");
                }
                if (field.getIsPrimaryKey() == 1) {
                    throw new BizException("5001", "创建索引时【" + field.getFieldCode() + "】字段为主键");
                }
                field.setFieldId(exsitFieldCodeMap.get(field.getFieldCode()).getFieldId());
                fieldStr.append("\"").append(field.getFieldCode()).append("\"").append(", "); // 使用双引号

                ModelTableIndexDO idx = new ModelTableIndexDO();
                idx.setIdxId(idxId);
                idx.setIdxName(indexAdd.getIdxName());
                idx.setTableId(exists.getTableId());
                idx.setTableCode(exists.getTableCode());
                idx.setNonUnique(indexAdd.getIdxName().equals("UNIQUE") ? 0 : 1);
                idx.setIdxSeq(i++);
                idx.setFieldId(field.getFieldId());
                idx.setFieldCode(field.getFieldCode());
                idx.setSortType(indexAdd.getSortType());
                idx.setIdxType(indexAdd.getIdxType());
                idx.setIdxWay("FULLTEXT".equalsIgnoreCase(indexAdd.getIdxType()) ? "" : "BTREE");
                idx.setRemark(indexAdd.getRemark());

                indexDOList.add(idx);
            }

            // 存在真实表
            if (exists.getIsGen() == 1) {
                // 移除最后的逗号和空格
                fieldStr.setLength(fieldStr.length() - 2);

                StringBuilder sql = new StringBuilder();
                sql.append("CREATE ");
                if ("UNIQUE".equalsIgnoreCase(indexAdd.getIdxType())) {
                    sql.append("UNIQUE ");
                }
                sql.append("INDEX \"").append(indexAdd.getIdxName()).append("\" ")
                        .append("ON ").append(prefix).append(".\"").append(exists.getTableCode()).append("\" ")
                        .append("(").append(fieldStr).append(")");

                //dbUtil.exeSQL(confId,sql.toString());
                String sql1 = sql.toString();
                //stmt.execute(sql1);
                sqlSB.append(sql1).append(";");

                // 添加注释
                if (ObjUtilX.isNotEmpty(indexAdd.getRemark())) {
                    String commentSql = "COMMENT ON INDEX \"" + indexAdd.getIdxName() + "\" IS '" + StringUtils.escapeSingleQuotes(indexAdd.getRemark()) + "'";
                    //dbUtil.exeSQL(confId,commentSql);
                    //stmt.execute(commentSql);
                    sqlSB.append(commentSql).append(";");
                }
            }
            allIndexDOList.addAll(indexDOList);
        }
        if (!allIndexDOList.isEmpty()) {
            tableIndexMapper.insertBatch(allIndexDOList);
        }
        if(exists.getNeedExe()) {
            dbUtil.exeSQL(exists, sqlSB.toString(), DBOPTypeEnum.ADD_INDEX);
        }
        //try (Connection connection = dbUtil.getCon(confId);
        //     Statement stmt = connection.createStatement()) {
        //    String sqlStr = sqlSB.toString();
        //    sqlLog.setSql(sqlStr);
        //    stmt.execute(sqlStr);
        //} catch (SQLException e) {
        //    sqlLog.setSucStatus(0);
        //    sqlLog.setErrMgs(e.getMessage());
        //    // 捕获 SQLException 并提供特定错误信息
        //    throw new BizException("5002", "数据库操作失败: " + e.getMessage());
        //} catch (Exception e) {
        //    sqlLog.setSucStatus(0);
        //    sqlLog.setErrMgs(e.getMessage());
        //    log.error("添加索引失败:" + e.getMessage());
        //    throw new BizException("5001", "添加索引失败: ");
        //} finally {
        //    try {
        //        if (exists.getIsGen() == 1) {
        //            sqlLogMapper.insert(sqlLog);
        //        }
        //    } catch (Exception e) {
        //        log.error("插入日志失败:" + e.getMessage());
        //    }
        //}
    }
    //public void addIndex(List<ModelTableIndexAditReqVO> indexAdds, Map<String, ModelTableIndexDO> exsitIdxMap, Map<String, ModelFieldDO> exsitFieldCodeMap, ModelTableDO exists) {
    //    Long confId = exists.getDataSourceConfigId();
    //    String prefix = dbUtil.getDBNameAllDynamic(confId, true);
    //    SqlLogDO sqlLog = new SqlLogDO(exists.getProjectId(), exists.getTableId(), exists.getTableCode(), "", DBOPTypeEnum.ADD_INDEX.getType(), 1, DBOPTypeEnum.ADD_INDEX.getDesc());
    //
    //    StringBuilder sqlSB = new StringBuilder();
    //    try(Connection connection = dbUtil.getCon(confId);
    //        Statement stmt = connection.createStatement()) {
    //        for (ModelTableIndexAditReqVO indexAdd : indexAdds) {
    //            if (ObjUtilX.isEmpty(indexAdd.getFields())) {
    //                throw new BizException("5001", "索引：" + indexAdd.getIdxName() + "，没有指定字段");
    //            }
    //            if (exsitIdxMap.containsKey(indexAdd.getIdxName())) {
    //                throw new BizException("5001", "索引名【" + indexAdd.getIdxName() + "】已存在");
    //            }
    //
    //            List<ModelTableIndexDO> indexDOList = new ArrayList<>(indexAdd.getFields().size());
    //            StringBuilder fieldStr = new StringBuilder();
    //            Long idxId = IdWorker.getId();
    //            int i = 1;
    //
    //            for (ModelFieldAditReqVO field : indexAdd.getFields()) {
    //                if (!exsitFieldCodeMap.containsKey(field.getFieldCode())) {
    //                    throw new BizException("5001", "创建索引时【" + field.getFieldCode() + "】字段不存在");
    //                }
    //                if (field.getIsPrimaryKey() == 1) {
    //                    throw new BizException("5001", "创建索引时【" + field.getFieldCode() + "】字段为主键");
    //                }
    //                field.setFieldId(exsitFieldCodeMap.get(field.getFieldCode()).getFieldId());
    //                fieldStr.append("\"").append(field.getFieldCode()).append("\"").append(", "); // 使用双引号
    //
    //                ModelTableIndexDO idx = new ModelTableIndexDO();
    //                idx.setIdxId(idxId);
    //                idx.setIdxName(indexAdd.getIdxName());
    //                idx.setTableId(exists.getTableId());
    //                idx.setTableCode(exists.getTableCode());
    //                idx.setNonUnique(indexAdd.getIdxName().equals("UNIQUE") ? 0 : 1);
    //                idx.setIdxSeq(i++);
    //                idx.setFieldId(field.getFieldId());
    //                idx.setFieldCode(field.getFieldCode());
    //                idx.setSortType(indexAdd.getSortType());
    //                idx.setIdxType(indexAdd.getIdxType());
    //                idx.setIdxWay("FULLTEXT".equalsIgnoreCase(indexAdd.getIdxType()) ? "" : "BTREE");
    //                idx.setRemark(indexAdd.getRemark());
    //
    //                indexDOList.add(idx);
    //            }
    //
    //            // 存在真实表
    //            if (exists.getIsGen() == 1) {
    //                // 移除最后的逗号和空格
    //                fieldStr.setLength(fieldStr.length() - 2);
    //
    //                StringBuilder sql = new StringBuilder();
    //                sql.append("CREATE ");
    //                if ("UNIQUE".equalsIgnoreCase(indexAdd.getIdxType())) {
    //                    sql.append("UNIQUE ");
    //                }
    //                sql.append("INDEX \"").append(indexAdd.getIdxName()).append("\" ")
    //                        .append("ON ").append(prefix).append(".\"").append(exists.getTableCode()).append("\" ")
    //                        .append("(").append(fieldStr).append(")");
    //
    //                //dbUtil.exeSQL(confId,sql.toString());
    //                String sql1 = sql.toString();
    //                stmt.execute(sql1);
    //                sqlSB.append(sql1).append(";");
    //                // 添加注释
    //                if (ObjUtilX.isNotEmpty(indexAdd.getRemark())) {
    //                    String commentSql = "COMMENT ON INDEX \"" + indexAdd.getIdxName() + "\" IS '" + indexAdd.getRemark() + "'";
    //                    //dbUtil.exeSQL(confId,commentSql);
    //                    stmt.execute(commentSql);
    //                    sqlSB.append(commentSql).append(";");
    //                }
    //            }
    //            tableIndexMapper.insertBatch(indexDOList);
    //        }
    //        sqlLog.setSql(sqlSB.toString());
    //    } catch (SQLException e) {
    //        sqlLog.setSucStatus(0);
    //        sqlLog.setErrMgs(e.getMessage());
    //        // 捕获 SQLException 并提供特定错误信息
    //        throw new BizException("5002", "数据库操作失败: " + e.getMessage());
    //    } catch (Exception e) {
    //        log.error("添加索引失败:" + e.getMessage());
    //        throw new BizException("5001", "添加索引失败: ");
    //    } finally {
    //        try {
    //            sqlLogMapper.insert(sqlLog);
    //        } catch (Exception e) {
    //            log.error("插入日志失败:" + e.getMessage());
    //        }
    //    }
    //}

    @Override
    public StringBuilder genCreateTempTableSQL(ModelQueryDO queryDO, String paramCode, List<ModelFieldDO> fields) {
        // 临时表名，不使用模式
        String tableName = paramCode.replaceAll("@", ""); // 替换字符以防止非法名称
        StringBuilder sql = new StringBuilder("DROP TABLE IF EXISTS ").append(tableName).append(";");

        sql.append("CREATE TEMPORARY TABLE ").append(tableName).append(" (");

        // 添加业务字段
        for (ModelFieldDO field : fields) {
            // 拼接字段名与类型
            this.getFullCode(field, sql);

            if (field.getIsNullable() == 1) {
                sql.append(" NOT NULL");
            }

            // 设置默认值
            if (ObjUtilX.isNotEmpty(field.getDefaultVal())) {
                // 这里根据类型判断是否需要加引号
                if (isNumeric(field.getDefaultVal())) {
                    sql.append(" DEFAULT ").append(field.getDefaultVal());
                } else {
                    sql.append(" DEFAULT '").append(field.getDefaultVal()).append("'");
                }
            }

            // 添加主键设置
            if (field.getIsPrimaryKey() == 1) {
                sql.append(", PRIMARY KEY (").append(field.getFieldName()).append(")"); // 假设 getFieldName() 返回字段名
            }

            sql.append(", ");
        }

        // 移除最后的逗号和空格
        if (sql.length() > 2) {
            sql.setLength(sql.length() - 2);
        }
        sql.append(");");

        log.info("生成建临时表SQL完毕: " + sql.toString());
        return sql;
    }

    @Override
    public StringBuilder genCreateTempTableInsertSQL(ModelQueryDO queryDO, String paramCode, JSONArray paramArray, List<ModelFieldDO> fieldDOS) {
//        String prefix = dbUtil.getPGDBName(true);
        StringBuilder sql = new StringBuilder("INSERT INTO ");
        sql.append( paramCode.replaceAll("@", "") + " "); // 使用双引号

        // 添加字段
        sql.append(" (");
        for (ModelFieldDO fieldDO : fieldDOS) {
            sql.append("\"").append(fieldDO.getFieldCode()).append("\"");
            sql.append(",");
        }
        // 移除最后的逗号
        sql.setLength(sql.length() - 1);
        sql.append(") VALUES ");

        // 添加业务字段
        for (int i = 0; i < paramArray.size(); i++) {
            JSONObject jsonObject = paramArray.getJSONObject(i);
            sql.append(" (");
            for (ModelFieldDO fieldDO : fieldDOS) {
                sql.append("'").append(jsonObject.get(fieldDO.getFieldCode())).append("'");
                sql.append(",");
            }
            // 移除最后的逗号
            sql.setLength(sql.length() - 1);
            sql.append("), ");
        }

        // 移除最后的逗号和空格
        sql.setLength(sql.length() - 2);
        sql.append(";");
        log.info("生成临时表插入SQL完毕:" + sql.toString());
        return sql;
    }

    @Override
    public TableStructure getTableStructure(ModelTableDO table) throws SQLException {
        TableStructure tableStructure = new TableStructure();
        String tableName = table.getTableCode();
        Long confId = table.getDataSourceConfigId();

        // 使用 try-with-resources 确保资源被关闭
        try (Connection connection = dbUtil.getCon(confId)) {
            DatabaseMetaData metaData = connection.getMetaData();

            // 获取当前连接的 schema 名称
            String schemaName = dbUtil.getDBNameAllDynamic(confId, false);

            // 获取表描述信息
            String descriptionQuery = "SELECT obj_description(oid) AS description FROM pg_class WHERE relname = ? AND relnamespace = (SELECT oid FROM pg_namespace WHERE nspname = ?)";

            try (PreparedStatement pstmt2 = connection.prepareStatement(descriptionQuery)) {
                pstmt2.setString(1, tableName);
                pstmt2.setString(2, schemaName);
                try (ResultSet descriptionResultSet = pstmt2.executeQuery()) {
                    if (descriptionResultSet.next()) {
                        String remarks = descriptionResultSet.getString("description");
                        if(ObjUtilX.isEmpty(remarks)){
                            throw new BizException("5001", "表描述信息为空，请补充");
                        }
                        table.setTableName(remarks);
                        table.setRemark(remarks);
                    }
                }
            }

            // 获取列信息
            try (ResultSet columns = metaData.getColumns(connection.getCatalog(), schemaName, tableName, null)) {
                tableStructure.setTableId(table.getTableId());
                tableStructure.setTableCode(table.getTableCode());
                tableStructure.setTableName(table.getTableName());
                Integer sort = 0;
                while (columns.next()) {
                    String columnName = columns.getString("COLUMN_NAME");
                    ColumnInfo column = new ColumnInfo();
                    column.setSort(sort++);
                    column.setName(columnName);
                    column.setType(columns.getString("TYPE_NAME")); // 数据类型名称
                    column.setType(column.getType().equals("bpchar") ? "CHAR" : column.getType().toUpperCase()); // 数据类型名称
                    if(column.getType().equals("TEXT") || column.getType().contains("JSON")){
                        column.setSize(0);
                    }else {
                        column.setSize(columns.getInt("COLUMN_SIZE")); // 列大小
                    }
                    column.setNullable(columns.getInt("NULLABLE") == DatabaseMetaData.columnNullable); // 是否可空
                    column.setFieldPrecision(columns.getInt("DECIMAL_DIGITS"));
                    column.setDefaultValue(columns.getString("COLUMN_DEF")); // 默认值
                    column.setRemark(columns.getString("REMARKS")); // 字段描述
                    if(ObjUtilX.isEmpty(column.getRemark())){
                        throw new BizException("5001", columnName + ":字段描述为空，请补充");
                    }
                    if(column.getRemark().length() > 100){
                        throw new BizException("5001", columnName + ":字段描述用于生成列中文名，请勿过长，其他描述请导入表之后复制到“描述”。");
                    }
                    column.setPrimaryKey(false);

                    // 判断是否为主键
                    try (ResultSet pkResultSet = metaData.getPrimaryKeys(connection.getCatalog(), schemaName, tableName)) {
                        while (pkResultSet.next()) {
                            String pkColumnName = pkResultSet.getString("COLUMN_NAME");
                            if (pkColumnName.equals(column.getName())) {
                                column.setPrimaryKey(true);
                                break;
                            }
                        }
                    }
                    // 这里不用自增
                    column.setIsAutoIncrease(0);
                    tableStructure.addColumn(column);
                }
            }

            // 获取索引信息
            String indexQuery = """
                    SELECT 
                    i.relname AS index_name, 
                    a.attname AS column_name, 
                    idx.indisunique AS is_unique, 
                    idx.indisprimary AS is_primary, 
                    idx.indkey AS indkey, 
                    am.amname AS index_type, 
                    pg_catalog.pg_get_indexdef(i.oid) AS index_definition, 
                    d.description AS index_description 
                    FROM pg_catalog.pg_index idx 
                    JOIN pg_catalog.pg_class i ON i.oid = idx.indexrelid 
                    JOIN pg_catalog.pg_class c ON c.oid = idx.indrelid 
                    JOIN pg_catalog.pg_attribute a ON a.attnum = ANY(idx.indkey) AND a.attrelid = c.oid 
                    JOIN pg_catalog.pg_am am ON am.oid = i.relam 
                    JOIN pg_catalog.pg_namespace n ON n.oid = c.relnamespace 
                    LEFT JOIN pg_catalog.pg_description d ON d.objoid = i.oid  
                    WHERE n.nspname = current_schema() AND c.relname = ?
                    """;

            try (PreparedStatement pstmt = connection.prepareStatement(indexQuery)) {
                pstmt.setString(1, tableName);
                try (ResultSet indexResultSet = pstmt.executeQuery()) {
                    while (indexResultSet.next()) {
                        String indexName = indexResultSet.getString("index_name");
                        String columnName = indexResultSet.getString("column_name");
                        boolean nonUnique = !indexResultSet.getBoolean("is_unique"); // true if it's not unique
                        boolean isPrimary = indexResultSet.getBoolean("is_primary"); // true if it's a primary key
                        String indexType = indexResultSet.getString("index_type");

                        // 过滤掉主键索引
                        if (isPrimary) {
                            continue;
                        }

                        ModelTableIndexRespVO index = new ModelTableIndexRespVO();
                        index.setFields(new ArrayList<>());
                        index.setIdxId(0L); // 新增时填充
                        index.setIdxName(indexName);
                        index.setTableId(table.getTableId());
                        index.setTableCode(tableName);
                        index.setNonUnique(nonUnique ? 1 : 0);
                        index.setFieldId(0L); // 新增时填充
                        index.setFieldCode(columnName);
                        index.setIdxType(nonUnique ? "NORMAL" : "UNIQUE"); // 索引类型
                        index.setIdxWay(indexType.toUpperCase());
                        String remark = indexResultSet.getString("index_description");
                        index.setRemark(remark); // 获取备注
                        tableStructure.addIndex(index);
                    }
                }
            }


        } catch (Exception e) {
            // 可以根据需求记录异常
            throw e; // 或者抛出自定义异常
        }

        return tableStructure;
    }
}
