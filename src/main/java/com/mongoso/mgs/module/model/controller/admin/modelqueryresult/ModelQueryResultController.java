package com.mongoso.mgs.module.model.controller.admin.modelqueryresult;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import jakarta.validation.*;
import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import static com.mongoso.mgs.framework.common.domain.ResultX.success;

import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.model.controller.admin.modelqueryresult.vo.*;
import com.mongoso.mgs.module.model.dal.db.modelqueryresult.ModelQueryResultDO;
import com.mongoso.mgs.module.model.service.modelqueryresult.ModelQueryResultService;

/**
 * 自定义查询结果 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/model")
@Validated
public class ModelQueryResultController {

    @Resource
    private ModelQueryResultService queryResultService;

    @OperateLog("自定义查询结果添加或编辑")
    @PostMapping("/modelQueryResultAdit")
    @PreAuthorize("@ss.hasPermission('modelQueryResult:adit')")
    public ResultX<Long> modelQueryResultAdit(@Valid @RequestBody ModelQueryResultAditReqVO reqVO) {
        return success(reqVO.getId() == null
                            ? queryResultService.modelQueryResultAdd(reqVO)
                            : queryResultService.modelQueryResultEdit(reqVO));
    }

    @OperateLog("自定义查询结果删除")
    @PostMapping("/modelQueryResultDel")
    @PreAuthorize("@ss.hasPermission('modelQueryResult:del')")
    public ResultX<Boolean> modelQueryResultDel(@Valid @RequestBody ModelQueryResultPrimaryReqVO reqVO) {
        queryResultService.modelQueryResultDel(reqVO.getId());
        return success(true);
    }

    @OperateLog("自定义查询结果详情")
    @PostMapping("/modelQueryResultDetail")
    @PreAuthorize("@ss.hasPermission('modelQueryResult:query')")
    public ResultX<ModelQueryResultRespVO> modelQueryResultDetail(@Valid @RequestBody ModelQueryResultPrimaryReqVO reqVO) {
        ModelQueryResultDO oldDO = queryResultService.modelQueryResultDetail(reqVO.getId());
        return success(BeanUtilX.copy(oldDO, ModelQueryResultRespVO::new));
    }

    @OperateLog("自定义查询结果列表")
    @PostMapping("/modelQueryResultList")
    @PreAuthorize("@ss.hasPermission('modelQueryResult:query')")
    public ResultX<List<ModelQueryResultRespVO>> modelQueryResultList(@Valid @RequestBody ModelQueryResultQueryReqVO reqVO) {
        List<ModelQueryResultDO> list = queryResultService.modelQueryResultList(reqVO);
        return success(BeanUtilX.copyList(list, ModelQueryResultRespVO::new));
    }

    @OperateLog("自定义查询结果分页")
    @PostMapping("/modelQueryResultPage")
    @PreAuthorize("@ss.hasPermission('modelQueryResult:query')")
    public ResultX<PageResult<ModelQueryResultRespVO>> modelQueryResultPage(@Valid @RequestBody ModelQueryResultPageReqVO reqVO) {
        PageResult<ModelQueryResultDO> pageResult = queryResultService.modelQueryResultPage(reqVO);
        return success(BeanUtilX.copyPage(pageResult, ModelQueryResultRespVO::new));
    }

}
