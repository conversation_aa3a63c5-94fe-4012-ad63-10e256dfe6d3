package com.mongoso.mgs.module.model.controller.admin.modelreport.vo;

import com.mongoso.mgs.module.model.controller.admin.modelreportconfig.vo.ModelReportConfigAditReqVO;
import lombok.*;

import java.util.List;

/**
 * 自定义报 AditReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ModelReportAditReqVO extends ModelReportBaseVO {

    // 查询条件
    List<ModelReportConfigAditReqVO> paramList;
    // 展示数据
    List<ModelReportConfigAditReqVO> resultList;
}
