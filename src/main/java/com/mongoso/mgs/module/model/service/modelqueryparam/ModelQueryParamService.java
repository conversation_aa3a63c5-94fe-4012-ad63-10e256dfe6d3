package com.mongoso.mgs.module.model.service.modelqueryparam;

import java.util.*;
import jakarta.validation.*;

import com.mongoso.mgs.module.model.controller.admin.modelquery.vo.ModelQueryAditReqVO;
import com.mongoso.mgs.module.model.controller.admin.modelqueryparam.vo.*;
import com.mongoso.mgs.module.model.dal.db.modelqueryparam.ModelQueryParamDO;
import com.mongoso.mgs.framework.common.domain.PageResult;

/**
 * 自定义查询参数 Service 接口
 *
 * <AUTHOR>
 */
public interface ModelQueryParamService {

    /**
     * 创建自定义查询参数
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long modelQueryParamAdd(@Valid ModelQueryParamAditReqVO reqVO);

    Long repeatValid(ModelQueryParamAditReqVO reqVO);

    /**
     * 更新自定义查询参数
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long modelQueryParamEdit(@Valid ModelQueryParamAditReqVO reqVO);

    /**
     * 删除自定义查询参数
     *
     * @param id 编号
     */
    void modelQueryParamDel(Long id);

    /**
     * 获得自定义查询参数信息
     *
     * @param id 编号
     * @return 自定义查询参数信息
     */
    ModelQueryParamDO modelQueryParamDetail(Long id);

    /**
     * 获得自定义查询参数列表
     *
     * @param reqVO 查询条件
     * @return 自定义查询参数列表
     */
    List<ModelQueryParamDO> modelQueryParamList(@Valid ModelQueryParamQueryReqVO reqVO);

    /**
     * 获得自定义查询参数分页
     *
     * @param reqVO 查询条件
     * @return 自定义查询参数分页
     */
    PageResult<ModelQueryParamDO> modelQueryParamPage(@Valid ModelQueryParamPageReqVO reqVO);

}
