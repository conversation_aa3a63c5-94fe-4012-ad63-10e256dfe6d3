package com.mongoso.mgs.module.model.dal.mysql.item;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.model.controller.admin.item.vo.CreateItemParams;
import com.mongoso.mgs.module.model.controller.admin.item.vo.ItemTreeVo;
import com.mongoso.mgs.module.model.dal.db.item.ItemDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/6
 * @description
 */
@Mapper
public interface ItemMapper extends BaseMapper<ItemDO> {

    /**
     * 新增文件
     * @param params
     * @return
     */
    void insertItemInfo(CreateItemParams params);

    /**
     * 查询项目外文件列表(包括项目)
     * @return
     */
    List<ItemTreeVo> queryItemOutList(@Param("projectId") Long projectId, @Param("itemName") String itemName,
                                       @Param("category") Integer category);

    /**
     * 查询项目内文件列表
     * @return
     */
    List<ItemTreeVo> queryItemInList(@Param("projectIds") List<Long> projectIds,  @Param("category") Integer category);

    /**
     * 更新排序号+1
     * @param parentItemId
     * @param itemSeq
     * @return
     */
    Integer updateItemSeqAddOne(@Param("parentItemId") Long parentItemId, @Param("itemSeq") int itemSeq);

    /**
     *
     * @param opType 0:修改code,1:修改name
     * @param projectId
     * @param apiUrl
     * @param oldName
     * @param newName
     * @return
     */
    Integer updateApiField(@Param("opType") Integer opType, @Param("projectId") Long projectId, @Param("apiUrl") String apiUrl,
                           @Param("oldName") String oldName, @Param("newName") String newName);

    void batchUpdateItemSeq(@Param("items") List<ItemDO> items);

    default ItemDO queryItemByUrl(Long projectId, String apiUrl){
        return selectOne(LambdaQueryWrapperX.<ItemDO>lambdaQueryX()
                .eq(ItemDO::getProjectId, projectId)
                .eq(ItemDO::getApiUrl, apiUrl)
                .last("limit 1")
        );
    }
}
