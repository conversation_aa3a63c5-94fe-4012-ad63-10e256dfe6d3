package com.mongoso.mgs.module.model.controller.admin.modeltable.vo;

import lombok.*;

import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 图形建模主 QueryReqVO
 *
 * <AUTHOR>
 */
@Data
public class ModelTableQueryReqVO {

    /** 模型表id */
    private Long tableId;

    /** 数据源id */
    private Long dataSourceConfigId;

    /** 项目id */
    private Long projectId;

    /** 项目编码 */
    private String projectCode;

    /** 模型表中文名 */
    private String tableName;

    /** 模型表实名 */
    private String tableCode;

    /** 备注描述 */
    private String remark;

    /** 升级标记 */
    private Integer upgradeFlag;

    /** 是否生成过 */
    private Integer isGen;

    /** 类型 */
    private Integer dirType;

    /** 父节点 */
    private Long parentId;
    /** 属性类型，0：系统，1：用户 */
    private Integer propType;
    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createdDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] updatedDt;

}
