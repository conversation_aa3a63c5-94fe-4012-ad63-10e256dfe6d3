package com.mongoso.mgs.module.model.dal.mysql.modelreportconfig;

import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.module.model.dal.db.modelreportconfig.ModelReportConfigDO;
import org.apache.ibatis.annotations.Mapper;
import com.mongoso.mgs.module.model.controller.admin.modelreportconfig.vo.*;

/**
 * 自定义报表配置 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ModelReportConfigMapper extends BaseMapperX<ModelReportConfigDO> {

    default PageResult<ModelReportConfigDO> selectPage(ModelReportConfigPageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<ModelReportConfigDO>lambdaQueryX()
                .eqIfPresent(ModelReportConfigDO::getReportId, reqVO.getReportId())
                .eqIfPresent(ModelReportConfigDO::getDataTable, reqVO.getDataTable())
                .eqIfPresent(ModelReportConfigDO::getFieldCode, reqVO.getFieldCode())
                .likeIfPresent(ModelReportConfigDO::getFieldName, reqVO.getFieldName())
                .eqIfPresent(ModelReportConfigDO::getControlType, reqVO.getControlType())
                .eqIfPresent(ModelReportConfigDO::getFieldType, reqVO.getFieldType())
                .eqIfPresent(ModelReportConfigDO::getLeng, reqVO.getLeng())
                .eqIfPresent(ModelReportConfigDO::getFieldPrecision, reqVO.getFieldPrecision())
                .eqIfPresent(ModelReportConfigDO::getRegExpression, reqVO.getRegExpression())
                .eqIfPresent(ModelReportConfigDO::getDataMask, reqVO.getDataMask())
                .eqIfPresent(ModelReportConfigDO::getIsEdit, reqVO.getIsEdit())
                .eqIfPresent(ModelReportConfigDO::getIsShow, reqVO.getIsShow())
                .eqIfPresent(ModelReportConfigDO::getEmptyShow, reqVO.getEmptyShow())
                .eqIfPresent(ModelReportConfigDO::getIsRequired, reqVO.getIsRequired())
                .eqIfPresent(ModelReportConfigDO::getDefaultVal, reqVO.getDefaultVal())
                .eqIfPresent(ModelReportConfigDO::getRemark, reqVO.getRemark())
                .eqIfPresent(ModelReportConfigDO::getVersionNo, reqVO.getVersionNo())
                .eqIfPresent(ModelReportConfigDO::getCategory, reqVO.getCategory())
                .betweenIfPresent(ModelReportConfigDO::getCreatedDt, reqVO.getCreatedDt())
                .orderByDesc(ModelReportConfigDO::getCreatedDt));
    }

    default List<ModelReportConfigDO> selectList(ModelReportConfigQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<ModelReportConfigDO>lambdaQueryX()
                .eqIfPresent(ModelReportConfigDO::getReportId, reqVO.getReportId())
                .eqIfPresent(ModelReportConfigDO::getDataTable, reqVO.getDataTable())
                .eqIfPresent(ModelReportConfigDO::getFieldCode, reqVO.getFieldCode())
                .likeIfPresent(ModelReportConfigDO::getFieldName, reqVO.getFieldName())
                .eqIfPresent(ModelReportConfigDO::getControlType, reqVO.getControlType())
                .eqIfPresent(ModelReportConfigDO::getFieldType, reqVO.getFieldType())
                .eqIfPresent(ModelReportConfigDO::getLeng, reqVO.getLeng())
                .eqIfPresent(ModelReportConfigDO::getFieldPrecision, reqVO.getFieldPrecision())
                .eqIfPresent(ModelReportConfigDO::getRegExpression, reqVO.getRegExpression())
                .eqIfPresent(ModelReportConfigDO::getDataMask, reqVO.getDataMask())
                .eqIfPresent(ModelReportConfigDO::getIsEdit, reqVO.getIsEdit())
                .eqIfPresent(ModelReportConfigDO::getIsShow, reqVO.getIsShow())
                .eqIfPresent(ModelReportConfigDO::getEmptyShow, reqVO.getEmptyShow())
                .eqIfPresent(ModelReportConfigDO::getIsRequired, reqVO.getIsRequired())
                .eqIfPresent(ModelReportConfigDO::getDefaultVal, reqVO.getDefaultVal())
                .eqIfPresent(ModelReportConfigDO::getRemark, reqVO.getRemark())
                .eqIfPresent(ModelReportConfigDO::getVersionNo, reqVO.getVersionNo())
                .eqIfPresent(ModelReportConfigDO::getCategory, reqVO.getCategory())
                .betweenIfPresent(ModelReportConfigDO::getCreatedDt, reqVO.getCreatedDt())
                    .orderByDesc(ModelReportConfigDO::getCreatedDt));
    }

}