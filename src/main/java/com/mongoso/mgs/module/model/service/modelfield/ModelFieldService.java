package com.mongoso.mgs.module.model.service.modelfield;

import java.util.*;
import jakarta.validation.*;
import com.mongoso.mgs.module.model.controller.admin.modelfield.vo.*;
import com.mongoso.mgs.module.model.dal.db.modelfield.ModelFieldDO;
import com.mongoso.mgs.framework.common.domain.PageResult;

/**
 * 图形建模字段 Service 接口
 *
 * <AUTHOR>
 */
public interface ModelFieldService {

    /**
     * 创建图形建模字段
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long modelFieldAdd(@Valid ModelFieldAditReqVO reqVO);

    /**
     * 更新图形建模字段
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long modelFieldEdit(@Valid ModelFieldAditReqVO reqVO);

    /**
     * 删除图形建模字段
     *
     * @param fieldId 编号
     */
    void modelFieldDel(Long fieldId);

    /**
     * 获得图形建模字段信息
     *
     * @param fieldId 编号
     * @return 图形建模字段信息
     */
    ModelFieldDO modelFieldDetail(Long fieldId);

    /**
     * 获得图形建模字段列表
     *
     * @param reqVO 查询条件
     * @return 图形建模字段列表
     */
    List<ModelFieldDO> modelFieldList(@Valid ModelFieldQueryReqVO reqVO);

    /**
     * 获得图形建模字段分页
     *
     * @param reqVO 查询条件
     * @return 图形建模字段分页
     */
    PageResult<ModelFieldDO> modelFieldPage(@Valid ModelFieldPageReqVO reqVO);

}
