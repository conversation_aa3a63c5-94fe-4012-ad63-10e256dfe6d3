package com.mongoso.mgs.module.model.service.modelfunctionversion;

import java.util.*;
import jakarta.validation.*;
import com.mongoso.mgs.module.model.controller.admin.modelfunctionversion.vo.*;
import com.mongoso.mgs.module.model.dal.db.modelfunctionversion.ModelFunctionVersionDO;
import com.mongoso.mgs.framework.common.domain.PageResult;

/**
 * 自定义报表版本 Service 接口
 *
 * <AUTHOR>
 */
public interface ModelFunctionVersionService {

    /**
     * 创建自定义报表版本
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long modelFunctionVersionAdd(@Valid ModelFunctionVersionAditReqVO reqVO);

    /**
     * 更新自定义报表版本
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long modelFunctionVersionEdit(@Valid ModelFunctionVersionAditReqVO reqVO);

    /**
     * 删除自定义报表版本
     *
     * @param funId 编号
     */
    void modelFunctionVersionDel(Long funId);

    /**
     * 获得自定义报表版本信息
     *
     * @param funId 编号
     * @return 自定义报表版本信息
     */
    ModelFunctionVersionDO modelFunctionVersionDetail(Long funId);

    /**
     * 获得自定义报表版本列表
     *
     * @param reqVO 查询条件
     * @return 自定义报表版本列表
     */
    List<ModelFunctionVersionDO> modelFunctionVersionList(@Valid ModelFunctionVersionQueryReqVO reqVO);

    /**
     * 获得自定义报表版本分页
     *
     * @param reqVO 查询条件
     * @return 自定义报表版本分页
     */
    PageResult<ModelFunctionVersionDO> modelFunctionVersionPage(@Valid ModelFunctionVersionPageReqVO reqVO);

}
