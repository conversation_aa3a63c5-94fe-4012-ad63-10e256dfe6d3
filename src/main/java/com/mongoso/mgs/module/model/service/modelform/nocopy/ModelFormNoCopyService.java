package com.mongoso.mgs.module.model.service.modelform.nocopy;

import java.util.*;
import jakarta.validation.*;
import com.mongoso.mgs.module.model.controller.admin.modelform.nocopy.vo.*;
import com.mongoso.mgs.module.model.dal.db.modelform.nocopy.ModelFormNoCopyDO;
import com.mongoso.mgs.framework.common.domain.PageResult;

/**
 * 单据不可复制字段 Service 接口
 *
 * <AUTHOR>
 */
public interface ModelFormNoCopyService {

    /**
     * 创建单据不可复制字段
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long modelFormNoCopyAdd(@Valid ModelFormNoCopyAditReqVO reqVO);

    /**
     * 更新单据不可复制字段
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long modelFormNoCopyEdit(@Valid ModelFormNoCopyAditReqVO reqVO);

    /**
     * 删除单据不可复制字段
     *
     * @param dataId 编号
     */
    void modelFormNoCopyDelete(Long dataId);

    /**
     * 获得单据不可复制字段信息
     *
     * @param dataId 编号
     * @return 单据不可复制字段信息
     */
    ModelFormNoCopyRespVO modelFormNoCopyDetail(Long dataId);

    /**
     * 获得单据不可复制字段列表
     *
     * @param reqVO 查询条件
     * @return 单据不可复制字段列表
     */
    List<ModelFormNoCopyRespVO> modelFormNoCopyList(@Valid ModelFormNoCopyQueryReqVO reqVO);

    /**
     * 获得单据不可复制字段分页
     *
     * @param reqVO 查询条件
     * @return 单据不可复制字段分页
     */
    PageResult<ModelFormNoCopyRespVO> modelFormNoCopyPage(@Valid ModelFormNoCopyPageReqVO reqVO);

    /**
     * 批量新增单据建模不可复制字段
     *
     * @param noCopyDOList 不可复制字段DO列表
     */
    void modelFormNoCopyBatchAdd(List<ModelFormNoCopyDO> noCopyDOList);

    /**
     * 根据单据建模编码批量删除不可复制字段
     *
     * @param modelFormCode 单据建模编码
     */
    void modelFormNoCopyBatchDeleteByCode(String modelFormCode);

}
