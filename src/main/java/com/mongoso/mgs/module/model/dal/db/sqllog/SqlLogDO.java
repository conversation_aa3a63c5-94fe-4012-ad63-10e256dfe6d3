package com.mongoso.mgs.module.model.dal.db.sqllog;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mongoso.mgs.framework.mybatis.core.pojo.BaseDO;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;
import lombok.*;

/**
 * 脚本日志 DO
 *
 * <AUTHOR>
 */
@TableName(value = "lowcode.sys_sql_log", autoResultMap = true)
//@KeySequence("sys_sql_log_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SqlLogDO extends BaseDO {

    /** 主键 */
        @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /** 项目id */
    private Long projectId;

    private Long tableId;

    /** 表名 */
    private String tableCode;

    /** 脚本 */
    private String sql;

    /** 操作类别 [建表，新增字段，修改字段，删除字段，修改表，删除表，清空数据] */
    private Integer opType;

    /** 执行状态 [失败，成功] */
    private Integer sucStatus;

    /** 异常信息 */
    private String errMgs;

    // 初始化属性的构造方法
    public SqlLogDO(Long projectId, Long tableId, String tableCode, String sql, Integer opType, Integer sucStatus, String errMgs) {
        this.projectId = projectId;
        this.tableId = tableId;
        this.tableCode = tableCode;
        this.sql = sql;
        this.opType = opType;
        this.sucStatus = sucStatus;
        this.errMgs = errMgs;
    }

}
