package com.mongoso.mgs.module.model.controller.admin.modelbiztable.detail.vo;

import lombok.*;

    


/**
 * 子表明细 QueryReqVO
 *
 * <AUTHOR>
 */
@Data
public class ModelBizTableDetailQueryReqVO {

    /** 数据id */
    private Long dataId;

    /** 明细表id */
    private Long tableId;

    /** 主表业务编码 */
    private String bizCode;

    /** 明细表 */
    private String tableCode;

    /** 明细表名称 */
    private String tableName;

    /** 级别 */
    private Integer level;

    /** 子表类型[0:表格 1:表格树] */
    private Integer tableType;

    /** 是否必有一行 */
    private Short isNullAble;

    /** 父数据id */
    private Long parentDataId;

    /** 备注 */
    private String remark;

}
