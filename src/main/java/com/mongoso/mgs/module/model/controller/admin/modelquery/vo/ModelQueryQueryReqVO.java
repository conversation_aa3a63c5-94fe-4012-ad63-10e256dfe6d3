package com.mongoso.mgs.module.model.controller.admin.modelquery.vo;

import lombok.*;

import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 自定义查询 QueryReqVO
 *
 * <AUTHOR>
 */
@Data
public class ModelQueryQueryReqVO implements Serializable {

    /** 主键ID */
    private Long queryId;
    private Long dataSourceConfigId;
    /** 查询编码 */
    private String queryCode;

    /** 查询中文名 */
    private String queryName;

    /** 查询语句 */
    private String queryStatment;

    /** 备注 */
    private String remark;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createdDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] updatedDt;

}
