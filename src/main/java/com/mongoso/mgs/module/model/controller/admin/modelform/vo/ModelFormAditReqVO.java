package com.mongoso.mgs.module.model.controller.admin.modelform.vo;

import com.mongoso.mgs.module.model.controller.admin.modelform.button.vo.ModelFormButtonAditReqVO;
import com.mongoso.mgs.module.model.controller.admin.modelform.nocopy.vo.ModelFormNoCopyAditReqVO;
import lombok.*;

import java.util.List;

/**
 * 单据建模主 AditReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ModelFormAditReqVO extends ModelFormBaseVO {

    /** 单据建模按钮列表 */
    private List<ModelFormButtonAditReqVO> modelFormButtonList;

    /** 单据建模不可复制字段列表 */
    private List<ModelFormNoCopyAditReqVO> modelFormNoCopyList;

}
