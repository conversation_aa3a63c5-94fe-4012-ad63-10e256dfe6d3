package com.mongoso.mgs.module.model.service.model;

import com.mongoso.mgs.module.model.controller.admin.modeltable.vo.ModelTableExportReqVO;
import com.mongoso.mgs.module.model.controller.admin.modeltable.vo.ModelTablePrimaryReqVO;
import com.mongoso.mgs.module.model.controller.admin.modeltable.vo.SchemaReqVO;
import org.springframework.core.io.InputStreamResource;

import jakarta.validation.constraints.NotNull;

/**
 * 建模 Service
 *
 * <AUTHOR>
 * @date 2024-9-12 14:59:04
 */
public interface SchemaService {

    /**
     * 新增
     */
    String schemaAdd(SchemaReqVO req);

    /**
     * 删除
     */
    String schemaDel(String id);


    String tableGenerate(ModelTablePrimaryReqVO reqVO);

    String modelTableDDL(@NotNull(message = "模型表id不能为空") Long tableId);

    void modelTableExportSQL(ModelTableExportReqVO reqVO);
}

