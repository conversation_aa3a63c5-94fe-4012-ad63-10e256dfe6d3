package com.mongoso.mgs.module.model.controller.admin.item.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by Fashion on 2020/2/19.
 */
@Data
public class CreateItemParams implements Serializable {
	
    private static final long serialVersionUID = 1L;

    private Long itemId;
    
    private String itemName;
    private String content;
    private String description;
    private String apiUrl = "/";
    /**
     * 文件类型 0:文件夹 1:项目文件夹 2:项目内文件夹 3:文件集 5:思维导图 6:数据导图
     */
    private Integer itemType;
    
    private int itemSeq;
    
    private Long parentItemId;
        
    private Long userId;
    
    private String isDefault;
    
    private Long projectId;

    private String projectType;

    /**
     * 任务集类型 ["信息架构图导图", "描述"]
     */
    private Integer saveType;

    private Integer taskSetType;
    
    private String createUser;
    
    private Date createTime;

    private Integer queryType = 0;
    /** 大类别 0:架构，1：接口 */
    private Integer category;
}
