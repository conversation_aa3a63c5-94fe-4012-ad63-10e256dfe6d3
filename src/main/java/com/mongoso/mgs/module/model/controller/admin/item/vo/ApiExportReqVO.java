package com.mongoso.mgs.module.model.controller.admin.item.vo;

import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * 接口文档导出
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
public class ApiExportReqVO implements Serializable {

    HttpServletResponse response;
    /** 模型表id集合 */
    @NotNull(message = "id集合不能为空")
    private List<Long> itemIds;

    @NotEmpty(message = "文件名不能为空")
    private String fileName;
    private String baseUrl;
    //0:openAPI方式
    private Integer apiType = 0;
}
