package com.mongoso.mgs.module.model.controller.admin.modelquery.vo;

import com.alibaba.fastjson.JSONObject;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 自定义查询 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class ModelQueryBaseVO implements Serializable {

    /** 主键ID */
    private Long queryId;

    private Long dataSourceConfigId;

    /** 查询编码 */
    @NotEmpty(message = "查询编码不能为空")
    private String queryCode;

    /** 查询中文名 */
    @NotEmpty(message = "查询中文名不能为空")
    private String queryName;

    /** 查询语句 */
//    @NotEmpty(message = "查询语句不能为空")
    private String queryStatment;

    /** 备注 */
    private String remark;

    /** 类型 0:目录，1：查询 */
    private Integer dirType = 1;

    /** 父节点 */
    private Long parentId;

    /** 类型配置 */
    private List<JSONObject> typeConf;
}
