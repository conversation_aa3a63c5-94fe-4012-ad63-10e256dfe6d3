package com.mongoso.mgs.module.model.service.modelfunctionhistory;

import java.util.*;
import jakarta.validation.*;
import com.mongoso.mgs.module.model.controller.admin.modelfunctionhistory.vo.*;
import com.mongoso.mgs.module.model.dal.db.modelfunctionhistory.ModelFunctionHistoryDO;
import com.mongoso.mgs.framework.common.domain.PageResult;

/**
 * 自定义函数历史 Service 接口
 *
 * <AUTHOR>
 */
public interface ModelFunctionHistoryService {

    /**
     * 创建自定义函数历史
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long modelFunctionHistoryAdd(@Valid ModelFunctionHistoryAditReqVO reqVO);

    /**
     * 更新自定义函数历史
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long modelFunctionHistoryEdit(@Valid ModelFunctionHistoryAditReqVO reqVO);

    /**
     * 删除自定义函数历史
     *
     * @param dId 编号
     */
    void modelFunctionHistoryDel(Long dId);

    /**
     * 获得自定义函数历史信息
     *
     * @param dId 编号
     * @return 自定义函数历史信息
     */
    ModelFunctionHistoryDO modelFunctionHistoryDetail(Long dId);

    /**
     * 获得自定义函数历史列表
     *
     * @param reqVO 查询条件
     * @return 自定义函数历史列表
     */
    List<ModelFunctionHistoryDO> modelFunctionHistoryList(@Valid ModelFunctionHistoryQueryReqVO reqVO);

    /**
     * 获得自定义函数历史分页
     *
     * @param reqVO 查询条件
     * @return 自定义函数历史分页
     */
    PageResult<ModelFunctionHistoryDO> modelFunctionHistoryPage(@Valid ModelFunctionHistoryPageReqVO reqVO);

}
