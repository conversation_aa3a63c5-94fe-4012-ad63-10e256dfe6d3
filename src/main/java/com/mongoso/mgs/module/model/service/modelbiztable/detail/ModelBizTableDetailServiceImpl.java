package com.mongoso.mgs.module.model.service.modelbiztable.detail;

import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.mongoso.mgs.module.model.controller.admin.modelbiztable.detail.vo.*;
import com.mongoso.mgs.module.model.dal.db.modelbiztable.detail.ModelBizTableDetailDO;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.module.model.dal.mysql.modelbiztable.detail.ModelBizTableDetailMapper;

// import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exception;
import com.mongoso.mgs.framework.common.exception.BizException;
// import static com.mongoso.mgs.module.model.enums.ErrorCodeConstants.*;


/**
 * 子表明细 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ModelBizTableDetailServiceImpl implements ModelBizTableDetailService {

    @Resource
    private ModelBizTableDetailMapper bizTableDetailMapper;

    @Override
    public Long modelBizTableDetailAdd(ModelBizTableDetailAditReqVO reqVO) {
        // 插入
        ModelBizTableDetailDO bizTableDetail = BeanUtilX.copy(reqVO, ModelBizTableDetailDO::new);
        bizTableDetailMapper.insert(bizTableDetail);
        // 返回
        return bizTableDetail.getDataId();
    }

    @Override
    public Long modelBizTableDetailEdit(ModelBizTableDetailAditReqVO reqVO) {
        // 校验存在
        this.modelBizTableDetailValidateExists(reqVO.getDataId());
        // 更新
        ModelBizTableDetailDO bizTableDetail = BeanUtilX.copy(reqVO, ModelBizTableDetailDO::new);
        bizTableDetailMapper.updateById(bizTableDetail);
        // 返回
        return bizTableDetail.getDataId();
    }

    @Override
    public void modelBizTableDetailDelete(Long dataId) {
        // 校验存在
        this.modelBizTableDetailValidateExists(dataId);
        // 删除
        bizTableDetailMapper.deleteById(dataId);
    }

    private ModelBizTableDetailDO modelBizTableDetailValidateExists(Long dataId) {
        ModelBizTableDetailDO bizTableDetail = bizTableDetailMapper.selectById(dataId);
        if (bizTableDetail == null) {
            // throw exception(BIZ_TABLE_DETAIL_NOT_EXISTS);
            throw new BizException("5001", "子表明细不存在");
        }
        return bizTableDetail;
    }

    @Override
    public ModelBizTableDetailRespVO modelBizTableDetailDetail(Long dataId) {
        ModelBizTableDetailDO data = bizTableDetailMapper.selectById(dataId);
        return BeanUtilX.copy(data, ModelBizTableDetailRespVO::new);
    }

    @Override
    public List<ModelBizTableDetailRespVO> modelBizTableDetailList(ModelBizTableDetailQueryReqVO reqVO) {
        List<ModelBizTableDetailDO> data = bizTableDetailMapper.selectList(reqVO);
        return BeanUtilX.copy(data, ModelBizTableDetailRespVO::new);
    }

    @Override
    public PageResult<ModelBizTableDetailRespVO> modelBizTableDetailPage(ModelBizTableDetailPageReqVO reqVO) {
        PageResult<ModelBizTableDetailDO> data = bizTableDetailMapper.selectPage(reqVO);
        return BeanUtilX.copy(data, ModelBizTableDetailRespVO::new);
    }

}
