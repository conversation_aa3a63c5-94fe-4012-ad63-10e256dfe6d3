package com.mongoso.mgs.module.model.service.comp;

import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.ObjUtilX;
import com.mongoso.mgs.module.model.controller.admin.comp.vo.ComponentBaseVO;
import com.mongoso.mgs.module.model.dal.db.comp.ComponentDO;
import com.mongoso.mgs.module.model.dal.mysql.comp.CompMapper;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/2/11
 * @description
 */

@Service
@Log4j2
public class ComponentServiceImpl implements ComponentService{

    @Resource
    private CompMapper compMapper;


    @Override
    public Boolean saveProjectComp(ComponentBaseVO comp) {
        if(ObjUtilX.isEmpty(comp.getCmptList())){
            throw new BizException("5001", "组件列表不能为空");
        }
        ComponentDO exist = compMapper.selectById(1L);
        if(ObjUtilX.isEmpty(exist)){
            exist = new ComponentDO();
            exist.setCompId(1L);
            exist.setCmptList(comp.getCmptList());
            compMapper.insert(exist);
        }else{
            exist.setCmptList(comp.getCmptList());
            compMapper.updateById(exist);
        }
        return true;
    }

    @Override
    public ComponentDO queryProjectComp() {
        return compMapper.selectById(1L);
    }
}
