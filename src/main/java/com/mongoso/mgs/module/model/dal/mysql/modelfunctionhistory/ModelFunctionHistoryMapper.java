package com.mongoso.mgs.module.model.dal.mysql.modelfunctionhistory;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.model.controller.admin.modelfunctionhistory.vo.ModelFunctionHistoryPageReqVO;
import com.mongoso.mgs.module.model.controller.admin.modelfunctionhistory.vo.ModelFunctionHistoryQueryReqVO;
import com.mongoso.mgs.module.model.dal.db.modelfunctionhistory.ModelFunctionHistoryDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 自定义函数历史 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ModelFunctionHistoryMapper extends BaseMapperX<ModelFunctionHistoryDO> {

    default PageResult<ModelFunctionHistoryDO> selectPage(ModelFunctionHistoryPageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<ModelFunctionHistoryDO>lambdaQueryX()
                .eqIfPresent(ModelFunctionHistoryDO::getFunId, reqVO.getFunId())
                .likeIfPresent(ModelFunctionHistoryDO::getFunName, reqVO.getFunName())
                .eqIfPresent(ModelFunctionHistoryDO::getFunCode, reqVO.getFunCode())
                .eqIfPresent(ModelFunctionHistoryDO::getFunBody, reqVO.getFunBody())
                .eqIfPresent(ModelFunctionHistoryDO::getRunEnv, reqVO.getRunEnv())
                .eqIfPresent(ModelFunctionHistoryDO::getTimeout, reqVO.getTimeout())
                .eqIfPresent(ModelFunctionHistoryDO::getRemark, reqVO.getRemark())
                .eqIfPresent(ModelFunctionHistoryDO::getVersionNo, reqVO.getVersionNo())
                .eqIfPresent(ModelFunctionHistoryDO::getIsPublish, reqVO.getIsPublish())
                .eqIfPresent(ModelFunctionHistoryDO::getIsLock, reqVO.getIsLock())
                .eqIfPresent(ModelFunctionHistoryDO::getDirType, reqVO.getDirType())
                .eqIfPresent(ModelFunctionHistoryDO::getParentId, reqVO.getParentId())
                .eqIfPresent(ModelFunctionHistoryDO::getPropType, reqVO.getPropType())
                .betweenIfPresent(ModelFunctionHistoryDO::getCreatedDt, reqVO.getCreatedDt())
                .orderByDesc(ModelFunctionHistoryDO::getCreatedDt));
    }

    default List<ModelFunctionHistoryDO> selectList(ModelFunctionHistoryQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<ModelFunctionHistoryDO>lambdaQueryX()
                .eqIfPresent(ModelFunctionHistoryDO::getFunId, reqVO.getFunId())
                .likeIfPresent(ModelFunctionHistoryDO::getFunName, reqVO.getFunName())
                .eqIfPresent(ModelFunctionHistoryDO::getFunCode, reqVO.getFunCode())
                .eqIfPresent(ModelFunctionHistoryDO::getFunBody, reqVO.getFunBody())
                .eqIfPresent(ModelFunctionHistoryDO::getRunEnv, reqVO.getRunEnv())
                .eqIfPresent(ModelFunctionHistoryDO::getTimeout, reqVO.getTimeout())
                .eqIfPresent(ModelFunctionHistoryDO::getRemark, reqVO.getRemark())
                .eqIfPresent(ModelFunctionHistoryDO::getVersionNo, reqVO.getVersionNo())
                .eqIfPresent(ModelFunctionHistoryDO::getIsPublish, reqVO.getIsPublish())
                .eqIfPresent(ModelFunctionHistoryDO::getIsLock, reqVO.getIsLock())
                .eqIfPresent(ModelFunctionHistoryDO::getDirType, reqVO.getDirType())
                .eqIfPresent(ModelFunctionHistoryDO::getParentId, reqVO.getParentId())
                .eqIfPresent(ModelFunctionHistoryDO::getPropType, reqVO.getPropType())
                .betweenIfPresent(ModelFunctionHistoryDO::getCreatedDt, reqVO.getCreatedDt())
                    .orderByDesc(ModelFunctionHistoryDO::getCreatedDt));
    }

    @Select({
            "SELECT *",
            "FROM lowcode.sys_model_function_history",
            "WHERE fun_id = #{funId}",
            "AND version_no = (",
            "    SELECT MAX(version_no)",
            "    FROM lowcode.sys_model_function_history",
            "    WHERE fun_id = #{funId}",
            ") LIMIT 1"
    })
    ModelFunctionHistoryDO findLatestVersionByFunId(@Param("funId") Long funId);


    @Select({
            "SELECT *",
            "FROM lowcode.sys_model_function_history",
            "WHERE fun_code = #{funCode}",
            "AND version_no = (",
            "    SELECT MAX(version_no)",
            "    FROM lowcode.sys_model_function_history",
            "    WHERE fun_code = #{funCode}",
            ") LIMIT 1"
    })
    ModelFunctionHistoryDO findLatestVersionByFunCode(@Param("funCode") String funCode);

}