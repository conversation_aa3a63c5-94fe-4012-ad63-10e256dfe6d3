package com.mongoso.mgs.module.model.controller.admin.modelreport.vo;

import lombok.*;

import jakarta.validation.constraints.*;
import java.io.Serializable;

/**
 * 自定义报 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class ModelReportBaseVO implements Serializable {

    /** 报表id   */
    private Long reportId;

    /** 报表名称 */
    @NotEmpty(message = "报表名称不能为空")
    private String reportName;

    /** 报表编号 */
//    @NotEmpty(message = "报表编号不能为空")
    private String reportCode;

    /** 查询id   */
//    @NotNull(message = "查询id不能为空")
    private Long queryId;

    /** 备注   */
    private String remark;

    /** 版本号 */
//    @NotNull(message = "版本号不能为空")
    private Integer versionNo;

    /** 是否发布 */
//    @NotNull(message = "是否发布不能为空")
    private Integer isPublish;

    /** 类型 */
    private Integer dirType = 1;
    /** 父节点 */
    private Long parentId;
}
