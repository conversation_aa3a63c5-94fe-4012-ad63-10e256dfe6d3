package com.mongoso.mgs.module.model.controller.admin.modelreportversion;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import jakarta.validation.*;
import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import static com.mongoso.mgs.framework.common.domain.ResultX.success;

import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.model.controller.admin.modelreportversion.vo.*;
import com.mongoso.mgs.module.model.dal.db.modelreportversion.ModelReportVersionDO;
import com.mongoso.mgs.module.model.service.modelreportversion.ModelReportVersionService;

/**
 * 自定义报表版本 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/model")
@Validated
public class ModelReportVersionController {

    @Resource
    private ModelReportVersionService reportVersionService;

    @OperateLog("自定义报表版本添加或编辑")
    @PostMapping("/modelReportVersionAdit")
    @PreAuthorize("@ss.hasPermission('modelReportVersion:adit')")
    public ResultX<Long> modelReportVersionAdit(@Valid @RequestBody ModelReportVersionAditReqVO reqVO) {
        return success(reqVO.getReportId() == null
                            ? reportVersionService.modelReportVersionAdd(reqVO)
                            : reportVersionService.modelReportVersionEdit(reqVO));
    }

    @OperateLog("自定义报表版本删除")
    @PostMapping("/modelReportVersionDel")
    @PreAuthorize("@ss.hasPermission('modelReportVersion:del')")
    public ResultX<Boolean> modelReportVersionDel(@Valid @RequestBody ModelReportVersionPrimaryReqVO reqVO) {
        reportVersionService.modelReportVersionDel(reqVO.getReportId());
        return success(true);
    }

    @OperateLog("自定义报表版本详情")
    @PostMapping("/modelReportVersionDetail")
    @PreAuthorize("@ss.hasPermission('modelReportVersion:query')")
    public ResultX<ModelReportVersionRespVO> modelReportVersionDetail(@Valid @RequestBody ModelReportVersionPrimaryReqVO reqVO) {
        ModelReportVersionDO oldDO = reportVersionService.modelReportVersionDetail(reqVO.getReportId());
        return success(BeanUtilX.copy(oldDO, ModelReportVersionRespVO::new));
    }

    @OperateLog("自定义报表版本列表")
    @PostMapping("/modelReportVersionList")
    @PreAuthorize("@ss.hasPermission('modelReportVersion:query')")
    public ResultX<List<ModelReportVersionRespVO>> modelReportVersionList(@Valid @RequestBody ModelReportVersionQueryReqVO reqVO) {
        List<ModelReportVersionDO> list = reportVersionService.modelReportVersionList(reqVO);
        return success(BeanUtilX.copyList(list, ModelReportVersionRespVO::new));
    }

    @OperateLog("自定义报表版本分页")
    @PostMapping("/modelReportVersionPage")
    @PreAuthorize("@ss.hasPermission('modelReportVersion:query')")
    public ResultX<PageResult<ModelReportVersionRespVO>> modelReportVersionPage(@Valid @RequestBody ModelReportVersionPageReqVO reqVO) {
        PageResult<ModelReportVersionDO> pageResult = reportVersionService.modelReportVersionPage(reqVO);
        return success(BeanUtilX.copyPage(pageResult, ModelReportVersionRespVO::new));
    }

}
