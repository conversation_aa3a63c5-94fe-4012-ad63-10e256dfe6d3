package com.mongoso.mgs.module.model.controller.admin.modeltable;

import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.model.controller.admin.modeltable.vo.ModelTableExportReqVO;
import com.mongoso.mgs.module.model.controller.admin.modeltable.vo.ModelTablePrimaryReqVO;
import com.mongoso.mgs.module.model.controller.admin.modeltable.vo.SchemaReqVO;
import com.mongoso.mgs.module.model.service.model.SchemaService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;

import static com.mongoso.mgs.framework.common.domain.ResultX.success;

/**
 * 建模 Controller
 * daijinbiao
 * 2024-9-12 14:29:28
 */
@RestController
@RequestMapping("/model")
@Validated
@Log4j2
public class ModelGenController {

    @Autowired
    private SchemaService schemaService;

    @OperateLog("直接操作数据库建表，暂时不用")
    @PostMapping("/modelTableGenerate")
    public ResultX<String> createTable(@RequestBody SchemaReqVO reqVO) {
        return success(schemaService.schemaAdd(reqVO));
    }

    @OperateLog("模型表执行建表")
    @PostMapping("/tableGenerate")
    public ResultX<String> tableGenerate(@RequestBody ModelTablePrimaryReqVO reqVO) {
        return success(schemaService.tableGenerate(reqVO));
    }

    @OperateLog("图形建模主DDL")
    @PostMapping("/modelTableDDL")
    @PreAuthorize("@ss.hasPermission('modelTable:query')")
    public ResultX<String> modelTableDDL(@Valid @RequestBody ModelTablePrimaryReqVO reqVO) {
        return success(schemaService.modelTableDDL(reqVO.getTableId()));
    }

    @OperateLog("批量导出")
    @PostMapping("/modelTableExportSQL")
    @PreAuthorize("@ss.hasPermission('modelTable:query')")
    public void modelTableExportSQL(@RequestBody ModelTableExportReqVO reqVO, HttpServletResponse response) {
        try {
            reqVO.setResponse(response);
            schemaService.modelTableExportSQL(reqVO);
        } catch (Exception e) {
            // 处理其他异常，设置 HTTP 500 状态码
            response.setCharacterEncoding("utf-8");
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR); // 500错误
            try {
                response.getWriter().write("文件生成失败，原因：" + e.getMessage()); // 返回错误信息
                response.getWriter().flush();
            } catch (IOException ex) {
                // 处理写入响应内容时的异常
                throw new RuntimeException("无法返回错误信息", ex);
            }
        }
    }
}
