package com.mongoso.mgs.module.model.controller.admin.modeltable.vo;

import lombok.*;

import com.mongoso.mgs.framework.common.domain.PageParam;






























import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 图形建模主 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ModelTablePageReqVO extends PageParam {

    /** 数据源id */
    private Long dataSourceConfigId;

    /** 项目id */
    private Long projectId;

    /** 模型表中文名 */
    private String tableName;

    /** 模型表实名 */
    private String tableCode;

    /** 备注描述 */
    private String remark;

    /** 升级标记 */
    private Integer upgradeFlag;

    /** 是否生成过 */
    private Integer isGen;

    /** 类型 */
    private Integer dirType;

    /** 父节点 */
    private Long parentId;

    /** 属性类型，0：系统，1：用户 */
    private Integer propType;
    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createdDt;

}
