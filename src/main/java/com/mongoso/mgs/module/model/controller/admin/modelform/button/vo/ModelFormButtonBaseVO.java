package com.mongoso.mgs.module.model.controller.admin.modelform.button.vo;

import lombok.*;

import java.io.Serializable;

  

/**
 * 单据建模按钮配置 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class ModelFormButtonBaseVO implements Serializable {

    /** 主键id */
    private Integer dataId;

    /** 单据建模编码 */
    private String modelFormCode;

    /** 按钮类型 */
    private Integer buttonType;

    /** 按钮名称 */
    private String buttonName;

    /** 是否选中 */
    private Boolean isSelect;

}
