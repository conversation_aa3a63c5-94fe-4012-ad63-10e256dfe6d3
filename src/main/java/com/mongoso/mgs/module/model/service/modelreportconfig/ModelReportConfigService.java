package com.mongoso.mgs.module.model.service.modelreportconfig;

import java.util.*;
import jakarta.validation.*;
import com.mongoso.mgs.module.model.controller.admin.modelreportconfig.vo.*;
import com.mongoso.mgs.module.model.dal.db.modelreportconfig.ModelReportConfigDO;
import com.mongoso.mgs.framework.common.domain.PageResult;

/**
 * 自定义报表配置 Service 接口
 *
 * <AUTHOR>
 */
public interface ModelReportConfigService {

    /**
     * 创建自定义报表配置
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long modelReportConfigAdd(@Valid ModelReportConfigAditReqVO reqVO);

    /**
     * 更新自定义报表配置
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long modelReportConfigEdit(@Valid ModelReportConfigAditReqVO reqVO);

    /**
     * 删除自定义报表配置
     *
     * @param id 编号
     */
    void modelReportConfigDel(Long id);

    /**
     * 获得自定义报表配置信息
     *
     * @param id 编号
     * @return 自定义报表配置信息
     */
    ModelReportConfigDO modelReportConfigDetail(Long id);

    /**
     * 获得自定义报表配置列表
     *
     * @param reqVO 查询条件
     * @return 自定义报表配置列表
     */
    List<ModelReportConfigDO> modelReportConfigList(@Valid ModelReportConfigQueryReqVO reqVO);

    /**
     * 获得自定义报表配置分页
     *
     * @param reqVO 查询条件
     * @return 自定义报表配置分页
     */
    PageResult<ModelReportConfigDO> modelReportConfigPage(@Valid ModelReportConfigPageReqVO reqVO);

}
