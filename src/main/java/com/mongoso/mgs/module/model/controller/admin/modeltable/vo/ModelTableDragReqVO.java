package com.mongoso.mgs.module.model.controller.admin.modeltable.vo;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * 图形建模主 AditReqVO
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
public class ModelTableDragReqVO implements Serializable {
    /** 模型表id */
    private Long tableId;

    /** 父节点 */
//    @NotNull(message = "父节点不能为空")
    private Long parentId;

    //@ApiModelProperty(value = "目标文件ID", required = false)
    private Long destTableId;

    //@ApiModelProperty(value = "拖动类型 0-目标之前  1-目标之后", required = false)
    private int dragType;

    //@ApiModelProperty(value = "项目ID", required = false)
    private Long projectId;

    //@ApiModelProperty(value = "文件类型 0-文件 1-文件夹")
    private Integer dirType;
}
