package com.mongoso.mgs.module.model.controller.admin.modelbiztable;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import jakarta.validation.*;
import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import static com.mongoso.mgs.framework.common.domain.ResultX.success;

import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.model.controller.admin.modelbiztable.vo.*;
import com.mongoso.mgs.module.model.dal.db.modelbiztable.ModelBizTableDO;
import com.mongoso.mgs.module.model.service.modelbiztable.ModelBizTableService;

/**
 * 主 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/model")
@Validated
public class ModelBizTableController {

    @Resource
    private ModelBizTableService bizTableService;

    @OperateLog("主添加或编辑")
    @PostMapping("/modelBizTableAdit")
    @PreAuthorize("@ss.hasPermission('modelBizTable:adit')")
    public ResultX<Long> modelBizTableAdit(@Valid @RequestBody ModelBizTableAditReqVO reqVO) {
        return success(reqVO.getDataId() == null
                            ? bizTableService.modelBizTableAdd(reqVO)
                            : bizTableService.modelBizTableEdit(reqVO));
    }

    @OperateLog("主删除")
    @PostMapping("/modelBizTableDelete")
    @PreAuthorize("@ss.hasPermission('modelBizTable:delete')")
    public ResultX<Boolean> modelBizTableDelete(@Valid @RequestBody ModelBizTablePrimaryReqVO reqVO) {
        bizTableService.modelBizTableDelete(reqVO.getDataId());
        return success(true);
    }

    @OperateLog("主详情")
    @PostMapping("/modelBizTableDetail")
    @PreAuthorize("@ss.hasPermission('modelBizTable:query')")
    public ResultX<ModelBizTableRespVO> modelBizTableDetail(@Valid @RequestBody ModelBizTablePrimaryReqVO reqVO) {
        return success(bizTableService.modelBizTableDetail(reqVO.getDataId()));
    }

    @OperateLog("主列表")
    @PostMapping("/modelBizTableList")
    @PreAuthorize("@ss.hasPermission('modelBizTable:query')")
    public ResultX<List<ModelBizTableRespVO>> modelBizTableList(@Valid @RequestBody ModelBizTableQueryReqVO reqVO) {
        return success(bizTableService.modelBizTableList(reqVO));
    }

    @OperateLog("主分页")
    @PostMapping("/modelBizTablePage")
    @PreAuthorize("@ss.hasPermission('modelBizTable:query')")
    public ResultX<PageResult<ModelBizTableRespVO>> modelBizTablePage(@Valid @RequestBody ModelBizTablePageReqVO reqVO) {
        return success(bizTableService.modelBizTablePage(reqVO));
    }

}
