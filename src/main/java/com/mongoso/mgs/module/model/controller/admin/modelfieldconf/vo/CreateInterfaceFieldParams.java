package com.mongoso.mgs.module.model.controller.admin.modelfieldconf.vo;

import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * @ClassName: CreateInterfaceFieldParams
 * @Description: 新增接口字段信息对象
 * <AUTHOR>
 * @date 2021-10-26
 */
@Data
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CreateInterfaceFieldParams implements Serializable {

    private static final long serialVersionUID = 1L;

    //@ApiModelProperty(value = "项目字段ID")
    private Integer id;

    //@ApiModelProperty(value = "项目ID")
    private Long projectId;

    //@ApiModelProperty(value = "字段ID")
    private Long itemId;

    //@ApiModelProperty(value = "字段中文名")
    private String itemName;

    //@ApiModelProperty(value = "字段英文名")
    private String englishName;

    //@ApiModelProperty(value = "字段拼音")
    private String chineseName;

    //@ApiModelProperty(value = "创建时间", hidden = true)
    private Date createTime;

    //@ApiModelProperty(value = "创建用户", hidden = true)
    private String createUser;

    //@ApiModelProperty(value = "是否已翻译 0-否 1-是", required = true)
    private String isTranslated;
}
