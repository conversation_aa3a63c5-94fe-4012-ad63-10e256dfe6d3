package com.mongoso.mgs.module.model.controller.admin.modelqueryresult.vo;

import lombok.*;

import jakarta.validation.constraints.*;
import java.io.Serializable;

/**
 * 自定义查询结果 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class ModelQueryResultBaseVO implements Serializable {

    /** 主键ID */
    private Long id;

    /** 查询id */
    @NotNull(message = "查询id不能为空")
    private Long queryId;

    /** 结果字段 */
    @NotEmpty(message = "结果字段不能为空")
    private String resultField;

    /** 结果字段别名 */
    @NotEmpty(message = "结果字段别名不能为空")
    private String resultFieldName;

    /** 数据类型 */
    @NotNull(message = "数据类型不能为空")
    private String dataType;

    /** 长度 */
//    @NotNull(message = "长度不能为空")
    private Integer leng;

    /** 精度 */
//    @NotNull(message = "精度不能为空")
    private Integer fieldPrecision;
    /**
     * 排序
     */
    private Integer sort;
    /** 备注 */
//    @NotEmpty(message = "备注不能为空")
    private String remark;

    private Integer isShow;
}
