package com.mongoso.mgs.module.model.controller.admin.modelreportconfig.vo;

import lombok.*;

import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 自定义报表配置 QueryReqVO
 *
 * <AUTHOR>
 */
@Data
public class ModelReportConfigQueryReqVO {

    /** 报表id   */
    private Long reportId;

    /** 数据源   */
    private String dataTable;

    /** 字段编码 */
    private String fieldCode;

    /** 字段名称 */
    private String fieldName;

    /** 控件类型 */
    private Integer controlType;

    /** 字段类型   */
    private String fieldType;

    /** 长度 */
    private Integer leng;

    /** 字段精度 */
    private Integer fieldPrecision;

    /** 正则表达式 */
    private String regExpression;

    /** 数据掩码 */
    private String dataMask;

    /** 可编辑 */
    private Integer isEdit;

    /** 是否显示 */
    private Integer isShow;

    /** 空值显示 */
    private String emptyShow;

    /** 是否必填 */
    private Integer isRequired;

    /** 默认值 */
    private String defaultVal;

    /** 备注   */
    private String remark;

    /** 版本号 */
    private Integer versionNo;

    /** 0:查询条件，1:展示数据 */
    private Integer category;

    /** 创建人   */
    private String createdBy;

    /** 创建时间   */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createdDt;

    /** 更新人   */
    private String updatedBy;

    /** 更新时间   */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] updatedDt;

}
