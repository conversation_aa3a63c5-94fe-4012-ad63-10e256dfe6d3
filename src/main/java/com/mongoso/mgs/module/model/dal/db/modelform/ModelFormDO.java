package com.mongoso.mgs.module.model.dal.db.modelform;

import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;
import lombok.*;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;


/**
 * 单据建模主 DO
 *
 * <AUTHOR>
 */
@TableName(value = "lowcode.sys_model_form", autoResultMap = true)
//@KeySequence("sys_model_form_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ModelFormDO extends OperateDO {

    /** 主键id */
        @TableId(type = IdType.ASSIGN_ID)
    private Long dataId;

    /** 单据对象编码 */
    private String dataCode;

    /** 单据对象类型 */
    private Short formType;

    /** 业务建模编码 */
    private String modelBizCode;

    /** 业务建模类型 */
    private Short modelBizType;

    /** 单据对象名称 */
    private String formName;

    /** 备注 */
    private String remark;


}
