package com.mongoso.mgs.module.model.controller.admin.modelfieldalter.vo;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * 图形建模字段 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class ModelFieldAlterBaseVO implements Serializable {

    /** 主键id */
    private Long id;

    /** 模型字段表id */
    private Long fieldId;

    /** 模型字段表id中文名 */
    @NotEmpty(message = "模型字段表id中文名不能为空")
    private String fieldName;

    /** 旧模型字段表id实名 */
    private String oldFieldCode;
    /** 模型字段表id实名 */
    @NotEmpty(message = "模型字段表id实名不能为空")
    private String fieldCode;

    /** 字段类型 */
    @NotNull(message = "字段类型不能为空")
    private String fieldType;

    /** 字段类型别名 */
    private String fieldTypeName;

    /** 字段长度 */
    private Integer leng;

    /** 字段精度 */
    private Integer fieldPrecision;

    /** 是否自增 */
    private Integer isAutoIncrease = 0;

    /** 模型表id */
    @NotNull(message = "模型表id不能为空")
    private Long tableId;

    /** 是否为空 */
    @NotNull(message = "是否必填不能为空")
    private Integer isNullable = 0;

    /** 是否为主键 */
    @NotNull(message = "是否为主键不能为空")
    private Integer isPrimaryKey = 0;

    /** 默认值 */
    private String defaultVal;

    /** 排序 */
    @NotNull(message = "排序不能为空")
    private Integer sort;

    /** 属性类型，0：系统，1：用户 */
    @NotNull(message = "属性类型，0：系统，1：用户不能为空")
    private Integer propType;

    /** 备注描述 */
    private String remark;

    /** 0:删除，1：新增，2：修改 */
    @NotNull(message = "0:删除，1：新增，2：修改不能为空")
    private Integer opType;

    /** 是否执行过 */
    @NotNull(message = "是否执行过不能为空")
    private Integer isProcessed;

}
