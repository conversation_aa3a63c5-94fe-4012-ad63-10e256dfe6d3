package com.mongoso.mgs.module.model.controller.admin.modelquery.vo;

import com.mongoso.mgs.framework.common.domain.BaseTree;
import com.mongoso.mgs.module.model.dal.db.modelqueryparam.ModelQueryParamDO;
import com.mongoso.mgs.module.model.dal.db.modelqueryresult.ModelQueryResultDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.validation.constraints.NotEmpty;
import java.time.LocalDateTime;
import java.util.List;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 自定义查询 RespVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ModelQueryRespVO extends BaseTree {

    /** 主键ID */
    private Long queryId;

    /** 查询编码 */
    @NotEmpty(message = "查询编码不能为空")
    private String queryCode;

    /** 查询中文名 */
    @NotEmpty(message = "查询中文名不能为空")
    private String queryName;

    /** 查询语句 */
    @NotEmpty(message = "查询语句不能为空")
    private String queryStatment;

    /** 备注 */
    private String remark;

    /** 类型 */
    private Integer dirType;

    /** 父节点 */
    private Long parentId;
    List<ModelQueryRespVO> children;

    //参数
    List<ModelQueryParamDO> params;
    //结果
    List<ModelQueryResultDO> results;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createdDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime updatedDt;

}
