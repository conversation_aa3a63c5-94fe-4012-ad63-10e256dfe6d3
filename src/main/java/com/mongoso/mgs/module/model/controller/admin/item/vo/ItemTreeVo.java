package com.mongoso.mgs.module.model.controller.admin.item.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/7
 * @description
 */
@Data
public class ItemTreeVo implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    private Long itemId;

    private Long parentItemId;

    private String parentItemName;

    private String itemCode;

    private String itemName;

    /**
     * 文件类型 0:文件夹 1:项目文件夹 2:项目内文件夹 3:文件集 5:思维导图 6:数据导图
     */
    private Integer itemType;

    private Integer itemSeq;

    /**
     * 任务集类型 ["工作",  "问题", "信息架构图导图", "数据库导图", "接口导图"]
     */
    private Integer taskSetType;

    private Long projectId;

    private String projectName;

    private String projectType;

    private String taskSetId;

    private String isMyProject = "0";

    private Integer isOwner = 0;

    List<ItemTreeVo> children = new ArrayList<>();

    /*查询任务树用到*/
    private String taskStatusCode;

    private String taskStatusType;

    /**
     *  任务类型 ["工作", "问题", "架构图", "数据库", "接口"]
     */
    private String taskType;

    private String priority;

    private String directorId;

    private String startDate;

    private String endDate;

    /**
     * 所属用户ID : 用户查询判断，不需要返回给前端
     */
    private String userId;

    private String roleId;

    private String roleName;

    private String testTaskFileId;

    /**
     * 文档类型 0-产品架构  1-数据库 2-接口文档
     */
    private String docType;
    /**
     * 是否可删除 0-不可删除 1-可删除
     */
    private String deletable = "1";

    /**
     * 是否有看板
     */
    private Integer hasDashboard;
    private Integer category;
}
