package com.mongoso.mgs.module.model.dal.db.modelfieldconf;

import com.mongoso.mgs.framework.typehandler.JsonbTypeHandler;
import lombok.*;
import com.alibaba.fastjson.JSONObject;

import com.baomidou.mybatisplus.annotation.*;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;

import java.util.List;

/**
 * 图形建模字段翻译配置 DO
 *
 * <AUTHOR>
 */
@TableName(value = "lowcode.sys_model_field_conf", autoResultMap = true)
//@KeySequence("sys_model_field_conf_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ModelFieldConfDO extends OperateDO {

    /** 模型字段表id */
        @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /** 模型字段表中文名 */
    private String fieldName;

    /** 模型字段表实名 */
    private String fieldCode;

    /** 字段类型 */
    private String fieldType;

    private String dataType;

    /** 长度 */
    private Integer leng;

    /** 精度 */
    private Integer fieldPrecision = 0;

    /** 是否必填 */
    private Integer isNullable = 0;

    /** 是否为主键 */
    private Integer isPrimaryKey = 0;

    /** 默认值 */
    private String defaultVal = "";

    /** 排序 */
    private Integer sort = 0;

    /** 属性类型，0：系统，1：用户 */
    private Integer propType = 1;

    /** 备注描述 */
    private String remark = "";

    /** 字段类型名称 */
    private String fieldTypeName;

    /** json字段配置 */
    @TableField(typeHandler = JsonbTypeHandler.class,fill = FieldFill.INSERT)
    private List<JSONObject> jsonFields;


    private String itemName;
    private String englishName;
    private Integer fieldLength;

}
