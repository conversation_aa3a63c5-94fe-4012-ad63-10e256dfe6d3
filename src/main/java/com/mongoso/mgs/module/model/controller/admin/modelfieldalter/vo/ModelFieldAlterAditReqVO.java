package com.mongoso.mgs.module.model.controller.admin.modelfieldalter.vo;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * 图形建模字段 AditReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ModelFieldAlterAditReqVO extends ModelFieldAlterBaseVO {

    /** JSON配置 */
    List<JSONObject> jsonFields;
}
