package com.mongoso.mgs.module.model.controller.admin.modelqueryparam.vo;

import lombok.*;

import com.mongoso.mgs.framework.common.domain.PageParam;







































import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 自定义查询参数 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ModelQueryParamPageReqVO extends PageParam {

    /** 查询id */
    private Long queryId;

    /** 参数英文名 */
    private String paramCode;

    /** 参数中文名 */
    private String paramName;

    /** 参数模式 */
    private Integer paramType;

    /** 数据类型 */
    private String dataType;

    /** 长度 */
    private Integer leng;

    /** 精度 */
    private Integer fieldPrecision;

    /** 数据源 */
    private String dataTable;

    /** 数据列 */
    private String dataFields;
    private String dataFieldStr;

    /** 备注 */
    private String remark;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createdDt;

}
