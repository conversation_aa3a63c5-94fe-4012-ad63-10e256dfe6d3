package com.mongoso.mgs.module.model.controller.admin.modelfunctionhistory.vo;

import lombok.*;

import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 自定义函数历史 QueryReqVO
 *
 * <AUTHOR>
 */
@Data
public class ModelFunctionHistoryQueryReqVO {

    /** 函数id   */
    private Long funId;

    /** 函数名称 */
    private String funName;

    /** 函数编号 */
    private String funCode;

    /** 函数主体 */
    private String funBody;

    /** 运行环境   */
    private String runEnv;

    /** 超时时间 */
    private Integer timeout;

    /** 备注   */
    private String remark;

    /** 版本号 */
    private Integer versionNo;

    /** 是否发布 */
    private Integer isPublish;

    /** 是否锁定 */
    private Integer isLock;

    /** 目录类型 */
    private Integer dirType;

    /** 父节点 */
    private Long parentId;

    /** 属性类型，0：系统，1：用户 */
    private Integer propType;

    /** 更新时间   */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] updatedDt;

    /** 创建人   */
    private String createdBy;

    /** 创建时间   */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createdDt;

    /** 更新人   */
    private String updatedBy;

    /** 数据id */
    private Long dataId;

}
