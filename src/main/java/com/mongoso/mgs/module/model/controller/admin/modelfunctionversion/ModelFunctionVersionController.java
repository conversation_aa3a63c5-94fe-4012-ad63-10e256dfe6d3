package com.mongoso.mgs.module.model.controller.admin.modelfunctionversion;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import jakarta.validation.*;
import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import static com.mongoso.mgs.framework.common.domain.ResultX.success;

import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.model.controller.admin.modelfunctionversion.vo.*;
import com.mongoso.mgs.module.model.dal.db.modelfunctionversion.ModelFunctionVersionDO;
import com.mongoso.mgs.module.model.service.modelfunctionversion.ModelFunctionVersionService;

/**
 * 自定义报表版本 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/model")
@Validated
public class ModelFunctionVersionController {

    @Resource
    private ModelFunctionVersionService functionVersionService;

    @OperateLog("自定义报表版本添加或编辑")
    @PostMapping("/modelFunctionVersionAdit")
    @PreAuthorize("@ss.hasPermission('modelFunctionVersion:adit')")
    public ResultX<Long> modelFunctionVersionAdit(@Valid @RequestBody ModelFunctionVersionAditReqVO reqVO) {
        return success(reqVO.getFunId() == null
                            ? functionVersionService.modelFunctionVersionAdd(reqVO)
                            : functionVersionService.modelFunctionVersionEdit(reqVO));
    }

    @OperateLog("自定义报表版本删除")
    @PostMapping("/modelFunctionVersionDel")
    @PreAuthorize("@ss.hasPermission('modelFunctionVersion:del')")
    public ResultX<Boolean> modelFunctionVersionDel(@Valid @RequestBody ModelFunctionVersionPrimaryReqVO reqVO) {
        functionVersionService.modelFunctionVersionDel(reqVO.getFunId());
        return success(true);
    }

    @OperateLog("自定义报表版本详情")
    @PostMapping("/modelFunctionVersionDetail")
    @PreAuthorize("@ss.hasPermission('modelFunctionVersion:query')")
    public ResultX<ModelFunctionVersionRespVO> modelFunctionVersionDetail(@Valid @RequestBody ModelFunctionVersionPrimaryReqVO reqVO) {
        ModelFunctionVersionDO oldDO = functionVersionService.modelFunctionVersionDetail(reqVO.getFunId());
        return success(BeanUtilX.copy(oldDO, ModelFunctionVersionRespVO::new));
    }

    @OperateLog("自定义报表版本列表")
    @PostMapping("/modelFunctionVersionList")
    @PreAuthorize("@ss.hasPermission('modelFunctionVersion:query')")
    public ResultX<List<ModelFunctionVersionRespVO>> modelFunctionVersionList(@Valid @RequestBody ModelFunctionVersionQueryReqVO reqVO) {
        List<ModelFunctionVersionDO> list = functionVersionService.modelFunctionVersionList(reqVO);
        return success(BeanUtilX.copyList(list, ModelFunctionVersionRespVO::new));
    }

    @OperateLog("自定义报表版本分页")
    @PostMapping("/modelFunctionVersionPage")
    @PreAuthorize("@ss.hasPermission('modelFunctionVersion:query')")
    public ResultX<PageResult<ModelFunctionVersionRespVO>> modelFunctionVersionPage(@Valid @RequestBody ModelFunctionVersionPageReqVO reqVO) {
        PageResult<ModelFunctionVersionDO> pageResult = functionVersionService.modelFunctionVersionPage(reqVO);
        return success(BeanUtilX.copyPage(pageResult, ModelFunctionVersionRespVO::new));
    }

}
