package com.mongoso.mgs.module.model.service.modelbiztable;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mongoso.mgs.framework.mybatis.core.util.PageUtilX;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mongoso.mgs.module.model.controller.admin.modelbiztable.vo.*;
import com.mongoso.mgs.module.model.controller.admin.modelbiztable.detail.vo.*;
import com.mongoso.mgs.module.model.dal.db.modelbiztable.ModelBizTableDO;
import com.mongoso.mgs.module.model.dal.db.modelbiztable.detail.ModelBizTableDetailDO;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.module.model.dal.mysql.modelbiztable.ModelBizTableMapper;
import com.mongoso.mgs.module.model.dal.mysql.modelbiztable.detail.ModelBizTableDetailMapper;

// import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exception;
import com.mongoso.mgs.framework.common.exception.BizException;
// import static com.mongoso.mgs.module.model.enums.ErrorCodeConstants.*;


/**
 * 主 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ModelBizTableServiceImpl implements ModelBizTableService {

    @Resource
    private ModelBizTableMapper bizTableMapper;

    @Resource
    private ModelBizTableDetailMapper bizTableDetailMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long modelBizTableAdd(ModelBizTableAditReqVO reqVO) {
        // 插入主表
        ModelBizTableDO bizTable = BeanUtilX.copy(reqVO, ModelBizTableDO::new);
        bizTableMapper.insert(bizTable);

        // 处理子表明细数据
        if (reqVO.getModelBizTableDetailList() != null && !reqVO.getModelBizTableDetailList().isEmpty()) {
            int rowNo = 1; // 行号从1开始
            for (ModelBizTableDetailAditReqVO detailReqVO : reqVO.getModelBizTableDetailList()) {
                // 设置子表的业务编码，关联到主表
                detailReqVO.setBizCode(bizTable.getBizCode());
                // 递归处理子表及其子集
                processDetailAndChildren(detailReqVO, null, rowNo, "0", null);
                rowNo++;
            }
        }

        // 返回主表ID
        return bizTable.getDataId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long modelBizTableEdit(ModelBizTableAditReqVO reqVO) {
        // 校验存在
        this.modelBizTableValidateExists(reqVO.getDataId());
        // 更新主表
        ModelBizTableDO bizTable = BeanUtilX.copy(reqVO, ModelBizTableDO::new);
        bizTableMapper.updateById(bizTable);

        // 处理子表明细数据 - 先删除原有的子表数据，再重新插入
        if (reqVO.getModelBizTableDetailList() != null) {
            // 删除原有的子表明细数据
            deleteDetailsByBizCode(bizTable.getBizCode());

            // 重新插入子表明细数据
            if (!reqVO.getModelBizTableDetailList().isEmpty()) {
                int rowNo = 1; // 行号从1开始
                for (ModelBizTableDetailAditReqVO detailReqVO : reqVO.getModelBizTableDetailList()) {
                    // 设置子表的业务编码，关联到主表
                    detailReqVO.setBizCode(bizTable.getBizCode());
                    // 递归处理子表及其子集
                    processDetailAndChildren(detailReqVO, null, rowNo, "0", null);
                    rowNo++;
                }
            }
        }

        // 返回主表ID
        return bizTable.getDataId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modelBizTableDelete(Long dataId) {
        // 校验存在
        ModelBizTableDO bizTable = this.modelBizTableValidateExists(dataId);

        // 先删除相关的子表明细数据
        deleteDetailsByBizCode(bizTable.getBizCode());

        // 删除主表
        bizTableMapper.deleteById(dataId);
    }

    private ModelBizTableDO modelBizTableValidateExists(Long dataId) {
        ModelBizTableDO bizTable = bizTableMapper.selectById(dataId);
        if (bizTable == null) {
            // throw exception(BIZ_TABLE_NOT_EXISTS);
            throw new BizException("5001", "主不存在");
        }
        return bizTable;
    }

    @Override
    public ModelBizTableRespVO modelBizTableDetail(Long dataId) {
        // 查询主表数据
        ModelBizTableDO data = bizTableMapper.selectById(dataId);
        if (data == null) {
            throw new BizException("5001", "主表数据不存在");
        }

        ModelBizTableRespVO result = BeanUtilX.copy(data, ModelBizTableRespVO::new);

        // 查询子表明细数据（连表查询获取表名称）
        List<ModelBizTableDetailRespVO> detailList = bizTableDetailMapper.selectListWithTableNameByBizCode(data.getBizCode());

        // 构建树结构
        List<ModelBizTableDetailRespVO> treeList = buildDetailTree(detailList);
        result.setModelBizTableDetailList(treeList);

        return result;
    }

    @Override
    public List<ModelBizTableRespVO> modelBizTableList(ModelBizTableQueryReqVO reqVO) {
        List<ModelBizTableDO> data = bizTableMapper.selectList(reqVO);
        return BeanUtilX.copy(data, ModelBizTableRespVO::new);
    }

    @Override
    public PageResult<ModelBizTableRespVO> modelBizTablePage(ModelBizTablePageReqVO reqVO) {
        // 创建分页参数
        IPage<ModelBizTableRespVO> page = new Page<>(reqVO.getCurrentPage(), reqVO.getPageSize());

        // 调用自定义分页查询方法（连表查询获取表名称）
        IPage<ModelBizTableRespVO> respVOIPage = bizTableMapper.selectPageWithTableName(page, reqVO);

        // 构建分页结果
        PageResult<ModelBizTableRespVO> pageResult = PageUtilX.buildResult(respVOIPage);


        return pageResult;
    }

    /**
     * 构建子表明细的树结构
     *
     * @param detailList 子表明细列表
     * @return 树结构列表
     */
    private List<ModelBizTableDetailRespVO> buildDetailTree(List<ModelBizTableDetailRespVO> detailList) {
        if (detailList == null || detailList.isEmpty()) {
            return new ArrayList<>();
        }

        // 按父行路径分组
        Map<String, List<ModelBizTableDetailRespVO>> pathGroupMap = new HashMap<>();
        for (ModelBizTableDetailRespVO detail : detailList) {
            String parentRowPath = detail.getParentRowPath();
            pathGroupMap.computeIfAbsent(parentRowPath, k -> new ArrayList<>()).add(detail);
        }

        // 构建树结构
        List<ModelBizTableDetailRespVO> rootList = pathGroupMap.getOrDefault("0", new ArrayList<>());

        // 递归构建每个节点的子节点
        for (ModelBizTableDetailRespVO root : rootList) {
            buildChildren(root, pathGroupMap);
        }

        return rootList;
    }

    /**
     * 递归构建子节点
     *
     * @param parent 父节点
     * @param pathGroupMap 按父行路径分组的数据
     */
    private void buildChildren(ModelBizTableDetailRespVO parent, Map<String, List<ModelBizTableDetailRespVO>> pathGroupMap) {
        // 构建当前节点的路径键
        String currentPath;
        if ("0".equals(parent.getParentRowPath())) {
            // 当前是二级表，子集的父行路径应该是当前行号
            currentPath = String.valueOf(parent.getRowNo());
        } else {
            // 当前是三级及以上，子集的父行路径应该是当前路径+当前行号
            currentPath = parent.getParentRowPath() + "-" + parent.getRowNo();
        }

        // 查找子节点
        List<ModelBizTableDetailRespVO> children = pathGroupMap.get(currentPath);
        if (children != null && !children.isEmpty()) {
            parent.setChildren(children);

            // 递归构建子节点的子节点
            for (ModelBizTableDetailRespVO child : children) {
                buildChildren(child, pathGroupMap);
            }
        }
    }

    /**
     * 根据业务编码删除所有相关的子表明细数据
     *
     * @param bizCode 业务编码
     */
    private void deleteDetailsByBizCode(String bizCode) {
        bizTableDetailMapper.delete(new LambdaQueryWrapper<ModelBizTableDetailDO>()
                .eq(ModelBizTableDetailDO::getBizCode, bizCode)
        );
    }

    /**
     * 递归处理子表明细及其子集
     *
     * @param detailReqVO 子表明细请求VO
     * @param parentDataId 父数据ID，用于绑定子表的子集
     * @param rowNo 当前行号
     * @param parentRowPath 父行路径
     * @param parentRowNo 父行号（只有树结构时才有值）
     */
    private void processDetailAndChildren(ModelBizTableDetailAditReqVO detailReqVO, Long parentDataId,
                                        int rowNo, String parentRowPath, Integer parentRowNo) {
        // 设置父数据ID
        detailReqVO.setParentDataId(parentDataId);

        // 设置行号
        detailReqVO.setRowNo(rowNo);

        // 设置父行路径
        detailReqVO.setParentRowPath(parentRowPath);

        // 设置父行号（只有当子表类型是树结构时才设置）
        if (detailReqVO.getTableType() != null && detailReqVO.getTableType() == 1) {
            detailReqVO.setParentRowNo(parentRowNo);
        }

        // 插入当前子表明细
        ModelBizTableDetailDO bizTableDetail = BeanUtilX.copy(detailReqVO, ModelBizTableDetailDO::new);
        bizTableDetailMapper.insert(bizTableDetail);

        // 递归处理子集
        if (detailReqVO.getChildList() != null && !detailReqVO.getChildList().isEmpty()) {
            int childRowNo = 1; // 子集行号从1开始
            for (ModelBizTableDetailAditReqVO childReqVO : detailReqVO.getChildList()) {
                // 子集继承父级的业务编码
                childReqVO.setBizCode(detailReqVO.getBizCode());

                // 构建子集的父行路径：如果当前是二级表，父行路径为当前行号；否则在原路径基础上追加
                String childParentRowPath;
                if ("0".equals(parentRowPath)) {
                    // 当前是二级表，子集是三级表
                    childParentRowPath = String.valueOf(rowNo);
                } else {
                    // 当前是三级及以上，子集需要在路径上追加
                    childParentRowPath = parentRowPath + "-" + rowNo;
                }

                // 递归处理，传入当前记录的dataId作为父数据ID
                Integer childParentRowNo = (detailReqVO.getTableType() != null && detailReqVO.getTableType() == 1) ? rowNo : null;
                processDetailAndChildren(childReqVO, bizTableDetail.getDataId(), childRowNo, childParentRowPath, childParentRowNo);
                childRowNo++;
            }
        }
    }

}
