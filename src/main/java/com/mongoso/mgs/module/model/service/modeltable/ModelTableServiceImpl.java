package com.mongoso.mgs.module.model.service.modeltable;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mongoso.mgs.common.util.ColumnInfo;
import com.mongoso.mgs.common.util.DatabaseUtil;
import com.mongoso.mgs.common.util.HttpRequestUtil;
import com.mongoso.mgs.common.util.TableStructure;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.common.util.CollUtilX;
import com.mongoso.mgs.framework.common.util.ObjUtilX;
import com.mongoso.mgs.framework.common.util.StrUtilX;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.module.codegen.common.util.StringUtils;
import com.mongoso.mgs.module.codegen.dal.db.codegen.CodegenColumnDO;
import com.mongoso.mgs.module.codegen.dal.db.codegen.CodegenTableDO;
import com.mongoso.mgs.module.codegen.dal.db.config.DataSourceConfigDO;
import com.mongoso.mgs.module.codegen.dal.mysql.codegen.CodegenColumnMapper;
import com.mongoso.mgs.module.codegen.dal.mysql.codegen.CodegenTableMapper;
import com.mongoso.mgs.module.codegen.dal.mysql.db.DataSourceConfigMapper;
import com.mongoso.mgs.module.enums.ApiURLEnum;
import com.mongoso.mgs.module.enums.DBOPTypeEnum;
import com.mongoso.mgs.module.enums.DBTypeEnum;
import com.mongoso.mgs.module.enums.DataTypeEnum;
import com.mongoso.mgs.module.model.controller.admin.item.vo.CreateItemParams;
import com.mongoso.mgs.module.model.controller.admin.item.vo.QueryItemParams;
import com.mongoso.mgs.module.model.controller.admin.modelfield.vo.ModelFieldAditReqVO;
import com.mongoso.mgs.module.model.controller.admin.modelfield.vo.ModelFieldRespVO;
import com.mongoso.mgs.module.model.controller.admin.modelfieldconf.vo.ModelFieldConfAditReqVO;
import com.mongoso.mgs.module.model.controller.admin.modelfieldconf.vo.ModelFieldTransReqVO;
import com.mongoso.mgs.module.model.controller.admin.modeltable.vo.*;
import com.mongoso.mgs.module.model.controller.admin.modeltableindex.vo.ModelTableIndexAditReqVO;
import com.mongoso.mgs.module.model.controller.admin.modeltableindex.vo.ModelTableIndexRespVO;
import com.mongoso.mgs.module.model.dal.db.item.ItemDO;
import com.mongoso.mgs.module.model.dal.db.modelfield.ModelFieldDO;
import com.mongoso.mgs.module.model.dal.db.modelfieldconf.ModelFieldConfDO;
import com.mongoso.mgs.module.model.dal.db.modeltable.ModelTableDO;
import com.mongoso.mgs.module.model.dal.db.modeltableindex.ModelTableIndexDO;
import com.mongoso.mgs.module.model.dal.db.pageconfig.PageConfigDO;
import com.mongoso.mgs.module.model.dal.db.sqllog.SqlLogDO;
import com.mongoso.mgs.module.model.dal.mysql.item.ItemMapper;
import com.mongoso.mgs.module.model.dal.mysql.modelfield.ModelFieldMapper;
import com.mongoso.mgs.module.model.dal.mysql.modelfieldconf.ModelFieldConfMapper;
import com.mongoso.mgs.module.model.dal.mysql.modeltable.ModelTableMapper;
import com.mongoso.mgs.module.model.dal.mysql.modeltableindex.ModelTableIndexMapper;
import com.mongoso.mgs.module.model.dal.mysql.pageconfig.PageConfigMapper;
import com.mongoso.mgs.module.model.dal.mysql.sqllog.SqlLogMapper;
import com.mongoso.mgs.module.model.service.dbfactory.DBCreator;
import com.mongoso.mgs.module.model.service.dbfactory.DBCreatorFactory;
import com.mongoso.mgs.module.model.service.item.ItemService;
import com.mongoso.mgs.module.model.service.modelfieldconf.ModelFieldConfService;
import com.mongoso.mgs.module.project.dal.db.projectapienv.ProjectApiEnvDO;
import com.mongoso.mgs.module.project.dal.db.projectapiparams.ProjectApiParamsDO;
import com.mongoso.mgs.module.project.dal.db.projectfieldtype.ProjectFieldTypeDO;
import com.mongoso.mgs.module.project.dal.db.projectmodelfield.ProjectModelFieldDO;
import com.mongoso.mgs.module.project.dal.mysql.projectapienv.ProjectApiEnvMapper;
import com.mongoso.mgs.module.project.dal.mysql.projectapiparams.ProjectApiParamsMapper;
import com.mongoso.mgs.module.project.dal.mysql.projectfieldtype.ProjectFieldTypeMapper;
import com.mongoso.mgs.module.project.dal.mysql.projectmodelfield.ProjectModelFieldMapper;
import jakarta.annotation.Resource;
import lombok.extern.log4j.Log4j2;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.sql.*;
import java.util.*;
import java.util.Date;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static cn.hutool.core.text.CharSequenceUtil.subBefore;
import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exception;
import static com.mongoso.mgs.module.model.enums.ErrorCodeConstants.TABLE_NOT_EXISTS;

/**
 * 图形建模主 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Log4j2
public class ModelTableServiceImpl implements ModelTableService {

    @Resource
    private ModelTableMapper tableMapper;
    @Resource
    private ModelFieldMapper fieldMapper;
    @Resource
    private ProjectModelFieldMapper projectModelFieldMapper;
    @Resource
    private ProjectFieldTypeMapper projectFieldTypeMapper;
    @Resource
    private ProjectApiEnvMapper apiEnvMapper;
    @Resource
    private ProjectApiParamsMapper projectApiParamsMapper;
//    @Resource
//    private ModelFieldJsonMapper fieldJsonMapper;
    @Autowired
    private DBCreatorFactory DBCreatorFactory;
    @Resource
    private ModelTableIndexMapper tableIndexMapper;
    @Resource
    private ModelFieldConfService confService;
    @Resource
    private DatabaseUtil dbUtil;
    @Resource
    private ModelFieldConfMapper fieldConfMapper;
    @Lazy
    @Resource
    private ModelFieldConfService fieldConfService;
    @Resource
    ItemService itemService;
    @Resource
    private ItemMapper itemMapper;
    @Resource
    private PageConfigMapper pageConfigMapper;
    @Resource
    private CodegenTableMapper codegenTableMapper;
    @Resource
    private CodegenColumnMapper codegenColumnMapper;
    @Resource
    private DataSourceConfigMapper dataSourceConfigMapper;

    @Value("${remote.generate.url}")
    private String generateUrl = "https://max.mongoso.vip/max/taskSet/genApiDocJsonb";
    @Value("${remote.generate.privateKey}")
    private String privateKey = "A8jD7KpQnxZsR6YgB4F2E3kJ8M1h";
    @Value("${remote.chatgpt.url}")
    private String gptUrl;
    @Value("${remote.chatgpt.token}")
    private String token;
    @Value("${remote.chatgpt.chatId}")
    private String chatId;
    @Resource
    private SqlLogMapper sqlLogMapper;

    private String createModelByStructure(String content){
        String s = "";
        try {
            //content = content+ "将上方的内容逐级提取建表所需的字段属性，生成建表接口的入参：\n" +
            //        "0.提取字段依据itemName主题含义判断所需建表的属性\n" +
            //        "1.英文翻译不要直接翻译成拼音，专业一点，且适当做缩写\n" +
            //        "2.将 content 内的属性逐级提取关键字段，添加到 fields 属性中，tableName 为第一个 itemName 的文本，tableCode 为翻译为英文并且添加 u_的前缀；\n" +
            //        "3.fields 中的 fieldCode 也进行翻译英文，但是不加 u_前缀；fields 的第一个数据额外生成一个主键数据，规范如下：fieldName 为: itemName+\"主键\"，fieldCode 为tableCode+\"_pk\"，增加一个属性 isPrimaryKey:1，且主键的 isNullable 为1，其他属性 isPrimaryKey:0\n" +
            //        "4.content 返回的 json 数据不要有多余描述，我需要解析 json\n" +
            //        "5.要返回的格式示例如下：\n" +
            //        "{\"tableName\":\"teststs\",\"tableCode\":\"u_tablecode\",\"remark\":\"\",\"projectId\":\"1889599398700933121\",\"fields\":[{\"sort\":0,\"propType\":1,\"isNullable\":0,\"fieldType\":\"VARCHAR\",\"leng\":50,\"rowIndex\":0,\"fieldName\":\"姓名\",\"fieldCode\":\"name\",\"defaultVal\":\"\",\"remark\":\"\",\"isPrimaryKey\":0}]}";

            //content = "{\n" +
            //        "    \"chatId\": \"67ac74d5423b4217bdb90de4\",\n" +
            //        "    \"stream\": false,\n" +
            //        "    \"detail\": false,\n" +
            //        "    \"messages\": [\n" +
            //        "        {\n" +
            //        "            \"content\": \"我想去三亚旅游，帮我推荐一些三亚酒店吧\",\n" +
            //        "            \"role\": \"user\"\n" +
            //        "        }\n" +
            //        "    ]\n" +
            //        "}";

            Map<String, String> headers = new HashMap<>();
            headers.put("Authorization", token);
            headers.put("Content-Type", "application/json");

            JSONObject reqJson = new JSONObject();
            // 填充基本字段
            reqJson.put("chatId", chatId);
            reqJson.put("stream", false);
            reqJson.put("detail", false);

            // 创建 messages 数组
            JSONArray messagesArray = new JSONArray();
            JSONObject messageJson = new JSONObject();

            // 将输入字符串转换为 JSONArray
            JSONArray jsonArray = JSONArray.parseArray(content);

            // 提取 itemName 并保留树形结构
            JSONArray result = extractItemNames(jsonArray);

            // 输出结果
            //System.out.println(result.toJSONString()); // 美化输出

            messageJson.put("content", result.toJSONString());
            messageJson.put("role", "user");

            // 将消息加入数组
            messagesArray.add(messageJson);
            reqJson.put("messages", messagesArray);

            //System.out.println(s);
            s = HttpRequestUtil.sendPost(gptUrl, reqJson.toJSONString(), headers, 300000,300000);
            JSONObject rst = JSONObject.parseObject(s);
            JSONArray array = (JSONArray) rst.get("choices");
            JSONObject choice = (JSONObject) array.get(0);
            JSONObject message = (JSONObject) choice.get("message");
            s = message.get("content").toString();
        }
        catch (Exception e) {
            throw new BizException("5001", "请求服务器失败" + e.getMessage());
        }
        //if(s.startsWith("```json")) {
        if(s.contains("```json")) {
            s = extractJsonString(s);
        }
        //return s.replace("```json","").replace("```","");
        return s;
    }

    private static JSONArray extractItemNames(JSONArray array) {
        JSONArray result = new JSONArray();

        for (int i = 0; i < array.size(); i++) {
            JSONObject item = array.getJSONObject(i);
            JSONObject newNode = new JSONObject();
            newNode.put("itemName", item.getString("itemName"));
            newNode.put("required", item.containsKey("required")?item.getString("required"):false);

            // 如果有 children，则递归调用
            if (item.containsKey("children")) {
                JSONArray children = item.getJSONArray("children");
                newNode.put("children", extractItemNames(children));
            }
            //else {
            //    newNode.put("children", new JSONArray()); // 确保 children 是一个空数组
            //}

            result.add(newNode);
        }

        return result;
    }

    private static String extractJsonString(String input) {
        // 正则表达式匹配 ```json 和 ```
        Pattern pattern = Pattern.compile("```json(.*?)```", Pattern.DOTALL);
        Matcher matcher = pattern.matcher(input);

        if (matcher.find()) {
            // 返回合法的 JSON 字符串
            return matcher.group(1).trim();
        }
        return null;
    }


    /**
     * 这个搞不了，用AI生成
     * @param aditReq
     * @param fieldNames
     */
    private void addDefault(ModelTableAditReqVO aditReq, List<String> fieldNames) {

        List<ModelFieldAditReqVO> fieldAdds = new ArrayList<>();

        // 定义默认字段的配置信息
        String[][] defaultFields = {
                {"责任人", "director_name", "VARCHAR", "100", "责任人"},
                {"责任部门", "director_org_name", "VARCHAR", "101", "责任部门"},
                {"创建人", "created_by", "VARCHAR", "102", "创建人"},
                {"创建时间", "created_dt", "TIMESTAMP", "103", "创建时间"},
                {"更新人", "updated_by", "VARCHAR", "104", "更新人"},
                {"更新时间", "updated_dt", "TIMESTAMP", "105", "更新时间"}
        };

        for (String[] fieldInfo : defaultFields) {
            String fieldName = fieldInfo[0];
            String fieldCode = fieldInfo[1];
            String fieldType = fieldInfo[2];
            int sort = Integer.parseInt(fieldInfo[3]);

            // 检查字段是否存在
            if (!fieldNames.contains(fieldName)) {
                ModelFieldAditReqVO field = new ModelFieldAditReqVO();
                field.setSort(sort);
                field.setPropType(1);
                field.setIsNullable(0);
                field.setIsPrimaryKey(0);
                field.setFieldType(fieldType);
                field.setLeng("VARCHAR".equals(fieldType) ? 50 : null); // 设置长度
                field.setFieldName(fieldName);
                field.setFieldCode(fieldCode);
                field.setFieldTypeName(fieldType);
                field.setFieldPrecision(0);
                field.setDefaultVal(null);
                field.setRemark("");
                fieldAdds.add(field);
            }else{
                fieldNames.add(fieldName);
            }
        }

        aditReq.getFields().addAll(fieldAdds);
    }

    @Override
    public Long modelTableInit(QueryItemParams reqVO) {
        ItemDO itemDO = itemMapper.selectById(reqVO.getItemId());
        if(ObjUtilX.isEmpty(itemDO)){
            throw new BizException("5001","选择的架构图不存在");
        }
        //区分数据库
        ModelTableDO parent = tableMapper.selectById(reqVO.getParentItemId());
        if(ObjUtilX.isEmpty(parent)){
            throw new BizException("5001","请选择一个文件夹");
        }
        String dbType = dbUtil.getDbTypeDynamic(parent.getDataSourceConfigId());
        if(StrUtilX.isNotEmpty(dbType)) {
            if (dbType.toLowerCase().equals(DBTypeEnum.PG.getValue())) {

            } else if (dbType.toLowerCase().equals(DBTypeEnum.MYSQL.getValue())) {

            } else {
                throw new BizException("5001", "暂不支持该数据库类型");
            }
        }
        //调用AI 解析模型结构
        String modelReq = createModelByStructure(itemDO.getContent());
        ModelTableAditReqVO aditReq;
        try {
            // 解析请求对象
            aditReq = JSONObject.parseObject(modelReq, ModelTableAditReqVO.class);
        } catch (Exception e) {
            throw new BizException("5001", "解析出错，请重试");
        }
        if(ObjUtilX.isEmpty(aditReq) || ObjUtilX.isEmpty(aditReq.getFields())){
            throw new BizException("5001","AI未提取到字段");
        }
        // 获取字段名称列表
        List<String> fieldNames = aditReq.getFields().stream()
                .map(ModelFieldAditReqVO::getFieldName)
                .collect(Collectors.toList());
        // 追加默认字段
        //addDefault(aditReq, fieldNames);

        // 查询字段配置
        List<ModelFieldConfDO> fieldConfList = fieldConfMapper.selectList(LambdaQueryWrapperX.<ModelFieldConfDO>lambdaQueryX()
                .in(ModelFieldConfDO::getFieldName, fieldNames)
                .ne(ModelFieldConfDO::getFieldType, null)
        );

        // 使用 Map 存储字段配置以便快速查找
        Map<String, ModelFieldConfDO> fieldConfMap = fieldConfList.stream()
                .filter(fieldConf -> StrUtilX.isNotEmpty(fieldConf.getFieldCode()))
                .collect(Collectors.toMap(
                        ModelFieldConfDO::getFieldCode,    // 以 EnglishName 作为 key
                        fieldConf -> fieldConf,              // 以 fieldConf 为 value
                        (existing, replacement) -> replacement // 如果有重复的 key，取后者
                ));

        // 遍历请求字段并更新字段代码
        aditReq.getFields().forEach(params -> {
            ModelFieldConfDO fieldConf = fieldConfMap.get(params.getFieldName());
            if (fieldConf != null) {
                // 更新关联字段的内容
                params.setFieldCode(fieldConf.getFieldCode()); // 更新字段代码
                params.setFieldType(fieldConf.getFieldType());
                params.setFieldTypeName(ObjUtilX.isEmpty(fieldConf.getFieldTypeName()) ? fieldConf.getFieldType() : fieldConf.getFieldTypeName());
                params.setLeng(fieldConf.getLeng());
                params.setFieldPrecision(fieldConf.getFieldPrecision());
                // 如有需要，还可以更新其他字段，例如：
                // params.setOtherField(fieldConf.getOtherFieldValue());
            }
        });


        aditReq.setParentId(ObjUtilX.isEmpty(reqVO.getParentItemId()) ? 0L : reqVO.getParentItemId());
        aditReq.setProjectId(itemDO.getProjectId());
        return modelTableAdd(aditReq);
    }

    @Override
    public Integer dragItemInfo(ModelTableDragReqVO reqVO) {
        try {
            log.info("dragTable,params : " + reqVO);

            // 目标文件
            ModelTableDO destItemInfo = tableMapper.selectById(reqVO.getDestTableId());
            if(ObjUtilX.isEmpty(destItemInfo)){
                throw new BizException("5001", "目标项不存在");
            }

            // 获取被拖动的项目
            ModelTableDO itemDo = tableMapper.selectById(reqVO.getTableId());
            if(ObjUtilX.isEmpty(itemDo)){
                throw new BizException("5001", "拖拽的项目不存在");
            }

            // 检查移动的有效性
            checkMoveType(itemDo, destItemInfo.getParentId());

            // 更新目标项目的父ID
            itemDo.setParentId(destItemInfo.getParentId());

            // 获取当前父节点下的所有同级条目
            List<ModelTableDO> siblings = tableMapper.selectList(LambdaQueryWrapperX.<ModelTableDO>lambdaQueryX()
                    .eq(ModelTableDO::getParentId, destItemInfo.getParentId())
            );

            // 计算新序号
            int newItemSeq;
            if (reqVO.getDragType() == 0) { // 拖到目标之前
                newItemSeq = destItemInfo.getSeq(); // 新序号为目标项的序号
            } else { // 拖到目标之后
                newItemSeq = destItemInfo.getSeq() + 1; // 新序号为目标项的序号加一
            }

            // 确保新序号大于等于0
            newItemSeq = Math.max(newItemSeq, 0);

            // 更新其他同级项目的序号，避免冲突
            for (ModelTableDO sibling : siblings) {
                if (!sibling.getTableId().equals(itemDo.getTableId())) {
                    if (sibling.getSeq() >= newItemSeq) {
                        sibling.setSeq(sibling.getSeq() + 1); // 序号加一
                    }
                }
            }

            // 一次性更新所有同级项的序号
            tableMapper.batchUpdateItemSeq(siblings);

            // 设置新项目的序号并更新
            itemDo.setSeq(newItemSeq);
            tableMapper.updateById(itemDo);
            return 1; // 返回更新成功状态
        }catch (Exception e) {
            log.error("[拖动建模]移动文件失败！参数[" + reqVO + "].", e);
            throw new BizException("5001", "操作失败");
        }
    }

    @Override
    public String modelTableImportBatch(ModelTableImportListReqVO reqVO) {
        ModelTableCodeReqVO req = new ModelTableCodeReqVO();
        req.setParentId(reqVO.getParentId());
        req.setDataSourceConfigId(reqVO.getDataSourceConfigId());

        List<String> successTables = new ArrayList<>();
        List<String> failedTables = new ArrayList<>();

        for (String tableCode : reqVO.getTableCodes()) {
            req.setTableCode(tableCode);
            try {
                modelTableImport(req);
                successTables.add(tableCode);
            } catch (Exception e) {
                log.error("导入表 {} 失败: {}", tableCode, e.getMessage(), e);
                failedTables.add(tableCode + ":" + e.getMessage());
            }
        }
        // 构建返回结果
        StringBuilder result = new StringBuilder();
        result.append("批量导入完成。");
        if (!successTables.isEmpty()) {
            result.append("成功: ").append(String.join(", ", successTables)).append("。");
        }
        if (!failedTables.isEmpty()) {
            result.append("失败: ").append(String.join(", ", failedTables)).append("。");
        }

        return result.toString();
    }

    /**
     * 优化后的批量导入方法 - 复用数据库连接，提升性能
     * @param reqVO 批量导入请求参数
     * @return 导入结果信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String modelTableImportBatchOptimized(ModelTableImportListReqVO reqVO) {
        // 参数验证
        if (ObjUtilX.isEmpty(reqVO.getParentId())) {
            throw new BizException("5001", "请选择父级");
        }
        if (CollUtilX.isEmpty(reqVO.getTableCodes())) {
            throw new BizException("5001", "请选择要导入的表");
        }

        // 查询父级目录
        ModelTableDO parent = tableMapper.selectById(reqVO.getParentId());
        if (ObjUtilX.isEmpty(parent)) {
            throw new BizException("5001", "找不到父级目录");
        }

        // 使用父级的数据源配置
        Long dataSourceConfigId = parent.getDataSourceConfigId();
        Connection connection = null;
        DBCreator creator = null;

        try {
            // 创建一个共享的数据库连接
            connection = dbUtil.getCon(dataSourceConfigId);
            creator = DBCreatorFactory.getDBCreatorDynamic(dataSourceConfigId);

            List<String> successTables = new ArrayList<>();
            List<String> failedTables = new ArrayList<>();

            // 批量处理每个表
            for (String tableCode : reqVO.getTableCodes()) {
                try {
                    // 检查表是否已存在
                    ModelTableDO existingTable = tableMapper.selectOne(LambdaQueryWrapperX.<ModelTableDO>lambdaQueryX()
                            .eq(ModelTableDO::getTableCode, tableCode)
                            .eq(ModelTableDO::getDataSourceConfigId, dataSourceConfigId)
                            .eq(ModelTableDO::getProjectId, parent.getProjectId())
                    );

                    if (ObjUtilX.isNotEmpty(existingTable)) {
                        // 如果表已存在，执行同步操作
                        modelTableSyncNew(existingTable.getTableId());
                        successTables.add(tableCode + "(已同步)");
                    } else {
                        // 导入新表
                        ModelTableCodeReqVO req = new ModelTableCodeReqVO();
                        req.setParentId(reqVO.getParentId());
                        req.setDataSourceConfigId(reqVO.getDataSourceConfigId());
                        req.setTableCode(tableCode);
                        modelTableImport(req);
                        //importSingleTableOptimized(tableCode, parent, connection, creator);
                        successTables.add(tableCode + "(新导入)");
                    }
                } catch (Exception e) {
                    log.error("导入表 {} 失败: {}", tableCode, e.getMessage(), e);
                    failedTables.add(tableCode + ":" + e.getMessage());
                }
            }

            // 构建返回结果
            StringBuilder result = new StringBuilder();
            result.append("批量导入完成。");
            if (!successTables.isEmpty()) {
                result.append("成功: ").append(String.join(", ", successTables)).append("。");
            }
            if (!failedTables.isEmpty()) {
                result.append("失败: ").append(String.join(", ", failedTables)).append("。");
            }

            return result.toString();

        } catch (Exception e) {
            log.error("批量导入失败", e);
            throw new BizException("5001", "批量导入失败: " + e.getMessage());
        } finally {
            // 确保连接被正确关闭
            if (connection != null) {
                try {
                    connection.close();
                } catch (Exception e) {
                    log.error("关闭数据库连接失败", e);
                }
            }
        }
    }

    /**
     * 优化的单表导入方法 - 复用数据库连接
     * @param tableCode 表编码
     * @param parent 父级目录
     * @param connection 共享的数据库连接
     * @param creator 数据库创建器
     * @return 表结构信息
     */
    private TableStructure importSingleTableOptimized(String tableCode, ModelTableDO parent,
                                                     Connection connection, DBCreator creator) throws Exception {
        TableStructure tableStructure = null;
        ModelTableDO table = new ModelTableDO();
        Long tableId = IdWorker.getId();

        // 设置表基本信息
        table.setTableId(tableId);
        table.setIsGen(1);
        table.setTableCode(tableCode);
        table.setDataSourceConfigId(parent.getDataSourceConfigId());
        table.setProjectId(parent.getProjectId());
        table.setParentId(parent.getTableId());

        // 创建SQL日志记录
        SqlLogDO sqlLog = new SqlLogDO(parent.getProjectId(), table.getTableId(),
                                      table.getTableCode(), "", DBOPTypeEnum.CREATE_TABLE.getType(),
                                      1, DBOPTypeEnum.CREATE_TABLE.getDesc());

        try {
            // 使用共享连接获取表结构，避免重复创建连接 这个还有问题
            //tableStructure = getTableStructureWithConnection(table, connection);
            tableStructure = creator.getTableStructure(table);

            // 验证表结构
            if (ObjUtilX.isEmpty(tableStructure) || CollUtilX.isEmpty(tableStructure.getColumns())) {
                throw new BizException("5001", "无法获取表 " + tableCode + " 的结构信息");
            }

            // 插入主表记录
            tableMapper.insert(table);

            // 批量插入字段信息
            List<ModelFieldDO> modelFieldDOList = new ArrayList<>();
            for (ColumnInfo column : tableStructure.getColumns()) {
                ModelFieldDO fieldDO = convertColumnToField(column, tableId);
                modelFieldDOList.add(fieldDO);
            }

            if (!modelFieldDOList.isEmpty()) {
                fieldMapper.insertBatch(modelFieldDOList);
            }

            // 处理索引信息
            if (!CollUtilX.isEmpty(tableStructure.getIndexs())) {
                List<ModelTableIndexDO> indexList = new ArrayList<>();
                for (ModelTableIndexRespVO indexInfo : tableStructure.getIndexs()) {
                    ModelTableIndexDO indexDO = convertIndexInfoToIndexDO(indexInfo, tableId);
                    indexList.add(indexDO);
                }

                if (!indexList.isEmpty()) {
                    tableIndexMapper.insertBatch(indexList);
                }
            }

            // 生成建表SQL（用于日志记录）
            StringBuilder sql = creator.genCreateTableSQL(table);
            sqlLog.setSql(sql.toString());

            log.info("导入表结构完毕：{}", table.getTableCode());

        } catch (Exception e) {
            sqlLog.setSucStatus(0);
            sqlLog.setErrMgs(e.getMessage());
            throw new BizException("5001", tableCode + " -> 导入表结构失败: " + e.getMessage());
        } finally {
            // 记录SQL日志
            try {
                sqlLogMapper.insert(sqlLog);
            } catch (Exception e) {
                log.error("插入日志失败: {}", e.getMessage());
            }
        }

        return tableStructure;
    }

    /**
     * 使用共享连接获取表结构信息
     * @param table 表信息
     * @param connection 共享的数据库连接
     * @return 表结构信息
     */
    private TableStructure getTableStructureWithConnection(ModelTableDO table, Connection connection) throws SQLException {
        String tableName = table.getTableCode();
        DatabaseMetaData metaData = connection.getMetaData();

        // 获取表信息
        ResultSet tables = metaData.getTables(connection.getCatalog(), connection.getSchema(), tableName, null);
        String remarks = "";
        if (tables.next()) {
            remarks = tables.getString("REMARKS");
            if (ObjUtilX.isEmpty(remarks)) {
                throw new BizException("5001", "表 " + tableName + " 描述信息为空，请补充");
            }
            table.setTableName(remarks);
            table.setRemark(remarks);
        } else {
            throw new BizException("5001", tableName + " 表未找到");
        }
        tables.close();

        // 构建表结构对象
        TableStructure tableStructure = new TableStructure();
        tableStructure.setTableId(table.getTableId());
        tableStructure.setTableCode(table.getTableCode());
        tableStructure.setTableName(remarks);

        // 获取列信息
        ResultSet columns = metaData.getColumns(connection.getCatalog(), connection.getSchema(), tableName, null);
        List<ColumnInfo> columnList = new ArrayList<>();
        Integer sort = 0;
        while (columns.next()) {
            String columnName = columns.getString("COLUMN_NAME");
            ColumnInfo column = new ColumnInfo();
            column.setSort(sort++);
            column.setName(columnName);
            column.setType(columns.getString("TYPE_NAME"));
            column.setSize(columns.getInt("COLUMN_SIZE"));
            column.setNullable(columns.getInt("NULLABLE") == DatabaseMetaData.columnNullable);
            column.setFieldPrecision(columns.getInt("DECIMAL_DIGITS"));

            // 获取列注释
            String columnComment = columns.getString("REMARKS");
            column.setRemark(ObjUtilX.isEmpty(columnComment) ? columnName : columnComment);

            // 获取默认值
            String defaultValue = columns.getString("COLUMN_DEF");
            column.setDefaultValue(defaultValue);

            columnList.add(column);
        }
        columns.close();

        tableStructure.setColumns(columnList);

        // 获取主键信息
        ResultSet primaryKeys = metaData.getPrimaryKeys(connection.getCatalog(), connection.getSchema(), tableName);
        List<String> pkColumns = new ArrayList<>();
        while (primaryKeys.next()) {
            pkColumns.add(primaryKeys.getString("COLUMN_NAME"));
        }
        primaryKeys.close();

        // 设置主键标识
        for (ColumnInfo column : columnList) {
            if (pkColumns.contains(column.getName())) {
                column.setPrimaryKey(true);
            }
        }

        // 获取索引信息
        try {
            ResultSet indexes = metaData.getIndexInfo(connection.getCatalog(), connection.getSchema(), tableName, false, false);
            List<ModelTableIndexRespVO> indexList = new ArrayList<>();
            Map<String, ModelTableIndexRespVO> indexMap = new HashMap<>();

            while (indexes.next()) {
                String indexName = indexes.getString("INDEX_NAME");
                if (ObjUtilX.isEmpty(indexName) || "PRIMARY".equals(indexName)) {
                    continue; // 跳过主键索引
                }

                ModelTableIndexRespVO indexInfo = indexMap.get(indexName);
                if (indexInfo == null) {
                    indexInfo = new ModelTableIndexRespVO();
                    indexInfo.setIdxName(indexName);
                    indexInfo.setNonUnique(indexes.getBoolean("NON_UNIQUE") ? 1 : 0);
                    indexInfo.setIdxType(indexes.getBoolean("NON_UNIQUE") ? "NORMAL" : "UNIQUE");
                    indexInfo.setFields(new ArrayList<>());
                    indexMap.put(indexName, indexInfo);
                    indexList.add(indexInfo);
                }

                String columnName = indexes.getString("COLUMN_NAME");
                if (ObjUtilX.isNotEmpty(columnName)) {
                    indexInfo.setFieldCode(columnName);
                }
            }
            indexes.close();

            tableStructure.setIndexs(indexList);
        } catch (Exception e) {
            log.warn("获取表 {} 索引信息失败: {}", tableName, e.getMessage());
            tableStructure.setIndexs(new ArrayList<>());
        }

        return tableStructure;
    }

    /**
     * 将列信息转换为字段DO对象
     * @param column 列信息
     * @param tableId 表ID
     * @return 字段DO对象
     */
    private ModelFieldDO convertColumnToField(ColumnInfo column, Long tableId) {
        ModelFieldDO fieldDO = new ModelFieldDO();
        fieldDO.setFieldId(IdWorker.getId());
        fieldDO.setTableId(tableId);
        fieldDO.setFieldCode(column.getName());
        fieldDO.setFieldName(column.getRemark());
        fieldDO.setFieldType(column.getType());
        fieldDO.setLeng(column.getSize());
        fieldDO.setFieldPrecision(column.getFieldPrecision());
        fieldDO.setIsNullable(column.getNullable() ? 0 : 1);
        fieldDO.setIsPrimaryKey(column.getPrimaryKey() ? 1 : 0);
        fieldDO.setDefaultVal(column.getDefaultValue());
        fieldDO.setSort(column.getSort());
        //fieldDO.setRemark(column.getRemark());

        // 设置字段属性类型（系统字段）
        fieldDO.setPropType(0);

        return fieldDO;
    }

    /**
     * 将索引信息转换为索引DO对象
     * @param indexInfo 索引信息
     * @param tableId 表ID
     * @return 索引DO对象
     */
    private ModelTableIndexDO convertIndexInfoToIndexDO(ModelTableIndexRespVO indexInfo, Long tableId) {
        ModelTableIndexDO indexDO = new ModelTableIndexDO();
        indexDO.setIdxId(IdWorker.getId());
        indexDO.setTableId(tableId);
        indexDO.setIdxName(indexInfo.getIdxName());
        indexDO.setIdxType(indexInfo.getIdxType());
        indexDO.setNonUnique(indexInfo.getNonUnique());
        indexDO.setFieldCode(indexInfo.getFieldCode());
        indexDO.setRemark("系统导入");

        return indexDO;
    }

    @Override
    public String modelTableDbType(Long tableId) {
        ModelTableDO modelTableDO = this.modelTableValidateExists(tableId);
        String dbType = dbUtil.getDbTypeDynamic(modelTableDO.getDataSourceConfigId());
        return dbType.toLowerCase();
    }

    private void checkMoveType(ModelTableDO itemDo, Long newParentItemId) throws BizException {
        if(newParentItemId == null || newParentItemId == 0L){
            //非项目外文件夹和项目文件夹，不能当作根节点
            //if(itemDo.getItemType() != 0 && itemDo.getItemType() != 1){
            if(itemDo.getDirType() != 0 && itemDo.getDirType() != 1){
                throw new BizException("5001", "该文件不能拖拽！");
            }
        }else{
            ModelTableDO destParentItemDo = tableMapper.selectById(newParentItemId);
            if(ObjUtilX.isEmpty(destParentItemDo)){
                throw new BizException("5001", "父节点不存在");
            }
            // 文件夹不能移动到项目内
            if(itemDo.getDirType() == 0 && (destParentItemDo.getDirType() != 0)){
                throw new BizException("5001", "文件夹不能移动到项目内！");
            }
            // 不能跨数据源拖拽
            if(!itemDo.getDataSourceConfigId().equals(destParentItemDo.getDataSourceConfigId())){
                throw new BizException("5001", "不能跨数据源拖拽！");
            }
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long modelTableAdd(ModelTableAditReqVO reqVO) {
        validPrentDir(reqVO);
        //验重
        Long duplicheck = repeatValid(reqVO);
        if (duplicheck > 0){
            throw new BizException("5001","您输入的属性值重复，请重新输入");
        }
        //建表时，type=1，必须传字段列表
//        if(reqVO.getDirType() == 1 && CollUtilX.isEmpty(reqVO.getFields())){
//            throw new BizException("5001","请填写表字段");
//        }
        //if(reqVO.getDirType() == -1 && StrUtilX.isEmpty(reqVO.getTableCode())){
        //    throw new BizException("5001","请填写项目编码");
        //}

        Long tableId = IdWorker.getId();
        // 新建项目
        ModelTableDO table = BeanUtilX.copy(reqVO, ModelTableDO::new);
        if(reqVO.getDirType() == -1){
            if(ObjUtilX.isEmpty(reqVO.getDataSourceConfigId())){
                throw new BizException("5001","请选择数据源");
            }
            DBCreatorFactory.getDBCreatorDynamic(reqVO.getDataSourceConfigId());
            // 数据源已经被人使用
            if(tableMapper.selectCount(LambdaQueryWrapperX.<ModelTableDO>lambdaQueryX()
                    .eq(ModelTableDO::getDirType, -1)
                    .eq(ModelTableDO::getDataSourceConfigId, reqVO.getDataSourceConfigId())) > 0){
                throw new BizException("5001","数据源已经被其他项目使用，请更换");
            }
        }else{
            if(ObjUtilX.isEmpty(reqVO.getProjectId())){
                throw new BizException("5001","请传入项目ID");
            }
            // 新建表
            if(reqVO.getDirType() == 1){
                if(StrUtilX.isEmpty(reqVO.getTableCode())) {
                    throw new BizException("5001", "请填写表英文名");
                }
                //查询项目初始化字段，初始化默认字段到表
                //去除初始化字段
//                List<ProjectModelFieldDO> modelFieldDOS = projectModelFieldMapper.selectList(LambdaQueryWrapperX.<ProjectModelFieldDO>lambdaQueryX()
//                        .orderByAsc(ProjectModelFieldDO::getSort)
//                );
//                if (ObjUtilX.isNotEmpty(modelFieldDOS)) {
//                    // 有快速建模字段
//                    if (ObjUtilX.isNotEmpty(reqVO.getFieldStr())) {
//                        int p = ObjUtilX.isEmpty(reqVO.getFields()) ? 0 : reqVO.getFields().size();
//                        int q = ObjUtilX.isEmpty(modelFieldDOS) ? 0 : modelFieldDOS.size();
//                        // 调翻译接口
//                        List<String> fieldNameList = Arrays.stream(reqVO.getFieldStr().split("[|\\n\\s,;，；]+")).toList();
//                        List<ModelFieldConfDO> fieldConfDOS = fieldConfService.getModelFieldConfDOS(reqVO.getProjectId(), reqVO.getFieldStr(), fieldNameList);
//                        if(StrUtilX.isEmpty(fieldConfDOS)) {
//                            throw new BizException("5001", "快速建模字段翻译失败");
//                        }
//                        for (int i = 0; i < fieldConfDOS.size(); i++) {
//                            ModelFieldConfDO confDO = fieldConfDOS.get(i);
//                            ModelFieldAditReqVO adit = BeanUtilX.copy(confDO, ModelFieldAditReqVO::new);
//                            adit.setTableId(tableId);
//                            adit.setSort(p + q + i);
//                            // 添加快速建模字段
//                            reqVO.getFields().add(adit);
//                        }
//                    }
//                    // 初始化 fields 集合
//                    List<ModelFieldAditReqVO> fields = new ArrayList<>(Optional.ofNullable(reqVO.getFields()).orElse(Collections.emptyList()));
//                    // 使用 Map 存储请求中的字段以便快速查找
//                    Map<String, ModelFieldAditReqVO> initMap = Optional.of(fields)
//                            .orElse(Collections.emptyList())
//                            .stream()
//                            .collect(Collectors.toMap(ModelFieldAditReqVO::getFieldCode, Function.identity()));
//
//                    // 创建并添加默认字段
//                    for (ProjectModelFieldDO fieldDO : modelFieldDOS) {
//                        String fieldCodeLower = fieldDO.getFieldCode().toLowerCase(); // 将字段代码转换为小写进行比较
//
//                        // 如果存在重复字段，则更新其值，否则添加新字段
//                        if (initMap.containsKey(fieldCodeLower)) {
//                            ModelFieldAditReqVO existingField = initMap.get(fieldCodeLower);
//                            // 以 fieldDO 的值为准更新现有字段
//                            existingField = BeanUtilX.copy(fieldDO, existingField);
//                            existingField.setPropType(1);
//                        } else {
//                            ModelFieldAditReqVO newField = BeanUtilX.copy(fieldDO, ModelFieldAditReqVO::new);
//                            newField.setTableId(tableId);
//                            newField.setPropType(1);
//                            newField.setFieldCode(fieldCodeLower); // 字段都保存小写
//                            fields.add(newField);
//                        }
//                    }
//
//                    // 设置更新后的字段列表
//                    reqVO.setFields(fields);
//                }
            }

            // 处理父节点
            Long pId = null;
            if(ObjUtilX.isNotEmpty(reqVO.getParentId()) && reqVO.getParentId() != 0L){
                pId = reqVO.getParentId();
            }else{
                pId = reqVO.getProjectId();
            }
            // 查询项目
            ModelTableDO project = tableMapper.selectById(pId);
            if(ObjUtilX.isEmpty(project)){
                throw new BizException("5001","选择的项目不存在");
            }

            // 如果前端传了数据源，就取传递的那个
            if(ObjUtilX.isNotEmpty(reqVO.getDataSourceConfigId())){
                table.setDataSourceConfigId(reqVO.getDataSourceConfigId());
            }else{
                table.setDataSourceConfigId(project.getDataSourceConfigId());
            }


        }

        table.setTableId(tableId);
        table.setIsGen(0);
        // 保存建模字段，type=1，插入模型字段表
        addFields(reqVO, tableId);
        if(ObjUtilX.isNotEmpty(reqVO.getParentId()) && reqVO.getParentId() > 0L){
            tableMapper.updateItemSeqAddOne(reqVO.getParentId(),-1);
        }
        // 插入主表
        tableMapper.insert(table);
        // 绑定附件
        //fileService.saveFile(tableId, reqVO.getAccountFileList(), FileTableEnum.ACCOUNT_FILE);

        // 返回
        return table.getTableId();
    }

    private void validPrentDir(ModelTableAditReqVO reqVO) {
        if (reqVO.getDirType() == 1){
            //if(!reqVO.getTableCode().toLowerCase().startsWith("u_")) {
            //    throw new BizException("5001", "用户表英文名必须是u_开头");
            //}
            // 平台做一下表名限制
            if (!reqVO.getTableCode().toLowerCase().matches("^(a_|u_|f_|o_|s_|t_).*") && reqVO.getProjectId() == 1889599398700933121L) {
                throw new BizException("5001", "用户表英文名必须是 a_, u_, f_, o_, s_, 或 t_ 开头");
            }
//            if(ObjUtilX.isEmpty(reqVO.getParentId()) || reqVO.getParentId() == 0L) {
//                throw new BizException("5001", "模型表必须放在目录下");
//            }
            // 校验父节点存在
            Long confId = validParent(reqVO.getParentId(), reqVO.getDataSourceConfigId());
            reqVO.setDataSourceConfigId(confId);
        }else{
            Long confId = validParent(reqVO.getParentId(), reqVO.getDataSourceConfigId());
            reqVO.setDataSourceConfigId(confId);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long modelTableAddNew(ModelTableJsonReqVO reqVO) {
        // 创建 ModelTableDO
        ModelTableDO modelTable = new ModelTableDO();
        Long tableId = IdWorker.getId();
        modelTable.setTableId(tableId);
        modelTable.setTableName(reqVO.getItemName());
        modelTable.setRemark(reqVO.getCompositeName()); // 根据需要设置
        modelTable.setIsGen(0); // 默认值或根据需求设置
        modelTable.setUpgradeFlag(0); // 默认值或根据需求设置
        modelTable.setDirType(1); // 默认值或根据需求设置
        modelTable.setPropType(1); // 用户属性类型
        modelTable.setParentId(null); // 根据业务逻辑设置父节点ID

        // 处理 level:2 中的第一个元素（children）
        if (reqVO.getChildren() != null && reqVO.getChildren().size() > 0) {
            ModelTableJsonReqVO.Child level2Child1 = reqVO.getChildren().get(0);
//            modelTableDO.setTableCode(level2Child1.getEnglishName()); // Level 2 第一个元素的 englishName

            // 处理 level:3 中的第一元素，并假设只有一个
            List<ModelTableJsonReqVO.Child> level3Children = level2Child1.getChildren();
            if (level3Children != null && level3Children.size() == 1) {
                ModelTableJsonReqVO.Child level3Child = level3Children.get(0);

                // 设置 ModelTableDO 的必要字段
                modelTable.setTableCode(level3Child.getItemName());
                modelTable.setRemark(level3Child.getRemark());
                // 根据需要设置其他字段
                // modelTableDO.setOtherField(value);
            }
        }
        //验重
        ModelTableAditReqVO req = new ModelTableAditReqVO();
        req.setTableCode(modelTable.getTableCode());
        req.setTableName(modelTable.getTableName());
        req.setParentId(modelTable.getParentId());
        Long duplicheck = repeatValid(req);
        if (duplicheck > 0){
            throw new BizException("5001","您输入的属性值重复，请重新输入");
        }

        // 插入主表
        tableMapper.insert(modelTable);

        // 处理 level:2 中的第二个元素（children）
        if (reqVO.getChildren().size() > 1) {
            ModelTableJsonReqVO.Child level2Child2 = reqVO.getChildren().get(1);
            if(ObjUtilX.isEmpty(level2Child2.getChildren())){
                throw new BizException("5001","字段信息缺失，请补充! ");
            }
            List<ModelFieldDO> modelFieldDOList = new ArrayList<>();

            boolean havePrimary = false;
            // 处理 level:3 中的所有元素
            for (ModelTableJsonReqVO.Child level3Child : level2Child2.getChildren()) {
                if(ObjUtilX.isEmpty(level3Child.getFieldCode()) || ObjUtilX.isEmpty(level3Child.getItemName())){
                    throw new BizException("5001","字段中文名或英文名缺失，请补充! ");
                }
                ModelFieldDO modelFieldDO = new ModelFieldDO();
                modelFieldDO.setTableId(tableId);
                modelFieldDO.setFieldCode(level3Child.getFieldCode()); // 将 English name 映射到 field code
                //modelFieldDO.setFieldCode(level3Child.getEnglishName()); // 将 English name 映射到 field code
                modelFieldDO.setFieldName(level3Child.getItemName()); // 将 Item name 映射到 field name
                modelFieldDO.setRemark(level3Child.getCompositeName()); // Composite name 映射到 remark
                //modelFieldDO.setIsPrimaryKey(null != level3Child.getIsPrimaryKey()
                //        && level3Child.getIsPrimaryKey()?1:0); // Primary key 标记
                modelFieldDO.setIsPrimaryKey(level3Child.getIsPrimaryKey()); // Primary key 标记
                if (modelFieldDO.getIsPrimaryKey() == 1) {
                    havePrimary = true;
                }
                String fieldType = level3Child.getFieldType().toUpperCase();
                modelFieldDO.setFieldType(fieldType); // 数据类型
                modelFieldDO.setFieldTypeName(fieldType);
                if(null != level3Child.getHasLength() && level3Child.getHasLength()) {
                    modelFieldDO.setLeng(level3Child.getDataLength()); // 长度
                }
                if(null != level3Child.getHasPoint() && level3Child.getHasPoint()) {
                    modelFieldDO.setFieldPrecision(level3Child.getDataPoint()); // 精度
                }

                // 处理 level:4 children 列表
                List<ModelTableJsonReqVO.Child> level4Children = level3Child.getChildren();
                if(ObjUtilX.isNotEmpty(level4Children)) {
                    List<JSONObject> jsonFields = new ArrayList<>();
                    for (ModelTableJsonReqVO.Child level4Child : level4Children) {
                        JSONObject jsonField = new JSONObject();
                        jsonField.put("fieldName", level4Child.getItemName());
                        jsonField.put("remark", level4Child.getRemark());
                        jsonField.put("fieldCode", level4Child.getEnglishName());

                        jsonFields.add(jsonField); // 将 JSON 字段添加到列表
                    }
                    modelFieldDO.setJsonFields(jsonFields); // 设置 JSON 字段
                }
                modelFieldDOList.add(modelFieldDO); // 将 ModelFieldDO 添加到列表
            }
            if(!havePrimary){
                throw new BizException("5001", "请添加一个主键");
            }

            // 将 ModelFieldDO 列表设置到 ModelTableDO 中
            fieldMapper.insertBatch(modelFieldDOList);
        }else{
            throw new BizException("5001", "数据格式错误");
        }
        return tableId;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long modelTableEditNew(ModelTableJsonReqVO reqVO, Long tableId) {
        // 查询模型表信息
        ModelTableDO modelTableDO = this.modelTableValidateExists(tableId);
        // 获取现有表字段
        List<ModelFieldDO> existingFields = fieldMapper.selectList(LambdaQueryWrapperX.<ModelFieldDO>lambdaQueryX()
                        .eq(ModelFieldDO::getTableId, tableId));

        // 解析传入的对象，获取表信息，字段信息
        Map<String, Object> tableStruct = null;
        try {
            tableStruct = parseTableAndFieldsFromReqVO(reqVO, modelTableDO);
        } catch (JsonProcessingException e) {
            throw new BizException("5001", "JSON解析失败");
        }
        List<ModelFieldAditReqVO> newFields = (List<ModelFieldAditReqVO>) tableStruct.getOrDefault("modelFields", new ArrayList<>());

        // 用于保存新增、修改和删除的字段列表
        List<ModelFieldAditReqVO> fieldsToAdd = new ArrayList<>();
        List<ModelFieldAditReqVO> fieldsToUpdate = new ArrayList<>();
        List<ModelFieldDO> fieldsToDelete = new ArrayList<>(existingFields); // 先假设所有现有字段都有可能被删除

        // 删除字段：保留那些未在 newFields 中找到的现有字段
        fieldsToDelete.removeIf(existingField -> newFields.stream()
                .anyMatch(newField -> existingField.getFieldCode().equals(newField.getFieldCode())));

        DBCreator creator = DBCreatorFactory.getDBCreatorDynamic(modelTableDO.getDataSourceConfigId());

        for (ModelFieldDO field : fieldsToDelete) {
            if (modelTableDO.getIsGen() == 1) {
                creator.dropField(field, modelTableDO);
            }
            fieldMapper.deleteById(field.getFieldId());//这里搞不了事务
            //删除索引相关
            tableIndexMapper.delete(LambdaQueryWrapperX.<ModelTableIndexDO>lambdaQueryX()
                    .eq(ModelTableIndexDO::getTableId, reqVO.getTableId())
                    .eq(ModelTableIndexDO::getFieldId, field.getFieldId()));
        }

        Map<Long, ModelFieldDO> exsitFieldIdMap = existingFields.stream().collect(Collectors.toMap(ModelFieldDO::getFieldId, a -> a));

        // 将 existingFields 转换为 Map 以便快速查找
        Map<String, ModelFieldDO> existingFieldsMap;
        try {
            existingFieldsMap = existingFields.stream()
                    .collect(Collectors.toMap(ModelFieldDO::getFieldCode, field -> field));
        }catch (Exception e){
            e.printStackTrace();
            throw new BizException("5001", "字段编码重复，请检查");
        }
        String upFieldCode = "";
        // 遍历新增字段以判断新增和修改
        List<ModelFieldConfAditReqVO> fieldConfList = new ArrayList<>();
        for (ModelFieldAditReqVO newField : newFields) {
            if (ObjUtilX.isEmpty(newField.getLeng())) {
                throw new BizException("5001", newField.getFieldName()+":字段长度不能为空");
            }
            boolean isExistingField = existingFieldsMap.containsKey(newField.getFieldCode());
            //记录上一行的字段
            if (ObjUtilX.isNotEmpty(upFieldCode)) {
                newField.setUpFieldCode(upFieldCode);
            }
            upFieldCode = newField.getFieldCode();
            // 新增字段
            //if (ObjUtilX.isEmpty(newField.getFieldId()) || !isExistingField) {
            if (!isExistingField) {
                newField.setFieldId(null);//这里去掉前端复制的id
                fieldsToAdd.add(newField);
                addConf(modelTableDO.getProjectId(), newField, fieldConfList);
            }

            // 修改字段
            else if (isExistingField) {
                ModelFieldDO existingField = existingFieldsMap.get(newField.getFieldCode());
                if (existingField.getFieldId().equals(newField.getFieldId())
                        && (!existingField.getFieldCode().equals(newField.getFieldCode())
                        || !existingField.getFieldName().equals(newField.getFieldName())
                        || !existingField.getFieldType().equals(newField.getFieldType())
                        || !existingField.getLeng().equals(newField.getLeng())
                        || !existingField.getFieldPrecision().equals(newField.getFieldPrecision())
                        || (null != existingField.getRemark() && !existingField.getRemark().equals(newField.getRemark()))
                        || !existingField.getSort().equals(newField.getSort())
                        || !existingField.getIsPrimaryKey().equals(newField.getIsPrimaryKey())
                        || !existingField.getIsNullable().equals(newField.getIsNullable())
                        || (ObjUtilX.isNotEmpty(newField.getJsonFields()) && !existingField.getJsonFields().equals(newField.getJsonFields()))
                        || (!areDefaultValuesEqual(newField.getDefaultVal(), existingField.getDefaultVal()))// 修改了默认值
                )) {
                    fieldsToUpdate.add(newField);
                    addConf(modelTableDO.getProjectId(), newField, fieldConfList);
                }
            }
        }

        for (ModelFieldAditReqVO vo : fieldsToAdd) {
            ModelFieldDO field = BeanUtilX.copy(vo, ModelFieldDO::new);
            field.setFieldId(null);//这里去掉前端复制的id
            if (modelTableDO.getIsGen() == 1) {
                creator.addField(field, modelTableDO);
            }
            fieldMapper.insert(field);//这里搞不了事务
        }

        for (ModelFieldAditReqVO vo : fieldsToUpdate) {
            ModelFieldDO field = BeanUtilX.copy(vo, ModelFieldDO::new);
            if (modelTableDO.getIsGen() == 1) {
                ModelFieldDO existingField = exsitFieldIdMap.get(field.getFieldId());
                if(!(
                        field.getFieldCode().equals(existingField.getFieldCode())
                                && field.getFieldName().equals(existingField.getFieldName())
                                && field.getFieldType().equals(existingField.getFieldType())
                                && field.getLeng().equals(existingField.getLeng())
                                && field.getFieldPrecision().equals(existingField.getFieldPrecision())
                                && field.getIsNullable().equals(existingField.getIsNullable())
                                && field.getIsPrimaryKey().equals(existingField.getIsPrimaryKey())
                                //&& field.getRemark().equals(existingField.getRemark())
                                && (areDefaultValuesEqual(field.getDefaultVal(), existingField.getDefaultVal())) // 使用新方法进行默认值比较
                )
                ){
                    creator.modifyField(field, existingField, modelTableDO);
                }
            }
            fieldMapper.updateById(field);//这里搞不了事务
            //修改索引里面的字段名称
            tableIndexMapper.update(new LambdaUpdateWrapper<ModelTableIndexDO>()
                    .eq(ModelTableIndexDO::getFieldId, field.getFieldId())
                    .set(ModelTableIndexDO::getFieldCode, field.getFieldCode())
            );
        }
        // 存储翻译配置
        if(ObjUtilX.isNotEmpty(fieldConfList)){
            ModelFieldTransReqVO req = new ModelFieldTransReqVO();
            req.setDataSourceConfigId(modelTableDO.getDataSourceConfigId());
            req.setProjectId(modelTableDO.getProjectId());
            req.setFields(fieldConfList);
            confService.modelFieldTransSave(req);
        }
        // 新增索引，修改索引，删除索引
        //查询索引集合
        List<ModelTableIndexDO> existingIndexes = tableIndexMapper.selectList(LambdaQueryWrapperX.<ModelTableIndexDO>lambdaQueryX()
                .eq(ModelTableIndexDO::getTableId, tableId));
        List<ModelTableIndexAditReqVO> newIndexes = (List<ModelTableIndexAditReqVO>) tableStruct.getOrDefault("modelIndexs", new ArrayList<>());
        if(ObjUtilX.isNotEmpty(newIndexes)) {
            // 根据 modelTableDetailNew 返回的json 进行逻辑处理，目前一个索引上有2个字段的话，数据库存储的是两条记录，需要合并成一个索引处理

            // 将现有索引信息转换为 Map 以便快速查找
            Map<String, ModelTableIndexDO> exsitIdxNameMap = existingIndexes.stream()
                    .collect(Collectors.toMap(ModelTableIndexDO::getIdxName, Function.identity(), (existing, newData) -> newData));

            // 索引字段分组
            Map<String, List<ModelFieldAditReqVO>> oldIndexMap = existingIndexes.stream()
                    .collect(Collectors.groupingBy(ModelTableIndexDO::getIdxName,
                            Collectors.mapping(index -> {
                                ModelFieldAditReqVO fieldAditReqVO = new ModelFieldAditReqVO();
                                fieldAditReqVO.setFieldCode(index.getFieldCode()); // 将 fieldCode 从 ModelTableIndexDO 转移到 ModelFieldAditReqVO
                                // 如果 ModelFieldAditReqVO 还有其他属性，也可以在这里设置
                                return fieldAditReqVO;
                            }, Collectors.toList())));

            // 存在的索引 id,name 映射
            //Map<Long, String> exsitIdxIdMap = existingIndexes.stream().collect(Collectors.toMap(ModelTableIndexDO::getIdxId
            //        , ModelTableIndexDO::getIdxName, (oldData, newData) -> newData));

            Map<Long, ModelTableIndexDO> existingIndexIdMap = existingIndexes.stream()
                    .collect(Collectors.toMap(ModelTableIndexDO::getIdxId, Function.identity(), (oldData, newData) -> newData));

            // 删除索引
            List<ModelTableIndexDO> indexesToDelete = existingIndexes.stream()
                    .filter(existingIndex -> newIndexes.stream()
                            .noneMatch(newIndex -> newIndex.getIdxName().equals(existingIndex.getIdxName())))
                    .toList();


            Map<Long, String> record = new HashMap<>();
            for (ModelTableIndexDO indexDel : indexesToDelete) {
                if(record.containsKey(indexDel.getIdxId())){
                    continue;
                }
                tableIndexMapper.deleteById(indexDel.getIdxId()); // 删除索引
                if (modelTableDO.getIsGen() == 1) {
                    creator.delIndex(modelTableDO, indexDel.getIdxName()); // 如果需要生成，则调用删除索引的方法
                }
                //如果存在索引使用了字段，也要同步删除
                tableIndexMapper.delete(LambdaQueryWrapperX.<ModelTableIndexDO>lambdaQueryX().eq(ModelTableIndexDO::getIdxId, indexDel.getIdxId()));
                record.put(indexDel.getIdxId(), indexDel.getIdxName());
            }

            // 新增索引
            List<ModelTableIndexAditReqVO> indexesToAdd = newIndexes.stream()
                    .filter(newIndex -> !exsitIdxNameMap.containsKey(newIndex.getIdxName()))
                    .collect(Collectors.toList());


            //现有字段集合
            existingFields = fieldMapper.selectList(LambdaQueryWrapperX.<ModelFieldDO>lambdaQueryX()
                    .eq(ModelFieldDO::getTableId, tableId));

            //List<ModelFieldDO> fields = fieldMapper.selectList(
            //        LambdaQueryWrapperX.<ModelFieldDO>lambdaQueryX().eq(ModelFieldDO::getTableId, tableId)
            //);
            Map<String, ModelFieldDO> exsitFieldCodeMap = existingFields.stream().collect(Collectors.toMap(ModelFieldDO::getFieldCode, a -> a));

            if (modelTableDO.getIsGen() == 1) {
                creator.addIndex(indexesToAdd, exsitIdxNameMap, exsitFieldCodeMap, modelTableDO);
            }

            // 修改索引 增加字段判断
            List<ModelTableIndexAditReqVO> indexesToUpdate = newIndexes.stream()
                    .filter(updIdx ->
                            !creator.fieldListsAreEqual(oldIndexMap.get(existingIndexIdMap.get(updIdx.getIdxId()).getIdxName()), updIdx.getFields()) ||
                            exsitIdxNameMap.containsKey(updIdx.getIdxName())
                            && (!updIdx.getIdxType().equals(exsitIdxNameMap.get(updIdx.getIdxName()).getIdxType())
                            || !updIdx.getIdxWay().equals(exsitIdxNameMap.get(updIdx.getIdxName()).getIdxWay())
                            || !updIdx.getRemark().equals(exsitIdxNameMap.get(updIdx.getIdxName()).getRemark())))
                    .toList();

            for (ModelTableIndexAditReqVO updIndex : indexesToUpdate) {
                ModelTableIndexDO existingIndex = exsitIdxNameMap.get(updIndex.getIdxName());
                String oldIdxName = existingIndexIdMap.get(updIndex.getIdxId()).getIdxName();
                if (modelTableDO.getIsGen() == 1) {
                    //todo  如果需要生成，则调用修改索引的方法
                    List<ModelFieldAditReqVO> oldFields = oldIndexMap.get(existingIndex.getIdxName());
                    ModelTableIndexAditReqVO oldIndex = BeanUtilX.copy(exsitIdxNameMap.get(oldIdxName), ModelTableIndexAditReqVO::new);
                    oldIndex.setFields(oldFields);
                    creator.modifyIndex(modelTableDO, oldIndex, updIndex);// 支持修改其他属性
                }
                //删除索引相关
                tableIndexMapper.delete(LambdaQueryWrapperX.<ModelTableIndexDO>lambdaQueryX()
                        .eq(ModelTableIndexDO::getTableId, modelTableDO.getTableId())
                        .eq(ModelTableIndexDO::getIdxName, oldIdxName)
                );
                //重新添加索引，但是不执行SQL
                modelTableDO.setNeedExe(false);
                creator.addIndex(indexesToUpdate, exsitIdxNameMap, exsitFieldCodeMap, modelTableDO);
            }
        }


        return tableId;
    }

    private Map<String, Object> parseTableAndFieldsFromReqVO(ModelTableJsonReqVO reqVO, ModelTableDO modelTable) throws JsonProcessingException {
        Long tableId = modelTable.getTableId();
        // 创建一个Map，用于存储解析后的结果
        Map<String, Object> resultMap = new HashMap<>();

        // 解析 ModelTableDO
        //ModelTableDO modelTable = new ModelTableDO();
        // 设置表ID
        //modelTable.setTableId(tableId);
        // 设置表名
        modelTable.setTableName(reqVO.getItemName());
        // 设置备注
        modelTable.setRemark(reqVO.getCompositeName());

        // 设置是否生成
        //modelTable.setIsGen(0);
        // 设置升级标志
        //modelTable.setUpgradeFlag(0);
        // 设置目录类型
        //modelTable.setDirType(1);
        // 设置属性类型
        //modelTable.setPropType(1);
        // 设置父ID
        modelTable.setParentId(null);

        // 获取 TableCode
        if (reqVO.getChildren() != null && reqVO.getChildren().size() > 0) {
            // 获取第二级子节点
            ModelTableJsonReqVO.Child level2Child1 = reqVO.getChildren().get(0);
            // 获取第三级子节点
            List<ModelTableJsonReqVO.Child> level3Children = level2Child1.getChildren();
            if (level3Children != null && level3Children.size() > 0) {
                // 获取第三级子节点中的第一个节点
                ModelTableJsonReqVO.Child level3Child1 = level3Children.get(0);
                // 设置 TableCode
                if(modelTable.getIsGen() == 0){
                    modelTable.setTableCode(level3Child1.getItemName());
                }
            }
        }

        // 将解析后的 ModelTableDO 放入结果Map中
        resultMap.put("modelTable", modelTable);
        ObjectMapper objectMapper = new ObjectMapper();
        // 解析 List<ModelFieldAditReqVO>
        List<ModelFieldAditReqVO> modelFieldDOList = new ArrayList<>();
        if (reqVO.getChildren() != null && reqVO.getChildren().size() > 1) {
            // 获取第二级子节点中的第二个节点
            ModelTableJsonReqVO.Child level2Child2 = reqVO.getChildren().get(1);
            if(ObjUtilX.isEmpty(level2Child2)){
                throw new BizException("5001", "字段信息缺失，请补充");
            }
            Integer sortIndex = 0;
            // 遍历第三级子节点
            for (ModelTableJsonReqVO.Child level3Child : level2Child2.getChildren()) {
                if(ObjUtilX.isEmpty(level3Child.getItemName())
                        || ObjUtilX.isEmpty(level3Child.getFieldCode())){
                    throw new BizException("5001","字段中文名或英文名缺失，请补充! ");
                }
                if(ObjUtilX.isEmpty(level3Child.getFieldType())){
                    throw new BizException("5001","字段类型缺失，请补充! ");
                }
                // 创建一个 ModelFieldAditReqVO 对象
                ModelFieldAditReqVO modelFieldDO = new ModelFieldAditReqVO();
                modelFieldDO.setSort(sortIndex++);
                //modelFieldDO.setSort(level3Child.getIndex());//这里前端更新不了，后台重新覆盖好了
                // 设置表ID
                modelFieldDO.setTableId(tableId);
                // 设置字段代码
                if(ObjUtilX.isNotEmpty(level3Child.getItemId())) {
                    modelFieldDO.setFieldId(Long.parseLong(level3Child.getItemId()));
                }
                modelFieldDO.setFieldCode(level3Child.getFieldCode());
                // 设置字段名
                modelFieldDO.setFieldName(level3Child.getItemName());
                // 设置备注
                modelFieldDO.setRemark(level3Child.getRemark());
                //modelFieldDO.setIsNullable(level3Child.getRequired() ? 1 : 0);
                modelFieldDO.setIsNullable(level3Child.getRequired());
                // 设置默认值
                modelFieldDO.setDefaultVal(level3Child.getDefaultVal());
                // 设置是否为主键
                //if(null != level3Child.getIsPrimaryKey() && level3Child.getIsPrimaryKey()){
                if(1 == level3Child.getIsPrimaryKey()){
                    modelFieldDO.setIsPrimaryKey(1);
                    modelFieldDO.setIsNullable(1);
                    modelFieldDO.setSort(0);
                } else {
                    modelFieldDO.setIsPrimaryKey(0);
                }
                // 设置字段类型
                String fieldType = level3Child.getFieldType().toUpperCase();
                modelFieldDO.setFieldType(fieldType);
                if(ObjUtilX.isNotEmpty(level3Child.getFieldTypeName())) {
                    modelFieldDO.setFieldTypeName(level3Child.getFieldTypeName());
                }else{
                    modelFieldDO.setFieldTypeName(fieldType);
                }
                // 设置字段长度
                //if (null != level3Child.getHasLength() && level3Child.getHasLength()) {
                    modelFieldDO.setLeng(level3Child.getDataLength());
                //}
                // 设置字段精度
                //if (null != level3Child.getHasPoint() && level3Child.getHasPoint()) {
                    modelFieldDO.setFieldPrecision(level3Child.getDataPoint());
                //}
                List<ModelTableJsonReqVO.Child> level4Children = level3Child.getChildren();
                if (ObjUtilX.isNotEmpty(level4Children)) {
                // 获取第四级子节点
                    // 创建一个 List，用于存储 JSON 字段
                    List<JSONObject> jsonFields = new ArrayList<>();
                    // 遍历第四级子节点
                    for (ModelTableJsonReqVO.Child level4Child : level4Children) {
                        // 创建一个 JSON 对象
                        //JSONObject jsonField = new JSONObject();
                        // 设置字段名
                        //jsonField.put("fieldName", level4Child.getItemName());
                        //jsonField.put("dataType", level4Child.getDataType());
                        //// 设置备注
                        //jsonField.put("remark", level4Child.getRemark());
                        //// 设置字段代码
                        //jsonField.put("fieldCode", level4Child.getEnglishName());
                        // 将 JSON 对象添加到 List 中

                        //jsonFields.add(jsonField);
                        // 将 level4Child 转换为 JSON 字符串
                        String jsonString = objectMapper.writeValueAsString(level4Child);

                        // 将 JSON 字符串解析为 JSONObject
                        JSONObject jsonField = JSONObject.parseObject(jsonString);
                        jsonFields.add(jsonField);
                    }
                    // 将 List 设置到 ModelFieldDO 中
                    modelFieldDO.setJsonFields(jsonFields);
                    modelFieldDO.setJsonType(level3Child.getJsonType());
                }
                // 将 ModelFieldDO 添加到 List 中
                modelFieldDOList.add(modelFieldDO);
            }
        }else{
            throw new BizException("5001", "字段信息缺失，请补充");
        }
        // 将解析后的 List<ModelFieldDO> 放入结果Map中
        resultMap.put("modelFields", modelFieldDOList);

        // 解析索引到
        List<ModelTableIndexAditReqVO> modelIndexDOList = new ArrayList<>();
        if (reqVO.getChildren() != null && reqVO.getChildren().size() > 2) {
            // 获取第二级子节点中的第三个节点
            ModelTableJsonReqVO.Child level2Child3 = reqVO.getChildren().get(2);
            if(ObjUtilX.isNotEmpty(level2Child3)){
                // 遍历第三级子节点
                for (ModelTableJsonReqVO.Child level3Child : level2Child3.getChildren()) {
                    if(ObjUtilX.isEmpty(level3Child.getItemName())){
                        throw new BizException("5001","索引名称缺失，请补充! ");
                    }
                    // 创建一个 ModelTableIndexAditReqVO 对象
                    ModelTableIndexAditReqVO modelIndexDO = new ModelTableIndexAditReqVO();
                    modelIndexDO.setIdxId(Long.parseLong(level3Child.getIdxId()));
                    modelIndexDO.setIdxName(level3Child.getItemName());
                    modelIndexDO.setRemark(ObjUtilX.isNotEmpty(level3Child.getRemark()) ? level3Child.getRemark() : "");
                    modelIndexDO.setIdxType(level3Child.getIdxType());
                    modelIndexDO.setIdxWay(level3Child.getIdxWay());
                    modelIndexDO.setTableId(tableId);
                    // 获取第四级子节点
                    List<ModelTableJsonReqVO.Child> level4Children = level3Child.getChildren();
                    if (ObjUtilX.isNotEmpty(level4Children)) {
                        // 创建一个 List，用于存储字段信息
                        List<ModelFieldAditReqVO> indexFields = new ArrayList<>();
                        // 遍历第四级子节点
                        for (ModelTableJsonReqVO.Child level4Child : level4Children) {
                            if(ObjUtilX.isEmpty(level4Child.getItemName())
                                    //|| ObjUtilX.isEmpty(level4Child.getEnglishName())){
                                    || ObjUtilX.isEmpty(level4Child.getFieldCode())){
                                throw new BizException("5001","索引字段中文名或英文名缺失，请补充! ");
                            }
                            // 创建一个 ModelFieldDO 对象
                            ModelFieldAditReqVO indexField = new ModelFieldAditReqVO();
                            indexField.setFieldName(level4Child.getItemName());
                            indexField.setFieldCode(level4Child.getFieldCode());
                            //indexField.setIsPrimaryKey(level4Child.getIsPrimaryKey() ? 1 : 0);
                            indexField.setIsPrimaryKey(level4Child.getIsPrimaryKey());
                            indexField.setFieldType(level4Child.getFieldType());
                            indexField.setFieldTypeName(level4Child.getFieldTypeName());
                            indexField.setLeng(level4Child.getDataLength());
                            indexField.setFieldPrecision(level4Child.getDataPoint());
                            // 将 ModelFieldDO 添加到 List 中
                            indexFields.add(indexField);
                        }
                        modelIndexDO.setFields(indexFields);
                    }
                    // 将 ModelTableIndexAditReqVO 添加到 List 中
                    modelIndexDOList.add(modelIndexDO);
                }
            }
        }
        // 将解析后的 List<ModelTableIndexAditReqVO> 放入结果Map中
        resultMap.put("modelIndexs", modelIndexDOList);

        // 返回结果Map
        return resultMap;
    }



    @Override
    public ModelTableListifyResVO modelTableDetailNew(Long tableId) {
        ModelTableListifyResVO res = new ModelTableListifyResVO();
        // 查询模型表信息
        ModelTableDO modelTableDO = tableMapper.selectById(tableId);

        if (modelTableDO == null) {
            throw new BizException("5001", "未找到对应的模型表");
        }

        // 查询对应的字段信息
        List<ModelFieldDO> fieldList = fieldMapper.selectList(LambdaQueryWrapperX.<ModelFieldDO>lambdaQueryX()
                .eq(ModelFieldDO::getTableId, tableId).orderByAsc(ModelFieldDO::getSort));

        // 反向封装到 ModelTableJsonReqVO
        ModelTableJsonReqVO reqVO = new ModelTableJsonReqVO();
        reqVO.setMindType("table");
        reqVO.setLevel(1);
        reqVO.setItemName(modelTableDO.getTableName());

        // 创建 children 列表
        List<ModelTableJsonReqVO.Child> children = new ArrayList<>();

        // Level 2 第一个元素 - 表信息
        ModelTableJsonReqVO.Child tableRoot = new ModelTableJsonReqVO.Child();
        tableRoot.setEnglishName("table_name"); // 设置 English name
        tableRoot.setLevel(2);
        tableRoot.setItemName("表名");
        tableRoot.setCompositeName("表名tableName");
        tableRoot.setReadonly(true);
        tableRoot.setChildren(new ArrayList<>()); // 子项初始化

        // Level 3 子项 (添加示例项)
        ModelTableJsonReqVO.Child tableDetailChild = new ModelTableJsonReqVO.Child();
//        level3Child.setEnglishName(modelTableDO.getTableCode());
        tableDetailChild.setItemName(modelTableDO.getTableCode());
        tableDetailChild.setRemark(modelTableDO.getRemark());
        tableDetailChild.setLevel(3); // 设置 Level 3
        tableRoot.getChildren().add(tableDetailChild); // 添加 表明细 子项

        children.add(tableRoot); // 将表信息子项添加到 children

        // Level 2 第二个元素 - 字段信息
        ModelTableJsonReqVO.Child fieldRoot = new ModelTableJsonReqVO.Child();
        fieldRoot.setEnglishName("table_fields");
        fieldRoot.setLevel(2);
        fieldRoot.setItemName("表字段");
        fieldRoot.setCompositeName("表字段tableFields");
        fieldRoot.setReadonly(true);

        List<ModelTableJsonReqVO.Child> fieldsChildren = new ArrayList<>();

        // 处理所有字段信息
        for (ModelFieldDO field : fieldList) {
            ModelTableJsonReqVO.Child fieldJson = new ModelTableJsonReqVO.Child();
            fieldJson.setEnglishName(StringUtils.strBarToHump(field.getFieldCode()));
            fieldJson.setFieldCode(field.getFieldCode());
            fieldJson.setItemName(field.getFieldName());
            fieldJson.setDefaultVal(field.getDefaultVal());
            fieldJson.setItemId(field.getFieldId().toString());
            //level3FieldChild.setCompositeName(field.getRemark() != null ? field.getRemark() : "");
            fieldJson.setRemark(field.getRemark() != null ? field.getRemark() : "");
            fieldJson.setFieldType(field.getFieldType());
            //fieldJson.setDateType(field.getDateType());
            fieldJson.setFieldTypeName(field.getFieldTypeName());
            //fieldJson.setIsPrimaryKey(field.getIsPrimaryKey() == 1); // 主键标记
            //fieldJson.setRequired(field.getIsNullable() == 1); // 必填标记
            fieldJson.setIsPrimaryKey(field.getIsPrimaryKey()); // 主键标记
            fieldJson.setRequired(field.getIsNullable()); // 必填标记
            fieldJson.setLevel(3); // 设置 Level 3
            fieldJson.setIndex(field.getSort());

            // 填充字段长度和精度
            if (field.getLeng() != null) {
                fieldJson.setDataLength(field.getLeng());
            }
            if (field.getFieldPrecision() != null) {
                fieldJson.setDataPoint(field.getFieldPrecision());
            }

            fieldJson.setJsonType(field.getJsonType()); // JSON类型
            // 处理 jsonFields 递归
            List<JSONObject> jsonFields = field.getJsonFields();
            if (jsonFields != null && !jsonFields.isEmpty()) {
                fieldJson.setChildren(processJsonFields(jsonFields, 4)); // 调用递归方法
            }

            // 添加 Level 3 子项
            fieldsChildren.add(fieldJson);
        }

        fieldRoot.setChildren(fieldsChildren); // 设置 Level 3 子项
        children.add(fieldRoot); // 将字段信息子项添加到 children

        // 索引
        ModelTableJsonReqVO.Child indexRoot = new ModelTableJsonReqVO.Child();
        indexRoot.setEnglishName("table_indexs");
        indexRoot.setLevel(2);
        indexRoot.setItemName("表索引");
        indexRoot.setCompositeName("表索引tableIndexs");
        indexRoot.setReadonly(true);

        List<ModelTableJsonReqVO.Child> indexsChildren = new ArrayList<>();

        //查询索引集合
        List<ModelTableIndexDO> indexDOS = tableIndexMapper.selectList(LambdaQueryWrapperX.<ModelTableIndexDO>lambdaQueryX()
                .eq(ModelTableIndexDO::getTableId, tableId));

        if(ObjUtilX.isNotEmpty(indexDOS)) {
            //现有字段集合
            Map<Long, ModelFieldDO> exsitFieldIdMap = fieldList.stream().collect(Collectors.toMap(ModelFieldDO::getFieldId, a -> a));
            //Map<String, ModelFieldDO> exsitFieldCodeMap = fieldList.stream().collect(Collectors.toMap(ModelFieldDO::getFieldCode, a -> a));

            Map<String, List<ModelTableIndexDO>> indexMap = indexDOS.stream().collect(Collectors.groupingBy(s -> s.getIdxName()));
            indexMap.forEach((key, value) -> {
                ModelTableJsonReqVO.Child indexChild = new ModelTableJsonReqVO.Child();
                indexChild.setItemName(key);
                ModelTableIndexDO idx = value.get(0);
                indexChild.setRemark(idx.getRemark());
                indexChild.setIdxId(idx.getIdxId().toString());
                indexChild.setIdxType(idx.getIdxType());
                indexChild.setIdxWay(idx.getIdxWay());
                List<ModelTableJsonReqVO.Child> indexFieldsChild = new ArrayList<>();
                value.forEach(item -> {
                    ModelFieldDO field = exsitFieldIdMap.get(item.getFieldId());
                    ModelTableJsonReqVO.Child idxField = new ModelTableJsonReqVO.Child();
                    //idxField.setItemId(item.getFieldId());
                    idxField.setItemName(field.getFieldName());
                    idxField.setEnglishName(StringUtils.strBarToHump(field.getFieldCode()));
                    idxField.setFieldCode(field.getFieldCode());
                    idxField.setFieldType(field.getFieldType());
                    idxField.setFieldTypeName(field.getFieldTypeName());
                    idxField.setDataLength(field.getLeng());
                    idxField.setDataPoint(field.getFieldPrecision());
                    //idxField.setFieldName(field.getFieldName());
                    indexFieldsChild.add(idxField);
                });
                indexChild.setChildren(indexFieldsChild);
                // 添加 Level 3 子项
                indexsChildren.add(indexChild);
            });
            indexRoot.setChildren(indexsChildren); // 设置 Level 3 子项
        }

        children.add(indexRoot); // 将索引信息子项添加到 children
        reqVO.setChildren(children); // 设置 children 到请求对象

        List<ModelTableJsonReqVO> items = new ArrayList<>(1);
        items.add(reqVO);
        res.setItemContent(JSONArray.toJSONString(items));
        res.setTaskId(tableId);
        res.setTableId(tableId);
        return res;
    }

    @Override
    public Integer modelTableLock(ModelTablePrimaryReqVO reqVO) {
        // 校验存在
        ModelTableDO exists = this.modelTableValidateExists(reqVO.getTableId());
        if(exists.getPropType() == 0){
            throw new BizException("5001", "系统内置的不可操作");
        }
        // 更新
        exists.setIsLock(reqVO.getIsLock());
        return tableMapper.updateById(exists);
    }

    // 递归处理 JSON 字段，生成树形结构
    private List<ModelTableJsonReqVO.Child> processJsonFields(List<JSONObject> jsonFields, int level) {
        List<ModelTableJsonReqVO.Child> children = new ArrayList<>();
        for (JSONObject jsonField : jsonFields) {
            ModelTableJsonReqVO.Child jsonFieldChild = new ModelTableJsonReqVO.Child();
            String fiedlCode = jsonField.getString("fieldCode");
            jsonFieldChild.setFieldCode(fiedlCode);
            jsonFieldChild.setDataType(jsonField.getString("dataType"));
            //jsonFieldChild.setDataLength(jsonField.getInteger("dataLength"));
            //jsonFieldChild.setDataPoint(jsonField.getInteger("dataPoint"));
            jsonFieldChild.setIndex(jsonField.getInteger("index"));
            jsonFieldChild.setTouched(jsonField.getBoolean("touched"));
            //jsonFieldChild.setEnglishName(StringUtils.strBarToHump(fiedlCode));
            jsonFieldChild.setCompositeName(jsonField.getString("compositeName"));
            jsonFieldChild.setEnglishName(jsonField.getString("englishName"));
            jsonFieldChild.setItemName(jsonField.getString("itemName"));
            jsonFieldChild.setFieldType(jsonField.getString("fieldType"));
            jsonFieldChild.setFieldTypeName(jsonField.getString("fieldTypeName"));
            jsonFieldChild.setCompositeName(jsonField.getString("remark"));
            jsonFieldChild.setSn(jsonField.getString("sn"));
            jsonFieldChild.setLevel(level); // 设置当前层级

            // 如果有子字段，递归处理
            if (jsonField.containsKey("children")) {
                List<JSONObject> childJsonFields = jsonField.getJSONArray("children").toJavaList(JSONObject.class);
                jsonFieldChild.setChildren(processJsonFields(childJsonFields, level + 1)); // 递归增加层级
            }

            children.add(jsonFieldChild); // 添加到 children 列表
        }
        return children;
    }
    //private List<ModelTableJsonReqVO.Child> processJsonFields(List<JSONObject> jsonFields, int level) {
    //    List<ModelTableJsonReqVO.Child> children = new ArrayList<>();
    //    ObjectMapper objectMapper = new ObjectMapper(); // 创建 ObjectMapper 实例
    //
    //    for (JSONObject jsonField : jsonFields) {
    //        try {
    //            ModelTableJsonReqVO.Child jsonFieldChild = objectMapper.readValue(
    //                    jsonField.toJSONString(), ModelTableJsonReqVO.Child.class);
    //
    //            // 设置当前层级
    //            jsonFieldChild.setLevel(level);
    //
    //            // 如果有子字段，递归处理
    //            if (jsonField.containsKey("children")) {
    //                JSONArray childJsonFields = jsonField.getJSONArray("children");
    //                List<JSONObject> childJsonList = childJsonFields.toJavaList(JSONObject.class);
    //                jsonFieldChild.setChildren(processJsonFields(childJsonList, level + 1)); // 递归增加层级
    //            }
    //
    //            children.add(jsonFieldChild); // 添加到 children 列表
    //        } catch (Exception e) {
    //            e.printStackTrace(); // 处理异常
    //        }
    //    }
    //
    //    return children;
    //}

    //添加模型字段  这个逻辑不会触发了
    private void addFields(ModelTableAditReqVO reqVO, Long tableId) {
        if(reqVO.getDirType() == 1 && ObjUtilX.isNotEmpty(reqVO.getFields())) {
            //字段验重
            Map<String, Integer> codeMap = new HashMap<>(reqVO.getFields().size());
//            List<ModelFieldJsonDO> jsonDOS = new ArrayList<>();
            boolean havePrimary = false;
            List<ModelFieldDO> fields = new ArrayList<>();
            List<ModelFieldConfAditReqVO> fieldConfList = new ArrayList<>();
            for (ModelFieldAditReqVO modelFieldAditReqVO : reqVO.getFields()) {
                if(ObjUtilX.isEmpty(modelFieldAditReqVO.getFieldName())
                        || ObjUtilX.isEmpty(modelFieldAditReqVO.getFieldCode())){
                    continue;
                }
                if (codeMap.containsKey(modelFieldAditReqVO.getFieldCode())) {
                    throw new BizException("5001", "存在重复字段：" + modelFieldAditReqVO.getFieldCode());
                }
                if (modelFieldAditReqVO.getIsPrimaryKey() == 1) {
                    havePrimary = true;
                }
                codeMap.put(modelFieldAditReqVO.getFieldCode(), 1);
                ModelFieldDO fieldDO = BeanUtilX.copy(modelFieldAditReqVO, ModelFieldDO::new);
                //if (fieldDO.getFieldType().equalsIgnoreCase("JSONB")
                //        && ObjUtilX.isEmpty(fieldDO.getJsonFields())) {
                //    throw new BizException("5001", "JSON类型的字段必须指定子字段配置");
                //}

                ModelFieldConfAditReqVO tempConf = new ModelFieldConfAditReqVO();
                tempConf.setFieldCode(modelFieldAditReqVO.getFieldCode());
                tempConf.setFieldName(modelFieldAditReqVO.getFieldName());
                tempConf.setEnglishName(StringUtils.strBarToHump(modelFieldAditReqVO.getFieldCode()));
                tempConf.setItemName(modelFieldAditReqVO.getFieldName());
                String fieldType = modelFieldAditReqVO.getFieldType();
                String fieldTypeName = modelFieldAditReqVO.getFieldType();
                if (fieldType.equalsIgnoreCase("ENUM")) {
                    fieldType = "INT2";
                    // AI 有时候翻译不过来。
                    fieldDO.setFieldType(fieldType);
                    fieldDO.setFieldTypeName(fieldType);
                }else if (fieldType.equalsIgnoreCase("DATETIME")) {
                    fieldType = "TIMESTAMP";
                    fieldDO.setFieldType(fieldType);
                    fieldDO.setFieldTypeName(fieldType);
                    // 日期类型最大为6
                    //if (modelFieldAditReqVO.getLeng() > 6){
                    //    modelFieldAditReqVO.setLeng(6);
                    //}
                }
                tempConf.setFieldType(fieldType);
                String dateType = "";
                if(fieldTypeName.equals("JSON数组")){
                    dateType = DataTypeEnum.ARRAY.getValue();
                }else if(fieldType.contains("BOOL")){
                    dateType = DataTypeEnum.BOOL.getValue();
                }else if(fieldType.contains("DECIMAL")
                        || fieldType.contains("NUMBER")
                        || fieldType.contains("INT")
                        || fieldType.contains("FLOAT")
                        || fieldType.contains("DOUBLE")
                ){
                    dateType = DataTypeEnum.NUMBER.getValue();
                }else if(fieldType.contains("JSONB")){
                    dateType = DataTypeEnum.JSONOBJECT.getValue();
                } else {
                    dateType = DataTypeEnum.STRING.getValue();
                }
                tempConf.setDataType(dateType);
                //tempConf.setFieldTypeName(ObjUtilX.isEmpty(fieldTypeName) ? fieldType : fieldTypeName);
                tempConf.setFieldLength(modelFieldAditReqVO.getLeng());
                tempConf.setLeng(modelFieldAditReqVO.getLeng());
                tempConf.setFieldPrecision(modelFieldAditReqVO.getFieldPrecision());
                fieldConfList.add(tempConf);

                Long fieldId = IdWorker.getId();
                fieldDO.setFieldId(fieldId);
                fieldDO.setTableId(tableId);
                fieldDO.setPropType(1);//防止篡改，设为用户属性类型
//                        if(field.getFieldType().equalsIgnoreCase("JSONB")
//                                ||field.getFieldType().equalsIgnoreCase("JSON")) {
//                            field.getJsonFields().forEach(
//                                    jsonField -> {
//                                        ModelFieldJsonDO jsonDO = BeanUtilX.copy(jsonField, ModelFieldJsonDO::new);
//                                        jsonDO.setTableId(tableId);
//                                        jsonDO.setFieldId(fieldId);
//                                        jsonDO.setFieldCode(field.getFieldCode());
//                                        jsonDOS.add(jsonDO);
//                                    }
//                            );
//                        }
                fields.add(fieldDO);
            }
            //if(!havePrimary){
            //    throw new BizException("5001", "请添加一个主键");
            //}
            fieldMapper.insertBatch(fields);
            if(ObjUtilX.isNotEmpty(fieldConfList)){
                ModelFieldTransReqVO req = new ModelFieldTransReqVO();
                req.setDataSourceConfigId(reqVO.getDataSourceConfigId());
                req.setProjectId(reqVO.getProjectId());
                req.setFields(fieldConfList);
                confService.modelFieldTransSave(req);
            }
//            if(ObjUtilX.isNotEmpty(jsonDOS)) {
//                fieldJsonMapper.insertBatch(jsonDOS);
//            }
        }
    }

    public static boolean checkForDuplicateFieldCodes(List<ModelFieldAditReqVO> fields) {
        Set<String> fieldCodeSet = new HashSet<>();
        for (ModelFieldAditReqVO field : fields) {
            String fieldCode = field.getFieldCode();
            // 尝试将 fieldCode 添加到 Set 中
            if (!fieldCodeSet.add(fieldCode)) {
                // 如果返回 false，表示这个 fieldCode 已经存在，即重复
                return true;
            }
        }
        return false; // 没有找到重复
    }

    @Override
    public Integer modelTableDrag(ModelTableDragReqVO reqVO) {
        // 校验存在
        ModelTableDO exists = this.modelTableValidateExists(reqVO.getTableId());
        if(exists.getPropType() == 0){
            throw new BizException("5001", "系统内置的不可操作");
        }
        // 更新
        Long parentDSID = validParent(reqVO.getParentId(), exists.getDataSourceConfigId());
        // 不能跨数据源拖拽
        if(!exists.getDataSourceConfigId().equals(parentDSID)){
            throw new BizException("5001", "不能跨数据源拖拽！");
        }
        exists.setParentId(reqVO.getParentId());
        return tableMapper.updateById(exists);
    }

    // 校验父节点存在
    private Long validParent(Long parentId, Long dsId) {
        if(null == parentId || parentId == 0L){
            return dsId;
        }
        ModelTableDO parent = tableMapper.selectById(parentId);
        if (parent == null) {
            throw new BizException("5001","父节点不存在");
        }
        if (parent.getDirType() != 0) {
            throw new BizException("5001","父节点必须是目录");
        }
        //子节点也用父节点的数据源配置
        return parent.getDataSourceConfigId();
    }

    public String getFullPath(Long tableId) {
        StringBuilder pathBuilder = new StringBuilder();
        buildPath(pathBuilder, tableId);
        return pathBuilder.toString();
    }

    private void buildPath(StringBuilder pathBuilder, Long tableId) {
        ModelTableDO currentNode = tableMapper.selectById(tableId); // 根据 ID 查询当前节点

        if (currentNode != null) {
            // 如果还有父节点，递归获取父节点路径
            if (currentNode.getParentId() != null && currentNode.getParentId() > 0) {
                buildPath(pathBuilder, currentNode.getParentId());
            }

            // 拼接当前节点的表名
            if (pathBuilder.length() > 0) {
                pathBuilder.append("|"); // 添加分隔符
            }
            pathBuilder.append(currentNode.getTableName());
        }
    }

    @Override
    public JSONObject modelTableDoc(Long tableId) {
        ModelTableRespVO resp =  modelTableDetail(tableId);
        if(ObjUtilX.isEmpty(resp.getFields())){
            throw new BizException("5001", "请先添加表字段保存，目前只有默认字段并未保存");
        }
        resp.setPrivateKey(privateKey);
        resp.setPath(getFullPath(tableId));
        JSONObject exeRst = null;
        try {
            genApiDoc(resp);
        } catch (JSONException e) {
            throw new BizException("5001", "文本解析错误");
        }
        return exeRst;
    }


    public void genApiDoc(ModelTableRespVO params) throws BizException{


        //Long dataSourceConfigId = params.getDataSourceConfigId();
        Long projectId = params.getProjectId();
        Long tableId = params.getTableId();


        List<ModelFieldRespVO> fieldList = params.getFields();
        if(fieldList != null && fieldList.size() != 0){
            // 获取api信息
            Map<String, Object> apiInfoMap = getApiInfoByTable(params);
            String tableName = params.getTableCode().toLowerCase();
            // 第一步，第二个 _ 前缀的前面，去掉第一个下划线的后面，作为 module 名字；第二步，moduleName 必须小写；
            String substring = tableName.substring(tableName.indexOf("_") + 1);
            String moduleName = subBefore(substring, '_', false).toLowerCase();

            //LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
            //Date now = new Date();
            // 循环生成5个接口

            String interfaceUrl = moduleName + "/" + apiInfoMap.get("interfaceUrl").toString();
            apiInfoMap.put("interfaceUrl", interfaceUrl);
            apiInfoMap.put("tableId", tableId);
            apiInfoMap.put("remark", params.getRemark());
            int max = 11;
            if(0L != params.getDataSourceConfigId()){//不是平台只生成前面4个接口
                max = 5;
            }
            for(int apiType = 0; apiType < max; apiType ++){
                interfaceUrl = apiInfoMap.get("interfaceUrl").toString();
                interfaceUrl = getUrl(apiType, interfaceUrl, tableId);

                ItemDO apiItem = itemService.getOne(new LambdaQueryWrapperX<ItemDO>()
                        .eq(ItemDO::getApiUrl, interfaceUrl)
                        .eq(ItemDO::getProjectId, projectId).last("limit 1"));
                if(ObjUtilX.isNotEmpty(apiItem)){
                    List<Map> docList = buildApiDoc(tableId, apiType, apiInfoMap);
                    apiItem.setContent(JSON.toJSONString(docList));
                    itemMapper.updateById(apiItem);
                }else{
                    newApiDoc(params, apiInfoMap, apiType);
                }
            }
        }
    }

    private static String getUrl(int apiType, String interfaceUrl, Long tableId) {
        switch (apiType) {
            case 0  -> interfaceUrl = interfaceUrl + ApiURLEnum.ADIT.getSuffix();
            case 1  -> interfaceUrl = interfaceUrl + ApiURLEnum.DELETE.getSuffix();
            case 2  -> interfaceUrl = interfaceUrl + ApiURLEnum.DETAIL.getSuffix();
            case 3  -> interfaceUrl = interfaceUrl + ApiURLEnum.LIST.getSuffix();
            case 4  -> interfaceUrl = interfaceUrl + ApiURLEnum.PAGE.getSuffix();
            case 5  -> interfaceUrl = ApiURLEnum.BASE_PREFIX.getSuffix() + "/" + tableId + "/" + ApiURLEnum.ADD.getSuffix();
            case 6  -> interfaceUrl = ApiURLEnum.BASE_PREFIX.getSuffix() + "/" + tableId + "/" + ApiURLEnum.UPDATE.getSuffix();
            case 7  -> interfaceUrl = ApiURLEnum.BASE_PREFIX.getSuffix() + "/" + tableId + "/" + ApiURLEnum.DELETE.getSuffix();
            case 8  -> interfaceUrl = ApiURLEnum.BASE_PREFIX.getSuffix() + "/" + tableId + "/" + ApiURLEnum.DETAIL.getSuffix();
            case 9  -> interfaceUrl = ApiURLEnum.BASE_PREFIX.getSuffix() + "/" + tableId + "/" + ApiURLEnum.LIST.getSuffix();
            case 10 -> interfaceUrl = ApiURLEnum.BASE_PREFIX.getSuffix() + "/" + tableId + "/" + ApiURLEnum.PAGE.getSuffix();
        }
        return interfaceUrl;
    }

    public Map<String, Object> getApiInfoByTable(ModelTableRespVO params) throws BizException {
        Map<String, Object> apiInfoMap = new HashMap<>();
        // 将表名转换为接口url
        String interfaceUrl = getInterfaceUrl(params.getTableCode());
        apiInfoMap.put("interfaceUrl", interfaceUrl);
        List<ModelFieldRespVO> modelFieldList =  params.getFields();
        List<Map> fieldListMap = new ArrayList<>();
        apiInfoMap.put("fieldList", fieldListMap);
        for(ModelFieldRespVO field : modelFieldList){
            String fieldCode = field.getFieldCode();
            // todo 排除字段后续做成可配置的，或者直接放在建模
            if("created_by".equals(fieldCode) || "created_dt".equals(fieldCode)
                    || "updated_by".equals(fieldCode) ||"updated_dt".equals(fieldCode)
                    || "tenant_id".equals(fieldCode)|| "created_id".equals(fieldCode)){
                continue;
            }

            Map<String, Object> map = new HashMap<>();
            map.put("itemId", field.getFieldId());
            map.put("itemName", field.getFieldName());
            map.put("fieldName", field.getFieldName());
            map.put("fieldCode", field.getFieldCode());
            map.put("englishName", StringUtils.strBarToHump(field.getFieldCode()));
            map.put("fieldType", field.getFieldType());
            if(ObjUtilX.isNotEmpty(field.getJsonFields())) {
                map.put("jsonFields", field.getJsonFields());
            }
            map.put("fieldTypeName", field.getFieldTypeName());
            map.put("isPrimaryKey", field.getIsPrimaryKey());
            map.put("remark", field.getRemark());
            //map.put("required", field.getIsNullable() == 1 ? true : false);
            map.put("required", field.getIsNullable());
            fieldListMap.add(map);
            List<JSONObject> jsonObjectList = field.getJsonFields();
            iteraJson(jsonObjectList, map);
        }
        return apiInfoMap;
    }

    private static String getInterfaceUrl(String tableCode) {
        int prefixIdx = tableCode.indexOf("_");
        if(prefixIdx != -1){
            tableCode = tableCode.substring(prefixIdx +1, tableCode.length());
        }
        String interfaceUrl = StringUtils.strBarToHump(tableCode);
        return interfaceUrl;
    }

    public void iteraJson(List<JSONObject> jsonObjectList, Map<String, Object> map){
        if(jsonObjectList !=null && jsonObjectList.size() != 0){
            List<Map> childrenListMap = new ArrayList<>();
            map.put("fieldList", childrenListMap);
            for(JSONObject jsonObject : jsonObjectList){
                Map<String, Object> childrenMap = new HashMap<>();
                if(jsonObject.containsKey("fieldName")){
                    childrenMap.put("itemName", jsonObject.get("fieldName"));
                }
                if(jsonObject.containsKey("itemName")){
                    childrenMap.put("itemName", jsonObject.get("itemName"));
                }
                childrenMap.put("fieldCode", jsonObject.get("fieldCode"));
                childrenMap.put("englishName", jsonObject.get("englishName"));
                childrenMap.put("fieldType", jsonObject.get("fieldType"));
                childrenMap.put("fieldTypeName", jsonObject.get("fieldTypeName"));
                if(jsonObject.containsKey("jsonFields")) {
                    map.put("jsonFields", jsonObject.get("jsonFields"));
                }
                childrenMap.put("remark", jsonObject.get("remark"));
                childrenMap.put("required", jsonObject.get("required"));
                childrenListMap.add(childrenMap);
                if(jsonObject.get("children") != null){
                    List<JSONObject> childrenObjectList = (List<JSONObject>) jsonObject.get("children");
                    iteraJson(childrenObjectList, childrenMap);
                }
            }
        }
    }

    private void newApiDoc(ModelTableRespVO params,
                           Map<String, Object> apiInfoMap, int apiType) throws BizException {
        Long tableId = params.getTableId();
        List<Map> docList = buildApiDoc(tableId, apiType, apiInfoMap);
        // 生成父目录树形结构
        Long parentId = genApiDocParentFile(params, 0L);
        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();

        Date curDate = new Date();

        CreateItemParams createItemParams = new CreateItemParams();
        createItemParams.setItemId(null);
        createItemParams.setParentItemId(parentId);
        createItemParams.setCategory(1);
        createItemParams.setProjectId(params.getProjectId());
        createItemParams.setItemType(1);
        createItemParams.setTaskSetType(0);
        createItemParams.setContent(JSON.toJSONString(docList));
        if(ObjUtilX.isNotEmpty(loginUser)) {
            createItemParams.setUserId(loginUser.getUserId());
            createItemParams.setCreateUser(loginUser.getUserName());
        }
        createItemParams.setCreateTime(curDate);

        String apiUrl = apiInfoMap.get("interfaceUrl").toString();
        // 加个排序
        createItemParams.setItemSeq(apiType);
        String apiUrlSuffix = "";
        String itemNameSuffix;
        switch (apiType) {
            case 0 -> {
                apiUrlSuffix = ApiURLEnum.ADIT.getSuffix();
                itemNameSuffix = "新增或编辑";
            }
            case 1 -> {
                apiUrlSuffix = ApiURLEnum.DELETE.getSuffix();
                itemNameSuffix = "删除";
            }
            case 2 -> {
                apiUrlSuffix = ApiURLEnum.DETAIL.getSuffix();
                itemNameSuffix = "详情";
            }
            case 3 -> {
                apiUrlSuffix = ApiURLEnum.LIST.getSuffix();
                itemNameSuffix = "列表（全部）";
            }
            case 4 -> {
                apiUrlSuffix = ApiURLEnum.PAGE.getSuffix();
                itemNameSuffix = "列表（分页）";
            }
            case 5 -> {
                apiUrl = ApiURLEnum.BASE_PREFIX.getSuffix() + "/" + tableId + "/" + ApiURLEnum.ADD.getSuffix();
                itemNameSuffix = "新增（平台）";
            }
            case 6 -> {
                apiUrl = ApiURLEnum.BASE_PREFIX.getSuffix() + "/" + tableId + "/" + ApiURLEnum.UPDATE.getSuffix();
                itemNameSuffix = "编辑（平台）";
            }
            case 7 -> {
                apiUrl = ApiURLEnum.BASE_PREFIX.getSuffix() + "/" + tableId + "/" + ApiURLEnum.DELETE.getSuffix();
                itemNameSuffix = "删除（平台）";
            }
            case 8 -> {
                apiUrl = ApiURLEnum.BASE_PREFIX.getSuffix() + "/" + tableId + "/" + ApiURLEnum.DETAIL.getSuffix();
                itemNameSuffix = "详情（平台）";
            }
            case 9 -> {
                apiUrl = ApiURLEnum.BASE_PREFIX.getSuffix() + "/" + tableId + "/" + ApiURLEnum.LIST.getSuffix();
                itemNameSuffix = "列表（全部）（平台）";
            }
            case 10 -> {
                apiUrl = ApiURLEnum.BASE_PREFIX.getSuffix() + "/" + tableId + "/" + ApiURLEnum.PAGE.getSuffix();
                itemNameSuffix = "列表（分页）（平台）";
            }
            default -> {
                apiUrlSuffix = "";
                itemNameSuffix = "";
            }
        }

        createItemParams.setItemName(params.getTableName() + itemNameSuffix);
        //非平台的添加前缀
        apiUrl = apiUrl + apiUrlSuffix;
        createItemParams.setApiUrl(apiUrl);
        itemService.insertItemInfo(createItemParams);
    }

    /**
     *  生成API文档父文件
     * @param parentFileId 父文件ID
     * @return
     */
    private Long genApiDocParentFile(ModelTableRespVO params, Long parentFileId) throws BizException {
        if(StrUtilX.isNotEmpty(params.getPath())){
            LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
            Date now = new Date();
            String [] parentFileNameList= params.getPath().split("\\|");
            for(int i=0; i< parentFileNameList.length; i++){
                String parentFileName = parentFileNameList[i];
                Integer itemType = 0;
                //if(i == parentFileNameList.length-1){
                //    itemType = 1;
                //}
                ItemDO parentItemDO = itemService.getOne(new LambdaQueryWrapperX<ItemDO>()
                        .eq(ItemDO::getParentItemId, parentFileId)
                        .eq(ItemDO::getCategory, 1)//接口文档
                        .eq(ItemDO::getProjectId, params.getProjectId())
                        .eq(ItemDO::getItemName, parentFileName)
                        .eq(ItemDO::getItemType, itemType)
                        .eq(ItemDO::getActive, 1).last("limit 1"));
                if(parentItemDO == null){
                    CreateItemParams createItemParams = new CreateItemParams();
                    createItemParams.setItemId(null);
                    createItemParams.setItemName(parentFileName);
                    createItemParams.setParentItemId(parentFileId);
                    createItemParams.setCategory(1);
                    createItemParams.setProjectId(params.getProjectId());
                    createItemParams.setItemType(itemType);
                    createItemParams.setTaskSetType(0);
                    createItemParams.setUserId(loginUser.getUserId());
                    createItemParams.setCreateUser(loginUser.getUserName());
                    createItemParams.setCreateTime(now);
                    itemService.insertItemInfo(createItemParams);
                    parentFileId = createItemParams.getItemId();
                }else{
                    parentFileId = parentItemDO.getItemId();
                }
            }
        }
        return parentFileId;
    }


    /**
     * 	 构建接口文档
     * @param apiType 0-新增或者编辑 1-查询 2-删除
     * @param apiInfoMap
     * @return
     * @throws BizException
     */
    public List<Map> buildApiDoc(Long tableId, Integer apiType, Map<String, Object> apiInfoMap) throws BizException{
        List<Map> docList = new ArrayList<>();
        Map oneDocMap = genMap("API", "", false, 1, 0, false, true,
                true, false,false, "0", true);
        oneDocMap.put("mindType", "api");
        oneDocMap.put("unexpandable", true);
        docList.add(oneDocMap);

        List<Map> twoDocList = new ArrayList();
        oneDocMap.put("children", twoDocList);

        //接口地址
        Map interfaceNameMap = genMap("接口地址", "", false, 2, 0, false, true,
                true, false,false, "0.1", true);
        twoDocList.add(interfaceNameMap);
        //接口URL
        List<Map> threeInterfaceUrlList = new ArrayList<>();
        interfaceNameMap.put("children", threeInterfaceUrlList);
        String interfaceUrl = apiInfoMap.get("interfaceUrl").toString();
        interfaceUrl = getUrl(apiType, interfaceUrl, tableId);
        Map threeInterfaceUrlMap = genMap(interfaceUrl, "", false, 3, 0, false, false,
                false, true,false, "0.0.0", false);
        threeInterfaceUrlMap.put("color", "dark");
        threeInterfaceUrlMap.put("remark", "post");//todo 后续按照 apiType 规则来获取
        threeInterfaceUrlList.add(threeInterfaceUrlMap);


        //请求参数
        Map requestParamsMap = genMap("请求参数", "params", false, 2, 0, false, true,
                true, true,false, "0.1", false);
        requestParamsMap.put("dataType", DataTypeEnum.OBJECT.getValue());
        //requestParamsMap.put("remark", apiInfoMap.getOrDefault("remark", "").toString());
        twoDocList.add(requestParamsMap);
        List<Map> fieldMapList = (List<Map>) apiInfoMap.get("fieldList");

        buildApiField(apiType, fieldMapList, requestParamsMap, 1);

        //响应参数
        Map responseParamsMap = genMap("响应参数", "response", false, 2, 0, false, true,
                true, true,true, "0.2", false);
        twoDocList.add(responseParamsMap);
        responseParamsMap.put("dataType", DataTypeEnum.OBJECT.getValue());
        // todo 如果 page 接口需要显示 list 这一层的话，就在这里多加一层
        if(apiType == 2 || apiType == 3 || apiType == 4 || apiType == 8 || apiType == 9 || apiType == 10){
            if(apiType == 3 || apiType == 4 || apiType == 9 || apiType == 10){
                responseParamsMap.put("dataType", DataTypeEnum.ARRAY.getValue());
            }else {
                responseParamsMap.put("dataType", DataTypeEnum.OBJECT.getValue());
            }
            buildApiField(apiType, fieldMapList, responseParamsMap, 0);
        }
        return docList;
    }

    /**
     *
     * @param apiType
     * @param fieldMapList
     * @param apiParamsMap
     * @param isRequest
     */
    private void buildApiField(Integer apiType, List<Map> fieldMapList, Map apiParamsMap, Integer isRequest) {
        if(fieldMapList !=null && fieldMapList.size() > 0){
            List<Map> threeFieldList = new ArrayList<>();
            apiParamsMap.put("children", threeFieldList);
            for(int i=0; i<fieldMapList.size(); i++){
                Map map = fieldMapList.get(i);
                if(!map.containsKey("itemName") || ObjUtilX.isEmpty(map.get("itemName"))){
                    continue;//这里暂不清楚什么原因
                }
                String itemName = map.get("itemName").toString();
                String englishName = map.get("englishName").toString();
                //String fieldCode = map.get("fieldCode").toString();

                if(!map.containsKey("fieldType")){
                    throw new BizException("5001",itemName+":字段类型不能为空");
                }
                String fieldType = map.get("fieldType").toString();
                String dateType = "";
                String fieldTypeName = map.get("fieldTypeName").toString();
                if(fieldTypeName.equals("JSON数组")){
                    dateType = DataTypeEnum.ARRAY.getValue();
                }else if(fieldType.contains("BOOL")){
                    dateType = DataTypeEnum.BOOL.getValue();
                }else if(fieldType.contains("DECIMAL")
                        || fieldType.contains("NUMBER")
                        || fieldType.contains("INT")
                        || fieldType.contains("FLOAT")
                        || fieldType.contains("DOUBLE")
                ){
                    dateType = DataTypeEnum.NUMBER.getValue();
                }else if(fieldType.contains("JSONB")){
                    dateType = DataTypeEnum.JSONOBJECT.getValue();
                } else {
                    dateType = DataTypeEnum.STRING.getValue();
                }
                String isPrimaryKey = map.get("isPrimaryKey") == null ? "0" : map.get("isPrimaryKey").toString();
                //// 非主键字段过滤
                //if(isRequest == 1 && (1 == apiType || 2 == apiType || 7 == apiType || 8 == apiType) && !"1".equals(isPrimaryKey)){
                //    continue;
                //}
                //// 主键字段过滤，或者JSONB过滤
                //if(isRequest == 1 && (3 == apiType || 4 == apiType || 9 == apiType || 10 == apiType)
                //        && ("1".equals(isPrimaryKey) || dateType == DataTypeEnum.JSONOBJECT.getValue())){// 列表，分页不要入参
                //    continue;
                //}
                //if(isRequest == 1 && (5 == apiType) && ("1".equals(isPrimaryKey))){
                //    continue;
                //}
                if (isRequest == 1) {
                    if ((1 == apiType || 2 == apiType || 7 == apiType || 8 == apiType) && !"1".equals(isPrimaryKey)) {
                        continue;
                    }
                    if ((3 == apiType || 4 == apiType || 9 == apiType || 10 == apiType) && ("1".equals(isPrimaryKey) || dateType == DataTypeEnum.JSONOBJECT.getValue())) {
                        continue;
                    }
                    if (5 == apiType && "1".equals(isPrimaryKey)) {
                        continue;
                    }
                }

                String sn = "0.1." + i;
                Map fieldMap = genMap(itemName, englishName, false, 3, i, false, false,
                        false, false,false, sn, false);

                //JSON 字段配置也添加进去
                if(dateType == DataTypeEnum.JSONOBJECT.getValue() && map.containsKey("jsonFields")){
                    List<Map> jsonFieldMap = (List<Map>) map.get("jsonFields");
                    buildApiField(apiType, jsonFieldMap, fieldMap, isRequest);
                }

                fieldMap.put("dataType", dateType);
                fieldMap.put("color", "dark");
                Object remark1 = map.getOrDefault("remark", "");
                String remark = ObjUtilX.isNotEmpty(remark1) ? remark1.toString() : "";
                fieldMap.put("remark", remark);
                fieldMap.put("required", false);
                if(isRequest == 1 && (0 == apiType || 1 == apiType || 2 == apiType
                        || 5 == apiType || 6 == apiType || 7 == apiType || 8 == apiType)) {// 新增编辑时，请求参数必填
                    //Boolean required = map.containsKey("required") && null != map.get("required") ? (Boolean) map.get("required") : false;
                    //fieldMap.put("required", required);
                    //Object requiredValue = map.get("required");

                    Object requiredValue = map.get("required");
                    Boolean required = false; // 默认值

                    if (requiredValue instanceof Boolean) {
                        required = (Boolean) requiredValue; // 如果是 Boolean 类型
                    } else if (requiredValue instanceof Integer) {
                        required = switch ((Integer) requiredValue) { // 使用 switch 处理 Integer
                            case 1 -> true; // 如果是 1，返回 true
                            default -> false; // 否则返回 false
                        };
                    }

                    fieldMap.put("required", required);
                }
                threeFieldList.add(fieldMap);

                // 子节点
                Object children = map.get("fieldList");
                //if(children != null && 9 != apiType && 10 != apiType){
                if(children != null){
                    List<Map> listMap = (List<Map>) map.get("fieldList");
                    buildApiField(apiType, listMap, fieldMap, isRequest);
                }
            }
            //如果是 page 新增通用入参
            if(isRequest == 1 && (apiType == 4 || apiType == 10)){//isRequest == 1  请求参数
                String sn1 = "0.1." + 800;
                String sn2 = "0.1." + 900;
                genPageParam("当前页", "currentPage", 800, sn1, "Number", threeFieldList);
                genPageParam("每页条数", "pageSize", 900, sn2, "Number", threeFieldList);
            }
        }
    }

    private void genPageParam(String itemName, String englishName, int i, String sn, String dataType, List<Map> threeFieldList) {
        Map pageMap1 = genMap(itemName, englishName, false, 3, i, false, false,
                false, false,false, sn, false);
        pageMap1.put("dataType", dataType);
        pageMap1.put("color", "dark");
        pageMap1.put("required", true);
        threeFieldList.add(pageMap1);
    }

    /**
     *
     * @param itemName
     * @param englishName
     * @param touched
     * @param level
     * @param index
     * @param editing
     * @param expanded
     * @param readonly
     * @param fixed
     * @param disabled
     * @param sn
     * @param selected
     * @return
     */
    public Map genMap(String itemName, String englishName, boolean touched, Integer level, Integer index, Boolean editing,
                      boolean expanded, boolean readonly, boolean fixed, boolean disabled, String sn, boolean selected){
        Map map = new HashMap();
        map.put("itemName", itemName);
        map.put("englishName", englishName);
        map.put("dataType", DataTypeEnum.OBJECT.getValue());
        map.put("touched", touched);
        map.put("level", level);
        map.put("index", index);
        map.put("editing", editing);
        map.put("expanded", expanded);
        map.put("readonly", readonly);
        map.put("fixed", fixed);
        map.put("disabled", disabled);
        map.put("sn", sn);
        map.put("selected", selected);
        return map;
    }

    private void updateFieldIfNeeded(ModelTableDO exists, ModelFieldDO field, ModelFieldDO existField, String apiUrl, String apiUrl2) {
        boolean codeShouldUpdate = false;
        boolean nameShouldUpdate = false;
        boolean remarkShouldUpdate = false;

        // 判断英文名是否需要修改
        if (!field.getFieldCode().equals(existField.getFieldCode())) {
            String oldFieldCode = StringUtils.strBarToHump(existField.getFieldCode());
            String newFieldCode = StringUtils.strBarToHump(field.getFieldCode());
            itemMapper.updateApiField(0, exists.getProjectId(), apiUrl, oldFieldCode, newFieldCode);
            codeShouldUpdate = true;
        }

        // 判断中文名是否需要修改
        if (ObjUtilX.isNotEmpty(existField.getFieldName()) && !field.getFieldName().equals(existField.getFieldName())) {
            String oldFieldName = StringUtils.strBarToHump(existField.getFieldName());
            String newFieldName = StringUtils.strBarToHump(field.getFieldName());
            itemMapper.updateApiField(1, exists.getProjectId(), apiUrl, oldFieldName, newFieldName);
            nameShouldUpdate = true;
        }

        // 判断备注是否需要修改
        if (ObjUtilX.isNotEmpty(existField.getRemark()) && !field.getRemark().equals(existField.getRemark())) {
            String oldFieldRemark = existField.getRemark().replace("'", "''");
            String newFieldRemark = field.getRemark().replace("'", "''");
            itemMapper.updateApiField(2, exists.getProjectId(), apiUrl, oldFieldRemark, newFieldRemark);
            remarkShouldUpdate = true;
        }

        // 判断平台通用接口
        if (exists.getDataSourceConfigId() == 0L) {
            if (codeShouldUpdate) {
                itemMapper.updateApiField(0, exists.getProjectId(), apiUrl2,
                        StringUtils.strBarToHump(existField.getFieldCode()),
                        StringUtils.strBarToHump(field.getFieldCode()));
            }
            if (nameShouldUpdate) {
                itemMapper.updateApiField(1,exists.getProjectId(), apiUrl2,
                        StringUtils.strBarToHump(existField.getFieldName()),
                        StringUtils.strBarToHump(field.getFieldName()));
            }
            if (remarkShouldUpdate) {
                itemMapper.updateApiField(2,exists.getProjectId(), apiUrl2,
                        existField.getRemark().replace("'", "''"),
                        field.getRemark().replace("'", "''"));
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long modelTableEdit(ModelTableAditReqVO reqVO) {
//        String dbName = databaseUtil.getCon.getSchema();
        // 校验存在
        ModelTableDO exists = this.modelTableValidateExists(reqVO.getTableId());
        if(reqVO.getDirType() == 1){
            if(StrUtilX.isEmpty(reqVO.getTableCode())) {
                throw new BizException("5001", "请填写表英文名");
            }
            //if (!reqVO.getTableCode().toLowerCase().startsWith("u_")){
            //    throw new BizException("5001","表英文名必须是u_开头");
            //}
            // 平台做一下表名限制
            if (!reqVO.getTableCode().toLowerCase().matches("^(a_|u_|f_|o_|s_|t_).*") && exists.getDataSourceConfigId() == 0L) {
                throw new BizException("5001", "用户表英文名必须是 a_, u_, f_, o_, s_, 或 t_ 开头");
            }
            reqVO.setParentId(exists.getParentId());
        }

        //验重
        Long duplicheck = repeatValid(reqVO);
        if (duplicheck > 0){
            throw new BizException("5001","您输入的属性值重复，请重新输入");
        }
        if(exists.getPropType() == 0){
            throw new BizException("5001", "系统内置的不可操作");
        }
        if(exists.getIsLock() == 1){
            throw new BizException("5001", "已锁定不可操作");
        }
        // 更新
        ModelTableDO table = BeanUtilX.copy(exists, ModelTableDO::new);
        if(exists.getIsGen() == 1) {
            table.setTableCode(exists.getTableCode());//防止修改表名
        }else{
            table.setTableCode(reqVO.getTableCode());
        }
        table.setTableName(reqVO.getTableName());
        table.setIsGen(exists.getIsGen());
        table.setDirType(exists.getDirType());
        table.setRemark(reqVO.getRemark());
        table.setVersion(reqVO.getVersion());
        table.setIcon(reqVO.getIcon());
        if(table.getDirType() == -1) {
            Long count = tableMapper.selectCount(LambdaQueryWrapperX.<ModelTableDO>lambdaQueryX()
                    .in(ModelTableDO::getDirType, Arrays.asList(0,1))
                    .eq(ModelTableDO::getProjectId, reqVO.getTableId())
            );
            if(count > 0 && !reqVO.getDataSourceConfigId().equals(exists.getDataSourceConfigId())){
                throw new BizException("5001", "当前项目下存在建模数据，不可切换数据源，如需修改，请编辑被绑定的数据源");
            }
            // 数据源已经被人使用
            if(tableMapper.selectCount(LambdaQueryWrapperX.<ModelTableDO>lambdaQueryX()
                    .eq(ModelTableDO::getDataSourceConfigId, reqVO.getDataSourceConfigId())
                    .eq(ModelTableDO::getDirType, -1)
                    .ne(ModelTableDO::getTableId, reqVO.getTableId())
            ) > 0){
                throw new BizException("5001","数据源已经被其他项目使用，请更换");
            }
        }
        table.setDataSourceConfigId(exists.getDataSourceConfigId());
        tableMapper.updateById(table);
        if(table.getDirType() == 1) {
            //若模型主表is_gen=1，还需要添加到模型字段变更表
            //先查出现有的字段集合
            List<ModelFieldDO> fields = fieldMapper.selectList(LambdaQueryWrapperX.<ModelFieldDO>lambdaQueryX()
                    .eq(ModelFieldDO::getTableId, reqVO.getTableId()));
            if (CollUtilX.isEmpty(fields)) {
//            throw new BizException("5001","数据错误，字段集合不存在");
                fields = new ArrayList<>();
            }
            if (ObjUtilX.isNotEmpty(reqVO.getFields())) {
                addFields(reqVO, reqVO.getTableId());
            }

            DBCreator creator = null;
            //if (exists.getIsGen() == 1) {
                creator = DBCreatorFactory.getDBCreatorDynamic(exists.getDataSourceConfigId());
            //}
            //现有字段集合
            Map<Long, ModelFieldDO> exsitFieldIdMap = fields.stream().collect(Collectors.toMap(ModelFieldDO::getFieldId, a -> a));
            Map<String, ModelFieldDO> exsitFieldCodeMap = fields.stream().collect(Collectors.toMap(ModelFieldDO::getFieldCode, a -> a));
            //删除
            if (ObjUtilX.isNotEmpty(reqVO.getFieldDels())) {
                List<Long> delIds = new ArrayList<>(reqVO.getFieldDels().size());
//            List<ModelFieldAlterDO> delAlterList = new ArrayList<>(reqVO.getFieldDels().size());
                for (ModelFieldAditReqVO fieldDel : reqVO.getFieldDels()) {
                    if (!exsitFieldIdMap.containsKey(fieldDel.getFieldId())) {
                        throw new BizException("5001", "删除字段列表中" + fieldDel.getFieldCode() + "字段不存在");
                    }
                    delIds.add(fieldDel.getFieldId());
                    if (exists.getIsGen() == 1) {
                        creator.dropField(exsitFieldIdMap.get(fieldDel.getFieldId()), table);
                    }
                    fieldMapper.deleteById(fieldDel.getFieldId());//这里搞不了事务
                    //删除索引相关
                    tableIndexMapper.delete(LambdaQueryWrapperX.<ModelTableIndexDO>lambdaQueryX()
                            .eq(ModelTableIndexDO::getTableId, reqVO.getTableId())
                            .eq(ModelTableIndexDO::getFieldId, fieldDel.getFieldId()));
                    exsitFieldIdMap.remove(fieldDel.getFieldId());
                    exsitFieldCodeMap.remove(fieldDel.getFieldCode());
                    // 删除JSON配置，如果有
//                    fieldJsonMapper.delete(LambdaQueryWrapperX.<ModelFieldJsonDO>lambdaQueryX().eq(ModelFieldJsonDO::getFieldId, fieldDel.getFieldId()));
                }
            }

            List<ModelFieldConfAditReqVO> fieldConfList = new ArrayList<>();
            //新增
            if (ObjUtilX.isNotEmpty(reqVO.getFieldAdds())) {

                List<ModelFieldDO> addList = new ArrayList<>(reqVO.getFieldAdds().size());
//            List<ModelFieldAlterDO> addAlterList = new ArrayList<>(reqVO.getFieldAdds().size());
//                List<ModelFieldJsonDO> jsonDOS = new ArrayList<>();
                for (ModelFieldAditReqVO fieldAdd : reqVO.getFieldAdds()) {

//                    if (fieldAdd.getFieldCode().equalsIgnoreCase("id")) {
////                        throw new BizException("5001", "请勿添加id字段");
//                        continue;
//                    }
                    if(ObjUtilX.isEmpty(fieldAdd.getFieldName())
                            || ObjUtilX.isEmpty(fieldAdd.getFieldCode())){
                        throw new BizException("5001", "字段中文名或英文名缺失，请补充！");
                    }
                    if (ObjUtilX.isEmpty(fieldAdd.getFieldType())) {
                        throw new BizException("5001", fieldAdd.getFieldCode() + ":请指定字段类型");
                    }
                    if (exsitFieldCodeMap.containsKey(fieldAdd.getFieldCode())) {
                        throw new BizException("5001", "新增字段列表中" + fieldAdd.getFieldCode() + "字段已存在");
                    }
                    addConf(exists.getProjectId(),fieldAdd, fieldConfList);

                    ModelFieldDO field = BeanUtilX.copy(fieldAdd, ModelFieldDO::new);
                    field.setTableId(exists.getTableId());
                    Long fieldId = IdWorker.getId();
                    field.setFieldId(fieldId);
                    field.setFieldCode(fieldAdd.getFieldCode().toLowerCase());//字段名小写
                    addList.add(field);
                    if (exists.getIsGen() == 1) {
                        creator.addField(field, table);
                    }
                    fieldMapper.insert(field);//这里搞不了事务
                }
            }
            //修改
            if (ObjUtilX.isNotEmpty(reqVO.getFieldUpds())) {
                List<ModelFieldDO> updList = new ArrayList<>(reqVO.getFieldUpds().size());
                for (ModelFieldAditReqVO fieldUpd : reqVO.getFieldUpds()) {
                    addConf(exists.getProjectId(), fieldUpd, fieldConfList);
//                    if (fieldUpd.getFieldCode().equalsIgnoreCase("id")) {
////                        throw new BizException("5001", "请勿添加id字段");
//                        continue;
//                    }
                    if(ObjUtilX.isEmpty(fieldUpd.getFieldName())
                            || ObjUtilX.isEmpty(fieldUpd.getFieldCode())){
                        continue;
                    }
                    ModelFieldDO field = BeanUtilX.copy(fieldUpd, ModelFieldDO::new);
                    field.setTableId(exists.getTableId());
                    if (ObjUtilX.isEmpty(field.getFieldId())) {
                        throw new BizException("5001", "修改的表字段主键不能为空：" + field.getFieldCode());
                    }
                    if (!exsitFieldIdMap.containsKey(fieldUpd.getFieldId())) {
                        throw new BizException("5001", "修改字段列表中" + fieldUpd.getFieldCode() + "字段主键不存在");
                    }
                    field.setFieldCode(field.getFieldCode().toLowerCase());//字段名小写
                    updList.add(field);
                    ModelFieldDO existField = exsitFieldIdMap.get(fieldUpd.getFieldId());
                    String oldCode = existField.getFieldCode();
                    if (!oldCode.equals(fieldUpd.getFieldCode())) {// 修改了字段名
                        field.setOldFieldCode(oldCode);
                        if(exsitFieldCodeMap.containsKey(fieldUpd.getFieldCode())){
                            throw new BizException("5001","修改字段列表中"+fieldUpd.getFieldCode()+"字段已存在");
                        }
                    } else {
                        field.setOldFieldCode("");
                    }
                    if (exists.getIsGen() == 1) {
                        if(!(
                                field.getFieldCode().equals(existField.getFieldCode())
                                        && field.getFieldName().equals(existField.getFieldName())
                                        && field.getFieldType().equals(existField.getFieldType())
                                        && field.getLeng().equals(existField.getLeng())
                                        && field.getFieldPrecision().equals(existField.getFieldPrecision())
                                        && field.getIsNullable().equals(existField.getIsNullable())
                                        && field.getIsPrimaryKey().equals(existField.getIsPrimaryKey())
                                        && field.getIsAutoIncrease().equals(existField.getIsAutoIncrease())
                                        && field.getRemark().equals(existField.getRemark())
                                        && (areDefaultValuesEqual(field.getDefaultVal(), existField.getDefaultVal())) // 使用新方法进行默认值比较
                                )
                        ){
                            creator.modifyField(field, existField, table);
                        }
                    }

                    // 修改 接口文档字段 center/centerTheme* 关联的字段
                    // 第一步，第二个 _ 前缀的前面，去掉第一个下划线的后面，作为 module 名字；第二步，moduleName 必须小写；
                    String substring = exists.getTableCode().substring(exists.getTableCode().indexOf("_") + 1);
                    String moduleName = subBefore(substring, '_', false).toLowerCase();
                    String interfaceUrl = getInterfaceUrl(exists.getTableCode());
                    String apiUrl = moduleName + "/" + interfaceUrl;
                    String apiUrl2 = ApiURLEnum.BASE_PREFIX.getSuffix() + "/" + exists.getTableId();

                    if (!apiUrl.equals("/")) {
                        updateFieldIfNeeded(exists, field, existField, apiUrl, apiUrl2);
                    }

                    fieldMapper.updateById(field);//这里搞不了事务
                    //修改索引里面的字段名称
                    tableIndexMapper.update(new LambdaUpdateWrapper<ModelTableIndexDO>()
                            .eq(ModelTableIndexDO::getFieldId, field.getFieldId())
                            .set(ModelTableIndexDO::getFieldCode, field.getFieldCode())
                    );
                }
            }

            if(ObjUtilX.isNotEmpty(fieldConfList)){
                ModelFieldTransReqVO req = new ModelFieldTransReqVO();
                req.setFields(fieldConfList);
                req.setDataSourceConfigId(exists.getDataSourceConfigId());
                req.setProjectId(reqVO.getProjectId());
                confService.modelFieldTransSave(req);
            }

            //处理索引
            //现有字段集合
            fields = fieldMapper.selectList(LambdaQueryWrapperX.<ModelFieldDO>lambdaQueryX()
                    .eq(ModelFieldDO::getTableId, reqVO.getTableId()));
            if (CollUtilX.isEmpty(fields)) {
                fields = new ArrayList<>();
            }
//        exsitFieldIdMap = fields.stream().collect(Collectors.toMap(ModelFieldDO::getFieldId, a -> a));

            //删除
            if (ObjUtilX.isNotEmpty(reqVO.getIndexDels())) {
                for (ModelTableIndexAditReqVO indexDel : reqVO.getIndexDels()) {
                    if (ObjUtilX.isEmpty(indexDel.getIdxId())) {
                        throw new BizException("5001", "索引主键丢失：" + indexDel.getIdxName());
                    }
                    //存在真实表
                    if (exists.getIsGen() == 1) {
                        // todo 这里从数据库MAP取
                        creator.delIndex(exists, indexDel.getIdxName());
                    }
                    //如果存在索引使用了字段，也要同步删除
                    tableIndexMapper.delete(LambdaQueryWrapperX.<ModelTableIndexDO>lambdaQueryX().eq(ModelTableIndexDO::getIdxId, indexDel.getIdxId()));
                }
            }
            //现有的索引，删除之后再查
            List<ModelTableIndexDO> indexDOS = tableIndexMapper.selectList(LambdaQueryWrapperX.<ModelTableIndexDO>lambdaQueryX()
                    .eq(ModelTableIndexDO::getTableId, reqVO.getTableId()));
            Map<String, ModelTableIndexDO> exsitIdxNameMap = indexDOS.stream().collect(Collectors.toMap(ModelTableIndexDO::getIdxName, a -> a, (existing, replacement) -> existing));

            Map<String, List<ModelFieldAditReqVO>> oldIndexMap = indexDOS.stream()
                    .collect(Collectors.groupingBy(ModelTableIndexDO::getIdxName,
                            Collectors.mapping(index -> {
                                ModelFieldAditReqVO fieldAditReqVO = new ModelFieldAditReqVO();
                                fieldAditReqVO.setFieldCode(index.getFieldCode()); // 将 fieldCode 从 ModelTableIndexDO 转移到 ModelFieldAditReqVO
                                // 如果 ModelFieldAditReqVO 还有其他属性，也可以在这里设置
                                return fieldAditReqVO;
                            }, Collectors.toList())));

            Map<Long, ModelTableIndexDO> exsitIdxIdMap = indexDOS.stream().collect(Collectors.toMap(ModelTableIndexDO::getIdxId
                    , Function.identity(), (oldData, newData) -> newData));
            //新增
            if (ObjUtilX.isNotEmpty(reqVO.getIndexAdds())) {
                // todo 如果字段被删了，创建索引也不要此字段
                creator.addIndex(reqVO.getIndexAdds(), exsitIdxNameMap, exsitFieldCodeMap, exists);
            }
            //更新
            if (ObjUtilX.isNotEmpty(reqVO.getIndexUpds())) {
                for (ModelTableIndexAditReqVO indexUpd : reqVO.getIndexUpds()) {
                    if (ObjUtilX.isEmpty(indexUpd.getIdxId())) {
                        throw new BizException("5001", "索引主键丢失：" + indexUpd.getIdxName());
                    }
                    if (!exsitIdxIdMap.containsKey(indexUpd.getIdxId())) {
                        throw new BizException("5001", "索引信息错误，请刷新页面：" + indexUpd.getIdxName());
                    }
                    ModelTableIndexDO oldIdx = exsitIdxIdMap.get(indexUpd.getIdxId());
                    String oldIdxName = oldIdx.getIdxName();
                    if (!indexUpd.getIdxName().equals(oldIdxName) //如果修改了索引名字
                            && exsitIdxNameMap.containsKey(indexUpd.getIdxName())) {//才判断重复
                        throw new BizException("5001", "索引名【" + indexUpd.getIdxName() + "】已存在");
                    }
                    //存在真实表
                    if (exists.getIsGen() == 1) {
                        //更新索引
                        if (!creator.fieldListsAreEqual(oldIndexMap.get(oldIdxName), indexUpd.getFields()) ||
                                exsitIdxIdMap.containsKey(indexUpd.getId())
                                        && (!oldIdxName.equals(indexUpd.getIdxName()))
                                        || !indexUpd.getIdxWay().equals(exsitIdxIdMap.get(indexUpd.getIdxId()).getIdxWay())
                                        || !indexUpd.getRemark().equals(exsitIdxIdMap.get(indexUpd.getIdxId()).getRemark())) {
                            List<ModelFieldAditReqVO> oldFields = oldIndexMap.get(oldIdxName);
                            ModelTableIndexAditReqVO oldIndex = BeanUtilX.copy(exsitIdxIdMap.get(indexUpd.getIdxId()), ModelTableIndexAditReqVO::new);
                            oldIndex.setFields(oldFields);
                            creator.modifyIndex(exists, oldIndex, indexUpd);
                        }
                    }
                    //删除索引相关
                    tableIndexMapper.delete(LambdaQueryWrapperX.<ModelTableIndexDO>lambdaQueryX()
                            .eq(ModelTableIndexDO::getTableId, reqVO.getTableId())
                            .eq(ModelTableIndexDO::getIdxId, indexUpd.getIdxId())
                    );
                    //重新添加索引，但是不执行SQL
                    exists.setNeedExe(false);
                    creator.addIndex(reqVO.getIndexUpds(), exsitIdxNameMap, exsitFieldCodeMap, exists);
                }
            }
        }
        // 返回
        return table.getTableId();
    }

    // 新方法来判断默认值是否相等或均为空
    private boolean areDefaultValuesEqual(String newDefaultVal, String existingDefaultVal) {
        return (ObjUtilX.isNotEmpty(newDefaultVal) && ObjUtilX.isNotEmpty(existingDefaultVal) && newDefaultVal.equals(existingDefaultVal))
                || (ObjUtilX.isEmpty(newDefaultVal) && ObjUtilX.isEmpty(existingDefaultVal)); // 当两个都是空或空字符串时，也认为相等
    }

    private static void addConf(Long projectId, ModelFieldAditReqVO fieldAdd, List<ModelFieldConfAditReqVO> fieldConf) {
        ModelFieldConfAditReqVO tempConf = new ModelFieldConfAditReqVO();
        tempConf.setFieldCode(fieldAdd.getFieldCode());
        tempConf.setEnglishName(StringUtils.strBarToHump(fieldAdd.getFieldCode()));
        tempConf.setFieldName(fieldAdd.getFieldName());
        tempConf.setItemName(fieldAdd.getFieldName());
        tempConf.setFieldType(fieldAdd.getFieldType());
        tempConf.setFieldLength(fieldAdd.getLeng());
        tempConf.setLeng(fieldAdd.getLeng());
        tempConf.setFieldPrecision(fieldAdd.getFieldPrecision());
        tempConf.setRemark(fieldAdd.getRemark());
        fieldConf.add(tempConf);
    }

    @NotNull
    public static StringBuilder getFullCode(ModelFieldDO field, StringBuilder sql) {
        sql.append(field.getFieldCode());
        sql.append(" ");
        sql.append(field.getFieldType());
        if(ObjUtilX.isNotEmpty(field.getLeng()) && field.getLeng() > 0){
            sql.append("(").append(field.getLeng());
            if(ObjUtilX.isNotEmpty(field.getFieldPrecision())  && field.getFieldPrecision() > 0){
                sql.append(",").append(field.getFieldPrecision());
            }
            sql.append(")");
        }
        return sql;
    }

    @Override
    public void modelTableDelBatch(ModelTableDelBatchReqVO reqVO) {
        for (Long tableId : reqVO.getTableIds()) {
            modelTableDel(tableId);
        }
    }

    @Override
    public ModelTableRespVO modelTableDetailByCode(ModelTableQueryReqVO reqVO) {

        if(ObjUtilX.isEmpty(reqVO.getProjectCode()) || ObjUtilX.isEmpty(reqVO.getTableCode())){
            throw new BizException("5001", "请传项目编码和表编码");
        }
        // 查找项目
        ModelTableDO project = tableMapper.selectOne(LambdaQueryWrapperX.<ModelTableDO>lambdaQueryX()
                .eq(ModelTableDO::getTableCode, reqVO.getProjectCode())
                .eq(ModelTableDO::getDirType, -1)
        );
        if(ObjUtilX.isEmpty(project)){
            throw new BizException("5001", "项目不存在");
        }
        ModelTableDO table;
        try {
            table = tableMapper.selectOne(LambdaQueryWrapperX.<ModelTableDO>lambdaQueryX()
                    .eq(ModelTableDO::getProjectId, project.getTableId())
                    .eqIfPresent(ModelTableDO::getTableId, reqVO.getTableId())// 如果传了tableId，就查，防止多数据源报错
                    .eq(ModelTableDO::getTableCode, reqVO.getTableCode())
            );
        } catch (Exception e) {
            throw new BizException("5001", "可能存在多个建模编码，请传tableId查询。");
        }

        if(ObjUtilX.isEmpty(table)){
            throw new BizException("5001", "建模不存在");
        }
        return  modelTableDetail(table.getTableId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modelTableDel(Long tableId) {
        // 校验存在
        ModelTableDO tableDO = this.modelTableValidateExists(tableId);
        if(tableDO.getPropType() == 0){
            throw new BizException("5001", "系统内置的不可删除");
        }
        if(tableDO.getIsLock() == 1){
            throw new BizException("5001", "已锁定不可删除");
        }
        if(tableDO.getIsGen() == 1){
            //物理删除 todo 先不做真正删除
            //modelTableRealDel(tableDO);
        }
        // 如果是目录，判断下面有没有子节点
        if(tableDO.getDirType() == 0){
            Long count = tableMapper.selectCount(LambdaQueryWrapperX.<ModelTableDO>lambdaQueryX().eq(ModelTableDO::getParentId, tableId));
            if(count > 0){
                throw new BizException("5001", "当前目录下存在数据，不可删除");
            }
        }

        // 删除项目
        if(tableDO.getDirType() == -1){
            Long count = tableMapper.selectCount(LambdaQueryWrapperX.<ModelTableDO>lambdaQueryX()
                    .eq(ModelTableDO::getProjectId, tableId)
                    .eq(ModelTableDO::getDirType, 1)
            );
            if(count > 0){
                throw new BizException("5001", "当前项目下存在建模数据，不可删除");
            }
            count = itemMapper.selectCount(LambdaQueryWrapperX.<ItemDO>lambdaQueryX()
                    .eq(ItemDO::getProjectId, tableId)
                    .eq(ItemDO::getCategory, 0)
                    .eq(ItemDO::getItemType, 1)
            );
            if(count > 0){
                throw new BizException("5001", "当前项目下存在架构图文档数据，不可删除");
            }
            count = itemMapper.selectCount(LambdaQueryWrapperX.<ItemDO>lambdaQueryX()
                    .eq(ItemDO::getProjectId, tableId)
                    .eq(ItemDO::getCategory, 1)
                    .eq(ItemDO::getItemType, 1)
            );
            if(count > 0){
                throw new BizException("5001", "当前项目下存在API文档数据，不可删除");
            }
            // 删除建模空目录
            tableMapper.delete(LambdaQueryWrapperX.<ModelTableDO>lambdaQueryX()
                    .eq(ModelTableDO::getProjectId, tableId)
            );
            // 删除API空目录
            itemMapper.delete(LambdaQueryWrapperX.<ItemDO>lambdaQueryX()
                    .eq(ItemDO::getProjectId, tableId)
            );
            // 删除项目字段类型
            projectFieldTypeMapper.delete(LambdaQueryWrapperX.<ProjectFieldTypeDO>lambdaQueryX()
                    .eq(ProjectFieldTypeDO::getProjectId, tableId)
            );

            // 删除环境配置
            apiEnvMapper.delete(LambdaQueryWrapperX.<ProjectApiEnvDO>lambdaQueryX()
                    .eq(ProjectApiEnvDO::getProjectId, tableId));
            // 删除公共参数
            projectApiParamsMapper.delete(LambdaQueryWrapperX.<ProjectApiParamsDO>lambdaQueryX()
                    .eq(ProjectApiParamsDO::getProjectId, tableId));


            // 删除页面数据
            pageConfigMapper.delete(LambdaQueryWrapperX.<PageConfigDO>lambdaQueryX().eq(PageConfigDO::getProjectId, tableId));
            // 删除接口数据
            List<CodegenTableDO> tableDOS = codegenTableMapper.selectList(LambdaQueryWrapperX.<CodegenTableDO>lambdaQueryX().eq(CodegenTableDO::getProjectId, tableId));
            if(CollUtilX.isNotEmpty(tableDOS)) {
                List<String> ids = tableDOS.stream().map(CodegenTableDO::getId).collect(Collectors.toList());
                codegenTableMapper.deleteBatchIds(ids);
                codegenColumnMapper.delete(LambdaQueryWrapperX.<CodegenColumnDO>lambdaQueryX().in(CodegenColumnDO::getTableId, ids));
            }
        }

        // 删除
        tableMapper.deleteById(tableId);
        //删除字段
        fieldMapper.delete(LambdaQueryWrapperX.<ModelFieldDO>lambdaQueryX()
                .eq(ModelFieldDO::getTableId, tableId));

        //删除JSON字段配置
//        fieldJsonMapper.delete(LambdaQueryWrapperX.<ModelFieldJsonDO>lambdaQueryX()
//                .eq(ModelFieldJsonDO::getTableId, tableId));

    }

    private ModelTableDO modelTableValidateExists(Long tableId) {
        ModelTableDO table = tableMapper.selectById(tableId);
        if (table == null) {
            throw exception(TABLE_NOT_EXISTS);
        }
        return table;
    }

    @Override
    public Long repeatValid(ModelTableAditReqVO reqVO) {
        long count = 0L;
        Long dsId = null;

        if (1 == reqVO.getDirType()){
            ModelTableDO parent = tableMapper.selectById(reqVO.getParentId());
            dsId = parent.getDataSourceConfigId();
        } else{
            if(ObjUtilX.isNotEmpty(reqVO.getProjectId()) && reqVO.getProjectId() > 0) {
                ModelTableDO project = tableMapper.selectById(reqVO.getProjectId());
                dsId = project.getDataSourceConfigId();
            }else{
                dsId = reqVO.getDataSourceConfigId();
            }
        }
        // 根据数据源判重
        if (StrUtilX.isEmpty(reqVO.getTableId())) {
            // 新增验重
            if (StrUtilX.isNotEmpty(reqVO.getTableName())) {
                //count = tableMapper.selectCountByName(reqVO.getTableName(),null, reqVO.getProjectId());
                count = tableMapper.selectCountByName(reqVO.getTableName(),null, dsId, reqVO.getProjectId());
                if (count>0){
//                    return count;
                    throw new BizException("5001", "您输入的中文名称值在项目中同数据库中重复，请重新输入：" + reqVO.getTableName());
                }
            }
            if (1 == reqVO.getDirType() && StrUtilX.isNotEmpty(reqVO.getTableCode())) {
                //count = tableMapper.selectCountByCode(reqVO.getTableCode(),null, reqVO.getProjectId());
                count = tableMapper.selectCountByCode(reqVO.getTableCode(),null, dsId, reqVO.getProjectId());
                if (count>0){
//                    return count;
                    throw new BizException("5001", "您输入的英文名称值在项目中同数据库中重复，请直接导入建模["+reqVO.getTableCode()+"]或重新输入名称");
                }
            }
        } else {
            // 编辑验重
            if (StrUtilX.isNotEmpty(reqVO.getTableName())) {
                //count = tableMapper.selectCountByName(reqVO.getTableName(),null, reqVO.getProjectId());
                count = tableMapper.selectCountByName(reqVO.getTableName(), reqVO.getTableId(), dsId, reqVO.getProjectId());
                if (count>0){
//                    return count;
                    throw new BizException("5001", "您输入的中文名称值在项目中同数据库中重复，请重新输入：" + reqVO.getTableName());
                }
            }
            if (1 == reqVO.getDirType() && StrUtilX.isNotEmpty(reqVO.getTableCode())) {
                //count = tableMapper.selectCountByCode(reqVO.getTableCode(),null, reqVO.getProjectId());
                count = tableMapper.selectCountByCode(reqVO.getTableCode(), reqVO.getTableId(), dsId, reqVO.getProjectId());
                if (count>0){
//                    return count;
                    throw new BizException("5001", "您输入的英文名称值在项目中同数据库中重复，请直接导入建模["+reqVO.getTableCode()+"]或重新输入名称");
                }
            }
        }
        return count;
    }

    @Override
    public ModelTableRespVO modelTableDetail(Long tableId) {
        ModelTableDO tableDO = this.modelTableValidateExists(tableId);
        ModelTableRespVO resp = BeanUtilX.copy(tableDO, ModelTableRespVO::new);
        String dbType = dbUtil.getDbTypeDynamic(tableDO.getDataSourceConfigId());
        if(StrUtilX.isNotEmpty(dbType)){
            resp.setDbType(dbType.toLowerCase());
        }
        //先查出现有的字段集合
        List<ModelFieldDO> fields = fieldMapper.selectList(LambdaQueryWrapperX.<ModelFieldDO>lambdaQueryX()
                .eq(ModelFieldDO::getTableId, tableId)
                .orderByAsc(ModelFieldDO::getSort)
                .orderByAsc(OperateDO::getCreatedDt)
        );

        List<ModelFieldRespVO> fieldResp = fields.stream().map((item) -> {
            ModelFieldRespVO respDO = new ModelFieldRespVO();
            BeanUtilX.copy(item, respDO);
//            List<ModelFieldJsonDO> jsonDOS = fieldJsonMapper.selectList(LambdaQueryWrapperX.<ModelFieldJsonDO>lambdaQueryX().eq(ModelFieldJsonDO::getFieldId, respDO.getFieldId()));
//            respDO.setJsonFields(jsonDOS);
            return respDO;
        }).collect(Collectors.toList());

        resp.setFields(fieldResp);
        //现有字段集合
        Map<Long, ModelFieldDO> exsitFieldIdMap = fields.stream().collect(Collectors.toMap(ModelFieldDO::getFieldId, a -> a));
        //查询索引集合
        List<ModelTableIndexDO> indexDOS = tableIndexMapper.selectList(LambdaQueryWrapperX.<ModelTableIndexDO>lambdaQueryX()
                .eq(ModelTableIndexDO::getTableId, tableId));

        Map<String, List<ModelTableIndexDO>> indexMap = indexDOS.stream().collect(Collectors.groupingBy(s -> s.getIdxName()));
        List<ModelTableIndexRespVO> indexs = new ArrayList<>(indexMap.size());
        indexMap.forEach((key, value) -> {
            ModelTableIndexRespVO idx = BeanUtilX.copy(value.get(0), ModelTableIndexRespVO::new);
            idx.setTableId(tableId);
            idx.setTableCode(tableDO.getTableCode());
            List<ModelFieldDO> idxFields = new ArrayList<>(value.size());
            for (ModelTableIndexDO item : value) {
                ModelFieldDO field = exsitFieldIdMap.get(item.getFieldId());
                if(ObjUtilX.isEmpty(field)){
                    continue;
                }
                ModelFieldDO f = new ModelFieldDO();
                f.setFieldId(item.getFieldId());
                f.setFieldCode(item.getFieldCode());
                f.setFieldType(field.getFieldType());
                f.setFieldTypeName(field.getFieldTypeName());
                f.setLeng(field.getLeng());
                f.setFieldPrecision(field.getFieldPrecision());
                f.setFieldName(field.getFieldName());
                idxFields.add(f);
            }
            idx.setFields(idxFields);
            indexs.add(idx);
        });
        resp.setIndexs(indexs);
        return resp;
    }

    @Override
    public ModelTableRespVO modelTableDetailByCode(String tableCode, Integer dirType) {
        ModelTableDO project = tableMapper.selectOne(LambdaQueryWrapperX.<ModelTableDO>lambdaQueryX()
                .eq(ModelTableDO::getTableCode, tableCode)
                .eq(ModelTableDO::getDirType, dirType)
        );
        if(ObjUtilX.isEmpty(project)){
            throw new BizException("5001","找不到对应的数据");
        }
        return BeanUtilX.copy(project, ModelTableRespVO::new);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TableStructure modelTableImport(ModelTableCodeReqVO reqVO) {
        //查询父级
        if(ObjUtilX.isEmpty(reqVO.getParentId())){
            throw new BizException("5001","请选择父级");
        }
        ModelTableDO parent = tableMapper.selectById(reqVO.getParentId());
        if(ObjUtilX.isEmpty(parent)){
            throw new BizException("5001","找不到父级目录");
        }
        // 如果存在，就做sync同步
        try {
            ModelTableDO tableDO = tableMapper.selectOne(LambdaQueryWrapperX.<ModelTableDO>lambdaQueryX()
                    .eq(ModelTableDO::getTableCode, reqVO.getTableCode())
                    .eq(ModelTableDO::getDataSourceConfigId, reqVO.getDataSourceConfigId())
                    .eq(ModelTableDO::getProjectId, parent.getProjectId())
            );
            if (ObjUtilX.isNotEmpty(tableDO)) {
                reqVO.setDataSourceConfigId(tableDO.getDataSourceConfigId());
                //return modelTableSync(tableDO.getTableId());
                return modelTableSyncNew(tableDO.getTableId());
            }
        }catch (Exception e){
            throw new BizException("5001", reqVO.getTableCode() + "建模查询失败：" + e.getMessage());
        }

        TableStructure tableStructure = null;
        ModelTableDO table = new ModelTableDO();
        Long tableId = IdWorker.getId();
        table.setTableId(tableId);
        table.setIsGen(1);
        table.setTableCode(reqVO.getTableCode());
        table.setDataSourceConfigId(parent.getDataSourceConfigId());
        table.setProjectId(parent.getProjectId());
        table.setParentId(reqVO.getParentId());
        //type=1，插入模型字段表
//        addFields(reqVO, tableId);
        SqlLogDO sqlLog = new SqlLogDO(parent.getProjectId(), table.getTableId(), table.getTableCode(), "", DBOPTypeEnum.CREATE_TABLE.getType(), 1, DBOPTypeEnum.CREATE_TABLE.getDesc());
        try {
            DBCreator creator = DBCreatorFactory.getDBCreatorDynamic(parent.getDataSourceConfigId());
            tableStructure = creator.getTableStructure(table);
            // 插入主表
            tableMapper.insert(table);
            //当前数据库结构
//            Map<String, ColumnInfo> realFieldMap = tableStructure.getColumns().stream().collect(Collectors.toMap(ColumnInfo::getName, a -> a));

            List<ModelFieldDO> addFields = new ArrayList<>();
            //添加真实表中不存在的字段
            Integer sort = 1;
            List<ModelFieldConfAditReqVO> fieldConfList = new ArrayList<>();
            for (ColumnInfo column : tableStructure.getColumns()) {
//                if(!column.getName().equals("id")){
                ModelFieldDO field = new ModelFieldDO();
                field.setTableId(tableId);
                field.setFieldCode(column.getName());
                field.setFieldType(column.getType());
                field.setFieldTypeName(column.getType());
                field.setLeng(column.getSize());
                field.setFieldPrecision(column.getFieldPrecision());
                field.setIsNullable(column.getNullable() ? 0 : 1);
                field.setIsPrimaryKey(column.getPrimaryKey() ? 1 : 0);
                //field.setRemark(column.getRemark());//这里不加备注了，有点冗余
                if(column.getRemark().contains("[")){
                    String[] split = column.getRemark().split("\\[");
                    if(split.length > 1){
                        field.setRemark(split[1].replace("]", ""));
                        field.setFieldName(split[0].replace("[", ""));
                        //columnInfo.setRemark(split[0]);
                    }
                }

                field.setDefaultVal(column.getDefaultValue());
                if(column.getPrimaryKey()){
                    field.setSort(0);
                }else {
                    field.setSort(sort++);
                }
                addFields.add(field);
                // 更新配置
                addConf(parent.getProjectId(), BeanUtilX.copy(field, ModelFieldAditReqVO::new), fieldConfList);
                field.setNeedExe(false);
                creator.addField(field, table);
//                    fieldMapper.insert(field);
//                }
            }
            fieldMapper.insertBatch(addFields);
            // 保存翻译配置
            if(ObjUtilX.isNotEmpty(fieldConfList)){
                ModelFieldTransReqVO req = new ModelFieldTransReqVO();
                req.setDataSourceConfigId(parent.getDataSourceConfigId());
                req.setProjectId(parent.getProjectId());
                req.setFields(fieldConfList);
                confService.modelFieldTransSave(req);
            }
            //当前数据库索引
            Map<String, String> realIdxMap = new HashMap<>();
            for (ModelTableIndexRespVO index : tableStructure.getIndexs()) {
                realIdxMap.put(index.getIdxName(), index.getIdxName());
            }
            //刷新现存字段
            List<ModelFieldDO> fields = fieldMapper.selectList(LambdaQueryWrapperX.<ModelFieldDO>lambdaQueryX()
                    .eq(ModelFieldDO::getTableId, tableId));
            Map<String, ModelFieldDO> exsitFieldCodeMap = fields.stream().collect(Collectors.toMap(ModelFieldDO::getFieldCode, a -> a));

            List<ModelTableIndexDO> idxAddList = new ArrayList<>(tableStructure.getIndexs().size());

            //索引处理
            Map<String, Long> idxIdMap = new HashMap<>();
            tableStructure.getIndexs().forEach(index -> {
                ModelTableIndexDO indexDO = BeanUtilX.copy(index, ModelTableIndexDO::new);
                //name一样的保持一样
                if(idxIdMap.containsKey(index.getIdxName())){
                    indexDO.setIdxId(idxIdMap.get(index.getIdxName()));
                    index.setIdxId(idxIdMap.get(index.getIdxName()));

                }else {
                    long idxId = IdWorker.getId();
                    idxIdMap.put(index.getIdxName(), idxId);
                    indexDO.setIdxId(idxId);
                    index.setIdxId(idxId);
                }
                indexDO.setRemark(index.getRemark());
                indexDO.setFieldId(exsitFieldCodeMap.get(index.getFieldCode()).getFieldId());
                index.setFieldId(exsitFieldCodeMap.get(index.getFieldCode()).getFieldId());
                idxAddList.add(indexDO);
            });
            //先删除名字存在的索引
            tableIndexMapper.insertBatch(idxAddList);
            log.info("导入表结构完毕：" + table.getTableCode());
            log.info("结构：" + tableStructure);

            StringBuilder sql = creator.genCreateTableSQL(table);
            String sqlStr = sql.toString();
            sqlLog.setSql(sqlStr);
            // 关闭资源
//            resultSet.close();
//            statement.close();
//            connection.close();
        } catch (Exception e) {
            sqlLog.setSucStatus(0);
            sqlLog.setErrMgs(e.getMessage());
            e.printStackTrace();
            throw new BizException("5001", reqVO.getTableCode() + " -> 导入表结构失败:"+e.getMessage());
        } finally {
            try {
                sqlLogMapper.insert(sqlLog);
            } catch (Exception e) {
                log.error("插入日志失败:" + e.getMessage());
            }
        }
        return tableStructure;
    }

    /**
     * 弃用了
     * @param tableId
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TableStructure modelTableSync(Long tableId) {
        // 校验存在
        ModelTableDO tableDO = this.modelTableValidateExists(tableId);
        TableStructure tableStructure = null;
        try {
//            stmt.execute("DESC " + tableDO.getTableCode());
//            stmt.getResultSet()
            //先查出建模表现有的字段集合
            List<ModelFieldDO> fields = fieldMapper.selectList(LambdaQueryWrapperX.<ModelFieldDO>lambdaQueryX()
                    .eq(ModelFieldDO::getTableId, tableId));
            DBCreator creator = DBCreatorFactory.getDBCreatorDynamic(tableDO.getDataSourceConfigId());
            tableStructure = creator.getTableStructure(tableDO);
            if(CollUtilX.isEmpty(tableStructure.getColumns())){
                throw new BizException("5001", "查询不到表结构信息，无法同步");
            }
            //当前数据库结构
            Map<String, ColumnInfo> realFieldMap = tableStructure.getColumns().stream().collect(Collectors.toMap(ColumnInfo::getName, a -> a));

            List<ModelFieldDO> fixField = new ArrayList<>();
            fields.forEach(field -> {
                if(realFieldMap.containsKey(field.getFieldCode())){
                    ColumnInfo columnInfo = realFieldMap.get(field.getFieldCode());
                    //当前表结构存在，将结构数据复制给建模字段进行更新
                    field.setFieldType(columnInfo.getType());
                    field.setLeng(columnInfo.getSize());
                    field.setFieldPrecision(columnInfo.getFieldPrecision());
                    field.setIsNullable(columnInfo.getNullable() ? 0 : 1);
                    field.setIsPrimaryKey(columnInfo.getPrimaryKey() ? 1 : 0);
                    field.setFieldName(columnInfo.getRemark());
                    field.setRemark(columnInfo.getRemark());
                    field.setDefaultVal(columnInfo.getDefaultValue());
                    fieldMapper.updateById(field);
                    fixField.add(field);
                }else{
                    //当前表结构不存在此字段，进行删除
                    fieldMapper.deleteById(field);
//                    fields.remove(field);
                }
            });
            //添加真实表中不存在的字段
            //模型中存在的记录
            Map<String, ModelFieldDO> modelFieldMap = fixField.stream().collect(Collectors.toMap(ModelFieldDO::getFieldCode, a -> a));
            tableStructure.getColumns().forEach(column -> {
                if(!modelFieldMap.containsKey(column.getName())){
                    ModelFieldDO field = new ModelFieldDO();
                    field.setTableId(tableId);
                    field.setFieldCode(column.getName());
                    field.setFieldName(column.getRemark());
                    field.setFieldType(column.getType());
                    field.setLeng(column.getSize());
                    field.setFieldPrecision(column.getFieldPrecision());
                    field.setIsNullable(column.getNullable() ? 0 : 1);
                    field.setIsPrimaryKey(column.getPrimaryKey() ? 1 : 0);
                    field.setRemark(column.getRemark());
                    field.setDefaultVal(column.getDefaultValue());
                    fieldMapper.insert(field);
                }
            });

            //查询缓存表中的索引集合
            List<ModelTableIndexDO> indexDOS = tableIndexMapper.selectList(LambdaQueryWrapperX.<ModelTableIndexDO>lambdaQueryX()
                    .eq(ModelTableIndexDO::getTableId, tableId));

            Map<String, List<ModelTableIndexDO>> exsistIndexMap = indexDOS.stream().collect(Collectors.groupingBy(s -> s.getIdxName()));

            //当前数据库索引
            Map<String, String> realIdxMap = new HashMap<>();
            for (ModelTableIndexRespVO index : tableStructure.getIndexs()) {
                realIdxMap.put(index.getIdxName(), index.getIdxName());
            }
            //刷新现存字段
            fields = fieldMapper.selectList(LambdaQueryWrapperX.<ModelFieldDO>lambdaQueryX()
                    .eq(ModelFieldDO::getTableId, tableId));
            Map<String, ModelFieldDO> exsitFieldCodeMap = fields.stream().collect(Collectors.toMap(ModelFieldDO::getFieldCode, a -> a));

            List<ModelTableIndexDO> idxAddList = new ArrayList<>(tableStructure.getIndexs().size());
            //存在的先删除
            List<String> idxNameDel = new ArrayList<>(tableStructure.getIndexs().size());
            //索引处理
            Map<String, Long> idxIdMap = new HashMap<>();
            tableStructure.getIndexs().forEach(index -> {
                if(exsistIndexMap.containsKey(index.getIdxName())){
                    idxNameDel.add(index.getIdxName());
                }
                ModelTableIndexDO indexDO = BeanUtilX.copy(index, ModelTableIndexDO::new);
                //name一样的保持一样
                if(idxIdMap.containsKey(index.getIdxName())){
                    indexDO.setIdxId(idxIdMap.get(index.getIdxName()));
                    index.setIdxId(idxIdMap.get(index.getIdxName()));

                }else {
                    long idxId = IdWorker.getId();
                    idxIdMap.put(index.getIdxName(), idxId);
                    indexDO.setIdxId(idxId);
                    index.setIdxId(idxId);
                }
                indexDO.setRemark(index.getRemark());
                indexDO.setFieldId(exsitFieldCodeMap.get(index.getFieldCode()).getFieldId());
                index.setFieldId(exsitFieldCodeMap.get(index.getFieldCode()).getFieldId());
                idxAddList.add(indexDO);
            });
            //当前缓存表的数据不在真实索引中，就删除
            for (ModelTableIndexDO indexDO : indexDOS) {
                if(!realIdxMap.containsKey(indexDO.getIdxName())){
                    idxNameDel.add(indexDO.getIdxName());
                }
            }
            //先删除名字存在的索引
            if(ObjUtilX.isNotEmpty(idxNameDel)) {
                tableIndexMapper.delete(LambdaQueryWrapperX.<ModelTableIndexDO>lambdaQueryX().in(ModelTableIndexDO::getIdxName, idxNameDel));
            }
            if(ObjUtilX.isNotEmpty(idxAddList)) {
                tableIndexMapper.insertBatch(idxAddList);
            }
            log.info("查询表结构完毕：" + tableDO.getTableCode());
            log.info("结构：" + tableStructure);
        } catch (Exception e) {
            log.error("执行表字段修改失败：" + tableDO.getTableCode());
            e.printStackTrace();
            throw new BizException("5001", "查询表结构失败");
        }
        return tableStructure;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public TableStructure modelTableSyncNew(Long tableId) {
        // 校验表是否存在
        ModelTableDO tableDO = modelTableValidateExists(tableId);

        // 获取当前表结构
        TableStructure tableStructure;
        //Connection connection = null;
        try {
            //connection = dbUtil.getCon(tableDO.getDataSourceConfigId());

            // 获取模型字段集合
            List<ModelFieldDO> fields = fieldMapper.selectList(
                    LambdaQueryWrapperX.<ModelFieldDO>lambdaQueryX().eq(ModelFieldDO::getTableId, tableId)
            );

            // 获取数据库中当前表的结构
            DBCreator creator = DBCreatorFactory.getDBCreatorDynamic(tableDO.getDataSourceConfigId());
            tableStructure = creator.getTableStructure(tableDO);
            validateTableStructure(tableStructure);

            //保存翻译配置
            List<ModelFieldConfAditReqVO> fieldConfList = new ArrayList<>();
            // 更新字段信息
            updateFields(fields, tableStructure, creator, tableDO, fieldConfList);

            // 更新索引信息
            updateIndexes(tableId, tableStructure);

            log.info("查询表结构完毕：" + tableDO.getTableCode());
        } catch (Exception e) {
            log.error("执行表字段修改失败：" + tableDO.getTableCode(), e);
            throw new BizException("5001", "查询表结构失败:"+e.getMessage());
        }finally {
            //try {
            //    connection.close();
            //} catch (SQLException e) {
            //    throw new RuntimeException(e);
            //}
        }
        return tableStructure;
    }

    private void validateTableStructure(TableStructure tableStructure) {
        if (CollUtilX.isEmpty(tableStructure.getColumns())) {
            throw new BizException("5001", "查询不到表结构信息，无法同步:"+tableStructure.getTableCode());
        }
    }

    private void updateFields(List<ModelFieldDO> fields, TableStructure tableStructure, DBCreator creator,
                              ModelTableDO tableDO, List<ModelFieldConfAditReqVO> fieldConfList) {
        Map<String, ColumnInfo> realFieldMap = tableStructure.getColumns().stream()
                .collect(Collectors.toMap(ColumnInfo::getName, column -> column));

        List<ModelFieldDO> existingFields = new ArrayList<>();

        for (ModelFieldDO field : fields) {
//            if(field.getFieldCode().equals("id")){
//                continue;
//            }
            if (realFieldMap.containsKey(field.getFieldCode())) {
                if(field.getIsPrimaryKey() == 1){
                    field.setSort(0);
                }
                // 更新字段信息
                updateExistingField(field, realFieldMap.get(field.getFieldCode()), creator, tableDO, fieldConfList);
                existingFields.add(field);
            } else {
                // 删除数据库没有的字段
                fieldMapper.deleteById(field);
            }
        }
        // 新增缺失的字段
        addMissingFields(creator, tableDO, existingFields, tableStructure, fieldConfList);
        // 保存翻译配置
        if(ObjUtilX.isNotEmpty(fieldConfList)){
            ModelFieldTransReqVO req = new ModelFieldTransReqVO();
            req.setDataSourceConfigId(tableDO.getDataSourceConfigId());
            req.setProjectId(tableDO.getProjectId());
            req.setFields(fieldConfList);
            confService.modelFieldTransSave(req);
        }
    }

    private void updateExistingField(ModelFieldDO field, ColumnInfo columnInfo,DBCreator creator,
                                     ModelTableDO tableDO, List<ModelFieldConfAditReqVO> fieldConfList) {
        ModelFieldDO oldField = BeanUtilX.copy(field, ModelFieldDO::new);
        if(field.getFieldCode().equals(columnInfo.getName())
                && field.getSort().equals(columnInfo.getSort())
                && field.getFieldName().equals(columnInfo.getRemark())
                && field.getFieldType().equals(columnInfo.getType())
                && field.getLeng().equals(columnInfo.getSize())
                && field.getFieldPrecision().equals(columnInfo.getFieldPrecision())
                && field.getIsNullable().equals(columnInfo.getNullable() ? 0 : 1)
                && field.getIsPrimaryKey().equals(columnInfo.getPrimaryKey() ? 1 : 0)
                && (ObjUtilX.isNotEmpty(columnInfo.getIsAutoIncrease()) && columnInfo.getIsAutoIncrease().equals(field.getIsAutoIncrease()))
                && areDefaultValuesEqual(field.getDefaultVal(), columnInfo.getDefaultValue())
        ){
            return;
        }
        field.setSort(columnInfo.getSort());
        field.setFieldType(columnInfo.getType());
        field.setFieldTypeName(columnInfo.getType());
        if (columnInfo.getType().equals("JSONB") || columnInfo.getType().equals("TEXT")) {//排除掉jsonb,text
            field.setLeng(0);
        }else{
            field.setLeng(columnInfo.getSize());
        }
        field.setFieldPrecision(columnInfo.getFieldPrecision());
        field.setIsNullable(columnInfo.getNullable() ? 0 : 1);
        field.setIsAutoIncrease(columnInfo.getIsAutoIncrease());
        field.setIsPrimaryKey(columnInfo.getPrimaryKey() ? 1 : 0);
        field.setFieldName(columnInfo.getRemark());
        //校验columnInfo.getRemark()里面是否有[]符号，需要把这个符合里面的内容拆分到field里面的remark
        if(columnInfo.getRemark().contains("[")){
            String[] split = columnInfo.getRemark().split("\\[");
            if(split.length > 1){
                field.setRemark(split[1].replace("]", ""));
                field.setFieldName(split[0].replace("[", ""));
                //columnInfo.setRemark(split[0]);
            }
        }
        //field.setRemark(columnInfo.getRemark());
        field.setDefaultVal(columnInfo.getDefaultValue());
        fieldMapper.updateById(field);
        // 更新配置
        addConf(tableDO.getProjectId(), BeanUtilX.copy(field, ModelFieldAditReqVO::new), fieldConfList);
        if(!(
            field.getFieldCode().equals(columnInfo.getName())
                    //&& field.getFieldName().equals(columnInfo.getRemark())
                    && field.getFieldType().equals(columnInfo.getType())
                    && field.getLeng().equals(columnInfo.getSize())
                    && field.getFieldPrecision().equals(columnInfo.getFieldPrecision())
                    && field.getIsNullable().equals(columnInfo.getNullable() ? 0 : 1)
                    && field.getIsPrimaryKey().equals(columnInfo.getPrimaryKey() ? 1 : 0)
                    && columnInfo.getRemark().equals(field.getFieldName())
                    && areDefaultValuesEqual(field.getDefaultVal(), columnInfo.getDefaultValue())
                    && (ObjUtilX.isNotEmpty(columnInfo.getIsAutoIncrease()) && columnInfo.getIsAutoIncrease().equals(field.getIsAutoIncrease()))
        )
        ){
            // 无需真的执行
            field.setNeedExe(false);
            creator.modifyField(field, oldField, tableDO);
        }
    }

    private void addMissingFields(DBCreator creator, ModelTableDO tableDO, List<ModelFieldDO> existingFields,
                                  TableStructure tableStructure , List<ModelFieldConfAditReqVO> fieldConfList) {
        Set<String> existingFieldCodes = existingFields.stream()
                .map(ModelFieldDO::getFieldCode)
                .collect(Collectors.toSet());
        Integer sort = 1;
        for (ColumnInfo column : tableStructure.getColumns()) {
            if (!existingFieldCodes.contains(column.getName())) {
                column.setSort(sort++);
                insertNewField(tableStructure, column, creator, tableDO,fieldConfList);
            }
        }
    }

    private void insertNewField(TableStructure tableStructure, ColumnInfo column, DBCreator creator,
                                ModelTableDO tableDO, List<ModelFieldConfAditReqVO> fieldConfList) {
        ModelFieldDO newField = new ModelFieldDO();
        newField.setTableId(tableStructure.getTableId()); // Assume this is available
        newField.setSort(column.getSort());
        newField.setFieldCode(column.getName());
        newField.setFieldName(ObjUtilX.isEmpty(column.getRemark()) ? "" : column.getRemark());
        newField.setFieldType(column.getType());
        newField.setFieldTypeName(column.getType());
        if (column.getType().equals("JSONB") || column.getType().equals("TEXT")) {//排除掉jsonb,text
            newField.setLeng(0);
        }else{
            newField.setLeng(column.getSize());
        }
        newField.setFieldPrecision(column.getFieldPrecision());
        newField.setIsNullable(column.getNullable() ? 0 : 1);
        newField.setIsPrimaryKey(column.getPrimaryKey() ? 1 : 0);
        //newField.setRemark(column.getRemark());
        newField.setDefaultVal(column.getDefaultValue());
        fieldMapper.insert(newField);
        // 更新配置
        addConf(tableDO.getProjectId(), BeanUtilX.copy(newField, ModelFieldAditReqVO::new), fieldConfList);
        // 无需真的执行
        newField.setNeedExe(false);
        creator.addField(newField,tableDO);
    }

    private void updateIndexes(Long tableId, TableStructure tableStructure) {
//        List<ModelTableIndexDO> indexDOS = tableIndexMapper.selectList(
//                LambdaQueryWrapperX.<ModelTableIndexDO>lambdaQueryX().eq(ModelTableIndexDO::getTableId, tableId)
//        );

//        Map<String, List<ModelTableIndexDO>> existingIndexMap = indexDOS.stream()
//                .collect(Collectors.groupingBy(ModelTableIndexDO::getIdxName));
//
//        Set<String> realIdxNames = tableStructure.getIndexs().stream()
//                .map(ModelTableIndexRespVO::getIdxName)
//                .collect(Collectors.toSet());

//        List<String> idxNamesToDelete = indexDOS.stream()
//                .filter(indexDO -> !realIdxNames.contains(indexDO.getIdxName()))
//                .map(ModelTableIndexDO::getIdxName)
//                .collect(Collectors.toList());
//
//        // 删除不再存在的索引
//        if (!idxNamesToDelete.isEmpty()) {
//            tableIndexMapper.delete(LambdaQueryWrapperX.<ModelTableIndexDO>lambdaQueryX().in(ModelTableIndexDO::getIdxName, idxNamesToDelete));
//        }

        tableIndexMapper.delete(LambdaQueryWrapperX.<ModelTableIndexDO>lambdaQueryX().in(ModelTableIndexDO::getTableId, tableId));

        // 重新获取模型字段集合
        List<ModelFieldDO> fields = fieldMapper.selectList(
                LambdaQueryWrapperX.<ModelFieldDO>lambdaQueryX().eq(ModelFieldDO::getTableId, tableId)
        );
        Map<String, ModelFieldDO> exsitFieldCodeMap = fields.stream().collect(Collectors.toMap(ModelFieldDO::getFieldCode, a -> a));

        List<ModelTableIndexDO> idxAddList = new ArrayList<>();
        Map<String, Long> idxIdMap = new HashMap<>();
        for (ModelTableIndexRespVO index : tableStructure.getIndexs()) {
            if (index.getFieldCode().equalsIgnoreCase("id")) {
                continue; // id跳过
            }
//            if (existingIndexMap.containsKey(index.getIdxName())) {
//                continue; // 已存在的索引，将跳过
//            }
            //name一样的保持一样
            if(idxIdMap.containsKey(index.getIdxName())){
                index.setIdxId(idxIdMap.get(index.getIdxName()));
            }else {
                long idxId = IdWorker.getId();
                idxIdMap.put(index.getIdxName(), idxId);
                index.setIdxId(idxId);
            }
            index.setRemark(index.getRemark());
            index.setFieldId(exsitFieldCodeMap.get(index.getFieldCode()).getFieldId());
            idxAddList.add(createNewIndex(index, tableId));
        }

        // 批量插入新索引
        if (!idxAddList.isEmpty()) {
            tableIndexMapper.insertBatch(idxAddList);
        }
    }

    private ModelTableIndexDO createNewIndex(ModelTableIndexRespVO index, Long tableId) {
        ModelTableIndexDO indexDO = BeanUtilX.copy(index, ModelTableIndexDO::new);
        indexDO.setTableId(tableId); // Assume this is set correctly
        return indexDO;
    }

    @Override
    public List<ModelTableDO> modelTableList(ModelTableQueryReqVO reqVO) {
        return tableMapper.selectList(reqVO);
    }

    @Override
    public PageResult<ModelTableRespVO> modelTablePage(ModelTablePageReqVO reqVO) {
        //reqVO.setDirType(-1);
        PageResult<ModelTableRespVO> resp = BeanUtilX.copyPage(tableMapper.selectPage(reqVO), ModelTableRespVO::new);
        Set<Long> ids = resp.getList().stream().map(ModelTableRespVO::getDataSourceConfigId).collect(Collectors.toSet());
        List<DataSourceConfigDO> configDOS = dataSourceConfigMapper.selectBatchIds(ids);
        if(ObjUtilX.isNotEmpty(configDOS)){
            Map<Long,String> configDOMap = configDOS.stream().collect(Collectors.toMap(DataSourceConfigDO::getId,a->a.getName()));
            resp.getList().forEach(item-> {
                if(item.getDataSourceConfigId().equals(0L)){
                    item.setDataSourceConfigName(DataSourceConfigDO.DB_NME);
                } else {
                    item.setDataSourceConfigName(configDOMap.get(item.getDataSourceConfigId()));
                }
            });
        }
        return resp;
    }

    @Override
    public List<ModelTableRespVO> modelTableTree(ModelTableQueryReqVO reqVO) {
        if(ObjUtilX.isEmpty(reqVO.getProjectId())){
            throw new BizException("5001","请传入项目ID");
        }
        List<ModelTableDO> list = tableMapper.selectList(reqVO);
        // 正序
//        Collections.sort(list);

        // 如果是根节点，需要显示数据源
        Map<Long, String> dsMap = new HashMap<>();
        ModelTableQueryReqVO reqVO1 = new ModelTableQueryReqVO();
        reqVO1.setProjectId(reqVO.getProjectId());
        reqVO1.setDirType(0);
        reqVO1.setParentId(0L);
        List<ModelTableDO> parentList = tableMapper.selectList(reqVO1);
        if(ObjUtilX.isNotEmpty(parentList)) {
            List<Long> dsIds = parentList.stream().map(ModelTableDO::getDataSourceConfigId).toList();
            List<DataSourceConfigDO> configDOS = dataSourceConfigMapper.selectBatchIds(dsIds);
            if (ObjUtilX.isNotEmpty(configDOS)) {
                dsMap = configDOS.stream().collect(Collectors.toMap(DataSourceConfigDO::getId, a -> a.getName()));
            }
        }

        // 如果是建模，全名称为表中文名（英文名），方便搜索
        List<ModelTableRespVO> collect = new ArrayList<>();
        for (ModelTableDO modelTableDO : list) {
            ModelTableRespVO convert = BeanUtilX.copy(modelTableDO, ModelTableRespVO::new);
            convert.setItemId(convert.getTableId().toString());
            convert.setItemName(convert.getTableName());
            convert.setItemCode(convert.getTableCode());
            convert.setParentItemId(convert.getParentId().toString());
            // 如果是建模，全名称为表中文名（英文名），方便搜索
            if (convert.getDirType() == 1) {
                convert.setFullName(convert.getTableName() + " (" + convert.getTableCode() + ")");
            } else {
                convert.setFullName(convert.getTableName());
            }
            if (convert.getParentId() == 0L) {
                String dsName = "";
                if(modelTableDO.getDataSourceConfigId() == 0L){
                    dsName = "平台默认库";
                }else{
                    dsName = dsMap.getOrDefault(modelTableDO.getDataSourceConfigId(), "未知数据源");
                }
                convert.setDataSourceConfigName(dsName);
                //convert.setTableName(convert.getItemName() + "【" + dsName + "】");
            }
            ModelTableRespVO apply = convert;
            collect.add(apply);
        }

        return listToTree(collect);
    }

    @Override
    public Boolean modelTableEmpty(Long tableId) {
        // 校验存在
        ModelTableDO tableDO = this.modelTableValidateExists(tableId);
        if(tableDO.getPropType() == 0){
            throw new BizException("5001", "系统内置的不可操作");
        }
        // 这里mysql,pg都是同一个语法，先直接这么写不放在creator里面了
        StringBuilder sql = new StringBuilder("TRUNCATE ");
        sql.append(tableDO.getTableCode());
        String sqlStr = sql.toString();
        dbUtil.exeSQL(tableDO, sqlStr, DBOPTypeEnum.EMPTY_TABLE);
        log.info("执行表数据清空完毕:" + tableDO.getTableCode());
        log.info("执行表数据清空完毕：" + sql);

        return true;
    }

    @Override
    public Integer modelTableCount(Long tableId) {
        // 校验存在
        ModelTableDO tableDO = this.modelTableValidateExists(tableId);
        if(tableDO.getIsGen() == 0){
            throw new BizException("5001", "当前表还未生成表结构");
        }
        return modelTableGetCount(tableDO);
    }


    private Integer modelTableGetCount(ModelTableDO tableDO) {
        String tableCode = tableDO.getTableCode();
        Long confId = tableDO.getDataSourceConfigId();
        StringBuilder sql = new StringBuilder("select count(*) from ");
        sql.append(tableCode);
        Integer count = 0;
        Connection connection = null;
        try {
            connection = dbUtil.getCon(confId);
             Statement stmt = connection.createStatement();
            ResultSet resultSet = stmt.executeQuery(sql.toString());
            if (resultSet.next()) { // 移动光标到第一行
                count = resultSet.getInt(1); // 获取第一个字段的值
            }
            stmt.close();
            resultSet.close();
            log.info("查询表数据统计完毕:" + tableCode);
            log.info("查询表数据统计完毕：" + sql);
        } catch (Exception e) {
            log.error("查询表数据统计失败：" + sql);
            e.printStackTrace();
            throw new BizException("5001","查询表数据统计失败："+ tableCode);
        } finally {
            try {
                connection.close();
            } catch (SQLException e) {
                throw new RuntimeException(e);
            }
        }
        return count;
    }

    private Boolean modelTableRealDel(ModelTableDO tableDO) {
        Long confId = tableDO.getDataSourceConfigId();
        String tableCode = tableDO.getTableCode();
        String dbType = dbUtil.getDBType(confId);
        StringBuilder sql = new StringBuilder();
        String prefix = dbUtil.getDBNameAllDynamic(confId, true);
        if(dbType.equals(DBTypeEnum.PG.getValue())){
            sql.append("DROP TABLE IF EXISTS ");
//        sql.append(tableCode);
            sql.append(prefix).append(".\"").append(tableDO.getTableCode()).append("\" "); // 使用双引号
        }else if (dbType.equals(DBTypeEnum.MYSQL.getValue())){
            sql.append("DROP TABLE ");
            sql.append(tableCode);
        }
        try {
            dbUtil.exeSQL(tableDO, sql.toString(), DBOPTypeEnum.DROP_TABLE);
            log.info("执行表物理删除完毕:" + tableCode);
            log.info("执行表物理删除完毕：" + sql);
        } catch (Exception e) {
            log.error("执行表物理删除失败：" + sql);
            e.printStackTrace();
            throw new BizException("5001","执行表物理删除失败："+ tableCode);
        } finally {
            // 删除SQL日志
            try {
                sqlLogMapper.delete(new LambdaQueryWrapper<SqlLogDO>().eq(SqlLogDO::getTableId, tableDO.getTableId()));
            } catch (Exception e) {
                log.error("删除SQL日志失败:" + e.getMessage());
            }
        }
        return true;
    }

    public static List<ModelTableRespVO> listToTree(List<ModelTableRespVO> list) {
        //用递归找子。
        List<ModelTableRespVO> treeList = new CopyOnWriteArrayList<>();
        for (ModelTableRespVO tree : list) {
            if ("0".equals(tree.getParentItemId())) {
                treeList.add(findChildren(tree, list));
            }
        }
        return treeList;
    }

    //寻找子节点
    private static ModelTableRespVO findChildren(ModelTableRespVO tree, List<ModelTableRespVO> list) {
        for (ModelTableRespVO node : list) {
            if (node.getParentItemId().equals(tree.getTableId().toString())) {
                if (tree.getChildren() == null) {
                    tree.setChildren(new CopyOnWriteArrayList<>());
                }
                tree.getChildren().add(findChildren(node, list));
            }
        }
        return tree;
    }
}
