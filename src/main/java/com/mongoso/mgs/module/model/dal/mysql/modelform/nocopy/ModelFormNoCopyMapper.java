package com.mongoso.mgs.module.model.dal.mysql.modelform.nocopy;

import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.module.model.dal.db.modelform.nocopy.ModelFormNoCopyDO;
import org.apache.ibatis.annotations.Mapper;
import com.mongoso.mgs.module.model.controller.admin.modelform.nocopy.vo.*;

/**
 * 单据不可复制字段 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ModelFormNoCopyMapper extends BaseMapperX<ModelFormNoCopyDO> {

    default PageResult<ModelFormNoCopyDO> selectPage(ModelFormNoCopyPageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<ModelFormNoCopyDO>lambdaQueryX()
                .likeIfPresent(ModelFormNoCopyDO::getModelFormCode, reqVO.getModelFormCode())
                .likeIfPresent(ModelFormNoCopyDO::getModelBizCode, reqVO.getModelBizCode())
                .eqIfPresent(ModelFormNoCopyDO::getTableId, reqVO.getTableId())
                .eqIfPresent(ModelFormNoCopyDO::getRowNo, reqVO.getRowNo())
                .eqIfPresent(ModelFormNoCopyDO::getSort, reqVO.getSort())
                .likeIfPresent(ModelFormNoCopyDO::getColumnCode, reqVO.getColumnCode())
                .likeIfPresent(ModelFormNoCopyDO::getColumnName, reqVO.getColumnName())
                .eqIfPresent(ModelFormNoCopyDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(ModelFormNoCopyDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .orderByDesc(ModelFormNoCopyDO::getDataId));
    }




    default List<ModelFormNoCopyDO> selectList(ModelFormNoCopyQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<ModelFormNoCopyDO>lambdaQueryX()
                .likeIfPresent(ModelFormNoCopyDO::getModelFormCode, reqVO.getModelFormCode())
                .likeIfPresent(ModelFormNoCopyDO::getModelBizCode, reqVO.getModelBizCode())
                .eqIfPresent(ModelFormNoCopyDO::getTableId, reqVO.getTableId())
                .eqIfPresent(ModelFormNoCopyDO::getRowNo, reqVO.getRowNo())
                .eqIfPresent(ModelFormNoCopyDO::getSort, reqVO.getSort())
                .likeIfPresent(ModelFormNoCopyDO::getColumnCode, reqVO.getColumnCode())
                .likeIfPresent(ModelFormNoCopyDO::getColumnName, reqVO.getColumnName())
                .eqIfPresent(ModelFormNoCopyDO::getRemark, reqVO.getRemark())
                .eqIfPresent(ModelFormNoCopyDO::getCreatedBy, reqVO.getCreatedBy())
                .betweenIfPresent(ModelFormNoCopyDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .eqIfPresent(ModelFormNoCopyDO::getUpdatedBy, reqVO.getUpdatedBy())
                .betweenIfPresent(ModelFormNoCopyDO::getUpdatedDt, reqVO.getStartUpdatedDt(), reqVO.getEndUpdatedDt())
                .eqIfPresent(ModelFormNoCopyDO::getDataId, reqVO.getDataId())
                .orderByDesc(ModelFormNoCopyDO::getDataId));
    }

    /**
     * 根据单据建模编码批量删除不可复制字段
     *
     * @param modelFormCode 单据建模编码
     * @return 删除的记录数
     */
    default int deleteByModelFormCode(String modelFormCode) {
        return delete(LambdaQueryWrapperX.<ModelFormNoCopyDO>lambdaQueryX()
                .eq(ModelFormNoCopyDO::getModelFormCode, modelFormCode));
    }


}