package com.mongoso.mgs.module.model.controller.admin.modelbiztable.detail;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import jakarta.validation.*;
import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;

import static com.mongoso.mgs.framework.common.domain.ResultX.success;

import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.model.controller.admin.modelbiztable.detail.vo.*;
import com.mongoso.mgs.module.model.service.modelbiztable.detail.ModelBizTableDetailService;

/**
 * 子表明细 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/model")
@Validated
public class ModelBizTableDetailController {

    @Resource
    private ModelBizTableDetailService bizTableDetailService;

    @OperateLog("子表明细添加或编辑")
    @PostMapping("/modelBizTableDetailAdit")
    @PreAuthorize("@ss.hasPermission('modelBizTableDetail:adit')")
    public ResultX<Long> modelBizTableDetailAdit(@Valid @RequestBody ModelBizTableDetailAditReqVO reqVO) {
        return success(reqVO.getDataId() == null
                            ? bizTableDetailService.modelBizTableDetailAdd(reqVO)
                            : bizTableDetailService.modelBizTableDetailEdit(reqVO));
    }

    @OperateLog("子表明细删除")
    @PostMapping("/modelBizTableDetailDelete")
    @PreAuthorize("@ss.hasPermission('modelBizTableDetail:delete')")
    public ResultX<Boolean> modelBizTableDetailDelete(@Valid @RequestBody ModelBizTableDetailPrimaryReqVO reqVO) {
        bizTableDetailService.modelBizTableDetailDelete(reqVO.getDataId());
        return success(true);
    }

    @OperateLog("子表明细详情")
    @PostMapping("/modelBizTableDetailDetail")
    @PreAuthorize("@ss.hasPermission('modelBizTableDetail:query')")
    public ResultX<ModelBizTableDetailRespVO> modelBizTableDetailDetail(@Valid @RequestBody ModelBizTableDetailPrimaryReqVO reqVO) {
        return success(bizTableDetailService.modelBizTableDetailDetail(reqVO.getDataId()));
    }

    @OperateLog("子表明细列表")
    @PostMapping("/modelBizTableDetailList")
    @PreAuthorize("@ss.hasPermission('modelBizTableDetail:query')")
    public ResultX<List<ModelBizTableDetailRespVO>> modelBizTableDetailList(@Valid @RequestBody ModelBizTableDetailQueryReqVO reqVO) {
        return success(bizTableDetailService.modelBizTableDetailList(reqVO));
    }

    @OperateLog("子表明细分页")
    @PostMapping("/modelBizTableDetailPage")
    @PreAuthorize("@ss.hasPermission('modelBizTableDetail:query')")
    public ResultX<PageResult<ModelBizTableDetailRespVO>> modelBizTableDetailPage(@Valid @RequestBody ModelBizTableDetailPageReqVO reqVO) {
        return success(bizTableDetailService.modelBizTableDetailPage(reqVO));
    }

}
