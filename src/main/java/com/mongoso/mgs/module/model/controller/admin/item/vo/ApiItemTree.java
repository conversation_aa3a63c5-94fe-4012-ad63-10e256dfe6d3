package com.mongoso.mgs.module.model.controller.admin.item.vo;

import lombok.Data;

import java.util.List;

/**
 *  API接口解析JSON树
 *  daijinbiao
 *  2025-4-2 10:59:11
 */
@Data
public class ApiItemTree {

    //@ApiModelProperty(value = "字段文名", required = true)
    private String itemName;

    //@ApiModelProperty(value = "字段英文名", required = true)
    private String englishName;

    //@ApiModelProperty(value = "字段类型", required = true)
    private String dataType;

    //@ApiModelProperty(value = "是否必填", required = true)
    private Boolean required = false;

    //@ApiModelProperty(value = "字段类型", required = true)
    private String remark;

    //@ApiModelProperty(value = "字段类型", required = true)
    private Integer level;

    private List<ApiItemTree> children;

}
