package com.mongoso.mgs.module.model.service.modelform;

import com.mongoso.mgs.framework.mybatis.core.util.IDUtilX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.*;
import com.mongoso.mgs.module.model.controller.admin.modelform.vo.*;
import com.mongoso.mgs.module.model.controller.admin.modelform.button.vo.*;
import com.mongoso.mgs.module.model.controller.admin.modelform.nocopy.vo.*;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.module.model.dal.db.modelform.ModelFormDO;
import com.mongoso.mgs.module.model.dal.db.modelform.button.ModelFormButtonDO;
import com.mongoso.mgs.module.model.dal.db.modelform.nocopy.ModelFormNoCopyDO;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.module.model.dal.mysql.modelform.ModelFormMapper;
import com.mongoso.mgs.module.model.service.modelform.button.ModelFormButtonService;
import com.mongoso.mgs.module.model.service.modelform.nocopy.ModelFormNoCopyService;
import com.mongoso.mgs.framework.enums.RedisKey;



/**
 * 单据建模主 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class ModelFormServiceImpl implements ModelFormService {

    @Resource
    private ModelFormMapper formMapper;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private ModelFormButtonService modelFormButtonService;

    @Resource
    private ModelFormNoCopyService modelFormNoCopyService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long modelFormAdd(ModelFormAditReqVO reqVO) {
        // 生成单据对象编码：DOC + 自增长
        String dataCode = generateDataCode();

        // 插入主表
        ModelFormDO form = BeanUtilX.copy(reqVO, ModelFormDO::new);
        form.setDataCode(dataCode);
        form.setDataId(IDUtilX.getId());
        formMapper.insert(form);

        // 处理单据建模按钮数据
        if (reqVO.getModelFormButtonList() != null && !reqVO.getModelFormButtonList().isEmpty()) {
            processFormButtons(dataCode, reqVO.getModelFormButtonList());
        }

        // 处理单据建模不可复制字段数据
        if (reqVO.getModelFormNoCopyList() != null && !reqVO.getModelFormNoCopyList().isEmpty()) {
            processFormNoCopyFields(dataCode, reqVO.getModelBizCode(), reqVO.getModelFormNoCopyList());
        }

        // 返回
        return form.getDataId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long modelFormEdit(ModelFormAditReqVO reqVO) {
        // 校验存在
        ModelFormDO existingForm = this.modelFormValidateExists(reqVO.getDataId());

        // 更新主表
        ModelFormDO form = BeanUtilX.copy(reqVO, ModelFormDO::new);
        // 保持原有的单据编码不变
        form.setDataCode(existingForm.getDataCode());
        formMapper.updateById(form);

        // 处理单据建模按钮数据 - 先删除再新增 当传递空数组的时候，说明需要删除当前的数据，因此这里判空，不判是否有数据
        if (reqVO.getModelFormButtonList() != null) {
            // 删除原有按钮数据
            deleteFormButtonsByCode(existingForm.getDataCode());
            // 重新插入按钮数据
            if (!reqVO.getModelFormButtonList().isEmpty()) {
                processFormButtons(existingForm.getDataCode(), reqVO.getModelFormButtonList());
            }
        }

        // 处理单据建模不可复制字段数据 - 先删除再新增  当传递空数组的时候，说明需要删除当前的数据，因此这里判空，不判是否有数据
        if (reqVO.getModelFormNoCopyList() != null) {
            // 删除原有不可复制字段数据
            deleteFormNoCopyFieldsByCode(existingForm.getDataCode());
            // 重新插入不可复制字段数据
            if (!reqVO.getModelFormNoCopyList().isEmpty()) {
                processFormNoCopyFields(existingForm.getDataCode(), reqVO.getModelBizCode(), reqVO.getModelFormNoCopyList());
            }
        }

        // 返回
        return form.getDataId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modelFormDelete(Long dataId) {
        // 校验存在
        ModelFormDO existingForm = this.modelFormValidateExists(dataId);

        // 删除关联的按钮数据
        deleteFormButtonsByCode(existingForm.getDataCode());

        // 删除关联的不可复制字段数据
        deleteFormNoCopyFieldsByCode(existingForm.getDataCode());

        // 删除主表
        formMapper.deleteById(dataId);
    }

    private ModelFormDO modelFormValidateExists(Long dataId) {
        ModelFormDO form = formMapper.selectById(dataId);
        if (form == null) {
            // throw exception(FORM_NOT_EXISTS);
            throw new BizException("5001", "单据建模主不存在");
        }
        return form;
    }

    @Override
    public ModelFormRespVO modelFormDetail(Long dataId) {
        // 查询主表数据
        ModelFormDO data = formMapper.selectById(dataId);
        if (data == null) {
            throw new BizException("5001", "单据建模数据不存在");
        }

        ModelFormRespVO result = BeanUtilX.copy(data, ModelFormRespVO::new);

        // 查询关联的按钮数据
        List<ModelFormButtonRespVO> buttonList = queryFormButtonsByCode(data.getDataCode());
        result.setModelFormButtonList(buttonList);

        // 查询关联的不可复制字段数据
        List<ModelFormNoCopyRespVO> noCopyList = queryFormNoCopyFieldsByCode(data.getDataCode());
        result.setModelFormNoCopyList(noCopyList);

        return result;
    }

    @Override
    public List<ModelFormRespVO> modelFormList(ModelFormQueryReqVO reqVO) {
        List<ModelFormDO> data = formMapper.selectList(reqVO);
        return BeanUtilX.copy(data, ModelFormRespVO::new);
    }

    @Override
    public PageResult<ModelFormRespVO> modelFormPage(ModelFormPageReqVO reqVO) {
        PageResult<ModelFormDO> data = formMapper.selectPage(reqVO);
        return BeanUtilX.copy(data, ModelFormRespVO::new);
    }

    /**
     * 生成单据对象编码：DOC + 自增长
     *
     * @return 单据对象编码
     */
    private String generateDataCode() {
        // 检查Redis中是否存在该key
        String redisKey = RedisKey.MODEL_FORM_CODE_INCR;
        Boolean hasKey = stringRedisTemplate.hasKey(redisKey);

        if (!hasKey) {
            // 如果Redis中不存在该key，先从数据库查询当前最大编码
            initializeRedisCounter(redisKey);
        }

        // 使用Redis自增生成编码
        Long increment = stringRedisTemplate.opsForValue().increment(redisKey);
        return "DOC" + increment;
    }

    /**
     * 初始化Redis计数器
     * 从数据库查询当前最大的单据编码，设置Redis初始值
     *
     * @param redisKey Redis键
     */
    private void initializeRedisCounter(String redisKey) {
        // 查询数据库中最大的单据编码
        String maxDataCode = formMapper.selectMaxDataCode();

        long maxNumber = 0;
        if (maxDataCode != null && maxDataCode.startsWith("DOC")) {
            try {
                // 提取DOC后面的数字
                String numberStr = maxDataCode.substring(3);
                maxNumber = Long.parseLong(numberStr);
            } catch (NumberFormatException e) {
                // 如果解析失败，从0开始
                log.info("解析最大单据编码失败: " + e.getMessage());
            }
        }

        // 设置Redis初始值为数据库中的最大值
        stringRedisTemplate.opsForValue().set(redisKey, String.valueOf(maxNumber));
    }

    /**
     * 处理单据建模按钮数据
     *
     * @param modelFormCode 单据建模编码
     * @param buttonList 按钮列表
     */
    private void processFormButtons(String modelFormCode, List<ModelFormButtonAditReqVO> buttonList) {
        if (buttonList == null || buttonList.isEmpty()) {
            return;
        }

        // 转换为DO对象列表
        List<ModelFormButtonDO> buttonDOList = new ArrayList<>();
        for (ModelFormButtonAditReqVO button : buttonList) {
            // 设置单据建模编码
            button.setModelFormCode(modelFormCode);
            // 转换为DO对象
            ModelFormButtonDO buttonDO = BeanUtilX.copy(button, ModelFormButtonDO::new);
            buttonDO.setDataId(IDUtilX.getId());
            buttonDOList.add(buttonDO);
        }

        // 批量插入按钮数据
        modelFormButtonService.modelFormButtonBatchAdd(buttonDOList);
    }

    /**
     * 处理单据建模不可复制字段数据
     *
     * @param modelFormCode 单据建模编码
     * @param modelBizCode 业务建模编码
     * @param noCopyList 不可复制字段列表
     */
    private void processFormNoCopyFields(String modelFormCode, String modelBizCode, List<ModelFormNoCopyAditReqVO> noCopyList) {
        if (noCopyList == null || noCopyList.isEmpty()) {
            return;
        }

        // 转换为DO对象列表
        List<ModelFormNoCopyDO> noCopyDOList = new ArrayList<>();
        for (ModelFormNoCopyAditReqVO noCopyField : noCopyList) {
            // 设置单据建模编码和业务建模编码
            noCopyField.setModelFormCode(modelFormCode);
            noCopyField.setModelBizCode(modelBizCode);
            // 转换为DO对象
            ModelFormNoCopyDO noCopyDO = BeanUtilX.copy(noCopyField, ModelFormNoCopyDO::new);
            noCopyDO.setDataId(IDUtilX.getId());
            noCopyDOList.add(noCopyDO);
        }

        // 批量插入不可复制字段数据
        modelFormNoCopyService.modelFormNoCopyBatchAdd(noCopyDOList);
    }

    /**
     * 根据单据建模编码批量删除按钮数据
     *
     * @param modelFormCode 单据建模编码
     */
    private void deleteFormButtonsByCode(String modelFormCode) {
        modelFormButtonService.modelFormButtonBatchDeleteByCode(modelFormCode);
    }

    /**
     * 根据单据建模编码批量删除不可复制字段数据
     *
     * @param modelFormCode 单据建模编码
     */
    private void deleteFormNoCopyFieldsByCode(String modelFormCode) {
        modelFormNoCopyService.modelFormNoCopyBatchDeleteByCode(modelFormCode);
    }

    /**
     * 根据单据建模编码查询按钮数据
     *
     * @param modelFormCode 单据建模编码
     * @return 按钮列表
     */
    private List<ModelFormButtonRespVO> queryFormButtonsByCode(String modelFormCode) {
        ModelFormButtonQueryReqVO queryReqVO = new ModelFormButtonQueryReqVO();
        queryReqVO.setModelFormCode(modelFormCode);

        return modelFormButtonService.modelFormButtonList(queryReqVO);
    }

    /**
     * 根据单据建模编码查询不可复制字段数据
     *
     * @param modelFormCode 单据建模编码
     * @return 不可复制字段列表
     */
    private List<ModelFormNoCopyRespVO> queryFormNoCopyFieldsByCode(String modelFormCode) {
        ModelFormNoCopyQueryReqVO queryReqVO = new ModelFormNoCopyQueryReqVO();
        queryReqVO.setModelFormCode(modelFormCode);
//        List<ModelFormNoCopyDO> noCopyDOList = modelFormNoCopyService.modelFormNoCopyList(queryReqVO);

        return modelFormNoCopyService.modelFormNoCopyList(queryReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modelFormBatchDelete(List<Long> dataIds) {
        if (dataIds == null || dataIds.isEmpty()) {
            return;
        }

        // 批量删除单据建模及其关联数据
        for (Long dataId : dataIds) {
            // 校验存在并获取单据建模信息
            ModelFormDO existingForm = this.modelFormValidateExists(dataId);

            // 删除关联的按钮数据
            deleteFormButtonsByCode(existingForm.getDataCode());

            // 删除关联的不可复制字段数据
            deleteFormNoCopyFieldsByCode(existingForm.getDataCode());
        }

        // 批量删除主表数据
        formMapper.deleteBatchIds(dataIds);
    }

}
