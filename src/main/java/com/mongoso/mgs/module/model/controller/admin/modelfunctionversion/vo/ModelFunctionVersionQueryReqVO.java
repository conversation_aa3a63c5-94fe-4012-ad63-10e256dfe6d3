package com.mongoso.mgs.module.model.controller.admin.modelfunctionversion.vo;

import lombok.*;

import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 自定义报表版本 QueryReqVO
 *
 * <AUTHOR>
 */
@Data
public class ModelFunctionVersionQueryReqVO {

    /** 函数id   */
    private Long funId;

    /** 版本号 */
    private Integer versionNo;

    /** 创建人   */
    private String createdBy;

    /** 创建时间   */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createdDt;

    /** 更新人   */
    private String updatedBy;

    /** 更新时间   */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] updatedDt;

}
