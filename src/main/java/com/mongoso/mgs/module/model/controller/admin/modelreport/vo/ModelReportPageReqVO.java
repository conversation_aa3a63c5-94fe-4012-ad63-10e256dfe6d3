package com.mongoso.mgs.module.model.controller.admin.modelreport.vo;

import lombok.*;

import com.mongoso.mgs.framework.common.domain.PageParam;





















import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 自定义报 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ModelReportPageReqVO extends PageParam {

    /** 报表名称 */
    private String reportName;

    /** 报表编号 */
    private String reportCode;

    /** 查询id   */
    private Long queryId;

    /** 备注   */
    private String remark;

    /** 版本号 */
    private Integer versionNo;

    /** 创建时间   */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createdDt;

    /** 是否发布 */
    private Integer isPublish;

    /** 类型 */
    private Integer dirType;
    /** 父节点 */
    private Long parentId;
}
