package com.mongoso.mgs.module.model.service.modelreportconfig;

import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.mongoso.mgs.module.model.controller.admin.modelreportconfig.vo.*;
import com.mongoso.mgs.module.model.dal.db.modelreportconfig.ModelReportConfigDO;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.module.model.dal.mysql.modelreportconfig.ModelReportConfigMapper;

// import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exception;
import com.mongoso.mgs.framework.common.exception.BizException;
// import static com.mongoso.mgs.module.model.enums.ErrorCodeConstants.*;


/**
 * 自定义报表配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ModelReportConfigServiceImpl implements ModelReportConfigService {

    @Resource
    private ModelReportConfigMapper reportConfigMapper;

    @Override
    public Long modelReportConfigAdd(ModelReportConfigAditReqVO reqVO) {
        // 插入
        ModelReportConfigDO reportConfig = BeanUtilX.copy(reqVO, ModelReportConfigDO::new);
        reportConfigMapper.insert(reportConfig);
        // 返回
        return reportConfig.getId();
    }

    @Override
    public Long modelReportConfigEdit(ModelReportConfigAditReqVO reqVO) {
        // 校验存在
        this.modelReportConfigValidateExists(reqVO.getId());
        // 更新
        ModelReportConfigDO reportConfig = BeanUtilX.copy(reqVO, ModelReportConfigDO::new);
        reportConfigMapper.updateById(reportConfig);
        // 返回
        return reportConfig.getId();
    }

    @Override
    public void modelReportConfigDel(Long id) {
        // 校验存在
        this.modelReportConfigValidateExists(id);
        // 删除
        reportConfigMapper.deleteById(id);
    }

    private ModelReportConfigDO modelReportConfigValidateExists(Long id) {
        ModelReportConfigDO reportConfig = reportConfigMapper.selectById(id);
        if (reportConfig == null) {
            // throw exception(REPORT_CONFIG_NOT_EXISTS);
            throw new BizException("5001", "自定义报表配置不存在");
        }
        return reportConfig;
    }

    @Override
    public ModelReportConfigDO modelReportConfigDetail(Long id) {
        return reportConfigMapper.selectById(id);
    }

    @Override
    public List<ModelReportConfigDO> modelReportConfigList(ModelReportConfigQueryReqVO reqVO) {
        return reportConfigMapper.selectList(reqVO);
    }

    @Override
    public PageResult<ModelReportConfigDO> modelReportConfigPage(ModelReportConfigPageReqVO reqVO) {
        return reportConfigMapper.selectPage(reqVO);
    }

}
