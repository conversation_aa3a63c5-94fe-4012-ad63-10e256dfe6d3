package com.mongoso.mgs.module.model.service.modelquery;

import cn.hutool.json.JSONArray;
import com.mongoso.mgs.common.util.DatabaseUtil;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.common.util.JsonUtilX;
import com.mongoso.mgs.framework.common.util.ObjUtilX;
import com.mongoso.mgs.framework.common.util.StrUtilX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.enums.DBTypeEnum;
import com.mongoso.mgs.module.model.controller.admin.modelquery.vo.*;
import com.mongoso.mgs.module.model.controller.admin.modelqueryparam.vo.ModelQueryParamAditReqVO;
import com.mongoso.mgs.module.model.controller.admin.modelqueryresult.vo.ModelQueryResultAditReqVO;
import com.mongoso.mgs.module.model.dal.db.modelfield.ModelFieldDO;
import com.mongoso.mgs.module.model.dal.db.modelquery.ModelQueryDO;
import com.mongoso.mgs.module.model.dal.db.modelqueryparam.ModelQueryParamDO;
import com.mongoso.mgs.module.model.dal.db.modelqueryresult.ModelQueryResultDO;
import com.mongoso.mgs.module.model.dal.mysql.modelfield.ModelFieldMapper;
import com.mongoso.mgs.module.model.dal.mysql.modelquery.ModelQueryMapper;
import com.mongoso.mgs.module.model.dal.mysql.modelqueryparam.ModelQueryParamMapper;
import com.mongoso.mgs.module.model.dal.mysql.modelqueryresult.ModelQueryResultMapper;
import com.mongoso.mgs.module.model.service.dbfactory.DBCreator;
import com.mongoso.mgs.module.model.service.dbfactory.DBCreatorFactory;
import jakarta.annotation.Resource;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;
//import static com.mongoso.mgs.module.model.enums.ErrorCodeConstants.*;


/**
 * 自定义查询 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Log4j2
public class ModelQueryServiceImpl implements ModelQueryService {

    @Resource
    private ModelQueryMapper queryMapper;
    @Resource
    private ModelQueryParamMapper queryParamMapper;
    @Resource
    private ModelQueryResultMapper queryResultMapper;
    @Resource
    private ModelFieldMapper fieldMapper;
    @Resource
    private DatabaseUtil dbUtil;
    @Autowired
    private DBCreatorFactory dbCreatorFactory;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long modelQueryAdd(ModelQueryAditReqVO reqVO) {
        // 插入
        ModelQueryDO query = BeanUtilX.copy(reqVO, ModelQueryDO::new);
//        if(reqVO.getType() == 1){
//            throw new BizException("5001", "查询语句不能为空");
//        }
        // 验重
        Long duplicheck = repeatValid(reqVO);
        if (duplicheck > 0){
            throw new BizException("5001", "您输入的名称值重复，请重新输入");
        }
        queryMapper.insert(query);
        if(reqVO.getDirType() == 1) {
            //插入参数
            saveParam(reqVO, query.getQueryId());
        }

        // 返回
        return query.getQueryId();
    }

    private void saveParam(ModelQueryAditReqVO reqVO, Long queryId) {
        queryParamMapper.delete(LambdaQueryWrapperX.<ModelQueryParamDO>lambdaQueryX()
                .eq(ModelQueryParamDO::getQueryId, reqVO.getQueryId()));
        for (ModelQueryParamAditReqVO param : reqVO.getParams()) {
            if (!param.getParamCode().startsWith("@")){
                throw new BizException("5001", "参数英文名必须是@开头");
            }
            param.setQueryId(queryId);
            ModelQueryParamDO paramDO = BeanUtilX.copy(param, ModelQueryParamDO::new);
            queryParamMapper.insert(paramDO);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long modelQueryEdit(ModelQueryAditReqVO reqVO) {
//        if(reqVO.getType() == 1){
//            throw new BizException("5001", "查询语句不能为空");
//        }
        // 校验存在
        this.modelQueryValidateExists(reqVO.getQueryId());
        // 验重
        Long duplicheck = repeatValid(reqVO);
        if (duplicheck > 0){
            throw new BizException("5001", "您输入的名称值重复，请重新输入");
        }
        // 更新
        ModelQueryDO query = BeanUtilX.copy(reqVO, ModelQueryDO::new);
        queryMapper.updateById(query);
        if(reqVO.getDirType() == 1) {
            //插入参数
            saveParam(reqVO, query.getQueryId());
            // 保存结果
            queryResultMapper.delete(LambdaQueryWrapperX.<ModelQueryResultDO>lambdaQueryX()
                    .eqIfPresent(ModelQueryResultDO::getQueryId, reqVO.getQueryId()));
            Integer sort = 0;
            for (ModelQueryResultAditReqVO result : reqVO.getResults()) {
                result.setQueryId(query.getQueryId());
                result.setSort(sort++);
                ModelQueryResultDO resultDO = BeanUtilX.copy(result, ModelQueryResultDO::new);
                queryResultMapper.insert(resultDO);
            }
        }
        // 返回
        return query.getQueryId();
    }

    @Override
    public Long repeatValid(ModelQueryAditReqVO reqVO) {
        long count = 0L;
        if (StrUtilX.isEmpty(reqVO.getQueryId())) {
            // 新增验重
            if (StrUtilX.isNotEmpty(reqVO.getQueryName())) {
                count = queryMapper.selectCountByName(reqVO.getQueryName(),null);
                if (count>0){
//                    return count;
                    throw new BizException("5001", "您输入的中文名称值重复，请重新输入");
                }
            }
            if (StrUtilX.isNotEmpty(reqVO.getQueryCode())) {
                count = queryMapper.selectCountByCode(reqVO.getQueryCode(),null);
                if (count>0){
//                    return count;
                    throw new BizException("5001", "您输入的编码/英文名称值重复，请重新输入");
                }
            }
        } else {
            // 编辑验重
            if (StrUtilX.isNotEmpty(reqVO.getQueryName())) {
                count = queryMapper.selectCountByName(reqVO.getQueryName(),reqVO.getQueryId());
                if (count>0){
                    throw new BizException("5001", "您输入的中文名称值重复，请重新输入");
//                    return count;
                }
            }
            if (StrUtilX.isNotEmpty(reqVO.getQueryCode())) {
                count = queryMapper.selectCountByCode(reqVO.getQueryCode(), reqVO.getQueryId());
                if (count>0){
                    throw new BizException("5001", "您输入的编码/英文名称值重复，请重新输入");
//                    return count;
                }
            }
        }
        return count;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modelQueryDel(Long queryId) {
        // 校验存在
        ModelQueryDO queryDO = this.modelQueryValidateExists(queryId);
        // 如果是目录，判断下面有没有子节点
        if(queryDO.getDirType() == 0){
            Long count = queryMapper.selectCount(LambdaQueryWrapperX.<ModelQueryDO>lambdaQueryX().eq(ModelQueryDO::getParentId, queryId));
            if(count > 0){
                throw new BizException("5001", "当前目录下存在数据，不可删除");
            }
        }
        // 删除
        queryMapper.deleteById(queryId);
        //删除参数，结果
        queryParamMapper.delete(LambdaQueryWrapperX.<ModelQueryParamDO>lambdaQueryX()
                .eq(ModelQueryParamDO::getQueryId, queryId));
        queryResultMapper.delete(LambdaQueryWrapperX.<ModelQueryResultDO>lambdaQueryX()
                .eq(ModelQueryResultDO::getQueryId, queryId));
    }

    private ModelQueryDO modelQueryValidateExists(Long queryId) {
        ModelQueryDO query = queryMapper.selectById(queryId);
        if (query == null) {
            //throw exception(QUERY_NOT_EXISTS);
            throw new BizException("5001", "自定义查询不存在");
        }
        return query;
    }

    @Override
    public ModelQueryRespVO modelQueryDetail(Long queryId) {
        // 校验存在
        ModelQueryDO modelQueryDO = this.modelQueryValidateExists(queryId);
        ModelQueryRespVO respVO = BeanUtilX.copy(modelQueryDO, ModelQueryRespVO::new);
        // 参数
        List<ModelQueryParamDO> paramDOS = queryParamMapper.selectList(LambdaQueryWrapperX.<ModelQueryParamDO>lambdaQueryX()
                .eq(ModelQueryParamDO::getQueryId, queryId));
        respVO.setParams(paramDOS);
        // 结果
        List<ModelQueryResultDO> resultDOS = queryResultMapper.selectList(LambdaQueryWrapperX.<ModelQueryResultDO>lambdaQueryX()
                .eq(ModelQueryResultDO::getQueryId, queryId).orderByAsc(ModelQueryResultDO::getSort));
        respVO.setResults(resultDOS);
        return respVO;
    }

    @Override
    public List<ModelQueryDO> modelQueryList(ModelQueryQueryReqVO reqVO) {
        return queryMapper.selectList(reqVO);
    }

    @Override
    public PageResult<ModelQueryDO> modelQueryPage(ModelQueryPageReqVO reqVO) {
        return queryMapper.selectPage(reqVO);
    }

    @Override
    public PageResult<Map<String, Object>> modelQueryExc(ModelQueryPrimaryReqVO req) {
        Long queryId = req.getQueryId();
        // 校验存在
        ModelQueryDO modelQueryDO = this.modelQueryValidateExists(queryId);
        // 获取SQL里的参数
//        Set<String> fillParams = DatabaseUtil.extractVariables(modelQueryDO.getQueryStatment());
        Set<String> fillParams = DatabaseUtil.extractVariables(req.getQueryStatment());//todo 要改为前端传的SQL语句
        StringBuilder tempTableSQL = new StringBuilder();
        String realSQL = modelQueryDO.getQueryStatment();

        if(ObjUtilX.isNotEmpty(fillParams)){
            DBCreator creator = dbCreatorFactory.getDBCreatorDynamic(modelQueryDO.getDataSourceConfigId());
            // 查询自定义参数
//            List<ModelQueryParamDO> queryParamDOS = queryParamMapper.selectList(LambdaQueryWrapperX.<ModelQueryParamDO>lambdaQueryX()
//                    .eq(ModelQueryParamDO::getQueryId, queryId)
//            );
//            Map<String, String> pMap = queryParamDOS.stream().collect(Collectors
//                    .toMap(ModelQueryParamDO::getParamCode, ModelQueryParamDO::getParamName));
            //拿到表变量，用于创建临时表
            List<ModelQueryParamAditReqVO> tempTableList = req.getParams().stream()
                    .filter(ModelQueryParamAditReqVO -> ModelQueryParamAditReqVO.getParamType() == 2) // 筛选表变量类型的
                    .collect(Collectors.toList());
            if(ObjUtilX.isNotEmpty(tempTableList)){
                // 创建临时表
                for (ModelQueryParamAditReqVO param : tempTableList) {
                    if(ObjUtilX.isEmpty(param.getDataFields())){
                        throw new BizException("5001", param.getParamCode() + "表变量没有数据列");
                    }
                    String[] fieldIds = param.getDataFields().split(",");
                    Long[] longFieldIds = Arrays.stream(fieldIds)
                            .map(Long::parseLong).toArray(Long[]::new);
                    List<ModelFieldDO> fieldDOS = fieldMapper.selectList(LambdaQueryWrapperX.<ModelFieldDO>lambdaQueryX()
                            .in(ModelFieldDO::getFieldId, longFieldIds));
                    tempTableSQL.append(creator.genCreateTempTableSQL(modelQueryDO, param.getParamCode(), fieldDOS));

                    // 插入前端传的数据
                    String paramVal = param.getParamVal();
                    JSONArray paramArray = JsonUtilX.parseArray(paramVal);
                    tempTableSQL.append(creator.genCreateTempTableInsertSQL(modelQueryDO, param.getParamCode(), paramArray, fieldDOS));
                }
            }

            // 前端传过来的自定义参数和值
            Map<String, ModelQueryParamAditReqVO> excMap = req.getParams().stream().collect(Collectors
                    .toMap(ModelQueryParamAditReqVO::getParamCode, o -> o));
            //替换SQL里的参数
            for (String param : fillParams) {
                if(!excMap.containsKey(param)){
                    throw new BizException("5001", param + "参数未赋值。");
                }
                ModelQueryParamAditReqVO reqParam = excMap.get(param);
                if(reqParam.getParamType() == 0){//数值
                    // 要不要考虑 dataType
                    realSQL = realSQL.replaceAll(param, "'"+reqParam.getParamVal()+"'");
                }if(reqParam.getParamType() == 1){//替换
                    realSQL = realSQL.replaceAll(param, reqParam.getParamVal());
                }else{//表变量
                    realSQL = realSQL.replaceAll(param, param.replaceAll("@", ""));
                }
            }
        }

        tempTableSQL.append(realSQL);
//        System.out.println(tempTableSQL.toString());
        try{
            // 动态表头
            // 先查询配置结果
//            List<ModelQueryResultDO> resultDOS = queryResultMapper.selectList(LambdaQueryWrapperX.<ModelQueryResultDO>lambdaQueryX()
//                    .eq(ModelQueryResultDO::getQueryId, queryId).orderByAsc(ModelQueryResultDO::getSort));
//            Map<String, String> rstMap = new LinkedHashMap<>(); // 使用 LinkedHashMap 保持插入顺序
//            if(ObjUtilX.isNotEmpty(resultDOS)){
//                rstMap = resultDOS.stream().collect(Collectors.toMap(
//                        ModelQueryResultDO::getResultField,
//                        ModelQueryResultDO::getResultFieldName,
//                        (existing, replacement) -> existing, // 在此处处理重复键的情况（如果需要）
//                        LinkedHashMap::new // 指定使用 LinkedHashMap
//                ));
//                rstMap = resultDOS.stream().collect(Collectors.toMap(ModelQueryResultDO::getResultField, ModelQueryResultDO::getResultFieldName));
//            }
            List<Map<String, Object>> maps = dbUtil.exeQueryLastSQL(modelQueryDO.getDataSourceConfigId(), tempTableSQL.toString());
            List<String> titleList = new LinkedList<>();
            if(maps.size() > 0) {
                for (Map.Entry<String, Object> entry : maps.get(0).entrySet()) {
                    String key = entry.getKey();
                    titleList.add(key);
                }
//                for (Map.Entry<String, String> entry : rstMap.entrySet()) {
////                    String key = entry.getKey();
////                    titleList.add(key);
//                    String value = entry.getValue();
//                    titleList.add(value);
//                }
            }
            PageResult pageResult = new PageResult();
            pageResult.setCurrentPage(1L);
            pageResult.setPageSize((long) maps.size());
            pageResult.setTotalPage(1L);
            pageResult.setTotalCount((long) maps.size());
            return PageResult.init(pageResult, maps, titleList);
        } catch (BizException e) {
            throw new BizException(e.getCode(), e.getMessage());
        } catch (Exception e) {
            try {
                throw e;
            } catch (Exception ex) {
                throw new RuntimeException(ex);
            }
        }
    }

    /**
     * 这个也要弃用，后续改为从数据源配置获取
     * @return
     */
    @Override
    public String dbType(Long dataSourceConfigId) {
        if(ObjUtilX.isEmpty(dataSourceConfigId)){
            throw new BizException("5001","数据源配置id不能为空");
        }
        //String dbType = dbUtil.getDbType();
        String dbType = dbUtil.getDbTypeDynamic(dataSourceConfigId);
        if (dbType.toLowerCase().contains(DBTypeEnum.MYSQL.getValue())) {
            dbType = DBTypeEnum.MYSQL.getValue();
        } else if (dbType.toLowerCase().contains(DBTypeEnum.PG.getValue())) {
            dbType = DBTypeEnum.PG.getValue();
        } else {
            dbType = "不支持此数据库类型";
        }
        return dbType;
    }

    @Override
    public Integer modelQueryDrag(ModelQueryDragReqVO reqVO) {
        // 校验存在
        ModelQueryDO exists = this.modelQueryValidateExists(reqVO.getQueryId());
        // 更新
        // 校验父节点存在
        ModelQueryDO parent = queryMapper.selectById(reqVO.getParentId());
        if (parent == null) {
            throw new BizException("5001","父节点不存在");
        }
        if (parent.getDirType() != 0) {
            throw new BizException("5001","父节点必须是目录");
        }
        exists.setParentId(reqVO.getParentId());
        return queryMapper.updateById(exists);
    }

    @Override
    public List<ModelQueryRespVO> modelQueryTree() {
        ModelQueryQueryReqVO reqVO = new ModelQueryQueryReqVO();
        //todo 添加条件
        List<ModelQueryDO> list = queryMapper.selectList(reqVO);
        // 正序
//        Collections.sort(list);

        List<ModelQueryRespVO> collect = list.stream().map(item -> {
            ModelQueryRespVO convert = BeanUtilX.copy(item, ModelQueryRespVO::new);
            convert.setItemId(convert.getQueryId().toString());
            convert.setItemName(convert.getQueryName());
            convert.setItemCode(convert.getQueryCode());
            convert.setParentItemId(convert.getParentId().toString());
//            convert.setPath(null);
            return convert;
        }).collect(Collectors.toList());

        List<ModelQueryRespVO> authMenuResps = this.listToTree(collect);

        return authMenuResps;
    }

    @Override
    public List<Map<String, Object>> modelQueryBatch(ModelQueryBatchReqVO reqVO) {
        return dbUtil.executeBatchQueries(reqVO.getQueryId(), reqVO.getQueryStatment(), reqVO.getDataSourceConfigId());
    }

    public static List<ModelQueryRespVO> listToTree(List<ModelQueryRespVO> list) {
        //用递归找子。
        List<ModelQueryRespVO> treeList = new CopyOnWriteArrayList<>();
        for (ModelQueryRespVO tree : list) {
            if ("0".equals(tree.getParentItemId())) {
                treeList.add(findChildren(tree, list));
            }
        }
        return treeList;
    }

    //寻找子节点
    private static ModelQueryRespVO findChildren(ModelQueryRespVO tree, List<ModelQueryRespVO> list) {
        for (ModelQueryRespVO node : list) {
            if (node.getParentItemId().equals(tree.getQueryId().toString())) {
                if (tree.getChildren() == null) {
                    tree.setChildren(new CopyOnWriteArrayList<>());
                }
                tree.getChildren().add(findChildren(node, list));
            }
        }
        return tree;
    }
}
