package com.mongoso.mgs.module.model.controller.admin.modeltable.vo;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 图形建模主 PrimaryReqVO
 *
 * <AUTHOR>
 */
@Data
public class ModelTableImportListReqVO {

    /** 模型表实名 */
    @NotEmpty(message = "模型表实名不能为空")
    private List<String> tableCodes;

    /** 父节点 */
    @NotNull(message = "父节点不能为空")
    private Long parentId;

    /** 数据源id */
    @NotNull(message = "数据源不能为空")
    private Long dataSourceConfigId;
}
