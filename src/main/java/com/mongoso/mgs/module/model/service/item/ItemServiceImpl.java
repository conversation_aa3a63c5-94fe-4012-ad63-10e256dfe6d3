package com.mongoso.mgs.module.model.service.item;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.LocalDateTimeUtilX;
import com.mongoso.mgs.framework.common.util.ObjUtilX;
import com.mongoso.mgs.framework.common.util.StrUtilX;
import com.mongoso.mgs.framework.file.core.domain.FileEnum;
import com.mongoso.mgs.framework.file.core.domain.FileReq;
import com.mongoso.mgs.framework.file.core.domain.FileResp;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.framework.service.file.SaasFileService;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.module.api.dal.db.apicaseinfo.ApiCaseInfoDO;
import com.mongoso.mgs.module.api.dal.mysql.apicaseinfo.ApiCaseInfoMapper;
import com.mongoso.mgs.module.codegen.common.util.StringUtils;
import com.mongoso.mgs.module.enums.DataTypeEnum;
import com.mongoso.mgs.module.model.controller.admin.item.vo.*;
import com.mongoso.mgs.module.model.dal.db.item.ItemDO;
import com.mongoso.mgs.module.model.dal.db.modeltable.ModelTableDO;
import com.mongoso.mgs.module.model.dal.mysql.item.ItemMapper;
import com.mongoso.mgs.module.model.dal.mysql.modeltable.ModelTableMapper;
import freemarker.template.Configuration;
import freemarker.template.Template;
import jakarta.annotation.Resource;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.BufferedWriter;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * <AUTHOR>
 * @date 2025/2/6
 * @description
 */

@Service
@Log4j2
public class ItemServiceImpl implements ItemService{

    @Autowired
    private Environment environment;
    @Resource
    private ItemMapper itemMapper;
    @Resource
    private ModelTableMapper tableMapper;
    @Resource
    private ApiCaseInfoMapper apiCaseInfoMapper;

    @Resource
    private SaasFileService fileService;

    @Override
    public ItemDO getOne(LambdaQueryWrapperX qw) {
        return itemMapper.selectOne(qw);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertItemInfo(CreateItemParams params) {
        if (ObjUtilX.isEmpty(params)) {
            log.warn("[新增文件]传入参数为空！");
            throw new BizException("5001", "传入参数不能为空！");
        }

        if (ObjUtilX.isEmpty(params.getProjectId())) {
            throw new BizException("5001", "项目不能为空！");
        }

        if (ObjUtilX.isEmpty(params.getCategory())) {
            throw new BizException("5001", "大类不能为空！");
        }

        if (ObjUtilX.isEmpty(params.getItemType())) {
            throw new BizException("5001", "小类不能为空！");
        }

        if (ObjUtilX.isEmpty(params.getItemName())) {
            throw new BizException("5001", "请填写名称！");
        }

        if(params.getItemType() == 1){
            if (ObjUtilX.isEmpty(params.getParentItemId())) {
                throw new BizException("5001", "请指定父节点！");
            }
            // 新增的接口绑定现有文件夹的tableId
            //ItemDO parent = itemMapper.selectById(params.getParentItemId());
            //if (ObjUtilX.isEmpty(parent)) {
            //    throw new BizException("5001", "父节点不存在！");
            //}
            //params.setTableId();
            String itemContent = params.getContent();
            if(params.getCategory() == 1 && ObjUtilX.isEmpty(itemContent)){//接口文档初始化
                itemContent = "[{\"mindType\":\"api\",\"itemName\":\"API\",\"dragging\":false,\"touched\":false,\"readonly\":true,\"children\":[{\"mindType\":\"api\",\"itemName\":\"接口地址\",\"dragging\":false,\"touched\":false,\"readonly\":true,\"children\":[],\"level\":2,\"index\":0,\"fixed\":true,\"sn\":\"0.0\",\"editing\":false},{\"englishName\":\"request\",\"touched\":false,\"level\":2,\"dataType\":\"Object\",\"index\":1,\"editing\":false,\"itemName\":\"请求参数\",\"expanded\":true,\"dragging\":false,\"readonly\":true,\"children\":[],\"mapped\":1,\"fixed\":true,\"sn\":\"0.1\",\"selected\":true},{\"englishName\":\"response\",\"touched\":false,\"level\":2,\"dataType\":\"Object\",\"index\":2,\"editing\":false,\"itemName\":\"响应参数\",\"expanded\":true,\"dragging\":false,\"readonly\":true,\"children\":[],\"fixed\":true,\"sn\":\"0.2\"}],\"level\":1,\"unexpandable\":true,\"sn\":\"0\",\"editing\":false}]";
            }
            //itemContent = "[{\"itemName\":\"中心主题\",\"children\":[],\"mindType\":\"arch\",\"sn\":\"0\",\"level\":1,\"dragging\":false,\"editing\":false,\"selected\":true,\"touched\":false}]";
            params.setContent(itemContent);
        }

        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        if(ObjUtilX.isNotEmpty(loginUser)) {
            params.setUserId(loginUser.getUserId());
        }

        params.setCreateTime(new Date());
        if(ObjUtilX.isEmpty(params.getItemSeq())){
            params.setItemSeq(0);
        }
        if(ObjUtilX.isNotEmpty(params.getContent())){
            params.setApiUrl(getApiUrl(params));
        }
        if(ObjUtilX.isNotEmpty(params.getParentItemId())){
            itemMapper.updateItemSeqAddOne(params.getParentItemId(),-1);
        }
        Long id = IdWorker.getId();
        params.setItemId(id);
        params.setParentItemId(ObjUtilX.isEmpty(params.getParentItemId())?0:params.getParentItemId());
        itemMapper.insertItemInfo(params);
        return id;
    }

    @Override
    public String saveItems(CreateItemParamsBatch params) {
        List<CreateItemParams> apiList = params.getApiList();
        if(apiList.isEmpty()){
            throw new BizException("5001", "请输入接口文档");
        }
        
        int totalCount = 0;
        int overwriteCount = 0;
        
        for (int i = 0; i < apiList.size(); i++) {
            CreateItemParams itemParams = apiList.get(i);
            itemParams.setProjectId(params.getProjectId());
            itemParams.setParentItemId(params.getParentItemId());
            itemParams.setCategory(1);
            itemParams.setItemType(1);
            itemParams.setItemSeq(i);
            
            if(params.getOverite() == 1){
                ItemDO itemDO = itemMapper.queryItemByUrl(params.getProjectId(), itemParams.getApiUrl());
                if(null != itemDO){
                    itemParams.setItemId(itemDO.getItemId());
                    itemParams.setParentItemId(itemDO.getParentItemId());
                    itemParams.setSaveType(0);
                    editItemInfo(itemParams);
                    overwriteCount++;
                }else {
                    insertItemInfo(itemParams);
                }
            }else{
                insertItemInfo(itemParams);
            }
            totalCount++;
        }
        
        // 根据是否覆盖返回不同的结果信息
        if(params.getOverite() == 1){
            return String.format("成功导入%d个接口，覆盖了%d个接口", totalCount, overwriteCount);
        }else{
            return String.format("成功导入%d个接口", totalCount);
        }
    }
    
    @Override
    public Boolean editItemInfo(CreateItemParams params) {
        if (ObjUtilX.isEmpty(params)) {
            log.warn("[新增文件]传入参数为空！");
            throw new BizException("5001", "传入参数不能为空！");
        }

        if (ObjUtilX.isEmpty(params.getItemId())) {
            throw new BizException("5001", "请选择一个对象！");
        }

        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        if(ObjUtilX.isNotEmpty(loginUser)) {
            params.setUserId(loginUser.getUserId());
        }
        ItemDO item = itemMapper.selectById(params.getItemId());
        if (null == item) {
            throw new BizException("5001", "选中的对象不存在");
        }
        if(null != params.getSaveType() && params.getSaveType() == 0) {
            if (ObjUtilX.isEmpty(params.getContent())) {
                throw new BizException("5001", "请输入内容！");
            }
            item.setContent(params.getContent());
            // 解析 JSON 字符串为 JsonNode
            if(StrUtilX.isEmpty(params.getApiUrl()) || "/".equals(params.getApiUrl())){
                item.setApiUrl(getApiUrl(params));
            }
        }else {//这里没用到
            if (ObjUtilX.isEmpty(params.getDescription())) {
                throw new BizException("5001", "请输入描述！");
            }
            item.setDescription(params.getDescription());
        }
        itemMapper.updateById(item);
        return true;
    }

    private static String getApiUrl(CreateItemParams params) {
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            JsonNode rootNode = objectMapper.readTree(params.getContent());
            if(!rootNode.isEmpty()){
                // 直接访问第一个元素的 children 数组
                JsonNode childrenNode = rootNode.get(0).get("children");
                // 查找 "接口地址" 这个节点
                for (JsonNode child : childrenNode) {
                    if ("接口地址".equals(child.get("itemName").asText())) {
                        // 访问该节点的 children
                        JsonNode itemChildren = child.get("children");
                        if (itemChildren.isArray() && itemChildren.size() > 0) {
                            return itemChildren.get(0).get("itemName").asText();
                        }
                    }
                }
            }
        } catch (JsonProcessingException e) {
            throw new BizException("5001", "JSON内容解析错误，请检查");
        }
        return "";
    }

    public static String findItemName(String jsonString, String target) throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();

        // 解析 JSON 字符串为 JsonNode
        JsonNode rootNode = objectMapper.readTree(jsonString);

        // 开始递归查找
        return searchInJsonArray(rootNode, target);
    }

    private static String searchInJsonArray(JsonNode arrayNode, String target) {
        if (arrayNode.isArray()) {
            for (JsonNode node : arrayNode) {
                // 检查当前节点是否有 "itemName" 属性，并与目标比较
                if (node.has("itemName") && target.equals(node.get("itemName").asText())) {
                    return node.get("itemName").asText(); // 返回找到的 itemName 值
                }

                // 递归调用到 "children" 节点
                if (node.has("children")) {
                    String found = searchInJsonArray(node.get("children"), target);
                    if (found != null) {
                        return found; // 如果在子节点中找到了，则返回
                    }
                }
            }
        }
        return null; // 没有找到
    }

    @Override
    public Integer itemDrag(UpdateItemParams params) {
        if (ObjUtilX.isEmpty(params)) {
            throw new BizException("5001", "传入参数不能为空！");
        }

        if (ObjUtilX.isEmpty(params.getItemId())) {
            throw new BizException("5001", "请选择一个对象！");
        }

        if (ObjUtilX.isEmpty(params.getParentItemId())) {
            throw new BizException("5001", "请选择父节点！");
        }

        ItemDO item = itemMapper.selectById(params.getItemId());
        if (null == item) {
            throw new BizException("5001", "选中的对象不存在");
        }

        ItemDO item2 = itemMapper.selectById(params.getParentItemId());
        if (null == item2) {
            throw new BizException("5001", "父节点对象不存在");
        }
        item.setParentItemId(params.getParentItemId());
        return itemMapper.updateById(item);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer dragItemInfo(DragItemParams params) {
        try {
            log.info("dragItemInfo, params: " + params);

            // 获取目标项信息
            ItemDO destItemInfo = itemMapper.selectById(params.getDestItemId());
            if (ObjUtilX.isEmpty(destItemInfo)) {
                throw new BizException("5001", "目标项不存在");
            }

            // 获取被拖动的项目
            ItemDO itemDo = itemMapper.selectById(params.getItemId());
            if (ObjUtilX.isEmpty(itemDo)) {
                throw new BizException("5001", "拖拽的项目不存在");
            }

            // 检查移动的有效性
            checkMoveType(itemDo, destItemInfo.getParentItemId());

            // 更新目标项目的父ID
            itemDo.setParentItemId(destItemInfo.getParentItemId());

            // 获取当前父节点下的所有同级条目
            List<ItemDO> siblings = itemMapper.selectList(LambdaQueryWrapperX.<ItemDO>lambdaQueryX()
                    .eq(ItemDO::getParentItemId, destItemInfo.getParentItemId())
            );

            // 计算新序号
            int newItemSeq;
            if (params.getDragType() == 0) { // 拖到目标之前
                newItemSeq = destItemInfo.getItemSeq(); // 新序号为目标项的序号
            } else { // 拖到目标之后
                newItemSeq = destItemInfo.getItemSeq() + 1; // 新序号为目标项的序号加一
            }

            // 确保新序号大于等于0
            newItemSeq = Math.max(newItemSeq, 0);

            // 更新其他同级项目的序号，避免冲突
            for (ItemDO sibling : siblings) {
                if (!sibling.getItemId().equals(itemDo.getItemId())) {
                    if (sibling.getItemSeq() >= newItemSeq) {
                        sibling.setItemSeq(sibling.getItemSeq() + 1); // 序号加一
                    }
                }
            }

            // 一次性更新所有同级项的序号
            itemMapper.batchUpdateItemSeq(siblings);

            // 设置新项目的序号并更新
            itemDo.setItemSeq(newItemSeq);
            itemMapper.updateById(itemDo);

            return 1; // 返回更新成功状态
        }catch (Exception e) {
            log.error("[拖动文件]移动文件失败！参数[" + params + "].", e);
            throw new BizException("5001", "操作失败");
        }
    }

    /**
     * 验证移动方式
     * @param itemDo
     * @param newParentItemId
     * @throws BizException
     */
    private void checkMoveType(ItemDO itemDo, Long newParentItemId) throws BizException {
        if(newParentItemId == null || newParentItemId == 0L){
            //非项目外文件夹和项目文件夹，不能当作根节点
            //if(itemDo.getItemType() != 0 && itemDo.getItemType() != 1){
            if(itemDo.getItemType() != 0 && itemDo.getItemType() != 1){
                throw new BizException("5001", "该文件不能拖拽！");
            }
        }else{
            ItemDO destParentItemDo = itemMapper.selectById(newParentItemId);
            if(ObjUtilX.isEmpty(destParentItemDo)){
                throw new BizException("5001", "父节点不存在");
            }
            // 文件夹不能移动到项目内
            if(itemDo.getItemType() == 0 && (destParentItemDo.getItemType() != 0)){
                throw new BizException("5001", "文件夹不能移动到项目内！");
            }
        }
    }

    @Override
    public List<ItemTreeVo> queryItemTreeList(QueryItemParams params) throws BizException {
        //log.info("********************查询文件树开始");
        if (ObjUtilX.isEmpty(params)) {
            throw new BizException("5001", "传入参数不能为空！");
        }

        if (ObjUtilX.isEmpty(params.getProjectId())) {
            throw new BizException("5001", "项目不能为空！");
        }

        if (ObjUtilX.isEmpty(params.getCategory())) {
            throw new BizException("5001", "大类不能为空！");
        }

        List<ItemTreeVo> itemTreeList = getItemTree(params);
        //log.info("********************查询文件树结束");
        return itemTreeList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteItemInfo(Long itemId) {
        if (ObjUtilX.isEmpty(itemId)) {
            throw new BizException("5001", "选项不能为空");
        }

        ItemDO item = itemMapper.selectById(itemId);

        if (null == item) {
            throw new BizException("5001", "选中的对象不存在");
        }

        QueryItemParams queryItemParams = new QueryItemParams();
        queryItemParams.setItemId(itemId);
        //queryItemParams.setUserId(params.getUpdateUserId());

        // 如果是目录，判断下面有没有子节点
        Long count = itemMapper.selectCount(LambdaQueryWrapperX.<ItemDO>lambdaQueryX().eq(ItemDO::getParentItemId, item.getItemId()));
        if(count > 0){
            throw new BizException("5001", "当前目录下存在数据，不可删除");
        }

        // 如果是接口文档，还需要删除 保存的用例
        if(item.getCategory() == 1) {
            apiCaseInfoMapper.delete(LambdaQueryWrapperX.<ApiCaseInfoDO>lambdaQueryX()
                    .eq(ApiCaseInfoDO::getItemId, item.getItemId()));
        }
        itemMapper.deleteById(item.getItemId());
    }

    @Override
    public void updateItemInfo(UpdateItemParams params) {
        if (null == params || ObjUtilX.isEmpty(params.getItemId())) {
            throw new BizException("5001", "id不能为空");
        }

        ItemDO item = itemMapper.selectById(params.getItemId());

        if (null == item) {
            throw new BizException("5001", "选中的对象不存在");
        }
        item.setItemName(params.getItemName());
        itemMapper.updateById(item);
    }

    @Override
    public ItemDO itemDetail(Long itemId) {
        if (ObjUtilX.isEmpty(itemId)) {
            throw new BizException("5001", "id不能为空");
        }
        ItemDO item = itemMapper.selectById(itemId);

        if (null == item) {
            throw new BizException("5001", "选中的对象不存在");
        }
        return item;
    }

    public List<ItemTreeVo> getItemTree(QueryItemParams params) throws BizException{
        List<ItemTreeVo> list = itemMapper.queryItemOutList(params.getProjectId(), params.getItemName(), params.getCategory());
        return listToTree(list);
    }

    public static List<ItemTreeVo> listToTree(List<ItemTreeVo> list) {
        //用递归找子。
        List<ItemTreeVo> treeList = new CopyOnWriteArrayList<>();
        for (ItemTreeVo tree : list) {
            if(!"/".equals(tree.getItemCode())) {
                tree.setItemCode(tree.getItemName() +" ("+ tree.getItemCode() + ")");
            }else{
                tree.setItemCode(tree.getItemName());
            }
            if (tree.getParentItemId() == 0) {
                treeList.add(findChildren(tree, list));
            }
        }
        return treeList;
    }

    //寻找子节点
    private static ItemTreeVo findChildren(ItemTreeVo tree, List<ItemTreeVo> list) {
        for (ItemTreeVo node : list) {
            if (Objects.equals(node.getParentItemId(), tree.getItemId())) {
                if (tree.getChildren() == null) {
                    tree.setChildren(new CopyOnWriteArrayList<>());
                }
                tree.getChildren().add(findChildren(node, list));
            }
        }
        return tree;
    }

    /**
     * 循环生成接口数据
     * @param resultMap
     * @param dataJson
     * @param isRequestParams 是否为请求参数
     * @return
     */
    public void generateInterfaceDataJason(String paramsType, Map resultMap, String dataJson, int isRequestParams) {
        if(!StringUtils.isEmpty(dataJson)){
            JSONArray jasonArray = JSON.parseArray(dataJson);
            if(isRequestParams == 0){
                for (int i = 0; i < jasonArray.size(); i++) {
                    JSONObject jsonObject = jasonArray.getJSONObject(i);
                    for (Map.Entry entry : jsonObject.entrySet()) {
                        String key = entry.getKey().toString();
                        Object value = entry.getValue();
                        if(value != null && "API".equalsIgnoreCase(value.toString()) ){
                            Object obj = jsonObject.get("children");
                            if(obj != null && obj.toString().indexOf("\"itemName\"") != -1){
                                generateInterfaceDataJason(paramsType, resultMap, obj.toString(), 0);
                            }else{
                                resultMap.put("code", "000001");
                                resultMap.put("msg", "API接口内容无效");
                                resultMap.put("result", "0");
                                resultMap.put("data", null);
                                return;
                            }
                        } else if("0".equals(paramsType) && "请求参数".equals(value)){
                            Object obj = jsonObject.get("children");
                            if(obj != null && obj.toString().indexOf("\"itemName\"") != -1){
                                resultMap.put("pageSize", 0);
                                generateInterfaceDataJason(paramsType, resultMap, obj.toString(), 1);
                            }else{

                            }
                        } else if("1".equals(paramsType) && "响应参数".equals(value)){
                            Object obj = jsonObject.get("children");
                            if(obj != null && obj.toString().indexOf("\"itemName\"") != -1){
                                generateInterfaceDataJason(paramsType, resultMap, obj.toString(), 1);
                            }else{

                            }
                        }else{
                            // 根据 pageSize判断是数组还是对象
                            int pageSize = 0;
                            for (int j = 0; j < jasonArray.size(); j++) {
                                JSONObject reqJsonObject = jasonArray.getJSONObject(i);
                                Object englishNameObj = reqJsonObject.get("englishName");
                                if("pageSize".equals(englishNameObj)){
                                    Object range = reqJsonObject.get("range");
                                    if(range !=null){
                                        pageSize = Integer.valueOf(range.toString());
                                    }
                                }
                            }
                            resultMap.put("pageSize", pageSize);
                        }

                        if(value != null && "接口地址".equals(value.toString())){
                            Object obj = jsonObject.get("children");
                            if(obj != null && obj.toString().indexOf("\"itemName\"") != -1){
                                JSONArray interfaceArray = JSON.parseArray(obj.toString());
                                JSONObject interfaceObject = interfaceArray.getJSONObject(0);
                                Object interfaceUrl = interfaceObject.get("itemName");
                                resultMap.put("interfaceUrl", interfaceUrl.toString());
                            }
                        }
                    }
                }
            }else{
                int pageSize = (int) resultMap.get("pageSize");
                if(pageSize == 0){
                    Map dataMap = new LinkedHashMap();
                    generateInterfacefieldData(paramsType, jasonArray, dataMap);
                    resultMap.put("data", dataMap);
                }else{
                    Map dataMap = new LinkedHashMap();
                    dataMap.put("currentPage", 1);
                    List<Map> listMap = new ArrayList<>();
                    for (int i = 0; i < pageSize; i++) {
                        Map objMap = new LinkedHashMap();
                        generateInterfacefieldData(paramsType, jasonArray, objMap);
                        listMap.add(objMap);
                    }
                    dataMap.put("list", listMap);
                    dataMap.put("pageSize", pageSize);
                    dataMap.put("totalCount", pageSize);
                    dataMap.put("totalPage", 1);
                    resultMap.put("data", dataMap);
                }
            }
        }
    }

    /**
     *
     * @param jasonArray 字段列表
     * @param dataMap 组织的字段
     */
    private void generateInterfacefieldData(String paramsType, JSONArray jasonArray, Map dataMap) {
        for (int i = 0; i < jasonArray.size(); i++) {
            String dataType = DataTypeEnum.STRING.getValue();
            JSONObject jsonObject = jasonArray.getJSONObject(i);
            Object englishNameObj = jsonObject.get("englishName");
            Object instanceTypeObj = jsonObject.get("instanceType");
            Object dataTypeObj = jsonObject.get("dataType");
            if (StringUtils.isEmpty(englishNameObj)) {
                continue;
            }
            if(dataTypeObj != null){
                dataType = dataTypeObj.toString();
            }
            if(englishNameObj == null){
                continue;
            }else{
                if(DataTypeEnum.ARRAY.getValue().equals(dataType)){
                    dataMap.put(englishNameObj.toString(), new ArrayList<>());
                }else if(DataTypeEnum.OBJECT.getValue().equals(dataType)){
                    dataMap.put(englishNameObj.toString(), null);
                }else if(DataTypeEnum.NUMBER.getValue().equals(dataType)){
                    dataMap.put(englishNameObj.toString(), 0);
                }else{
                    dataMap.put(englishNameObj.toString(), "");
                }
                //数据类型
                //自定义随机取子节点中的值
                Object childrenObj = jsonObject.get("children");
                if(childrenObj != null && childrenObj.toString().indexOf("\"itemName\"") != -1){
                    JSONArray dataArray = JSON.parseArray(childrenObj.toString());
                    if(dataArray !=null && dataArray.size() > 0){
                        if(DataTypeEnum.ARRAY.getValue().equals(dataType)){
                            String [] dataAarryList =  new String[dataArray.size()];
                            List<Map> arrayListMap = new ArrayList<>();
                            dataMap.put(englishNameObj.toString(), arrayListMap);
                            // 默认生成几条数据
                            int dataCount = 10;
                            if("0".equals(paramsType)){
                                dataCount = 2;
                            }
                            for (int j = 0; j < dataCount; j++) {
                                Map objectMap = new LinkedHashMap();
                                generateInterfacefieldData(paramsType, dataArray, objectMap);
                                arrayListMap.add(objectMap);
                            }
                        }else if(DataTypeEnum.OBJECT.getValue().equals(dataType)){
                            Map objectMap = new LinkedHashMap();
                            generateInterfacefieldData(paramsType, dataArray, objectMap);
                            dataMap.put(englishNameObj.toString(), objectMap);
                        }else{
                            String [] dataStrList =  new String[dataArray.size()];
                            for (int j = 0; j < dataArray.size(); j++) {
                                JSONObject ranJason = dataArray.getJSONObject(j);
                                Object itemNameObj = ranJason.get("itemName");
                                if(itemNameObj != null){
                                    dataStrList[j] = itemNameObj.toString();
                                }
                            }
                            if(dataStrList !=null && dataStrList.length > 0){
                                String ranInString = getRanInArr(dataStrList);
                                if(DataTypeEnum.NUMBER.getValue().equals(dataType)){
                                    try{
                                        dataMap.put(englishNameObj.toString(), Long.valueOf(ranInString));
                                    }catch (Exception e){
                                        log.error("字符串转整型异常：" + e);
                                        dataMap.put(englishNameObj.toString(), ranInString);
                                    }
                                }else{
                                    dataMap.put(englishNameObj.toString(), ranInString);
                                }
                            }
                        }
                    }
                }else{

                }
            }
        }
    }

    /**
     * s
     * @param array
     * @return
     */
    static String getRanInArr(String[] array){
        int length=array.length;
        int index= (int) (Math.random()*length);
        return array[index];
    }

    @Override
    public void apiExport(ApiExportReqVO reqVO) {
        List<ItemDO> apiList = itemMapper.selectList(
                LambdaQueryWrapperX.<ItemDO>lambdaQueryX()
                        .in(ItemDO::getItemId, reqVO.getItemIds())
                        .eq(ItemDO::getItemType, 1)
        );
        if(ObjUtilX.isEmpty(apiList)){
            throw new BizException("5001", "选中的对象不存在");
        }
        // 创建 OpenAPI 对象
        Map<String, Object> openAPI = new HashMap<>();
        openAPI.put("openapi", "3.0.0");
        openAPI.put("baseUrl", reqVO.getBaseUrl());
        Map<String, Object> info = new HashMap<>();
        info.put("title", "API Documentation");
        info.put("version", "1.0.0");
        openAPI.put("info", info);
        Map<String, Object> paths = new HashMap<>();
        openAPI.put("paths", paths);

        ObjectMapper objectMapper = new ObjectMapper();

        for (ItemDO itemDO : apiList) {
            try {
                String apiDocJson = itemDO.getContent();
                if(StrUtilX.isNotEmpty(apiDocJson)) {
                    JsonNode contentNode = objectMapper.readTree(apiDocJson);
                    parseApiContent(contentNode, paths);
                }
            } catch (Exception e) {
                e.printStackTrace();
                throw new BizException("5001", "解析API文档失败:" + itemDO.getItemName());
            }
        }

        // 输出 OpenAPI 文档
        HttpServletResponse response = reqVO.getResponse();
        response.setCharacterEncoding("utf-8");
        response.setContentType("application/octet-stream");
        try {
            String jsonOutput = objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(openAPI);
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + reqVO.getFileName() + ".json"); // 使用请求中的文件名
            ServletOutputStream outputStream = response.getOutputStream();
            // 将生成的 SQL 内容写入输出流
            outputStream.write(jsonOutput.getBytes(StandardCharsets.UTF_8));
            outputStream.flush(); // 刷新输出流
            response.flushBuffer();
        } catch (Exception var14) {
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR); // 500错误
            throw new BizException("5001","生成建表SQL失败");
        }
    }

    @Override
    public void deleteItemInfoBatch(DeleteBatchItemParams params) {
        for (Long itemId : params.getItemIds()) {
            deleteItemInfo(itemId);
        }
    }

    @Override
    public FileResp exportApiDoc(QueryApiParams reqVO) {

        ModelTableDO projectDO = null;
        //HttpServletResponse response = reqVO.getResponse();
        try {
            projectDO = tableMapper.selectById(reqVO.getProjectId());

            List<ItemDO> apiList = itemMapper.selectList(
                    LambdaQueryWrapperX.<ItemDO>lambdaQueryX()
                            .eq(ItemDO::getProjectId, reqVO.getProjectId())
                            .eq(ItemDO::getCategory, 1)// 接口文档
                            .eq(ItemDO::getItemType, 1)// 项，排除文件夹
            );

            List<ExportApi> exportApiList = new ArrayList<>();
            for (ItemDO apiInfo : apiList) {
                String docContent = apiInfo.getContent();
                List<ApiItemTree> apiItemTreeList = JSON.parseArray(docContent, ApiItemTree.class);
                if (apiItemTreeList != null && apiItemTreeList.size() != 0) {
                    String apiUrl = "";
                    ExportApi exportApi = new ExportApi();
                    List<ApiItemTree> childrenList = apiItemTreeList.get(0).getChildren();
                    for (ApiItemTree apiItemTree : childrenList) {
                        if ("接口地址".equals(apiItemTree.getItemName())) {
                            List<ApiItemTree> urlList = apiItemTree.getChildren();
                            if(urlList !=null && urlList.size() > 0){
                                apiUrl = urlList.get(0).getItemName();
                            }
                        } else if ("请求参数".equals(apiItemTree.getItemName())) {
                            List<ApiItemTree> requestFieldList = apiItemTree.getChildren();
                            getParams(requestFieldList, exportApi.getRequestTitles(), exportApi.getRequestFields(), 0, 1);
                            if(exportApi.getRequestTitles().size() > 0){
                                exportApi.getRequestTitles().add("是否必填");
                                exportApi.getRequestTitles().add("数据类型");
                                exportApi.getRequestTitles().add("描述");
                            }
                        } else if ("响应参数".equals(apiItemTree.getItemName())) {
                            List<ApiItemTree> responseFieldList = apiItemTree.getChildren();
                            getParams(responseFieldList, exportApi.getResponseTitles(), exportApi.getResponseFields(), 1, 1);
                            if(exportApi.getResponseTitles().size() > 0){
                                exportApi.getResponseTitles().add("数据类型");
                                exportApi.getResponseTitles().add("描述");
                            }
                        }
                    }
                    exportApi.setApiUrl(apiUrl);
                    exportApi.setApiDesc(apiInfo.getItemName());
                    exportApiList.add(exportApi);
                }
            }

            //System.out.println(JSON.toJSONString(exportApiList));
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("projectName", projectDO.getTableName());
            dataMap.put("dataList", exportApiList);
            dataMap.put("docDate", LocalDateTimeUtilX.formatDate(LocalDate.now()));
            LoginUser user = WebFrameworkUtilX.getLoginUser();
            dataMap.put("docAuth", user.getUserName());

            //Configuration用于读取ftl文件
            Configuration configuration = new Configuration();
            configuration.setDefaultEncoding("utf-8");

            String baseDir = System.getProperty("user.dir") ;
            // 本地调试不要src目录
            String[] activeProfiles = environment.getActiveProfiles();
            //System.out.println("Active Profiles:");
            //for (String profile : activeProfiles) {
            //    System.out.println(profile);
            //}
            String tempURI = baseDir;
            if(activeProfiles[0].equals("local")){
                tempURI = baseDir + "/src/main/resources";
            }
            //String resourcePath = Paths.get(Main.class.getClassLoader().getResource("config/接口文档模板.flt").toURI()).toString();
            configuration.setDirectoryForTemplateLoading(new File(tempURI+ "/config"));

            /*
            // 输出文档路径及名称，这里会生成本地文件，本地调试用
            //File dir = new File(baseDir + "/api/");
            //if(!dir.exists()){
            //    dir.mkdirs();
            //}
            //File outFile = new File(baseDir + "/api/" + projectDO.getTableName() +"_接口文档.doc");
            //
            //// 以utf-8的编码读取ftl文件
            //Template t = configuration.getTemplate("接口文档模板.flt", "utf-8");
            //try (BufferedWriter out = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(outFile), "utf-8"), 1024)) {
            //    t.process(dataMap, out);
            //}catch (Exception e) {
            //    response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR); // 500错误
            //    throw new BizException("5001","导出接口文档失败");
            //}
            //
            //// 设置响应头
            //response.setCharacterEncoding("utf-8");
            //response.setContentType("application/octet-stream");
            //
            //// 处理中文文件名
            //String fileName = java.net.URLEncoder.encode(projectDO.getTableName() + "接口文档.doc", "UTF-8"); // 对文件名进行 URL 编码
            //response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + fileName + "\"");
            //
            //// 将文件内容写入输出流
            //try (ServletOutputStream outputStream = response.getOutputStream();
            //     FileInputStream fileInputStream = new FileInputStream(outFile)) {
            //
            //    byte[] buffer = new byte[1024]; // 创建缓冲区
            //    int bytesRead;
            //    while ((bytesRead = fileInputStream.read(buffer)) != -1) {
            //        outputStream.write(buffer, 0, bytesRead); // 将读到的字节写入响应输出流
            //    }
            //    outputStream.flush(); // 刷新输出流
            //}catch (Exception e) {
            //    response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR); // 500错误
            //    throw new BizException("5001","导出接口文档失败");
            //}
            */
            
            // 以utf-8的编码读取模板文件，这里不生成本地文件直接，发布环境用
            Template template = configuration.getTemplate("接口文档模板.flt", "utf-8");

            try (ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                 BufferedWriter out = new BufferedWriter(new OutputStreamWriter(byteArrayOutputStream, "UTF-8"))) {

                // 使用模板处理数据并写入输出流
                template.process(dataMap, out);
                out.flush(); // 确保所有数据都被写入

                // 设置响应头
                //response.setCharacterEncoding("utf-8");
                //response.setContentType("application/octet-stream");

                // 处理中文文件名
                String fileName = java.net.URLEncoder.encode(projectDO.getTableName() + "_接口文档.doc", "UTF-8"); // 对文件名进行 URL 编码
                //String fileName = projectDO.getTableName() + "_接口文档.doc"; // 对文件名进行 URL 编码
                //response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + fileName + "\"");
                // 将内存中的字节流写入响应输出流
                //try (ServletOutputStream outputStream = response.getOutputStream()) {
                    byte[] bytes = byteArrayOutputStream.toByteArray();
                    //outputStream.write(bytes); // 将字节数组写入输出流
                    //outputStream.flush(); // 刷新输出流
                    FileReq req = new FileReq();
                    req.setOssType(FileEnum.ali.getKey());
                    FileResp upload = fileService.upload(req, projectDO.getTableName() + "_接口文档.doc", bytes);
                    return upload;
                //} catch (Exception e) {
                //    e.printStackTrace();
                //    response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR); // 500错误
                //    throw new BizException("5001", "输出文档内容失败");
                //}
            } catch (Exception e) {
                //response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR); // 500错误
                throw new BizException("5001", "导出接口文档失败");
            }
        }catch (Exception e) {
            //response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR); // 500错误
            throw new BizException("5001","导出接口文档失败");
        }
        //String serviceUrl  = "http://127.0.0.1:8018";
        //return serviceUrl + "/api/" + projectDO.getTableName() +"_接口文档.doc";
    }


    /**
     *  组装请求或响应参数
     * @param fieldItemList 字段item列表
     * @param titleList 标题列表
     * @param fieldList 字段列表
     * @param paramsType 0-请求参数 1-响应参数
     * @param level 参数当前层级
     */
    private void getParams(List<ApiItemTree> fieldItemList, List<String> titleList, List<List<String>> fieldList, Integer paramsType, Integer level){
        Integer maxMaxlevel = 1;
        if(titleList.size() < level){
            titleList.add("参数名称");
        }

        if(fieldItemList == null || fieldItemList.size() == 0){
            return;
        }

        for(ApiItemTree apiItemTree : fieldItemList){
            apiItemTree.setLevel(level);

            String fieldName =  apiItemTree.getEnglishName();
            if(StringUtils.isEmpty(fieldName)){
                continue;
            }

            List<String> fieldInfos = new ArrayList<>();
            fieldList.add(fieldInfos);
            //多层级，直接继承上面的格子，下面的格子放空
            for(int i=1; i< level; i++){
                fieldInfos.add("");
            }

            fieldInfos.add(fieldName);

            String dataType = apiItemTree.getDataType() == null ? "" : apiItemTree.getDataType();
            //请求参数才带是否必填
            if(paramsType == 0){
                boolean required = apiItemTree.getRequired();
                if(required){
                    fieldInfos.add("是");
                }else{
                    fieldInfos.add("否");
                }
            }
            fieldInfos.add(dataType);
            String fieldDesc =  apiItemTree.getItemName();
            fieldInfos.add(fieldDesc + System.lineSeparator());

            List<ApiItemTree> childrenList = apiItemTree.getChildren();
            if(childrenList != null && childrenList.size() > 0){
                if(DataTypeEnum.OBJECT.getValue().equals(dataType) || DataTypeEnum.ARRAY.getValue().equals(dataType)){
                    getParams(childrenList, titleList, fieldList, paramsType, level+1);
                    //赋值参数层级
                    Integer childrenLevel =  childrenList.get(0).getLevel();
                    if(childrenLevel > maxMaxlevel){
                        maxMaxlevel = childrenLevel;
                    }
                }
            }
//            for(int j=level; j<apiItemTree.getMaxlevel(); j++){
//               fieldInfos.add("");
//            }
        }
        if(level == 1){
            //计算字段属性长度
            int maxfieldPropertyLength = 2+maxMaxlevel;
            if(paramsType == 0){
                //请求参数多一个属性
                maxfieldPropertyLength = maxfieldPropertyLength + 1;
            }
            // 多层级参数，签名层级的参数名称补空
            for(List<String> fieldPropertyList: fieldList){
                if(fieldPropertyList.size() < maxfieldPropertyLength){
                    if(fieldPropertyList.size()>1){
                        fieldPropertyList.add(1, "");
                    }
                }
            }
        }
    }

    //public static void main(String[] args) throws Exception {
    //
    //    List<String> requestTitles = new ArrayList();
    //    requestTitles.add("参数名称");
    //    requestTitles.add("是否必填");
    //    requestTitles.add("数据类型");
    //    requestTitles.add("描述");
    //
    //    List<String> requestFields = new ArrayList();
    //    requestFields.add("orgId");
    //    requestFields.add("是");
    //    requestFields.add("Integer");
    //    requestFields.add("组织ID");
    //
    //    List<String> responseTitles = new ArrayList();
    //    responseTitles.add("参数名称2");
    //    responseTitles.add("数据类型");
    //    responseTitles.add("描述");
    //
    //    List<String> responseFields = new ArrayList();
    //    responseFields.add("orgId2");
    //    responseFields.add("Integer");
    //    responseFields.add("组织ID");
    //
    //
    //    Map<String, Object> dataMap = new HashMap<>();
    //    dataMap.put("requestTitles", requestTitles);
    //    dataMap.put("requestFields", requestFields);
    //    dataMap.put("responseTitles", responseTitles);
    //    dataMap.put("responseFields", responseFields);
    //
    //    dataMap.put("apiDesc", "测试啦");
    //    dataMap.put("apiUrl", "http://server:port/report/org/queryOrgList1");
    //
    //
    //    //Configuration用于读取ftl文件
    //    Configuration configuration = new Configuration();
    //    configuration.setDefaultEncoding("utf-8");
    //
    //    configuration.setDirectoryForTemplateLoading(new File("C:\\Users\\<USER>\\Desktop\\代码生成\\导出接口文档word"));
    //
    //    // 输出文档路径及名称
    //    File outFile = new File("D:\\迅雷下载\\数据字典.docx");
    //
    //    //以utf-8的编码读取ftl文件
    //    Template t = configuration.getTemplate("接口文档模板.flt", "utf-8");
    //    BufferedWriter out = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(outFile), "utf-8"), 1024);
    //    t.process(dataMap, out);
    //    out.close();
    //}

    private static void parseApiContent(JsonNode contentNode, Map<String, Object> paths) {
        String endpoint = null;
        List<Map<String, Object>> parameters = new ArrayList<>();

        // 遍历内容节点
        for (JsonNode node : contentNode) {
            if (node.has("children")) {
                for (JsonNode childNode : node.get("children")) {
                    if (childNode.get("itemName").asText().equals("接口地址") && childNode.has("children") && childNode.get("children").size() > 0) {
                        JsonNode itemNameNode = childNode.get("children").get(0).get("itemName");
                        if (itemNameNode != null) {
                            endpoint = itemNameNode.asText();
                            if (!endpoint.startsWith("/")) {
                                endpoint = "/" + endpoint;
                            }
                        } else {
                            log.error("itemName节点为空或不存在");
                        }
                    } else if (childNode.get("itemName").asText().equals("请求参数")) {
                        parameters = parseParameters(childNode);
                    } else if (childNode.get("itemName").asText().equals("响应参数")) {
                        // 响应参数处理，这里可以跳过直接处理如果没有 children
                        continue;
                    }
                }
            }
        }

        if (endpoint != null) {
            Map<String, Object> pathItem = new HashMap<>();
            Map<String, Object> operation = new HashMap<>();
            operation.put("summary", "Generated API Operation");
            operation.put("parameters", parameters); // 请求参数列表
            operation.put("responses", createResponseObject(contentNode));

            pathItem.put("post", operation); // 假设使用 POST 方法
            paths.put(endpoint, pathItem);
        }
    }

    private static List<Map<String, Object>> parseParameters(JsonNode parametersNode) {
        List<Map<String, Object>> parameters = new ArrayList<>();
        if (parametersNode.has("children") && parametersNode.get("children").size() > 0) {
            for (JsonNode paramNode : parametersNode.get("children")) {
                Map<String, Object> parameter = new HashMap<>();
                parameter.put("name", paramNode.get("itemName").asText());
                parameter.put("in", "query"); // 请求参数假设为查询参数
                parameter.put("required", paramNode.has("required") && paramNode.get("required").asBoolean()); // 根据 JSON 判断是否必需
                parameter.put("description", paramNode.has("remark") ? paramNode.get("remark").asText() : "");
                parameter.put("schema", createSchema(paramNode));
                parameters.add(parameter);
            }
        }
        return parameters; // 返回请求参数列表（可能为空）
    }

    private static Map<String, Object> createSchema(JsonNode paramNode) {
        Map<String, Object> schema = new HashMap<>();
        JsonNode dataTypeNode = paramNode.get("dataType");
        if (dataTypeNode != null) {
            schema.put("type", mapDataType(dataTypeNode.asText()));
        } else {
            log.error("dataType节点为空或不存在");
            schema.put("type", "string"); // 默认类型
        }

        return schema;
    }

    private static Map<String, Object> createResponseObject(JsonNode contentNode) {
        Map<String, Object> responseObject = new HashMap<>();
        Map<String, Object> responseDetails = new HashMap<>();

        // 假设定义一个成功的响应
        responseDetails.put("200", new HashMap<String, Object>() {{
            put("description", "Successful response");
            put("content", new HashMap<String, Object>() {{
                put("application/json", new HashMap<String, Object>() {{
                    put("schema", createResponseSchema(contentNode));
                }});
            }});
        }});

        responseObject.putAll(responseDetails);
        return responseObject;
    }

    private static Map<String, Object> createResponseSchema(JsonNode contentNode) {
        Map<String, Object> responseSchema = new HashMap<>();
        List<Map<String, Object>> properties = new ArrayList<>();

        for (JsonNode node : contentNode) {
            if (node.has("children")) {
                for (JsonNode childNode : node.get("children")) {
                    if (childNode.get("itemName").asText().equals("响应参数")) {
                        if (childNode.has("children")) {
                            for (JsonNode responseParamNode : childNode.get("children")) {
                                Map<String, Object> property = new HashMap<>();
                                property.put("name", responseParamNode.get("itemName").asText());

                                JsonNode dataTypeNode = responseParamNode.get("dataType");
                                if (dataTypeNode != null) {
                                    property.put("type", mapDataType(dataTypeNode.asText()));
                                } else {
                                    log.error("dataType节点为空或不存在");
                                    property.put("type", "string"); // 默认类型
                                }

                                property.put("in", "query"); // 请求参数假设为查询参数
                                property.put("required", responseParamNode.has("required") && responseParamNode.get("required").asBoolean()); // 根据 JSON 判断是否必需
                                property.put("description", responseParamNode.has("remark") ? responseParamNode.get("remark").asText() : "");
                                property.put("schema", createSchema(responseParamNode));
                                properties.add(property);
                            }
                        }
                    }
                }
            }
        }

        // 如果响应参数为空，则返回一个空对象
        if (properties.isEmpty()) {
            responseSchema.put("type", "object");
            responseSchema.put("properties", new HashMap<>()); // 返回空对象
        } else {
            responseSchema.put("type", "array");
            responseSchema.put("items", new HashMap<Object, Object>() {{
                put("type", "object");
                put("properties", properties);
            }});
        }

        return responseSchema;
    }

    private static String mapDataType(String dataType) {
        if(ObjUtilX.isEmpty(dataType)){
            return "string"; // 默认类型
        }
        switch (dataType.toLowerCase()) {
            case "string":
                return "string";
            case "number":
            case "int":
                return "integer";
            case "bool":
            case "boolean":
                return "boolean";
            case "float":
                return "number";
            case "object":
                return "object";
            case "array":
                return "array";
            default:
                return "string"; // 默认类型
        }
    }


/***************************listify老版本，弃用******************************/
    public List<ItemTreeVo> queryItemAllTreeList(QueryItemParams params) throws BizException{
        // 查询文件树
        List<ItemTreeVo> itemOutList = itemMapper.queryItemOutList(params.getProjectId(), params.getItemName(), params.getCategory());
        if(itemOutList !=null && itemOutList.size() > 0){
            List<Long> projectIds = new ArrayList<>();
            for(ItemTreeVo itemTreeVo : itemOutList){
                if(!StringUtils.isEmpty(itemTreeVo.getProjectId()) && "1".equals(itemTreeVo.getItemType())){
                    projectIds.add(itemTreeVo.getProjectId());
                }
            }
            Integer itemType = StringUtils.isEmpty(params.getItemType()) ? 10 : params.getItemType();
            if(itemType > 1) {
                if (projectIds != null && projectIds.size() > 0) {
                    List<ItemTreeVo> itemInList = itemMapper.queryItemInList(projectIds,  params.getCategory());
                    if (itemInList != null && itemInList.size() > 0) {
                        if (!StringUtils.isEmpty(params.getProjectId()) && 0 == params.getHasPermission()) {
                            itemOutList.addAll(itemInList);
                        }
                    }

                    ////查询测试任务-用例文件夹
                    //if (params.getQueryType() == 1) {
                    //    List<ItemTreeVo> caseItemList = queryTestTaskCaseList(projectIds, params.getUserId());
                    //    if (!ObjUtilX.isEmpty(caseItemList)) {
                    //        itemOutList.addAll(caseItemList);
                    //    }
                    //}

                }
            }


            //HashMap projectDocInfoMap = new HashMap();
            //if (projectIds != null && projectIds.size() > 0) {
            //    List<ProjectDocInfo> projectDocInfoList = projectMapper.queryProjectDocInfo(projectIds);
            //    for(ProjectDocInfo projectDocInfo: projectDocInfoList){
            //        projectDocInfoMap.put(projectDocInfo.getProjectId(), projectDocInfo);
            //    }
            //}

            List<ItemTreeVo> result = new ArrayList<>();
            itemOutList.stream().map((parent) -> {
                if(!StringUtils.isEmpty(params.getItemId())){
                    if(params.getItemId().equals(parent.getItemId())){
                        result.add(parent);
                    }
                } else if (StringUtils.isEmpty(parent.getParentItemId()) || "0".equals(parent.getParentItemId())
                        || (!StringUtils.isEmpty(params.getProjectId()) && itemOutList.get(0).getItemId().equals(parent.getItemId()))) {
                    result.add(parent);
                }
                return parent;
            }).forEach((parent) -> {
                itemOutList.stream().filter((child) -> (parent.getItemId().equals(child.getParentItemId()))).forEach((child) -> {
                    child.setIsMyProject(parent.getIsMyProject());
                    //如果父级别有文档类型，则继承父类的
                    //if(!StringUtils.isEmpty(parent.getDocType())){
                    //    child.setDocType(parent.getDocType());
                    //    if(child.getItemType().equals("3")){
                    //        if("0".equals(parent.getDocType())){
                    //            child.setTaskSetType("2");
                    //        }else if("1".equals(parent.getDocType())){
                    //            child.setTaskSetType("3");
                    //        }else if("2".equals(parent.getDocType())){
                    //            child.setTaskSetType("4");
                    //        }
                    //    }
                    //}else{
                    //String projectId = child.getProjectId();
                    //ProjectDocInfo projectDocInfo = (ProjectDocInfo) projectDocInfoMap.get(projectId);
                    //if(projectDocInfo != null){
                    //    if(child.getItemId().equals(projectDocInfo.getProductArchFolderId())){
                    //        // 产品架构图主目录
                    //        child.setDocType("0");
                    //        child.setDeletable("0");
                    //    }else if(child.getItemId().equals(projectDocInfo.getDatabaseFolderId())){
                    //        // 数据库文档主目录
                    //        child.setDocType("1");
                    //        child.setDeletable("0");
                    //    }else if(child.getItemId().equals(projectDocInfo.getInterfaceFolderId())){
                    //        // 接口文档主目录
                    //        child.setDocType("2");
                    //        child.setDeletable("0");
                    //    }
                    //}
                    //}
                    parent.getChildren().add(child);
                });
            });
            iteraItemTree(result, null);
            return result;
        }
        return new ArrayList<>();
    }

    /**
     * 迭代继承父文件的docType, 父文件类型为：数据库文件、接口文档、产品文档
     * @param itemTreeList
     * @param parentItem
     */
    private void iteraItemTree(List<ItemTreeVo> itemTreeList, ItemTreeVo parentItem){
        for(ItemTreeVo item : itemTreeList){
            //if(parentItem != null && !StringUtils.isEmpty(parentItem.getDocType())) {
            //    item.setDocType(parentItem.getDocType());
            //    if (item.getItemType().equals("3")) {
            //        if ("0".equals(item.getDocType())) {
            //            item.setTaskSetType("2");
            //        } else if ("1".equals(item.getDocType())) {
            //            item.setTaskSetType("3");
            //        } else if ("2".equals(item.getDocType())) {
            //            item.setTaskSetType("4");
            //        }
            //    }
            //}
            List<ItemTreeVo> childreItemTreeList = item.getChildren();
            if(!ObjUtilX.isEmpty(childreItemTreeList)){
                iteraItemTree(childreItemTreeList, item);
            }
        }
    }
}
