package com.mongoso.mgs.module.model.dal.mysql.modelfieldalter;

import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.module.model.dal.db.modelfieldalter.ModelFieldAlterDO;
import org.apache.ibatis.annotations.Mapper;
import com.mongoso.mgs.module.model.controller.admin.modelfieldalter.vo.*;

/**
 * 图形建模字段 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ModelFieldAlterMapper extends BaseMapperX<ModelFieldAlterDO> {

    default PageResult<ModelFieldAlterDO> selectPage(ModelFieldAlterPageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<ModelFieldAlterDO>lambdaQueryX()
                .eqIfPresent(ModelFieldAlterDO::getFieldId, reqVO.getFieldId())
                .likeIfPresent(ModelFieldAlterDO::getFieldName, reqVO.getFieldName())
                .eqIfPresent(ModelFieldAlterDO::getFieldCode, reqVO.getFieldCode())
                .eqIfPresent(ModelFieldAlterDO::getFieldType, reqVO.getFieldType())
                .eqIfPresent(ModelFieldAlterDO::getTableId, reqVO.getTableId())
                .eqIfPresent(ModelFieldAlterDO::getIsNullable, reqVO.getIsNullable())
                .eqIfPresent(ModelFieldAlterDO::getIsPrimaryKey, reqVO.getIsPrimaryKey())
                .eqIfPresent(ModelFieldAlterDO::getDefaultVal, reqVO.getDefaultVal())
                .eqIfPresent(ModelFieldAlterDO::getSort, reqVO.getSort())
                .eqIfPresent(ModelFieldAlterDO::getPropType, reqVO.getPropType())
                .eqIfPresent(ModelFieldAlterDO::getRemark, reqVO.getRemark())
                .eqIfPresent(ModelFieldAlterDO::getOpType, reqVO.getOpType())
                .eqIfPresent(ModelFieldAlterDO::getIsProcessed, reqVO.getIsProcessed())
                .betweenIfPresent(ModelFieldAlterDO::getCreatedDt, reqVO.getCreatedDt())
                .orderByDesc(ModelFieldAlterDO::getCreatedDt));
    }

    default List<ModelFieldAlterDO> selectList(ModelFieldAlterQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<ModelFieldAlterDO>lambdaQueryX()
                .eqIfPresent(ModelFieldAlterDO::getFieldId, reqVO.getFieldId())
                .likeIfPresent(ModelFieldAlterDO::getFieldName, reqVO.getFieldName())
                .eqIfPresent(ModelFieldAlterDO::getFieldCode, reqVO.getFieldCode())
                .eqIfPresent(ModelFieldAlterDO::getFieldType, reqVO.getFieldType())
                .eqIfPresent(ModelFieldAlterDO::getTableId, reqVO.getTableId())
                .eqIfPresent(ModelFieldAlterDO::getIsNullable, reqVO.getIsNullable())
                .eqIfPresent(ModelFieldAlterDO::getIsPrimaryKey, reqVO.getIsPrimaryKey())
                .eqIfPresent(ModelFieldAlterDO::getDefaultVal, reqVO.getDefaultVal())
                .eqIfPresent(ModelFieldAlterDO::getSort, reqVO.getSort())
                .eqIfPresent(ModelFieldAlterDO::getPropType, reqVO.getPropType())
                .eqIfPresent(ModelFieldAlterDO::getRemark, reqVO.getRemark())
                .eqIfPresent(ModelFieldAlterDO::getOpType, reqVO.getOpType())
                .eqIfPresent(ModelFieldAlterDO::getIsProcessed, reqVO.getIsProcessed())
                .betweenIfPresent(ModelFieldAlterDO::getCreatedDt, reqVO.getCreatedDt())
                    .orderByDesc(ModelFieldAlterDO::getCreatedDt));
    }

}