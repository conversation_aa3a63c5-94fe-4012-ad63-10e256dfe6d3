package com.mongoso.mgs.module.model.controller.admin.item.vo;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 导出API
 * daijinbiao
 * 2025-4-2 10:58:05
 */
@Data
public class ExportApi {

    //@ApiModelProperty(value = "字段文名", required = true)
    private String apiUrl;

    //@ApiModelProperty(value = "字段英文名", required = true)
    private String apiDesc;

    //@ApiModelProperty(value = "请求参数标题", required = true)
    List<String> requestTitles = new ArrayList();

    //@ApiModelProperty(value = "请求参数字段", required = true)
    List<List<String>> requestFields = new ArrayList();

    //@ApiModelProperty(value = "响应参数字段", required = true)
    List<String> responseTitles = new ArrayList();

    //@ApiModelProperty(value = "响应参数字段", required = true)
    List<List<String>> responseFields = new ArrayList();
}
