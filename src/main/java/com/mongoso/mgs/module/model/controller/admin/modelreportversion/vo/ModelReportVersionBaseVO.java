package com.mongoso.mgs.module.model.controller.admin.modelreportversion.vo;

import lombok.*;

import jakarta.validation.constraints.*;
import java.io.Serializable;

/**
 * 自定义报表版本 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class ModelReportVersionBaseVO implements Serializable {

    /** 主键ID   */
    private Long id;

    /** 报表id   */
    @NotNull(message = "报表id  不能为空")
    private Long reportId;

    /** 版本号 */
    @NotEmpty(message = "版本号不能为空")
    private Integer versionNo;

}
