package com.mongoso.mgs.module.model.service.modelqueryparam;

import com.mongoso.mgs.framework.common.util.StrUtilX;
import com.mongoso.mgs.module.model.controller.admin.modelquery.vo.ModelQueryAditReqVO;
import com.mongoso.mgs.module.model.dal.db.modelquery.ModelQueryDO;
import com.mongoso.mgs.module.model.dal.mysql.modelquery.ModelQueryMapper;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.mongoso.mgs.module.model.controller.admin.modelqueryparam.vo.*;
import com.mongoso.mgs.module.model.dal.db.modelqueryparam.ModelQueryParamDO;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.module.model.dal.mysql.modelqueryparam.ModelQueryParamMapper;

import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exception;
import com.mongoso.mgs.framework.common.exception.BizException;
//import static com.mongoso.mgs.module.model.enums.ErrorCodeConstants.*;


/**
 * 自定义查询参数 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ModelQueryParamServiceImpl implements ModelQueryParamService {

    @Resource
    private ModelQueryMapper queryMapper;
    @Resource
    private ModelQueryParamMapper queryParamMapper;

    @Override
    public Long modelQueryParamAdd(ModelQueryParamAditReqVO reqVO) {
        if (!reqVO.getParamCode().startsWith("@")){
            throw new BizException("5001","参数英文名必须是@开头");
        }
        // 判断查询是否存在
        ModelQueryDO queryDO = queryMapper.selectById(reqVO.getQueryId());
        if (queryDO == null) {
            throw new BizException("5001", "自定义查询不存在");
        }
        // 验重
        Long duplicheck = repeatValid(reqVO);
        if (duplicheck > 0){
            throw new BizException("5001","您输入的名称值重复，请重新输入");
        }
        // 插入
        ModelQueryParamDO queryParam = BeanUtilX.copy(reqVO, ModelQueryParamDO::new);
        queryParamMapper.insert(queryParam);
        // 返回
        return queryParam.getId();
    }

    @Override
    public Long repeatValid(ModelQueryParamAditReqVO reqVO) {
        long count = 0L;
        if (StrUtilX.isEmpty(reqVO.getQueryId())) {
            if (StrUtilX.isNotEmpty(reqVO.getParamCode())) {
                count = queryParamMapper.selectCountByCode(reqVO.getParamCode(),null);
                if (count>0){
                    return count;
                }
            }
        } else {
            // 编辑验重
            if (StrUtilX.isNotEmpty(reqVO.getParamCode())) {
                count = queryParamMapper.selectCountByCode(reqVO.getParamCode(),reqVO.getId());
                if (count>0){
                    return count;
                }
            }
        }
        return count;
    }

    @Override
    public Long modelQueryParamEdit(ModelQueryParamAditReqVO reqVO) {
        if (!reqVO.getParamCode().startsWith("@")){
            throw new BizException("5001","参数英文名必须是@开头");
        }
        // 校验存在
        this.modelQueryParamValidateExists(reqVO.getId());
        // 更新
        ModelQueryParamDO queryParam = BeanUtilX.copy(reqVO, ModelQueryParamDO::new);
        queryParamMapper.updateById(queryParam);
        // 返回
        return queryParam.getId();
    }

    @Override
    public void modelQueryParamDel(Long id) {
        // 校验存在
        this.modelQueryParamValidateExists(id);
        // 删除
        queryParamMapper.deleteById(id);
    }

    private ModelQueryParamDO modelQueryParamValidateExists(Long id) {
        ModelQueryParamDO queryParam = queryParamMapper.selectById(id);
        if (queryParam == null) {
//            throw exception(QUERY_PARAM_NOT_EXISTS);
            throw new BizException("5001", "自定义查询参数不存在");
        }
        return queryParam;
    }

    @Override
    public ModelQueryParamDO modelQueryParamDetail(Long id) {
        return queryParamMapper.selectById(id);
    }

    @Override
    public List<ModelQueryParamDO> modelQueryParamList(ModelQueryParamQueryReqVO reqVO) {
        return queryParamMapper.selectList(reqVO);
    }

    @Override
    public PageResult<ModelQueryParamDO> modelQueryParamPage(ModelQueryParamPageReqVO reqVO) {
        return queryParamMapper.selectPage(reqVO);
    }

}
