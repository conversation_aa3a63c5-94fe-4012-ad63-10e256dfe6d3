package com.mongoso.mgs.module.model.controller.admin.modelfieldalter;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import jakarta.validation.*;
import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import static com.mongoso.mgs.framework.common.domain.ResultX.success;

import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.model.controller.admin.modelfieldalter.vo.*;
import com.mongoso.mgs.module.model.dal.db.modelfieldalter.ModelFieldAlterDO;
import com.mongoso.mgs.module.model.service.modelfieldalter.ModelFieldAlterService;

/**
 * 图形建模字段 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/model")
@Validated
public class ModelFieldAlterController {

    @Resource
    private ModelFieldAlterService fieldAlterService;

    @OperateLog("图形建模字段添加或编辑")
    @PostMapping("/modelFieldAlterAdit")
    @PreAuthorize("@ss.hasPermission('modelFieldAlter:adit')")
    public ResultX<Long> modelFieldAlterAdit(@Valid @RequestBody ModelFieldAlterAditReqVO reqVO) {
        return success(reqVO.getId() == null
                            ? fieldAlterService.modelFieldAlterAdd(reqVO)
                            : fieldAlterService.modelFieldAlterEdit(reqVO));
    }

    @OperateLog("图形建模字段删除")
    @PostMapping("/modelFieldAlterDel")
    @PreAuthorize("@ss.hasPermission('modelFieldAlter:del')")
    public ResultX<Boolean> modelFieldAlterDel(@Valid @RequestBody ModelFieldAlterPrimaryReqVO reqVO) {
        fieldAlterService.modelFieldAlterDel(reqVO.getId());
        return success(true);
    }

    @OperateLog("图形建模字段详情")
    @PostMapping("/modelFieldAlterDetail")
    @PreAuthorize("@ss.hasPermission('modelFieldAlter:query')")
    public ResultX<ModelFieldAlterRespVO> modelFieldAlterDetail(@Valid @RequestBody ModelFieldAlterPrimaryReqVO reqVO) {
        ModelFieldAlterDO oldDO = fieldAlterService.modelFieldAlterDetail(reqVO.getId());
        return success(BeanUtilX.copy(oldDO, ModelFieldAlterRespVO::new));
    }

    @OperateLog("图形建模字段列表")
    @PostMapping("/modelFieldAlterList")
    @PreAuthorize("@ss.hasPermission('modelFieldAlter:query')")
    public ResultX<List<ModelFieldAlterRespVO>> modelFieldAlterList(@Valid @RequestBody ModelFieldAlterQueryReqVO reqVO) {
        List<ModelFieldAlterDO> list = fieldAlterService.modelFieldAlterList(reqVO);
        return success(BeanUtilX.copyList(list, ModelFieldAlterRespVO::new));
    }

    @OperateLog("图形建模字段分页")
    @PostMapping("/modelFieldAlterPage")
    @PreAuthorize("@ss.hasPermission('modelFieldAlter:query')")
    public ResultX<PageResult<ModelFieldAlterRespVO>> modelFieldAlterPage(@Valid @RequestBody ModelFieldAlterPageReqVO reqVO) {
        PageResult<ModelFieldAlterDO> pageResult = fieldAlterService.modelFieldAlterPage(reqVO);
        return success(BeanUtilX.copyPage(pageResult, ModelFieldAlterRespVO::new));
    }

}
