package com.mongoso.mgs.module.model.dal.mysql.modelqueryresult;

import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.module.model.dal.db.modelqueryresult.ModelQueryResultDO;
import org.apache.ibatis.annotations.Mapper;
import com.mongoso.mgs.module.model.controller.admin.modelqueryresult.vo.*;

/**
 * 自定义查询结果 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ModelQueryResultMapper extends BaseMapperX<ModelQueryResultDO> {

    default PageResult<ModelQueryResultDO> selectPage(ModelQueryResultPageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<ModelQueryResultDO>lambdaQueryX()
                .eqIfPresent(ModelQueryResultDO::getQueryId, reqVO.getQueryId())
                .eqIfPresent(ModelQueryResultDO::getResultField, reqVO.getResultField())
                .likeIfPresent(ModelQueryResultDO::getResultFieldName, reqVO.getResultFieldName())
                .eqIfPresent(ModelQueryResultDO::getDataType, reqVO.getDataType())
                .eqIfPresent(ModelQueryResultDO::getLeng, reqVO.getLeng())
                .eqIfPresent(ModelQueryResultDO::getFieldPrecision, reqVO.getFieldPrecision())
                .eqIfPresent(ModelQueryResultDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(ModelQueryResultDO::getCreatedDt, reqVO.getCreatedDt())
                .orderByDesc(ModelQueryResultDO::getCreatedDt));
    }

    default List<ModelQueryResultDO> selectList(ModelQueryResultQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<ModelQueryResultDO>lambdaQueryX()
                .eqIfPresent(ModelQueryResultDO::getQueryId, reqVO.getQueryId())
                .eqIfPresent(ModelQueryResultDO::getResultField, reqVO.getResultField())
                .likeIfPresent(ModelQueryResultDO::getResultFieldName, reqVO.getResultFieldName())
                .eqIfPresent(ModelQueryResultDO::getDataType, reqVO.getDataType())
                .eqIfPresent(ModelQueryResultDO::getLeng, reqVO.getLeng())
                .eqIfPresent(ModelQueryResultDO::getFieldPrecision, reqVO.getFieldPrecision())
                .eqIfPresent(ModelQueryResultDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(ModelQueryResultDO::getCreatedDt, reqVO.getCreatedDt())
                    .orderByDesc(ModelQueryResultDO::getCreatedDt));
    }

}