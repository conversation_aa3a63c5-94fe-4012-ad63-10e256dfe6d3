package com.mongoso.mgs.module.model.dal.db.modelreportconfig;

import lombok.*;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;

/**
 * 自定义报表配置 DO
 *
 * <AUTHOR>
 */
@TableName("lowcode.sys_model_report_config")
//@KeySequence("sys_model_report_config_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ModelReportConfigDO extends OperateDO {

    /** 条件id */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /** 报表id   */
    private Long reportId;

    /** 数据源   */
    private String dataTable;

    /** 字段编码 */
    private String fieldCode;

    /** 字段名称 */
    private String fieldName;

    /** 控件类型 */
    private Integer controlType;

    /** 字段类型   */
    private String fieldType;

    /** 长度 */
    private Integer leng;

    /** 字段精度 */
    private Integer fieldPrecision;

    /** 正则表达式 */
    private String regExpression;

    /** 数据掩码 */
    private String dataMask;

    /** 可编辑 */
    private Integer isEdit;

    /** 是否显示 */
    private Integer isShow;

    /** 空值显示 */
    private String emptyShow;

    /** 是否必填 */
    private Integer isRequired;

    /** 默认值 */
    private String defaultVal;

    /** 备注   */
    private String remark;

    /** 版本号 */
    private Integer versionNo;

    /** 0:查询条件，1:展示数据 */
    private Integer category;


}
