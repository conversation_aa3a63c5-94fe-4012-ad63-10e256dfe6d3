package com.mongoso.mgs.module.model.dal.db.modelfunctionversion;

import lombok.*;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;

/**
 * 自定义报表版本 DO
 *
 * <AUTHOR>
 */
@TableName("lowcode.sys_model_function_version")
//@KeySequence("sys_model_function_version_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ModelFunctionVersionDO extends OperateDO {

    /** 主键ID   */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;


    private Long funId;

    /** 版本号 */
    private Integer versionNo;


}
