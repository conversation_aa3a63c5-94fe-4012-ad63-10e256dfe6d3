package com.mongoso.mgs.module.model.service.modelreport;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mongoso.mgs.common.util.CodeGenUtil;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.framework.mybatis.core.util.IDUtilX;
import com.mongoso.mgs.module.model.controller.admin.modelquery.vo.ModelQueryRespVO;
import com.mongoso.mgs.module.model.controller.admin.modelreport.vo.*;
import com.mongoso.mgs.module.model.controller.admin.modelreportconfig.vo.ModelReportConfigAditReqVO;
import com.mongoso.mgs.module.model.dal.db.modelfield.ModelFieldDO;
import com.mongoso.mgs.module.model.dal.db.modelqueryparam.ModelQueryParamDO;
import com.mongoso.mgs.module.model.dal.db.modelqueryresult.ModelQueryResultDO;
import com.mongoso.mgs.module.model.dal.db.modelreport.ModelReportDO;
import com.mongoso.mgs.module.model.dal.db.modelreportconfig.ModelReportConfigDO;
import com.mongoso.mgs.module.model.dal.db.modelreportversion.ModelReportVersionDO;
import com.mongoso.mgs.module.model.dal.mysql.modelfield.ModelFieldMapper;
import com.mongoso.mgs.module.model.dal.mysql.modelreport.ModelReportMapper;
import com.mongoso.mgs.module.model.dal.mysql.modelreportconfig.ModelReportConfigMapper;
import com.mongoso.mgs.module.model.dal.mysql.modelreportversion.ModelReportVersionMapper;
import com.mongoso.mgs.module.model.service.modelquery.ModelQueryService;

import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;


// import static com.mongoso.mgs.module.model.enums.ErrorCodeConstants.*;


/**
 * 自定义报 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ModelReportServiceImpl implements ModelReportService {

    @Resource
    private ModelReportMapper reportMapper;
    @Resource
    private ModelQueryService queryService;
    @Resource
    private ModelFieldMapper fieldMapper;
    @Resource
    private ModelReportConfigMapper reportConfigMapper;
    @Resource
    private ModelReportVersionMapper reportVersionMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long modelReportAdd(ModelReportAditReqVO reqVO) {
        // 插入
        ModelReportDO report = BeanUtilX.copy(reqVO, ModelReportDO::new);
        Long reportId = IDUtilX.getId();
        report.setReportId(reportId);
        // 生成编码
        String code = CodeGenUtil.generateCode(CodeGenUtil.CodeType.REPORT_CODE);
        if(reqVO.getDirType() == 1){
            // 自定义查询
            ModelQueryRespVO respVO = queryService.modelQueryDetail(reqVO.getQueryId());
            Integer version = 1;
            report.setVersionNo(version);
            //根据绑定的自定义查询，初始化查询条件，展示数据的配置信息
            List<ModelReportConfigDO> configList = new ArrayList<>(respVO.getParams().size() + respVO.getResults().size());
            List<ModelQueryParamDO> params = respVO.getParams();
            for (ModelQueryParamDO param : params) {
                if (param.getParamType() == 2) {// 表变量
                    //如果是表变量
                    String[] fieldIds = param.getDataFields().split(",");
                    Long[] longFieldIds = Arrays.stream(fieldIds)
                            .map(Long::parseLong).toArray(Long[]::new);
                    List<ModelFieldDO> fieldDOS = fieldMapper.selectList(LambdaQueryWrapperX.<ModelFieldDO>lambdaQueryX()
                            .in(ModelFieldDO::getFieldId, longFieldIds));
                    for (ModelFieldDO fieldDO : fieldDOS) {
                        ModelReportConfigDO temp = new ModelReportConfigDO();
                        temp.setCategory(0);//查询参数类型
                        temp.setReportId(reportId);
                        temp.setDataTable(param.getParamCode());
                        temp.setFieldCode(fieldDO.getFieldCode());
                        temp.setFieldName(fieldDO.getFieldName());
                        temp.setFieldType(fieldDO.getFieldType());
                        temp.setLeng(fieldDO.getLeng());
                        temp.setFieldPrecision(fieldDO.getFieldPrecision());
    //                    temp.setRegExpression("");
    //                    temp.setDataMask("");
    //                    temp.setIsEdit(0);
    //                    temp.setIsShow(0);
    //                    temp.setEmptyShow("");
    //                    temp.setIsRequired(0);
    //                    temp.setDefaultVal("");
    //                    temp.setRemark("");
                        temp.setVersionNo(version);

                        configList.add(temp);
                    }
                } else {
                    ModelReportConfigDO temp = new ModelReportConfigDO();
                    temp.setCategory(0);//查询参数类型
                    temp.setReportId(reportId);
                    temp.setDataTable("table1");
                    temp.setFieldCode(param.getParamCode());
                    temp.setFieldName("");
                    temp.setFieldType(param.getDataType());
                    temp.setLeng(param.getLeng());
                    temp.setFieldPrecision(param.getFieldPrecision());
                    temp.setVersionNo(version);
                    configList.add(temp);
                }
            }
            //返回结果作为展示数据配置
            List<ModelQueryResultDO> result = respVO.getResults();
            for (ModelQueryResultDO rst : result) {
                ModelReportConfigDO temp = new ModelReportConfigDO();
                temp.setCategory(1);//展示数据类型
                temp.setReportId(reportId);
                temp.setDataTable("table1");
                temp.setFieldCode(rst.getResultField());
                temp.setFieldName(rst.getResultFieldName());
                temp.setFieldType(rst.getDataType());
                temp.setLeng(rst.getLeng());
                temp.setFieldPrecision(rst.getFieldPrecision());
                temp.setVersionNo(version);
                configList.add(temp);
            }
            reportConfigMapper.insertBatch(configList);

        }else{
            report.setVersionNo(0);//目录
        }
        report.setReportCode(code);
        report.setIsPublish(0);
        reportMapper.insert(report);
        // 返回
        return report.getReportId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long modelReportEdit(ModelReportAditReqVO reqVO) {
        // 校验存在
        ModelReportDO reportDO = this.modelReportValidateExists(reqVO.getReportId());
        // 更新
        ModelReportDO report = BeanUtilX.copy(reqVO, ModelReportDO::new);
        report.setReportCode(reportDO.getReportCode());//防止篡改
        report.setQueryId(reportDO.getQueryId());//防止篡改

        if(reqVO.getDirType() == 1) {
            // 查询版本号+1
            Integer version = reportVersionMapper.selectObjs(new LambdaQueryWrapper<ModelReportVersionDO>()
                            .select(ModelReportVersionDO::getVersionNo)
                            .eq(ModelReportVersionDO::getReportId, reportDO.getReportId())
                            .orderByDesc(ModelReportVersionDO::getVersionNo) // 选择降序排列
                            .last("LIMIT 1")) // 只取第一条记录，即最大值
                    .stream()
                    .map(obj -> (Integer) obj)
                    .findFirst()
                    .orElse(0);
            version = version + 1;

            if(report.getIsPublish() == 1) {//发布操作，保存时+1
                // 插入历史版本
                ModelReportVersionDO versionDO = new ModelReportVersionDO();
                versionDO.setReportId(report.getReportId());
                versionDO.setVersionNo(version);
                reportVersionMapper.insert(versionDO);
            }
            report.setVersionNo(version);

            // 先删除
            reportConfigMapper.delete(LambdaQueryWrapperX.<ModelReportConfigDO>lambdaQueryX()
                    .eq(ModelReportConfigDO::getReportId, reqVO.getReportId())
                    .eq(ModelReportConfigDO::getVersionNo, version)
            );

            List<ModelReportConfigDO> configList = new ArrayList<>(reqVO.getParamList().size() + reqVO.getResultList().size());
            // 修改查询条件
            for (ModelReportConfigAditReqVO condition : reqVO.getParamList()) {
                ModelReportConfigDO config = BeanUtilX.copy(condition, ModelReportConfigDO::new);
                config.setCategory(0);
                config.setReportId(report.getReportId());
                config.setVersionNo(version);
                configList.add(config);
            }
            // 修改展示数据
            for (ModelReportConfigAditReqVO result : reqVO.getResultList()) {
                ModelReportConfigDO config = BeanUtilX.copy(result, ModelReportConfigDO::new);
                config.setCategory(1);
                config.setReportId(report.getReportId());
                config.setVersionNo(version);
                configList.add(config);
            }
            reportConfigMapper.insertBatch(configList);
        }else{
            report.setVersionNo(0);
        }

        reportMapper.updateById(report);
        // 返回
        return report.getReportId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modelReportDel(Long reportId) {
        // 校验存在
        ModelReportDO reportDO = this.modelReportValidateExists(reportId);

        // 如果是目录，判断下面有没有子节点
        if(reportDO.getDirType() == 0){
            Long count = reportMapper.selectCount(LambdaQueryWrapperX.<ModelReportDO>lambdaQueryX().eq(ModelReportDO::getParentId, reportId));
            if(count > 0){
                throw new BizException("5001", "当前目录下存在数据，不可删除");
            }
        }
        // 删除
        reportMapper.deleteById(reportId);

        // 删除配置
        reportConfigMapper.delete(LambdaQueryWrapperX.<ModelReportConfigDO>lambdaQueryX().eq(ModelReportConfigDO::getReportId, reportId));

        // 删除版本
        reportVersionMapper.delete(LambdaQueryWrapperX.<ModelReportVersionDO>lambdaQueryX().eq(ModelReportVersionDO::getReportId, reportId));
    }

    private ModelReportDO modelReportValidateExists(Long reportId) {
        ModelReportDO report = reportMapper.selectById(reportId);
        if (report == null) {
            // throw exception(REPORT_NOT_EXISTS);
            throw new BizException("5001", "自定义报不存在");
        }
        return report;
    }

    @Override
    public ModelReportRespVO modelReportDetail(Long reportId) {
        ModelReportDO reportDO = this.modelReportValidateExists(reportId);
        ModelReportRespVO respVO = BeanUtilX.copy(reportDO, ModelReportRespVO::new);
        // 查询查询条件，返回参数
        List<ModelReportConfigDO> configList = reportConfigMapper.selectList(LambdaQueryWrapperX.<ModelReportConfigDO>lambdaQueryX()
                .eq(ModelReportConfigDO::getReportId, reportId)
                .eq(ModelReportConfigDO::getVersionNo, reportDO.getVersionNo())
        );
        List<ModelReportConfigDO> paramList = configList.stream()
                .filter(config -> config.getCategory() == 0)
                .collect(Collectors.toList());
        respVO.setParamList(paramList);

        List<ModelReportConfigDO> resultList = configList.stream()
                .filter(config -> config.getCategory() == 1)
                .collect(Collectors.toList());
        respVO.setResultList(resultList);
        return respVO;
    }

    @Override
    public List<ModelReportDO> modelReportList(ModelReportQueryReqVO reqVO) {
        return reportMapper.selectList(reqVO);
    }

    @Override
    public PageResult<ModelReportDO> modelReportPage(ModelReportPageReqVO reqVO) {
        return reportMapper.selectPage(reqVO);
    }

    @Override
    public List<ModelReportRespVO> modelReportTree() {
        ModelReportQueryReqVO reqVO = new ModelReportQueryReqVO();
        //todo 添加条件
        List<ModelReportDO> list = reportMapper.selectList(reqVO);
        // 正序
//        Collections.sort(list);

        List<ModelReportRespVO> collect = list.stream().map(item -> {
            ModelReportRespVO convert = BeanUtilX.copy(item, ModelReportRespVO::new);
            convert.setItemId(convert.getReportId().toString());
            convert.setItemName(convert.getReportName());
            convert.setItemCode(convert.getReportCode());
            convert.setParentItemId(convert.getParentId().toString());
//            convert.setPath(null);
            return convert;
        }).collect(Collectors.toList());

        List<ModelReportRespVO> authMenuResps = this.listToTree(collect);

        return authMenuResps;
    }

    @Override
    public Integer modelReportDrag(ModelReportDragReqVO reqVO) {
        // 校验存在
        ModelReportDO exists = this.modelReportValidateExists(reqVO.getReportId());
        // 更新
        // 校验父节点存在
        ModelReportDO parent = reportMapper.selectById(reqVO.getParentId());
        if (parent == null) {
            throw new BizException("5001","父节点不存在");
        }
        if (parent.getDirType() != 0) {
            throw new BizException("5001","父节点必须是目录");
        }
        exists.setParentId(reqVO.getParentId());
        return reportMapper.updateById(exists);
    }

    public static List<ModelReportRespVO> listToTree(List<ModelReportRespVO> list) {
        //用递归找子。
        List<ModelReportRespVO> treeList = new CopyOnWriteArrayList<>();
        for (ModelReportRespVO tree : list) {
            if ("0".equals(tree.getParentItemId())) {
                treeList.add(findChildren(tree, list));
            }
        }
        return treeList;
    }

    //寻找子节点
    private static ModelReportRespVO findChildren(ModelReportRespVO tree, List<ModelReportRespVO> list) {
        for (ModelReportRespVO node : list) {
            if (node.getParentItemId().equals(tree.getReportId().toString())) {
                if (tree.getChildren() == null) {
                    tree.setChildren(new CopyOnWriteArrayList<>());
                }
                tree.getChildren().add(findChildren(node, list));
            }
        }
        return tree;
    }
}
