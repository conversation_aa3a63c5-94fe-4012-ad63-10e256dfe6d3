package com.mongoso.mgs.module.model.dal.db.modelform.button;

import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;
import lombok.*;

import com.baomidou.mybatisplus.annotation.*;

/**
 * 单据建模按钮配置 DO
 *
 * <AUTHOR>
 */
@TableName(value = "lowcode.sys_model_form_button", autoResultMap = true)
//@KeySequence("sys_model_form_button_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ModelFormButtonDO extends OperateDO {

    /** 主键id */
        @TableId(type = IdType.ASSIGN_ID)
    private Integer dataId;

    /** 单据建模编码 */
    private String modelFormCode;

    /** 按钮类型 */
    private Integer buttonType;

    /** 按钮名称 */
    private String buttonName;

    /** 是否选中 */
    private Boolean isSelect;


}
