package com.mongoso.mgs.module.model.dal.mysql.comp;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.model.controller.admin.modelfield.vo.ModelFieldPageReqVO;
import com.mongoso.mgs.module.model.controller.admin.modelfield.vo.ModelFieldQueryReqVO;
import com.mongoso.mgs.module.model.dal.db.comp.ComponentDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 前端组件 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CompMapper extends BaseMapperX<ComponentDO> {

    default PageResult<ComponentDO> selectPage(ModelFieldPageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<ComponentDO>lambdaQueryX()
                .orderByDesc(ComponentDO::getCompId));
    }

    default List<ComponentDO> selectList(ModelFieldQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<ComponentDO>lambdaQueryX()
                    .orderByDesc(ComponentDO::getCompId));
    }

}