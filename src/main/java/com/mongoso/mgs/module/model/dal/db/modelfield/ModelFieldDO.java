package com.mongoso.mgs.module.model.dal.db.modelfield;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;
import com.mongoso.mgs.framework.typehandler.JsonbTypeHandler;
import lombok.*;

import java.util.List;

/**
 * 图形建模字段 DO
 *
 * <AUTHOR>
 */
@TableName(value = "lowcode.sys_model_field", autoResultMap = true)
//@KeySequence("sys_model_field_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ModelFieldDO extends OperateDO {

    /** 模型字段表id */
    @TableId(type = IdType.ASSIGN_ID)
    private Long fieldId;

    /** 模型字段表id中文名 */
    private String fieldName;

    /** 模型字段表id实名 */
    private String fieldCode;

    /** 上一行字段编码 */
    @TableField(exist = false)
    private String upFieldCode;

    /** 旧字段编码 */
    @TableField(exist = false)
    private String oldFieldCode;

    /** 是否需要执行 */
    @TableField(exist = false)
    private Boolean needExe = true;

    /** 字段类型 */
    private String fieldType;

    /** 字段类型名称 */
    private String fieldTypeName;

    /** 字段长度 */
    private Integer leng;

    /** 字段精度 */
    private Integer fieldPrecision;

    /** 模型表id */
    private Long tableId;

    /** 是否为空 */
    private Integer isNullable;

    /** 是否自增 */
    private Integer isAutoIncrease;

    /** 是否为主键 */
    private Integer isPrimaryKey;

    /** 默认值 */
    private String defaultVal;

    /** 排序 */
    private Integer sort;

    /** JSON类型，0：子表，1：主表 */
    private Integer jsonType;

    /** 属性类型，0：系统，1：用户 */
    private Integer propType;

    /** 备注描述 */
    private String remark;

    /** JSON配置 */
    @TableField(typeHandler = JsonbTypeHandler.class,fill = FieldFill.INSERT)
    private List<JSONObject> jsonFields;
}
