package com.mongoso.mgs.module.model.service.modelfunction;

import java.util.*;
import jakarta.validation.*;
import jakarta.validation.constraints.NotNull;

import com.alibaba.fastjson.JSONObject;
import com.mongoso.mgs.module.model.controller.admin.modelfunction.vo.*;
import com.mongoso.mgs.module.model.dal.db.modelfunction.ModelFunctionDO;
import com.mongoso.mgs.framework.common.domain.PageResult;

/**
 * 自定义函数 Service 接口
 *
 * <AUTHOR>
 */
public interface ModelFunctionService {

    /**
     * 创建自定义函数
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long modelFunctionAdd(@Valid ModelFunctionAditReqVO reqVO);

    /**
     * 更新自定义函数
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long modelFunctionEdit(@Valid ModelFunctionAditReqVO reqVO);

    /**
     * 删除自定义函数
     *
     * @param funId 编号
     */
    void modelFunctionDel(Long funId);

    /**
     * 获得自定义函数信息
     *
     * @param funId 编号
     * @return 自定义函数信息
     */
    ModelFunctionDO modelFunctionDetail(Long funId);

    /**
     * 获得自定义函数列表
     *
     * @param reqVO 查询条件
     * @return 自定义函数列表
     */
    List<ModelFunctionDO> modelFunctionList(@Valid ModelFunctionQueryReqVO reqVO);

    /**
     * 获得自定义函数分页
     *
     * @param reqVO 查询条件
     * @return 自定义函数分页
     */
    PageResult<ModelFunctionDO> modelFunctionPage(@Valid ModelFunctionPageReqVO reqVO);

    List<ModelFunctionRespVO> modelFunctionTree(ModelFunctionQueryReqVO reqVO);

    Integer modelFunctionDrag(@Valid ModelFunctionDragReqVO reqVO);

    void modelFunctionLock(@Valid ModelFunctionLockReqVO reqVO);

    JSONObject modelFunctionExecute(@Valid ModelFunctionPrimaryReqVO reqVO);
}
