package com.mongoso.mgs.module.model.service.sqllog;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.module.model.controller.admin.sqllog.vo.SqlLogAditReqVO;
import com.mongoso.mgs.module.model.controller.admin.sqllog.vo.SqlLogPageReqVO;
import com.mongoso.mgs.module.model.controller.admin.sqllog.vo.SqlLogQueryReqVO;
import com.mongoso.mgs.module.model.controller.admin.sqllog.vo.SqlLogRespVO;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 脚本日志 Service 接口
 *
 * <AUTHOR>
 */
public interface SqlLogService {

    /**
     * 创建脚本日志
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long sqlLogAdd(@Valid SqlLogAditReqVO reqVO);

    /**
     * 更新脚本日志
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long sqlLogEdit(@Valid SqlLogAditReqVO reqVO);

    /**
     * 删除脚本日志
     *
     * @param id 编号
     */
    void sqlLogDel(Long id);

    /**
     * 获得脚本日志信息
     *
     * @param id 编号
     * @return 脚本日志信息
     */
    SqlLogRespVO sqlLogDetail(Long id);

    /**
     * 获得脚本日志列表
     *
     * @param reqVO 查询条件
     * @return 脚本日志列表
     */
    List<SqlLogRespVO> sqlLogList(@Valid SqlLogQueryReqVO reqVO);

    /**
     * 获得脚本日志分页
     *
     * @param reqVO 查询条件
     * @return 脚本日志分页
     */
    PageResult<SqlLogRespVO> sqlLogPage(@Valid SqlLogPageReqVO reqVO);

}
