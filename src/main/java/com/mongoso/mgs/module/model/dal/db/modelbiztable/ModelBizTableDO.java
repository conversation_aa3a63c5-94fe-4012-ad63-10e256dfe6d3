package com.mongoso.mgs.module.model.dal.db.modelbiztable;

import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;
import lombok.*;

import com.baomidou.mybatisplus.annotation.*;

/**
 * 主 DO
 *
 * <AUTHOR>
 */
@TableName(value = "lowcode.sys_model_biz_table", autoResultMap = true)
//@KeySequence("sys_model_biz_table_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ModelBizTableDO extends OperateDO {

    /** 表id */
    private Long tableId;

    /** 业务编码 */
    private String bizCode;

    /** 业务类型[来自字典库] */
    private Integer bizType;

    /** 主表code */
    private String tableCode;

    /** 业务名称 */
    private String bizName;

    /** 备注 */
    private String remark;

    /** 数据id */
        @TableId(type = IdType.ASSIGN_ID)
    private Long dataId;


}
