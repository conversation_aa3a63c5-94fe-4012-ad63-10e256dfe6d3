package com.mongoso.mgs.module.model.dal.mysql.modeltableindex;

import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.module.model.dal.db.modeltableindex.ModelTableIndexDO;
import org.apache.ibatis.annotations.Mapper;
import com.mongoso.mgs.module.model.controller.admin.modeltableindex.vo.*;

/**
 * 图形建模主表索引 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ModelTableIndexMapper extends BaseMapperX<ModelTableIndexDO> {

    default PageResult<ModelTableIndexDO> selectPage(ModelTableIndexPageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<ModelTableIndexDO>lambdaQueryX()
                .eqIfPresent(ModelTableIndexDO::getTableId, reqVO.getTableId())
                .eqIfPresent(ModelTableIndexDO::getTableCode, reqVO.getTableCode())
                .eqIfPresent(ModelTableIndexDO::getNonUnique, reqVO.getNonUnique())
                .eqIfPresent(ModelTableIndexDO::getIdxSeq, reqVO.getIdxSeq())
                .eqIfPresent(ModelTableIndexDO::getFieldCode, reqVO.getFieldCode())
                .eqIfPresent(ModelTableIndexDO::getSortType, reqVO.getSortType())
                .eqIfPresent(ModelTableIndexDO::getIdxType, reqVO.getIdxType())
                .eqIfPresent(ModelTableIndexDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(ModelTableIndexDO::getCreatedDt, reqVO.getCreatedDt())
                .orderByDesc(ModelTableIndexDO::getCreatedDt));
    }

    default List<ModelTableIndexDO> selectList(ModelTableIndexQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<ModelTableIndexDO>lambdaQueryX()
                .eqIfPresent(ModelTableIndexDO::getTableId, reqVO.getTableId())
                .eqIfPresent(ModelTableIndexDO::getTableCode, reqVO.getTableCode())
                .eqIfPresent(ModelTableIndexDO::getNonUnique, reqVO.getNonUnique())
                .eqIfPresent(ModelTableIndexDO::getIdxSeq, reqVO.getIdxSeq())
                .eqIfPresent(ModelTableIndexDO::getFieldCode, reqVO.getFieldCode())
                .eqIfPresent(ModelTableIndexDO::getSortType, reqVO.getSortType())
                .eqIfPresent(ModelTableIndexDO::getIdxType, reqVO.getIdxType())
                .eqIfPresent(ModelTableIndexDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(ModelTableIndexDO::getCreatedDt, reqVO.getCreatedDt())
                    .orderByDesc(ModelTableIndexDO::getCreatedDt));
    }

}