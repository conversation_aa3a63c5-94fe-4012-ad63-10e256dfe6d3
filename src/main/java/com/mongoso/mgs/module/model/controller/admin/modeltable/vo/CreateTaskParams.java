package com.mongoso.mgs.module.model.controller.admin.modeltable.vo;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/6
 * @description
 */
@Data
public class CreateTaskParams {

    private String taskId;

    private String taskCode;

    private String taskTitle;

    private String taskDesc;

    private Long directorId;

    private String directorName;

    /**
     *  任务类型 ["工作", "问题", "架构图", "数据库", "接口"]
     */
    private String taskType;

    private String startDate;

    private String endDate;

    private String priority;

    private Long projectId;

    private String projectName;

    private String taskSetId;

    private String taskSetName;

    private String completeDate;

    private String taskStatusName;

    private String taskStatusCode;

    private String taskStatusType = "0";

    private String active;

    private Integer taskSeq;

    private Long creatorId;

    private String creator;

    private Date createdDt;

    private String updator;

    private Date updatedDt;

    private String itemContent;

    //private List<CreateTaskAssociationParams> associationTaskList;
    //
    //private List<FileInfoVo> fileList;

    private String caseTaskId;

    private String testTaskCaseId;

    private String testTaskFileId;
}
