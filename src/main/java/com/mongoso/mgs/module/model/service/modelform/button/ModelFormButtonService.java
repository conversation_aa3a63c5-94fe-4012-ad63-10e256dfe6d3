package com.mongoso.mgs.module.model.service.modelform.button;

import java.util.*;
import jakarta.validation.*;
import com.mongoso.mgs.module.model.controller.admin.modelform.button.vo.*;
import com.mongoso.mgs.module.model.dal.db.modelform.button.ModelFormButtonDO;
import com.mongoso.mgs.framework.common.domain.PageResult;

/**
 * 单据建模按钮配置 Service 接口
 *
 * <AUTHOR>
 */
public interface ModelFormButtonService {

    /**
     * 创建单据建模按钮配置
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long modelFormButtonAdd(@Valid ModelFormButtonAditReqVO reqVO);

    /**
     * 更新单据建模按钮配置
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long modelFormButtonEdit(@Valid ModelFormButtonAditReqVO reqVO);

    /**
     * 删除单据建模按钮配置
     *
     * @param dataId 编号
     */
    void modelFormButtonDelete(Long dataId);

    /**
     * 获得单据建模按钮配置信息
     *
     * @param dataId 编号
     * @return 单据建模按钮配置信息
     */
    ModelFormButtonRespVO modelFormButtonDetail(Long dataId);

    /**
     * 获得单据建模按钮配置列表
     *
     * @param reqVO 查询条件
     * @return 单据建模按钮配置列表
     */
    List<ModelFormButtonRespVO> modelFormButtonList(@Valid ModelFormButtonQueryReqVO reqVO);

    /**
     * 获得单据建模按钮配置分页
     *
     * @param reqVO 查询条件
     * @return 单据建模按钮配置分页
     */
    PageResult<ModelFormButtonRespVO> modelFormButtonPage(@Valid ModelFormButtonPageReqVO reqVO);

    /**
     * 批量新增单据建模按钮
     *
     * @param buttonDOList 按钮DO列表
     */
    void modelFormButtonBatchAdd(List<ModelFormButtonDO> buttonDOList);

    /**
     * 根据单据建模编码批量删除按钮
     *
     * @param modelFormCode 单据建模编码
     */
    void modelFormButtonBatchDeleteByCode(String modelFormCode);

}
