package com.mongoso.mgs.module.model.controller.admin.modeltableindex.vo;

import lombok.*;

import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 图形建模主表索引 QueryReqVO
 *
 * <AUTHOR>
 */
@Data
public class ModelTableIndexQueryReqVO {

    /**
     * 索引ID
     */
    private Long idxId;

    /**
     * 索引名称
     */
    private String idxName;

    /** 模型表id */
    private Long tableId;

    /** 模型表实名 */
    private String tableCode;

    /** 是否生成过 */
    private Integer nonUnique;

    /** 索引序号 */
    private Integer idxSeq;

    /** 字段id */
    private Long fieldId;

    /** 字段编码 */
    private String fieldCode;

    /** 排序类型(asc,desc) */
    private String sortType;

    /** 索引类型(betree,hash) */
    private String idxType;
    private String idxWay;

    /** 备注描述 */
    private String remark;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createdDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] updatedDt;

}
