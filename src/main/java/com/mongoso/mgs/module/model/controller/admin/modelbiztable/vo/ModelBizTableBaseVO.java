package com.mongoso.mgs.module.model.controller.admin.modelbiztable.vo;

import lombok.*;

import jakarta.validation.constraints.*;
import java.io.Serializable;

  

/**
 * 主 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class ModelBizTableBaseVO implements Serializable {

    /** 表id */
    @NotNull(message = "表id不能为空")
    private Long tableId;

    /** 业务编码 */
    @NotEmpty(message = "业务编码不能为空")
    private String bizCode;

    /** 业务类型[来自字典库] */
    @NotNull(message = "业务类型[来自字典库]不能为空")
    private Integer bizType;

    /** 主表code */
    @NotEmpty(message = "主表code不能为空")
    private String tableCode;

    /** 业务名称 */
    @NotEmpty(message = "业务名称不能为空")
    private String bizName;

    /** 备注 */
    private String remark;

    /** 数据id */
    private Long dataId;

    /** 表名称 */
    private String tableName;

}
