package com.mongoso.mgs.module.model.dal.mysql.modelfunctionversion;

import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.module.model.dal.db.modelfunctionversion.ModelFunctionVersionDO;
import org.apache.ibatis.annotations.Mapper;
import com.mongoso.mgs.module.model.controller.admin.modelfunctionversion.vo.*;

/**
 * 自定义报表版本 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ModelFunctionVersionMapper extends BaseMapperX<ModelFunctionVersionDO> {

    default PageResult<ModelFunctionVersionDO> selectPage(ModelFunctionVersionPageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<ModelFunctionVersionDO>lambdaQueryX()
                .eqIfPresent(ModelFunctionVersionDO::getFunId, reqVO.getFunId())
                .eqIfPresent(ModelFunctionVersionDO::getVersionNo, reqVO.getVersionNo())
                .betweenIfPresent(ModelFunctionVersionDO::getCreatedDt, reqVO.getCreatedDt())
                .orderByDesc(ModelFunctionVersionDO::getCreatedDt));
    }

    default List<ModelFunctionVersionDO> selectList(ModelFunctionVersionQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<ModelFunctionVersionDO>lambdaQueryX()
                .eqIfPresent(ModelFunctionVersionDO::getFunId, reqVO.getFunId())
                .eqIfPresent(ModelFunctionVersionDO::getVersionNo, reqVO.getVersionNo())
                .betweenIfPresent(ModelFunctionVersionDO::getCreatedDt, reqVO.getCreatedDt())
                    .orderByDesc(ModelFunctionVersionDO::getCreatedDt));
    }

}