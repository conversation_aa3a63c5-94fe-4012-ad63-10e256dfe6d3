package com.mongoso.mgs.module.model.controller.admin.item;

import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.file.core.domain.FileResp;
import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.model.controller.admin.item.vo.*;
import com.mongoso.mgs.module.model.controller.admin.item.vo.CreateItemParams;
import com.mongoso.mgs.module.model.dal.db.item.ItemDO;
import com.mongoso.mgs.module.model.service.item.ItemService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static com.mongoso.mgs.framework.common.domain.ResultX.success;

/**
 * <AUTHOR>
 * @date 2025/2/7
 * @description 架构图，接口文档查询
 */

@RestController
@RequestMapping("/item")
@Validated
public class ItemController {

    @Autowired
    ItemService itemService;

    @RequestMapping(value = "/addItem", method = RequestMethod.POST)
    public ResultX<Long> insertItemInfo(@RequestBody CreateItemParams params) {
        return success(itemService.insertItemInfo(params));
    }

    @RequestMapping(value = "/saveItems", method = RequestMethod.POST)
    public ResultX<String> saveItems(@RequestBody CreateItemParamsBatch params) {
        return success(itemService.saveItems(params));
    }

    @RequestMapping(value = "/editItem", method = RequestMethod.POST)
    public ResultX<Boolean> editItem(@RequestBody CreateItemParams params) {
        return success(itemService.editItemInfo(params));
    }

    @RequestMapping(value = "/renameItem", method = RequestMethod.POST)
    public ResultX<Boolean> updateItemInfo(@RequestBody UpdateItemParams params) {
        itemService.updateItemInfo(params);
        return success(true);
    }

    @OperateLog("修改父节点")
    @PostMapping("/itemDrag")
    @PreAuthorize("@ss.hasPermission('modelTable:adit')")
    public ResultX<Integer> itemDrag(@Valid @RequestBody UpdateItemParams params) {
        return success(itemService.itemDrag(params));
    }

    @RequestMapping(value = "/itemDetail", method = RequestMethod.POST)
    public ResultX<ItemDO> itemDetail(@RequestBody DeleteItemParams params) {
        return success(itemService.itemDetail(params.getItemId()));
    }

    @RequestMapping(value = "/deleteItem", method = RequestMethod.POST)
    public ResultX<Boolean> deleteItemInfo(@RequestBody DeleteItemParams params) {
        itemService.deleteItemInfo(params.getItemId());
        return success(true);
    }

    @RequestMapping(value = "/deleteItemBatch", method = RequestMethod.POST)
    public ResultX<Boolean> deleteItemInfo(@RequestBody DeleteBatchItemParams params) {
        itemService.deleteItemInfoBatch(params);
        return success(true);
    }

    @RequestMapping(value = "/queryItemTree", method = RequestMethod.POST)
    public ResultX<List<ItemTreeVo>> queryItemTreeList(@RequestBody QueryItemParams params) {
        List<ItemTreeVo> itemTree = itemService.queryItemTreeList(params);
        return success(itemTree);
    }

    @RequestMapping(value = "/dragItem", method = RequestMethod.POST)
    @OperateLog(value="拖动文件")
    public ResultX<Integer> drayItemInfo(@RequestBody DragItemParams params) {
        return success(itemService.dragItemInfo(params));
    }

    @OperateLog("批量导出")
    @PostMapping("/apiExport")
    @PreAuthorize("@ss.hasPermission('modelTable:query')")
    public void modelTableExportSQL(@RequestBody ApiExportReqVO reqVO, HttpServletResponse response) {
        try {
            // 设置响应类型为文件下载
            reqVO.setResponse(response);
            itemService.apiExport(reqVO);
        } catch (Exception e) {
            // 处理其他异常，设置 HTTP 500 状态码
            response.setCharacterEncoding("utf-8");
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR); // 500错误
            try {
                response.getWriter().write("文件生成失败，原因：" + e.getMessage()); // 返回错误信息
                response.getWriter().flush();
            } catch (IOException ex) {
                // 处理写入响应内容时的异常
                throw new RuntimeException("无法返回错误信息", ex);
            }
        }
    }

    @OperateLog("导出API接口文档")
    @PostMapping("/exportApiDoc")
    @PreAuthorize("@ss.hasPermission('modelTable:query')")
    public ResultX<FileResp> exportApiDoc(HttpServletResponse response, @RequestBody QueryApiParams reqVO) {
        //try {
        //    String exportUrl = itemService.exportApiDoc(params);
        //    ResponseEntity<String> responseEntity = new ResponseEntity<>();
        //    responseEntity.setData(exportUrl);
        //    return responseEntity;
        //} catch (AppException e) {
        //    return new ResponseEntity(e.getErrorCode(), getMessage(e.getErrorCode()));
        //}
        FileResp fileResp = new FileResp();
        try {
            // 设置响应类型为文件下载
            //reqVO.setResponse(response);
            fileResp = itemService.exportApiDoc(reqVO);
        } catch (Exception e) {
            // 处理其他异常，设置 HTTP 500 状态码
            response.setCharacterEncoding("utf-8");
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR); // 500错误
            try {
                response.getWriter().write("文件生成失败，原因：" + e.getMessage()); // 返回错误信息
                response.getWriter().flush();
            } catch (IOException ex) {
                // 处理写入响应内容时的异常
                throw new RuntimeException("无法返回错误信息", ex);
            }
        }
        return ResultX.success(fileResp);
    }
}
