package com.mongoso.mgs.module.model.dal.mysql.generator;

import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.model.dal.db.generator.BizRelationDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 业务建模 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface BizRelationMapper extends BaseMapperX<BizRelationDO> {

    /**
     * 根据业务 ID 查询业务关系列表
     * @param bizId 业务 ID
     * @return 业务关系列表
     */
    default List<BizRelationDO> selectByBizId(@Param("bizId") Long bizId) {
        return selectList(LambdaQueryWrapperX.<BizRelationDO>lambdaQueryX()
                .eq(BizRelationDO::getBizId, bizId));
    }

}