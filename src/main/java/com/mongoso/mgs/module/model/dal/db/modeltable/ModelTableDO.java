package com.mongoso.mgs.module.model.dal.db.modeltable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;
import lombok.*;

/**
 * 图形建模主 DO
 *
 * <AUTHOR>
 */
@TableName("lowcode.sys_model_table")
//@KeySequence("sys_model_table_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ModelTableDO extends OperateDO {

    /** 模型表id */
    @TableId(type = IdType.ASSIGN_ID)
    private Long tableId;

    /**
     * 数据源编号
     *
     */
    private Long dataSourceConfigId;
    private Long projectId;
    private String version;
    private String icon;

    private Integer isLock;
    /** 模型表中文名 */
    private String tableName;

    /** 模型表实名 */
    private String tableCode;

    /** 备注描述 */
    private String remark;

    /** 升级标记 */
    private Integer upgradeFlag;

    /** 是否生成过 */
    private Integer isGen;

    private Integer seq;

    @TableField(exist = false)
    private Boolean needExe = true;

    /** 类型 */
    private Integer dirType;

    /** 父节点 */
    private Long parentId;
    /** 属性类型，0：系统，1：用户 */
    private Integer propType;
//    @Override
//    public int hashCode() {
//        return Objects.hash(tableId);
//    }
//
//    @Override
//    public int compareTo(Object arg0) {
//        AuthMenuDO sysMenu = (AuthMenuDO)arg0;
//        // 根据 sort 排序
//        return sort.compareTo(sysMenu.getSort());
//    }
}
