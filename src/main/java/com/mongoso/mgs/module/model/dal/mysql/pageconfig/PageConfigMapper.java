package com.mongoso.mgs.module.model.dal.mysql.pageconfig;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.model.controller.admin.pageconfig.vo.PageConfigPageReqVO;
import com.mongoso.mgs.module.model.controller.admin.pageconfig.vo.PageConfigQueryReqVO;
import com.mongoso.mgs.module.model.dal.db.pageconfig.PageConfigDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 页面配置 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PageConfigMapper extends BaseMapperX<PageConfigDO> {

    default PageResult<PageConfigDO> selectPage(PageConfigPageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<PageConfigDO>lambdaQueryX()
                .eq(PageConfigDO::getProjectId, reqVO.getProjectId())
                .likeIfPresent(PageConfigDO::getName, reqVO.getName())
                .likeIfPresent(PageConfigDO::getContent, reqVO.getContent())
                .orderByAsc(PageConfigDO::getSeq));
    }

    default List<PageConfigDO> selectList(PageConfigQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<PageConfigDO>lambdaQueryX()
                .eq(PageConfigDO::getProjectId, reqVO.getProjectId())
                .likeIfPresent(PageConfigDO::getName, reqVO.getName())
                .likeIfPresent(PageConfigDO::getContent, reqVO.getContent())
                .orderByAsc(PageConfigDO::getSeq));
    }

    default Long selectCount(String name) {
        return selectCount(LambdaQueryWrapperX.<PageConfigDO>lambdaQueryX()
                .eq(PageConfigDO::getName, name));
    }

    default PageConfigDO selectOne(String name) {
        return selectOne(LambdaQueryWrapperX.<PageConfigDO>lambdaQueryX()
                .eq(PageConfigDO::getName, name));
    }

    Long getMaxSeq(@Param("parentId") Long parentId, @Param("projectId") Long projectId);

    void updateSeqAddOne(@Param("parentId") Long parentId, @Param("seq") Long seq, @Param("projectId") Long projectId);

}
