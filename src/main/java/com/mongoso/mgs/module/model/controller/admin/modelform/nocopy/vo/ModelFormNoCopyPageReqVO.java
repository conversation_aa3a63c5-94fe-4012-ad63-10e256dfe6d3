package com.mongoso.mgs.module.model.controller.admin.modelform.nocopy.vo;

import lombok.*;

import com.mongoso.mgs.framework.common.domain.PageParam;


    
 import org.springframework.format.annotation.DateTimeFormat;
 
import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
  


/**
 * 单据不可复制字段 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ModelFormNoCopyPageReqVO extends PageParam {

    /** 单据建模编码 */
    private String modelFormCode;

    /** 业务建模编码 */
    private String modelBizCode;

    /** 表id */
    private Long tableId;

    /** 行号 */
    private Integer rowNo;

    /** 排序 */
    private Integer sort;

    /** 字段英文名称 */
    private String columnCode;

    /** 字段中文名称 */
    private String columnName;

    /** 备注 */
    private String remark;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

}
