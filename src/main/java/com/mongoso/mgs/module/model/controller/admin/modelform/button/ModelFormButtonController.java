package com.mongoso.mgs.module.model.controller.admin.modelform.button;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import jakarta.validation.*;
import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;

import static com.mongoso.mgs.framework.common.domain.ResultX.success;

import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.model.controller.admin.modelform.button.vo.*;
import com.mongoso.mgs.module.model.service.modelform.button.ModelFormButtonService;

/**
 * 单据建模按钮配置 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/model")
@Validated
public class ModelFormButtonController {

    @Resource
    private ModelFormButtonService formButtonService;

    @OperateLog("单据建模按钮配置添加或编辑")
    @PostMapping("/modelFormButtonAdit")
    @PreAuthorize("@ss.hasPermission('modelFormButton:adit')")
    public ResultX<Long> modelFormButtonAdit(@Valid @RequestBody ModelFormButtonAditReqVO reqVO) {
        return success(reqVO.getDataId() == null
                            ? formButtonService.modelFormButtonAdd(reqVO)
                            : formButtonService.modelFormButtonEdit(reqVO));
    }

    @OperateLog("单据建模按钮配置删除")
    @PostMapping("/modelFormButtonDelete")
    @PreAuthorize("@ss.hasPermission('modelFormButton:delete')")
    public ResultX<Boolean> modelFormButtonDelete(@Valid @RequestBody ModelFormButtonPrimaryReqVO reqVO) {
        formButtonService.modelFormButtonDelete(reqVO.getDataId());
        return success(true);
    }

    @OperateLog("单据建模按钮配置详情")
    @PostMapping("/modelFormButtonDetail")
    @PreAuthorize("@ss.hasPermission('modelFormButton:query')")
    public ResultX<ModelFormButtonRespVO> modelFormButtonDetail(@Valid @RequestBody ModelFormButtonPrimaryReqVO reqVO) {
        return success(formButtonService.modelFormButtonDetail(reqVO.getDataId()));
    }

    @OperateLog("单据建模按钮配置列表")
    @PostMapping("/modelFormButtonList")
    @PreAuthorize("@ss.hasPermission('modelFormButton:query')")
    public ResultX<List<ModelFormButtonRespVO>> modelFormButtonList(@Valid @RequestBody ModelFormButtonQueryReqVO reqVO) {
        return success(formButtonService.modelFormButtonList(reqVO));
    }

    @OperateLog("单据建模按钮配置分页")
    @PostMapping("/modelFormButtonPage")
    @PreAuthorize("@ss.hasPermission('modelFormButton:query')")
    public ResultX<PageResult<ModelFormButtonRespVO>> modelFormButtonPage(@Valid @RequestBody ModelFormButtonPageReqVO reqVO) {
        return success(formButtonService.modelFormButtonPage(reqVO));
    }

}
