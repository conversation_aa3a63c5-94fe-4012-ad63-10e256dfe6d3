package com.mongoso.mgs.module.model.service.modelreport;

import java.util.*;
import jakarta.validation.*;
import com.mongoso.mgs.module.model.controller.admin.modelreport.vo.*;
import com.mongoso.mgs.module.model.dal.db.modelreport.ModelReportDO;
import com.mongoso.mgs.framework.common.domain.PageResult;

/**
 * 自定义报 Service 接口
 *
 * <AUTHOR>
 */
public interface ModelReportService {

    /**
     * 创建自定义报
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long modelReportAdd(@Valid ModelReportAditReqVO reqVO);

    /**
     * 更新自定义报
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long modelReportEdit(@Valid ModelReportAditReqVO reqVO);

    /**
     * 删除自定义报
     *
     * @param reportId 编号
     */
    void modelReportDel(Long reportId);

    /**
     * 获得自定义报信息
     *
     * @param reportId 编号
     * @return 自定义报信息
     */
    ModelReportRespVO modelReportDetail(Long reportId);

    /**
     * 获得自定义报列表
     *
     * @param reqVO 查询条件
     * @return 自定义报列表
     */
    List<ModelReportDO> modelReportList(@Valid ModelReportQueryReqVO reqVO);

    /**
     * 获得自定义报分页
     *
     * @param reqVO 查询条件
     * @return 自定义报分页
     */
    PageResult<ModelReportDO> modelReportPage(@Valid ModelReportPageReqVO reqVO);

    List<ModelReportRespVO> modelReportTree();

    Integer modelReportDrag(@Valid ModelReportDragReqVO reqVO);
}
