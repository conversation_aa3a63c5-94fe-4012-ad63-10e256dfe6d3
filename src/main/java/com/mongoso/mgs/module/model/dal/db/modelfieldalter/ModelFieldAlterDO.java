package com.mongoso.mgs.module.model.dal.db.modelfieldalter;

import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;

/**
 * 图形建模字段 DO
 *
 * <AUTHOR>
 */
@TableName("lowcode.sys_model_field_alter")
//@KeySequence("sys_model_field_alter_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ModelFieldAlterDO extends OperateDO {

    /** 主键id */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /** 模型字段表id */
    private Long fieldId;

    /** 模型字段表id中文名 */
    private String fieldName;

    /** 旧模型字段表id实名 */
    private String oldFieldCode;

    /** 模型字段表id实名 */
    private String fieldCode;

    /** 字段类型 */
    private String fieldType;

    /** 字段长度 */
    private Integer leng;

    /** 字段精度 */
    private Integer fieldPrecision;

    /** 模型表id */
    private Long tableId;

    /** 是否为空 */
    private Integer isNullable;

    /** 是否为主键 */
    private Integer isPrimaryKey;

    /** 默认值 */
    private String defaultVal;

    /** 排序 */
    private Integer sort;

    /** 属性类型，0：系统，1：用户 */
    private Integer propType;

    /** 备注描述 */
    private String remark;

    /** 0:删除，1：新增，2：修改 */
    private Integer opType;

    /** 是否执行过 */
    private Integer isProcessed;


}
