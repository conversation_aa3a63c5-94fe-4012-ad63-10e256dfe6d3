package com.mongoso.mgs.module.model.service.modeltable;

import com.alibaba.fastjson.JSONObject;
import com.mongoso.mgs.common.util.TableStructure;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.module.model.controller.admin.item.vo.QueryItemParams;
import com.mongoso.mgs.module.model.controller.admin.modeltable.vo.*;
import com.mongoso.mgs.module.model.dal.db.modeltable.ModelTableDO;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.transaction.annotation.Transactional;

import java.sql.SQLException;
import java.util.List;

/**
 * 图形建模主 Service 接口
 *
 * <AUTHOR>
 */
public interface ModelTableService {

    /**
     * 创建图形建模主
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long modelTableAdd(@Valid ModelTableAditReqVO reqVO);

    Long modelTableAddNew(@Valid ModelTableJsonReqVO reqVO);

    /**
     * 更新图形建模主
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long modelTableEdit(@Valid ModelTableAditReqVO reqVO) throws SQLException;

    Long modelTableEditNew(@Valid ModelTableJsonReqVO reqVO, Long tableId) throws SQLException;

    /**
     * 删除图形建模主
     *
     * @param tableId 编号
     */
    void modelTableDel(Long tableId);

    Long repeatValid(ModelTableAditReqVO reqVO);

    /**
     * 获得图形建模主信息
     *
     * @param tableId 编号
     * @return 图形建模主信息
     */
    ModelTableRespVO modelTableDetail(Long tableId);
    ModelTableRespVO modelTableDetailByCode(String tableCode, Integer dirType);

    TableStructure modelTableSync(Long tableId);

    @Transactional(rollbackFor = Exception.class)
    TableStructure modelTableSyncNew(Long tableId);

    /**
     * 获得图形建模主列表
     *
     * @param reqVO 查询条件
     * @return 图形建模主列表
     */
    List<ModelTableDO> modelTableList(@Valid ModelTableQueryReqVO reqVO);

    /**
     * 获得图形建模主分页
     *
     * @param reqVO 查询条件
     * @return 图形建模主分页
     */
    PageResult<ModelTableRespVO> modelTablePage(@Valid ModelTablePageReqVO reqVO);

    List<ModelTableRespVO> modelTableTree(ModelTableQueryReqVO reqVO);

    Boolean modelTableEmpty(@NotNull(message = "模型表id不能为空") Long tableId);

    Integer modelTableCount(@NotNull(message = "模型表id不能为空") Long tableId);

    TableStructure modelTableImport(@Valid ModelTableCodeReqVO reqVO);

    Integer modelTableDrag(@Valid ModelTableDragReqVO reqVO);

    JSONObject modelTableDoc(@NotNull(message = "模型表id不能为空") Long tableId);

    ModelTableListifyResVO modelTableDetailNew(@NotNull(message = "模型表id不能为空") Long tableId);

    Integer modelTableLock(@Valid ModelTablePrimaryReqVO reqVO);

    Long modelTableInit(@Valid QueryItemParams reqVO);

    Integer dragItemInfo(ModelTableDragReqVO reqVO);

    String modelTableImportBatch(@Valid ModelTableImportListReqVO reqVO);

    /**
     * 优化后的批量导入现有表方法 - 复用数据库连接，提升性能
     * @param reqVO 批量导入请求参数
     * @return 导入结果信息
     */
    String modelTableImportBatchOptimized(@Valid ModelTableImportListReqVO reqVO);

    String modelTableDbType(@NotNull(message = "模型表id不能为空") Long tableId);

    void modelTableDelBatch(@Valid ModelTableDelBatchReqVO reqVO);

    ModelTableRespVO modelTableDetailByCode(@Valid ModelTableQueryReqVO reqVO);
}
