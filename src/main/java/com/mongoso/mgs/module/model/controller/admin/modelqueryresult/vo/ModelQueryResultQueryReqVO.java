package com.mongoso.mgs.module.model.controller.admin.modelqueryresult.vo;

import lombok.*;

import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 自定义查询结果 QueryReqVO
 *
 * <AUTHOR>
 */
@Data
public class ModelQueryResultQueryReqVO {

    /** 查询id */
    private Long queryId;

    /** 结果字段 */
    private String resultField;

    /** 结果字段别名 */
    private String resultFieldName;

    /** 数据类型 */
    private String dataType;

    /** 长度 */
    private Integer leng;

    /** 精度 */
    private Integer fieldPrecision;
    /**
     * 排序
     */
    private Integer sort;
    /** 备注 */
    private String remark;

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createdDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] updatedDt;

}
