package com.mongoso.mgs.module.model.controller.admin.modelqueryparam.vo;

import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 自定义查询参数 AditReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ModelQueryParamAditReqVO extends ModelQueryParamBaseVO {

    /** 参数值 */
    private String paramVal;
}
