package com.mongoso.mgs.module.model.service.modelfield;

import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.mongoso.mgs.module.model.controller.admin.modelfield.vo.*;
import com.mongoso.mgs.module.model.dal.db.modelfield.ModelFieldDO;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.module.model.dal.mysql.modelfield.ModelFieldMapper;

import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exception;
import static com.mongoso.mgs.module.model.enums.ErrorCodeConstants.*;

/**
 * 图形建模字段 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ModelFieldServiceImpl implements ModelFieldService {

    @Resource
    private ModelFieldMapper fieldMapper;

    @Override
    public Long modelFieldAdd(ModelFieldAditReqVO reqVO) {
        // 插入
        ModelFieldDO field = BeanUtilX.copy(reqVO, ModelFieldDO::new);
        fieldMapper.insert(field);
        // 返回
        return field.getFieldId();
    }

    @Override
    public Long modelFieldEdit(ModelFieldAditReqVO reqVO) {
        // 校验存在
        this.modelFieldValidateExists(reqVO.getFieldId());
        // 更新
        ModelFieldDO field = BeanUtilX.copy(reqVO, ModelFieldDO::new);
        fieldMapper.updateById(field);
        // 返回
        return field.getFieldId();
    }

    @Override
    public void modelFieldDel(Long fieldId) {
        // 校验存在
        this.modelFieldValidateExists(fieldId);
        // 删除
        fieldMapper.deleteById(fieldId);
    }

    private ModelFieldDO modelFieldValidateExists(Long fieldId) {
        ModelFieldDO field = fieldMapper.selectById(fieldId);
        if (field == null) {
            throw exception(FIELD_NOT_EXISTS);
        }
        return field;
    }

    @Override
    public ModelFieldDO modelFieldDetail(Long fieldId) {
        return fieldMapper.selectById(fieldId);
    }

    @Override
    public List<ModelFieldDO> modelFieldList(ModelFieldQueryReqVO reqVO) {
        return fieldMapper.selectList(reqVO);
    }

    @Override
    public PageResult<ModelFieldDO> modelFieldPage(ModelFieldPageReqVO reqVO) {
        return fieldMapper.selectPage(reqVO);
    }

}
