package com.mongoso.mgs.module.model.service.modelfieldalter;

import java.util.*;
import jakarta.validation.*;
import com.mongoso.mgs.module.model.controller.admin.modelfieldalter.vo.*;
import com.mongoso.mgs.module.model.dal.db.modelfieldalter.ModelFieldAlterDO;
import com.mongoso.mgs.framework.common.domain.PageResult;

/**
 * 图形建模字段 Service 接口
 *
 * <AUTHOR>
 */
public interface ModelFieldAlterService {

    /**
     * 创建图形建模字段
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long modelFieldAlterAdd(@Valid ModelFieldAlterAditReqVO reqVO);

    /**
     * 更新图形建模字段
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long modelFieldAlterEdit(@Valid ModelFieldAlterAditReqVO reqVO);

    /**
     * 删除图形建模字段
     *
     * @param id 编号
     */
    void modelFieldAlterDel(Long id);

    /**
     * 获得图形建模字段信息
     *
     * @param id 编号
     * @return 图形建模字段信息
     */
    ModelFieldAlterDO modelFieldAlterDetail(Long id);

    /**
     * 获得图形建模字段列表
     *
     * @param reqVO 查询条件
     * @return 图形建模字段列表
     */
    List<ModelFieldAlterDO> modelFieldAlterList(@Valid ModelFieldAlterQueryReqVO reqVO);

    /**
     * 获得图形建模字段分页
     *
     * @param reqVO 查询条件
     * @return 图形建模字段分页
     */
    PageResult<ModelFieldAlterDO> modelFieldAlterPage(@Valid ModelFieldAlterPageReqVO reqVO);

}
