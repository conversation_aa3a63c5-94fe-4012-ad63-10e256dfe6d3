package com.mongoso.mgs.module.model.dal.db.modelquery;

import com.alibaba.fastjson.JSONObject;
import com.mongoso.mgs.framework.typehandler.JsonbTypeHandler;
import lombok.*;
import java.time.LocalDateTime;
import java.util.List;

import com.baomidou.mybatisplus.annotation.*;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;

/**
 * 自定义查询 DO
 *
 * <AUTHOR>
 */
@TableName("lowcode.sys_model_query")
//@KeySequence("sys_model_query_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ModelQueryDO extends OperateDO {

    /** 主键ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long queryId;

    /**
     * 数据源编号
     *
     */
    private Long dataSourceConfigId = 0L;

    /** 查询编码 */
    private String queryCode;

    /** 查询中文名 */
    private String queryName;

    /** 查询语句 */
    private String queryStatment;

    /** 备注 */
    private String remark;

    /** 类型 */
    private Integer dirType;

    /** 父节点 */
    private Long parentId;

    /** 类型配置 */
    @TableField(typeHandler = JsonbTypeHandler.class,fill = FieldFill.INSERT)
    private List<JSONObject> typeConf;
}
