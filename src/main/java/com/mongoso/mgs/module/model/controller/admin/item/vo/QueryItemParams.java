package com.mongoso.mgs.module.model.controller.admin.item.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/2/7
 * @description
 */
@Data
public class QueryItemParams implements Serializable {
	
    private static final long serialVersionUID = 1L;
    
    private Long itemId;

    private String itemName;

    private Long parentItemId;

    private Long projectId;

    private Integer category;

    private Integer itemType;
    
    private String hasDefault;
    
    //private String userId;

    private String orgId;

    private String hasItem;

    private String hasRole;

    private Integer queryType = 0;

    private Integer hasRoot = 0;

    private Integer hasPermission = 1;
}
