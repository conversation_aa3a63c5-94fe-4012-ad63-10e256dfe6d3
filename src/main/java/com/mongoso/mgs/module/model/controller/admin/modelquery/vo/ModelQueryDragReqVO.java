package com.mongoso.mgs.module.model.controller.admin.modelquery.vo;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * 图形建模主 AditReqVO
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
public class ModelQueryDragReqVO implements Serializable {
    /** 模型表id */
    private Long queryId;

    /** 父节点 */
//    @NotNull(message = "父节点不能为空")
    private Long parentId;

}
