package com.mongoso.mgs.module.model.dal.db.modeltableindex;

import lombok.*;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;

/**
 * 图形建模主表索引 DO
 *
 * <AUTHOR>
 */
@TableName("lowcode.sys_model_table_index")
//@KeySequence("sys_model_table_index_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ModelTableIndexDO extends OperateDO {

    /** id */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 索引ID
     */
    private Long idxId;

    /**
     * 索引名称
     */
    private String idxName;

    /** 模型表id */
    private Long tableId;

    /** 模型表实名 */
    private String tableCode;

    /** 是否生成过 */
    private Integer nonUnique;

    /** 索引序号 */
    private Integer idxSeq;

    /** 字段id */
    private Long fieldId;

    /** 字段编码 */
    private String fieldCode;

    /** 排序类型(asc,desc) */
    private String sortType;

    /** 索引类型(FULLTEXT,NORMAL,UNIQUE) */
    private String idxType;
    /**
     * 索引类型(betree,hash)
     */
    private String idxWay;

    /** 备注描述 */
    private String remark;


}
