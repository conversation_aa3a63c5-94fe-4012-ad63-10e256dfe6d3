package com.mongoso.mgs.module.model.controller.admin.modeltable.vo;

import com.mongoso.mgs.module.model.controller.admin.modelfield.vo.ModelFieldAditReqVO;
import com.mongoso.mgs.module.model.controller.admin.modeltableindex.vo.ModelTableIndexAditReqVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

/**
 * 图形建模主 AditReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ModelTableAditReqVO extends ModelTableBaseVO {

    /** 字段集字符串 */
    private String fieldStr;

    //private List<FileLogReqVO> fileList;
    //字段列表
//    @NotEmpty(message = "字段列表不能为空")
    /** 字段列表 */
    private List<ModelFieldAditReqVO> fields = new ArrayList<>();
    /** 添加字段列表 */
    private List<ModelFieldAditReqVO> fieldAdds;
    /** 更新字段列表 */
    private List<ModelFieldAditReqVO> fieldUpds;
    /** 删除字段列表 */
    private List<ModelFieldAditReqVO> fieldDels;
    /** 添加索引列表 */
    private List<ModelTableIndexAditReqVO> indexAdds;
    /** 更新索引列表 */
    private List<ModelTableIndexAditReqVO> indexUpds;
    /** 删除索引列表 */
    private List<ModelTableIndexAditReqVO> indexDels;
}
