package com.mongoso.mgs.module.model.controller.admin.modeltable.vo;

import com.mongoso.mgs.framework.common.domain.BaseTree;
import com.mongoso.mgs.module.model.controller.admin.modelfield.vo.ModelFieldRespVO;
import com.mongoso.mgs.module.model.controller.admin.modeltableindex.vo.ModelTableIndexRespVO;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 图形建模主 RespVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ModelTableRespVO extends BaseTree {

    /** 模型表id */
    private Long tableId;
    /** 项目id */
    private Long projectId;
    /** 数据源id */
    private Long dataSourceConfigId;
    /** 数据源名称 */
    private String dataSourceConfigName;
    /** 版本 */
    private String version;
    /** 图标 */
    private String icon;
    /** 模型表中文名 */
    @NotEmpty(message = "模型表中文名不能为空")
    private String tableName;
    /** 数据库类型 */
    private String dbType;
    /** 私钥 */
    private String privateKey;
    /** 路径 */
    private String path;
    /** 模型表全名 */
    private String fullName;

    /** 模型表实名 */
    @NotEmpty(message = "模型表实名不能为空")
    private String tableCode;

    /** 备注描述 */
    private String remark;

    /** 升级标记 */
//    @NotNull(message = "升级标记不能为空")
    private Integer upgradeFlag = 0;

    /** 是否生成过 */
//    @NotNull(message = "是否生成过不能为空")
    private Integer isGen = 0;
    /** 是否锁定 */
    private Integer isLock;

    /** JSON类型，0：子表，1：主表 */
    private Integer jsonType;

    /** 类型 */
//    @NotNull(message = "类型不能为空")
    private Integer dirType = 1;

    /** 父节点 */
//    @NotNull(message = "父节点不能为空")
    private Long parentId;
    /** 子节点 */
    private List<ModelTableRespVO> children;

    /** 属性类型，0：系统，1：用户 */
    private Integer propType;

    /** 字段列表 */
    private List<ModelFieldRespVO> fields;
    /** 索引列表 */
    private List<ModelTableIndexRespVO> indexs;


    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createdDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime updatedDt;

}
