package com.mongoso.mgs.module.model.dal.mysql.modelfield;

import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.module.model.dal.db.modelfield.ModelFieldDO;
import org.apache.ibatis.annotations.Mapper;
import com.mongoso.mgs.module.model.controller.admin.modelfield.vo.*;

/**
 * 图形建模字段 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ModelFieldMapper extends BaseMapperX<ModelFieldDO> {

    default PageResult<ModelFieldDO> selectPage(ModelFieldPageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<ModelFieldDO>lambdaQueryX()
                .likeIfPresent(ModelFieldDO::getFieldName, reqVO.getFieldName())
                .eqIfPresent(ModelFieldDO::getFieldCode, reqVO.getFieldCode())
                .eqIfPresent(ModelFieldDO::getFieldType, reqVO.getFieldType())
                .eqIfPresent(ModelFieldDO::getTableId, reqVO.getTableId())
                .eqIfPresent(ModelFieldDO::getIsNullable, reqVO.getIsNullable())
                .eqIfPresent(ModelFieldDO::getIsPrimaryKey, reqVO.getIsPrimaryKey())
                .eqIfPresent(ModelFieldDO::getDefaultVal, reqVO.getDefaultVal())
                .eqIfPresent(ModelFieldDO::getSort, reqVO.getSort())
                .eqIfPresent(ModelFieldDO::getPropType, reqVO.getPropType())
                .eqIfPresent(ModelFieldDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(ModelFieldDO::getCreatedDt, reqVO.getCreatedDt())
                .orderByDesc(ModelFieldDO::getCreatedDt));
    }

    default List<ModelFieldDO> selectList(ModelFieldQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<ModelFieldDO>lambdaQueryX()
                .likeIfPresent(ModelFieldDO::getFieldName, reqVO.getFieldName())
                .eqIfPresent(ModelFieldDO::getFieldCode, reqVO.getFieldCode())
                .eqIfPresent(ModelFieldDO::getFieldType, reqVO.getFieldType())
                .eqIfPresent(ModelFieldDO::getTableId, reqVO.getTableId())
                .eqIfPresent(ModelFieldDO::getIsNullable, reqVO.getIsNullable())
                .eqIfPresent(ModelFieldDO::getIsPrimaryKey, reqVO.getIsPrimaryKey())
                .eqIfPresent(ModelFieldDO::getDefaultVal, reqVO.getDefaultVal())
                .eqIfPresent(ModelFieldDO::getSort, reqVO.getSort())
                .eqIfPresent(ModelFieldDO::getPropType, reqVO.getPropType())
                .eqIfPresent(ModelFieldDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(ModelFieldDO::getCreatedDt, reqVO.getCreatedDt())
                    .orderByDesc(ModelFieldDO::getCreatedDt));
    }

}