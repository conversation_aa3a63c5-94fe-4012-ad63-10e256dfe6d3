package com.mongoso.mgs.module.model.controller.admin.modeltableindex.vo;

import lombok.*;

import com.mongoso.mgs.framework.common.domain.PageParam;

































import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 图形建模主表索引 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ModelTableIndexPageReqVO extends PageParam {

    /** 模型表id */
    private Long tableId;

    /** 模型表实名 */
    private String tableCode;

    /** 是否生成过 */
    private Integer nonUnique;

    /** 索引序号 */
    private Integer idxSeq;

    /** 字段编码 */
    private String fieldCode;

    /** 排序类型(asc,desc) */
    private String sortType;

    /** 索引类型(betree,hash) */
    private String idxType;
    private String idxWay;

    /** 备注描述 */
    private String remark;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createdDt;

}
