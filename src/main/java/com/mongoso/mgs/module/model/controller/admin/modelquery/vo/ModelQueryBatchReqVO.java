package com.mongoso.mgs.module.model.controller.admin.modelquery.vo;

import com.mongoso.mgs.module.model.controller.admin.modelqueryparam.vo.ModelQueryParamAditReqVO;
import com.mongoso.mgs.module.table.dal.db.tablerelationconf.TableRelationConfDO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 自定义查询 PrimaryReqVO
 *
 * <AUTHOR>
 */
@Data
public class ModelQueryBatchReqVO implements Serializable {

    /** 查询语句 */
//    @NotEmpty(message = "SQL语句不能为空")
    private String queryStatment;

    /** 数据源id */
    private Long dataSourceConfigId;

    /** 查询id */
    private Long queryId;

    /** 查询参数列表 */
    List<ModelQueryParamAditReqVO> params;

    /** 关联表配置列表 */
    List<TableRelationConfDO> relations;
}
