package com.mongoso.mgs.module.model.service.modelfieldconf;

import java.util.*;

import com.mongoso.mgs.module.model.controller.admin.modeltable.vo.ModelTableListifyReqVO;
import jakarta.validation.*;
import com.mongoso.mgs.module.model.controller.admin.modelfieldconf.vo.*;
import com.mongoso.mgs.module.model.dal.db.modelfieldconf.ModelFieldConfDO;
import com.mongoso.mgs.framework.common.domain.PageResult;

/**
 * 图形建模字段翻译配置 Service 接口
 *
 * <AUTHOR>
 */
public interface ModelFieldConfService {

    /**
     * 创建图形建模字段翻译配置
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long modelFieldConfAdd(@Valid ModelFieldConfAditReqVO reqVO);

    /**
     * 更新图形建模字段翻译配置
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long modelFieldConfEdit(@Valid ModelFieldConfAditReqVO reqVO);

    /**
     * 删除图形建模字段翻译配置
     *
     * @param id 编号
     */
    void modelFieldConfDel(Long id);

    /**
     * 获得图形建模字段翻译配置信息
     *
     * @param id 编号
     * @return 图形建模字段翻译配置信息
     */
    ModelFieldConfDO modelFieldConfDetail(Long id);

    /**
     * 获得图形建模字段翻译配置列表
     *
     * @param reqVO 查询条件
     * @return 图形建模字段翻译配置列表
     */
    List<ModelFieldConfDO> modelFieldConfList(@Valid ModelFieldConfQueryReqVO reqVO);

    /**
     * 获得图形建模字段翻译配置分页
     *
     * @param reqVO 查询条件
     * @return 图形建模字段翻译配置分页
     */
    PageResult<ModelFieldConfRespVO> modelFieldConfPage(@Valid ModelFieldConfPageReqVO reqVO);

    /**
     * list格式字段翻译
     * @param reqVO
     * @return
     */
    List<ModelFieldConfDO> modelFieldTrans(@Valid ModelFieldTransReqVO reqVO);

    /**
     * 根据项目id和字段名列表查询字段配置
     * @param projectId
     * @param param
     * @param fieldNameList
     * @return
     */
    List<ModelFieldConfDO> getModelFieldConfDOS(Long projectId, String param, List<String> fieldNameList);

    /**
     * 字段翻译保存
     * @param reqVO
     * @return
     */
    Boolean modelFieldTransSave(@Valid ModelFieldTransReqVO reqVO);

    /**
     * JSON格式的字段翻译
     * @param reqVO
     * @param translateCountMap
     * @return
     */
    String fieldTranslate(ModelTableListifyReqVO reqVO, Map<String, Integer> translateCountMap);

    /**
     * 匹配字段返回前端
     * @param list
     * @return
     */
    List<CreateInterfaceFieldParams> matchField(List<CreateInterfaceFieldParams> list);

    /**
     * 批量删除翻译配置
     * @param reqVO
     */
    void modelFieldConfDelBatch(@Valid ModelFieldConfDelBatchReqVO reqVO);
}
