package com.mongoso.mgs.module.model.service.modelquery;

import cn.hutool.db.Entity;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.module.model.controller.admin.modelquery.vo.*;
import com.mongoso.mgs.module.model.dal.db.modelquery.ModelQueryDO;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 自定义查询 Service 接口
 *
 * <AUTHOR>
 */
public interface ModelQueryService {

    /**
     * 创建自定义查询
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long modelQueryAdd(@Valid ModelQueryAditReqVO reqVO);

    /**
     * 更新自定义查询
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long modelQueryEdit(@Valid ModelQueryAditReqVO reqVO);

    Long repeatValid(ModelQueryAditReqVO reqVO);
    /**
     * 删除自定义查询
     *
     * @param queryId 编号
     */
    void modelQueryDel(Long queryId);

    /**
     * 获得自定义查询信息
     *
     * @param queryId 编号
     * @return 自定义查询信息
     */
    ModelQueryRespVO modelQueryDetail(Long queryId);

    /**
     * 获得自定义查询列表
     *
     * @param reqVO 查询条件
     * @return 自定义查询列表
     */
    List<ModelQueryDO> modelQueryList(@Valid ModelQueryQueryReqVO reqVO);

    /**
     * 获得自定义查询分页
     *
     * @param reqVO 查询条件
     * @return 自定义查询分页
     */
    PageResult<ModelQueryDO> modelQueryPage(@Valid ModelQueryPageReqVO reqVO);

    PageResult<Map<String, Object>> modelQueryExc(ModelQueryPrimaryReqVO req);

    String dbType(Long dataSourceConfigId);

    Integer modelQueryDrag(@Valid ModelQueryDragReqVO reqVO);

    List<ModelQueryRespVO> modelQueryTree();

    List<Map<String, Object>> modelQueryBatch(@Valid ModelQueryBatchReqVO reqVO);
}
