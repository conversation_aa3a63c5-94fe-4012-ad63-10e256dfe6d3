package com.mongoso.mgs.module.model.controller.admin.modelreport;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.model.controller.admin.modelreport.vo.*;
import com.mongoso.mgs.module.model.controller.admin.modeltable.vo.ModelTableDragReqVO;
import com.mongoso.mgs.module.model.dal.db.modelreport.ModelReportDO;
import com.mongoso.mgs.module.model.service.modelreport.ModelReportService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

import static com.mongoso.mgs.framework.common.domain.ResultX.success;

/**
 * 自定义报 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/model")
@Validated
public class ModelReportController {

    @Resource
    private ModelReportService reportService;

    @OperateLog("自定义报添加或编辑")
    @PostMapping("/modelReportAdit")
    @PreAuthorize("@ss.hasPermission('modelReport:adit')")
    public ResultX<Long> modelReportAdit(@Valid @RequestBody ModelReportAditReqVO reqVO) {
        return success(reqVO.getReportId() == null
                            ? reportService.modelReportAdd(reqVO)
                            : reportService.modelReportEdit(reqVO));
    }

    @OperateLog("自定义报删除")
    @PostMapping("/modelReportDel")
    @PreAuthorize("@ss.hasPermission('modelReport:del')")
    public ResultX<Boolean> modelReportDel(@Valid @RequestBody ModelReportPrimaryReqVO reqVO) {
        reportService.modelReportDel(reqVO.getReportId());
        return success(true);
    }

    @OperateLog("自定义报详情")
    @PostMapping("/modelReportDetail")
    @PreAuthorize("@ss.hasPermission('modelReport:query')")
    public ResultX<ModelReportRespVO> modelReportDetail(@Valid @RequestBody ModelReportPrimaryReqVO reqVO) {
        return success(reportService.modelReportDetail(reqVO.getReportId()));
    }

    @OperateLog("自定义报列表")
    @PostMapping("/modelReportList")
    @PreAuthorize("@ss.hasPermission('modelReport:query')")
    public ResultX<List<ModelReportRespVO>> modelReportList(@Valid @RequestBody ModelReportQueryReqVO reqVO) {
        List<ModelReportDO> list = reportService.modelReportList(reqVO);
        return success(BeanUtilX.copyList(list, ModelReportRespVO::new));
    }

    @OperateLog("自定义报分页")
    @PostMapping("/modelReportPage")
    @PreAuthorize("@ss.hasPermission('modelReport:query')")
    public ResultX<PageResult<ModelReportRespVO>> modelReportPage(@Valid @RequestBody ModelReportPageReqVO reqVO) {
        PageResult<ModelReportDO> pageResult = reportService.modelReportPage(reqVO);
        return success(BeanUtilX.copyPage(pageResult, ModelReportRespVO::new));
    }

    @OperateLog("查询Tree")
    @PostMapping("/modelReportTree")
    @PreAuthorize("@ss.hasPermission('modelReport:query')")
    public ResultX<List<ModelReportRespVO>> modelReportTree() {
        return success(reportService.modelReportTree());
    }

    @OperateLog("自定义报表修改父节点")
    @PostMapping("/modelReportDrag")
    @PreAuthorize("@ss.hasPermission('modelReport:adit')")
    public ResultX<Integer> modelReportDrag(@Valid @RequestBody ModelReportDragReqVO reqVO) {
        return success(reportService.modelReportDrag(reqVO));
    }
}
