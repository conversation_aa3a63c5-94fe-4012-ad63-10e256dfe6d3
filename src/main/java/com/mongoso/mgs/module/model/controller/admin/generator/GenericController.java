package com.mongoso.mgs.module.model.controller.admin.generator;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.module.business.controller.vo.BusinessNodeConfigVO;
import com.mongoso.mgs.module.business.service.BusinessNodeService;
import com.mongoso.mgs.module.business.service.BusinessProcessService;
import com.mongoso.mgs.module.model.service.generator.GenericCrudService;
import com.mongoso.mgs.module.rule.domain.RuleValidationResult;
import com.mongoso.mgs.module.rule.service.RuleEngineService;
import com.mongoso.mgs.module.script.service.ScriptExecutionService;
import com.mongoso.mgs.module.workflow.controller.vo.WorkflowStartReqVO;
import com.mongoso.mgs.module.workflow.service.WorkflowService;
import jakarta.annotation.Resource;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/2/14
 * @description 建模通用接口
 */
@RestController
@RequestMapping("/baseapi")
@Validated
@Log4j2
public class GenericController {

    @Autowired
    private GenericCrudService genericCrudService;

    @Autowired
    private RuleEngineService ruleEngineService;
    
    @Autowired
    private WorkflowService workflowService;

    @Resource
    private ScriptExecutionService scriptExecutionService;

    @Resource
    private BusinessNodeService businessNodeService;

    @Resource
    private BusinessProcessService businessProcessService;

    // 测试单据SQL增删改（集成规则引擎和工作流）
    /**
     * 多表增删改
     * @param bizId 业务编码
     * @param data
     * @return
     */
    @PostMapping("/saveBatch/{bizId}")
    public ResultX<Long> saveBatch(@PathVariable Long bizId, @RequestBody Map<String, Object> data) {
        try {
            log.info("开始批量保存, bizId: {}, 数据结构: {}", bizId, data.keySet());

            Long resultMainId = executeWithBusinessNodes(bizId, "SAVE_BATCH", data, (preprocessedData) -> {
                log.info("预处理后的数据字段: {}", preprocessedData.keySet());

                // 调用新的包含脚本执行的方法，确保在同一Spring事务中
                Long mainId = genericCrudService.generateTreeInsertSQLWithScript(0L, bizId, preprocessedData);
                
                return mainId;
            });

            log.info("批量保存完成, bizId: {}, mainId: {}", bizId, resultMainId);
            return ResultX.success(resultMainId);

        } catch (BizException e) {
            log.error("批量保存业务异常, bizId: {}, error: {}", bizId, e.getMessage(), e);
            return ResultX.error(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("批量保存系统异常, bizId: {}, error: {}", bizId, e.getMessage(), e);
            return ResultX.error("5001", "保存失败: " + e.getMessage());
        }
    }

    // 测试单据SQL查询
    /**
     * 多表查询
     * @param bizId 业务编码
     * @param mainId 数据主表id
     * @return
     * @throws SQLException
     */
    @PostMapping("/batchRead/{bizId}/{mainId}")
    public ResultX<Map<String, Object>> batchRead(@PathVariable Long bizId, @PathVariable Long mainId) throws SQLException {
        return ResultX.success(genericCrudService.batchRead(bizId, mainId));
    }

    // 创建记录（集成规则引擎和工作流）
    @PostMapping("/{tableId}/Add")
    public ResultX<Long> create(@PathVariable Long tableId, @RequestBody Map<String, Object> data) {
        try {
            // 1. 执行规则校验
            RuleValidationResult validation = ruleEngineService.validateBusinessRules(tableId, data);
            if (!validation.isValid()) {
                return ResultX.error("5002", validation.getErrorMessage());
            }

            // 2. 执行规则计算（自动填充字段）
            Map<String, Object> calculatedData = ruleEngineService.executeCalculationRules(tableId, data);

            // 3. 保存数据
            Long id = genericCrudService.create(tableId, calculatedData);
            
            //Map<String, Object> result = new HashMap<>();
            //result.put("id", id);
            //result.put("processInstanceId", processInstanceId);
            return ResultX.success(id);
            
        } catch (Exception e) {
            log.error("创建记录失败, tableId: {}, error: {}", tableId, e.getMessage(), e);
            return ResultX.error("5001", "创建失败: " + e.getMessage());
        }
    }

    // 更新记录（集成规则引擎）
    @PostMapping("/{tableId}/Update")
    @Transactional(rollbackFor = Exception.class)
    public ResultX<Boolean> update(@PathVariable Long tableId, @RequestBody Map<String, Object> params) {
        try {
            // 1. 执行规则校验
            RuleValidationResult validation = ruleEngineService.validateBusinessRules(tableId, params);
            if (!validation.isValid()) {
                return ResultX.error("5002", validation.getErrorMessage());
            }

            // 2. 执行规则计算（自动填充字段）
            Map<String, Object> calculatedData = ruleEngineService.executeBusinessCalculationRules(tableId, params);

            // 3. 更新数据
            genericCrudService.update(tableId, calculatedData);
            return ResultX.success(true);
            
        } catch (Exception e) {
            log.error("更新记录失败, tableId: {}, error: {}", tableId, e.getMessage(), e);
            return ResultX.error("5001", "更新失败: " + e.getMessage());
        }
    }

    // 读取记录
    @PostMapping("/{tableId}/Detail")
    public ResultX<Map<String, Object>> read(@PathVariable Long tableId, @RequestBody Map<String, Object> params) {
        try {
            Map<String, Object> data = genericCrudService.read(tableId, params);
            return ResultX.success(data);
        } catch (Exception e) {
            log.error("读取记录失败, tableId: {}, error: {}", tableId, e.getMessage(), e);
            return ResultX.error("5001", "读取失败: " + e.getMessage());
        }
    }

    // 删除记录
    @PostMapping("/{tableId}/Delete")
    @Transactional(rollbackFor = Exception.class)
    public ResultX<Boolean> delete(@PathVariable Long tableId, @RequestBody Map<String, Object> params) {
        try {
            genericCrudService.delete(tableId, params);
            return ResultX.success(true);
        } catch (Exception e) {
            log.error("删除记录失败, tableId: {}, error: {}", tableId, e.getMessage(), e);
            return ResultX.error("5001", "删除失败: " + e.getMessage());
        }
    }

    // 获取分页数据
    @PostMapping("/{tableId}/Page")
    public ResultX<PageResult> getPagedData(
            @PathVariable Long tableId,
            @RequestBody Map<String, Object> params) {
        try {
            String filter = (String) params.get("filter");
            int currentPage = (int) params.getOrDefault("currentPage", 1);
            int pageSize = (int) params.getOrDefault("pageSize", 25);

            // 获取分页数据和总记录数
            Long totalCount = genericCrudService.getCount(tableId, filter);
            if (totalCount == null || totalCount == 0) {
                return ResultX.success(PageResult.empty());
            }
            List<Map<String, Object>> data = genericCrudService.getPagedData(tableId, filter, currentPage, pageSize);

            // 计算总页数
            int totalPages = (int) Math.ceil((double) totalCount / pageSize);
            return ResultX.success(PageResult.init(currentPage, totalPages, pageSize, totalCount, data));
        } catch (Exception e) {
            log.error("获取分页数据失败, tableId: {}, error: {}", tableId, e.getMessage(), e);
            return ResultX.error("5001", "获取数据失败: " + e.getMessage());
        }
    }

    // 获取所有记录
    @PostMapping("/{tableId}/List")
    public ResultX<List<Map<String, Object>>> getAllData(@PathVariable("tableId") Long tableId, @RequestBody Map<String, Object> params) {
        try {
            String filter = (String) params.get("filter");
            List<Map<String, Object>> data = genericCrudService.getAllData(tableId, filter);
            return ResultX.success(data);
        } catch (Exception e) {
            log.error("获取所有数据失败, tableId: {}, error: {}", tableId, e.getMessage(), e);
            return ResultX.error("5001", "获取数据失败: " + e.getMessage());
        }
    }
    
    // 工作流相关接口
    
    // 启动工作流
    @PostMapping("/{tableId}/workflow/start")
    @Transactional(rollbackFor = Exception.class)
    public ResultX<String> startWorkflow(@PathVariable Long tableId, @RequestBody Map<String, Object> params) {
        try {
            String processKey = (String) params.get("processKey");
            String businessId = (String) params.get("businessId");
            
            if (processKey == null || businessId == null) {
                return ResultX.error("5001", "processKey和businessId不能为空");
            }
            
            WorkflowStartReqVO workflowReq = new WorkflowStartReqVO();
            workflowReq.setTableId(tableId);
            workflowReq.setBusinessId(businessId);
            workflowReq.setBusinessKey("table_" + tableId + "_record_" + businessId);
            workflowReq.setFormData(params);
            
            String processInstanceId = workflowService.startProcess(processKey, workflowReq);
            return ResultX.success(processInstanceId);
            
        } catch (Exception e) {
            log.error("启动工作流失败, tableId: {}, error: {}", tableId, e.getMessage(), e);
            return ResultX.error("5001", "启动工作流失败: " + e.getMessage());
        }
    }
    
    // 规则校验接口（支持bizId维度）
    @PostMapping("/{tableId}/validate")
    public ResultX<RuleValidationResult> validateRules(@PathVariable Long tableId, @RequestBody Map<String, Object> data) {
        try {
            // 检查是否有bizId参数
            Long bizId = data.containsKey("bizId") ? 
                Long.valueOf(data.get("bizId").toString()) : null;
            
            RuleValidationResult result = bizId != null ? 
                ruleEngineService.validateBusinessRules(bizId, tableId, data) :
                ruleEngineService.validateBusinessRules(tableId, data);
                
            return ResultX.success(result);
        } catch (Exception e) {
            log.error("规则校验失败, tableId: {}, error: {}", tableId, e.getMessage(), e);
            return ResultX.error("5001", "规则校验失败: " + e.getMessage());
        }
    }
    
    // 规则计算接口（支持bizId维度）
    @PostMapping("/{tableId}/calculate")
    public ResultX<Map<String, Object>> calculateRules(@PathVariable Long tableId, @RequestBody Map<String, Object> data) {
        try {
            // 检查是否有bizId参数
            Long bizId = data.containsKey("bizId") ? 
                Long.valueOf(data.get("bizId").toString()) : null;
                
            Map<String, Object> result = bizId != null ?
                ruleEngineService.executeCalculationRules(bizId, data) :
                ruleEngineService.executeCalculationRules(tableId, data);
                
            return ResultX.success(result);
        } catch (Exception e) {
            log.error("规则计算失败, tableId: {}, error: {}", tableId, e.getMessage(), e);
            return ResultX.error("5001", "规则计算失败: " + e.getMessage());
        }
    }

    // 新增：基于bizId的规则校验接口
    @PostMapping("/validate/{bizId}")
    public ResultX<RuleValidationResult> validateBusinessRules(@PathVariable Long bizId, @RequestBody Map<String, Object> data) {
        try {
            RuleValidationResult result = ruleEngineService.validateBusinessRules(bizId, null, data);
            return ResultX.success(result);
        } catch (Exception e) {
            log.error("业务规则校验失败, bizId: {}, error: {}", bizId, e.getMessage(), e);
            return ResultX.error("5001", "业务规则校验失败: " + e.getMessage());
        }
    }
    
    // 新增：基于bizId的规则计算接口
    @PostMapping("/calculate/{bizId}")
    public ResultX<Map<String, Object>> calculateBusinessRules(@PathVariable Long bizId, @RequestBody Map<String, Object> data) {
        try {
            Map<String, Object> result = ruleEngineService.executeCalculationRules(bizId, data);
            return ResultX.success(result);
        } catch (Exception e) {
            log.error("业务规则计算失败, bizId: {}, error: {}", bizId, e.getMessage(), e);
            return ResultX.error("5001", "业务规则计算失败: " + e.getMessage());
        }
    }

    // 执行前端定制脚本
    @PostMapping("/{bizId}/execute")
    public ResultX<Object> executeCustomScript(
            @PathVariable Long bizId,
            @RequestBody Map<String, Object> params) {
        try {
            String scriptId = (String) params.get("scriptId");
            Map<String, Object> scriptParams = (Map<String, Object>) params.getOrDefault("params", new HashMap<>());
            
            // 执行自定义脚本
            Object result = scriptExecutionService.executeScript(bizId, scriptId, scriptParams);
            return ResultX.success(result);
        } catch (Exception e) {
            log.error("执行自定义脚本失败, bizId: {}, error: {}", bizId, e.getMessage(), e);
            return ResultX.error("5001", "执行失败: " + e.getMessage());
        }
    }

    // 获取业务流程节点配置
    @GetMapping("/{bizId}/nodes")
    public ResultX<List<BusinessNodeConfigVO>> getBusinessNodes(@PathVariable Long bizId) {
        try {
            List<BusinessNodeConfigVO> nodes = businessNodeService.getNodesByBizId(bizId);
            return ResultX.success(nodes);
        } catch (Exception e) {
            log.error("获取业务节点失败, bizId: {}, error: {}", bizId, e.getMessage(), e);
            return ResultX.error("5001", "获取业务节点失败: " + e.getMessage());
        }
    }

    // 保存业务流程节点配置
    @PostMapping("/{bizId}/nodes/save")
    public ResultX<Boolean> saveBusinessNodes(
            @PathVariable Long bizId,
            @RequestBody List<BusinessNodeConfigVO> nodes) {
        try {
            businessNodeService.saveNodes(bizId, nodes);
            return ResultX.success(true);
        } catch (Exception e) {
            log.error("保存业务节点失败, bizId: {}, error: {}", bizId, e.getMessage(), e);
            return ResultX.error("5001", "保存业务节点失败: " + e.getMessage());
        }
    }

    // 启动业务流程（优化事务管理）
    @PostMapping("/{bizId}/process/start")
    public ResultX<Map<String, Object>> startBusinessProcess(
            @PathVariable Long bizId,
            @RequestBody Map<String, Object> data) {
        try {
            log.info("开始启动业务流程, bizId: {}", bizId);
            
            // 1. 数据保存和校验（独立事务）
            Long mainId = saveBusinessDataWithTransaction(bizId, data);
            
            // 2. 启动工作流（独立事务）
            String processInstanceId = startWorkflowWithTransaction(bizId, mainId, data);
            
            // 3. 执行后续节点（独立事务，允许失败）
            executePostStartNodesAsync(bizId, mainId, data);
            
            Map<String, Object> result = new HashMap<>();
            result.put("mainId", mainId);
            result.put("processInstanceId", processInstanceId);
            return ResultX.success(result);
            
        } catch (Exception e) {
            log.error("启动业务流程失败, bizId: {}, error: {}", bizId, e.getMessage(), e);
            return ResultX.error("5001", "启动业务流程失败: " + e.getMessage());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public Long saveBusinessDataWithTransaction(Long bizId, Map<String, Object> data) throws SQLException {
        // 校验和保存数据
        RuleValidationResult validation = ruleEngineService.validateBusinessRules(bizId, null, data);
        if (!validation.isValid()) {
            throw new BizException("5002", validation.getErrorMessage());
        }
        
        Map<String, Object> calculatedData = ruleEngineService.executeCalculationRules(bizId, data);
        return genericCrudService.generateTreeInsertSQL(0L, bizId, calculatedData);
    }

    @Transactional(rollbackFor = Exception.class)
    public String startWorkflowWithTransaction(Long bizId, Long mainId, Map<String, Object> data) {
        // 启动工作流
        String processKey = businessProcessService.getProcessKeyByBizId(bizId);
        if (processKey == null) {
            return null;
        }
        
        WorkflowStartReqVO workflowReq = new WorkflowStartReqVO();
        workflowReq.setBusinessId(mainId.toString());
        workflowReq.setBusinessKey("biz_" + bizId + "_record_" + mainId);
        workflowReq.setFormData(data);
        
        return workflowService.startProcess(processKey, workflowReq);
    }

    @Async
    public void executePostStartNodesAsync(Long bizId, Long mainId, Map<String, Object> data) {
        try {
            businessNodeService.executeNodesByEvent(bizId, "PROCESS_STARTED", "START", mainId, data);
        } catch (Exception e) {
            log.error("执行启动后节点失败, bizId: {}, mainId: {}, error: {}", 
                    bizId, mainId, e.getMessage(), e);
        }
    }

    // 完成工作流任务（集成节点执行）
    @PostMapping("/workflow/task/complete")
    @Transactional(rollbackFor = Exception.class)
    public ResultX<Boolean> completeWorkflowTask(@RequestBody Map<String, Object> params) {
        try {
            String taskId = (String) params.get("taskId");
            Map<String, Object> variables = (Map<String, Object>) params.getOrDefault("variables", new HashMap<>());
            String action = (String) params.get("action");
            
            // 1. 获取任务信息 - 修复：从任务变量中获取业务信息
            Map<String, Object> taskVariables = workflowService.getTaskVariables(taskId);
            Long bizId = taskVariables.containsKey("bizId") ? 
                Long.valueOf(taskVariables.get("bizId").toString()) : null;
            Long mainId = taskVariables.containsKey("businessId") ? 
                Long.valueOf(taskVariables.get("businessId").toString()) : null;
            
            // 2. 执行任务前的自定义节点
            if (bizId != null && mainId != null) {
                businessNodeService.executeNodesByEvent(bizId, "BEFORE_TASK_COMPLETE", "TASK_COMPLETE", mainId, variables);
            }
            
            // 3. 完成任务 - 修复：使用正确的方法签名
            workflowService.completeTask(taskId, variables);
            
            // 4. 执行任务后的自定义节点
            if (bizId != null && mainId != null) {
                businessNodeService.executeNodesByEvent(bizId, "AFTER_TASK_COMPLETE", "TASK_COMPLETE", mainId, variables);
            }
            
            return ResultX.success(true);
        } catch (Exception e) {
            log.error("完成工作流任务失败, error: {}", e.getMessage(), e);
            return ResultX.error("5001", "完成任务失败: " + e.getMessage());
        }
    }

    // 通用的事务内代码嵌入方法
    @Transactional(rollbackFor = Exception.class)
    public <T> T executeWithBusinessNodes(Long bizId, String operation, Map<String, Object> data, 
                                         TransactionalOperation<T> coreOperation) {
        try {
            // 前置节点执行
            Map<String, Object> preprocessedData = businessNodeService.executeNodesByEvent(
                bizId, "BEFORE", operation, null, data);

            // 核心业务逻辑执行（包含SQL和脚本）
            T result = coreOperation.execute(preprocessedData);

            // 后置节点执行
            //Map<String, Object> postProcessData = new HashMap<>(preprocessedData);
            //if (result != null) {
            //    postProcessData.put("operationResult", result);
            //    postProcessData.put("mainId", result);
            //}
            //businessNodeService.executeNodesByEvent(
            //    bizId, "AFTER", operation, result instanceof Long ? (Long)result : null, postProcessData);

            return result;
            
        } catch (BizException e) {
            log.error("执行业务操作失败, bizId: {}, operation: {}, error: {}", 
                     bizId, operation, e.getMessage(), e);
            throw e; // 直接重新抛出，保持原始错误信息
        } catch (Exception e) {
            log.error("执行业务操作系统异常, bizId: {}, operation: {}, error: {}", 
                     bizId, operation, e.getMessage(), e);
            throw new BizException("5001", "执行业务操作失败: " + e.getMessage());
        }
    }

    @FunctionalInterface
    public interface TransactionalOperation<T> {
        T execute(Map<String, Object> data) throws Exception;
    }

    @PostMapping("/executeSql")
    public ResultX<Boolean> executeSql(@RequestBody Map<String, Object> request) {
        try {
            String sql = (String) request.get("sql");

            if (sql == null || sql.trim().isEmpty()) {
                throw new BizException("5002", "SQL不能为空");
            }

            // 执行SQL
            Boolean result = genericCrudService.executeBatchQueries(sql);

            return ResultX.success(result);

        } catch (Exception e) {
            log.error("执行SQL失败: {}", e.getMessage(), e);
            throw new BizException("5002", "SQL执行失败:" + e.getMessage());
        }
    }
}
