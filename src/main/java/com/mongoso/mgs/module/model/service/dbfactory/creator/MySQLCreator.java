package com.mongoso.mgs.module.model.service.dbfactory.creator;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.mongoso.mgs.common.util.ColumnInfo;
import com.mongoso.mgs.common.util.DatabaseUtil;
import com.mongoso.mgs.common.util.TableStructure;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.ObjUtilX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.enums.DBOPTypeEnum;
import com.mongoso.mgs.module.model.controller.admin.modelfield.vo.ModelFieldAditReqVO;
import com.mongoso.mgs.module.model.controller.admin.modeltableindex.vo.ModelTableIndexAditReqVO;
import com.mongoso.mgs.module.model.controller.admin.modeltableindex.vo.ModelTableIndexRespVO;
import com.mongoso.mgs.module.model.dal.db.modelfield.ModelFieldDO;
import com.mongoso.mgs.module.model.dal.db.modelquery.ModelQueryDO;
import com.mongoso.mgs.module.model.dal.db.modeltable.ModelTableDO;
import com.mongoso.mgs.module.model.dal.db.modeltableindex.ModelTableIndexDO;
import com.mongoso.mgs.module.model.dal.db.sqllog.SqlLogDO;
import com.mongoso.mgs.module.model.dal.mysql.modelfield.ModelFieldMapper;
import com.mongoso.mgs.module.model.dal.mysql.modeltableindex.ModelTableIndexMapper;
import com.mongoso.mgs.module.model.dal.mysql.sqllog.SqlLogMapper;
import com.mongoso.mgs.module.model.service.dbfactory.DBCreator;
import jakarta.annotation.Resource;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 *  mysql 数据库操作类
 *  daijinbiao
 *  2024-10-12
 */
@Service
@Log4j2
public class MySQLCreator implements DBCreator {

    @Resource
    private ModelFieldMapper fieldMapper;
    @Resource
    private ModelTableIndexMapper tableIndexMapper;
    @Resource
    private DatabaseUtil dbUtil;
    @Resource
    private SqlLogMapper sqlLogMapper;

    /********************************************/
    // 插入数据
    public int insertData(Long dataSourceId, String tableCode, String fields, String values) {
        String sql = String.format("INSERT INTO %s (%s) VALUES (%s)", tableCode, fields, values);
        try (Connection con = dbUtil.getCon(dataSourceId); Statement stmt = con.createStatement()) {
            return stmt.executeUpdate(sql);
        } catch (SQLException e) {
            throw new BizException("5001", "插入数据失败: " + e.getMessage());
        }
    }

    // 查询数据
    public Map<String, Object> selectData(Long dataSourceId, String tableCode, String pk, Long id) {
        String sql = String.format("SELECT * FROM %s WHERE %s = %d", tableCode, pk, id);
        try (Connection con = dbUtil.getCon(dataSourceId); Statement stmt = con.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {
            if (rs.next()) {
                return convertResultSetToMap(rs);
            }
        } catch (SQLException e) {
            throw new BizException("5001", "查询数据失败: " + e.getMessage());
        }
        return null;
    }

    // 更新数据
    public int updateData(Long dataSourceId, String tableCode, String pk, Long id, String updateFields) {
        String sql = String.format("UPDATE %s SET %s WHERE %s = %d", tableCode, updateFields, pk, id);
        try (Connection con = dbUtil.getCon(dataSourceId); Statement stmt = con.createStatement()) {
            return stmt.executeUpdate(sql);
        } catch (SQLException e) {
            throw new BizException("5001", "更新数据失败: " + e.getMessage());
        }
    }

    // 删除数据
    public int deleteData(Long dataSourceId, String tableCode, String pk, Long id) {
        String sql = String.format("DELETE FROM %s WHERE %s = %d", tableCode, pk, id);
        try (Connection con = dbUtil.getCon(dataSourceId); Statement stmt = con.createStatement()) {
            return stmt.executeUpdate(sql);
        } catch (SQLException e) {
            throw new BizException("5001", "删除数据失败: " + e.getMessage());
        }
    }

    // 分页查询
    public List<Map<String, Object>> selectPage(Long dataSourceId, String tableCode, String filter, int offset, int pageSize) {
        StringBuilder sql = new StringBuilder();
        sql.append(String.format("SELECT * FROM %s ", tableCode));
        if (filter != null) {
            sql.append("WHERE ").append(filter).append(" ");
        }
        sql.append("LIMIT ").append(pageSize).append(" OFFSET ").append(offset);

        try (Connection con = dbUtil.getCon(dataSourceId); Statement stmt = con.createStatement();
             ResultSet rs = stmt.executeQuery(sql.toString())) {
            List<Map<String, Object>> resultList = new ArrayList<>();
            while (rs.next()) {
                resultList.add(convertResultSetToMap(rs));
            }
            return resultList;
        } catch (SQLException e) {
            throw new BizException("5001", "分页查询失败: " + e.getMessage());
        }
    }

    // 查询所有数据
    public List<Map<String, Object>> selectAll(Long dataSourceId, String tableCode, String filter) {
        StringBuilder sql = new StringBuilder();
        sql.append(String.format("SELECT * FROM %s ", tableCode));
        if (filter != null) {
            sql.append("WHERE ").append(filter).append(" ");
        }

        try (Connection con = dbUtil.getCon(dataSourceId); Statement stmt = con.createStatement();
             ResultSet rs = stmt.executeQuery(sql.toString())) {
            List<Map<String, Object>> resultList = new ArrayList<>();
            while (rs.next()) {
                resultList.add(convertResultSetToMap(rs));
            }
            return resultList;
        } catch (SQLException e) {
            throw new BizException("5001", "查询所有数据失败: " + e.getMessage());
        }
    }


    /**********************************************/



    @Override
    public void createTable(ModelTableDO tableDO, Statement stmt) throws SQLException {
        StringBuilder sql = genCreateTableSQL(tableDO);
        stmt.execute(sql.toString());
        log.info("MySQL 表创建成功:" + tableDO.getTableCode());
    }

    @Override
    public void createTableDynamic(ModelTableDO tableDO) throws SQLException {
        SqlLogDO sqlLog = new SqlLogDO(tableDO.getProjectId(), tableDO.getTableId(), tableDO.getTableCode(), "", DBOPTypeEnum.CREATE_TABLE.getType(), 1, DBOPTypeEnum.CREATE_TABLE.getDesc());

        try(Connection con = dbUtil.getCon(tableDO.getDataSourceConfigId());
            Statement stmt = con.createStatement()){
            StringBuilder sql = genCreateTableSQL(tableDO);
            String sqlStr = sql.toString();
            stmt.execute(sqlStr);
            sqlLog.setSql(sqlStr);
            System.out.println("PostgreSQL 表创建成功");
        } catch (SQLException e) {
            sqlLog.setSucStatus(0);
            sqlLog.setErrMgs(e.getMessage());
            // 捕获 SQLException 并提供特定错误信息
            throw new BizException("5002", "脚本执行失败，请检查原因:" + e.getMessage());
        } catch (Exception e) {
            sqlLog.setSucStatus(0);
            sqlLog.setErrMgs(e.getMessage());
            throw new BizException("5001", "操作失败，请检查原因:"+e.getMessage());
        } finally {
            insertLog(sqlLog); // 调用独立方法处理日志插入
        }
    }

    @Override
    public StringBuilder genCreateTableSQL(ModelTableDO tableDO) {

        List<ModelFieldDO> fields = fieldMapper.selectList(LambdaQueryWrapperX.<ModelFieldDO>lambdaQueryX()
                .eq(ModelFieldDO::getTableId, tableDO.getTableId())
                .orderByAsc(ModelFieldDO::getSort));
        if(ObjUtilX.isEmpty(fields)){
            throw new BizException("5001","找不到模型表字段列表");
        }
        //String prefix = dbUtil.getDBNameAllDynamic(tableDO.getDataSourceConfigId(), true);
        //System.out.println(prefix);
        log.info("开始生成建表SQL:" + tableDO.getTableCode());
        StringBuilder sql = new StringBuilder();
        String lineSeparator = System.getProperty("line.separator");
        sql.append("-- ----------------------------").append(lineSeparator);
        sql.append("-- 表结构： ").append(tableDO.getTableCode()).append(lineSeparator);
        sql.append("-- ----------------------------").append(lineSeparator);
        sql.append("CREATE TABLE ");
        sql.append("`"+tableDO.getTableCode()+"` ");//
        sql.append(" (").append(lineSeparator);
        //添加ID，不要了，自己指定
//        sql.append("`id` int NOT NULL AUTO_INCREMENT COMMENT 'id',");
        // 用于存储主键字段
        List<String> primaryKeyFields = new ArrayList<>();
        //添加业务字段
        for (int i = 0; i < fields.size(); i++) {
            ModelFieldDO field = fields.get(i);
            //拼接类型
            sql.append("  ");
            getMysqlFullCode(field, sql);

            // 检查是否为自增字段
            if (ObjUtilX.isNotEmpty(field.getIsAutoIncrease()) && field.getIsAutoIncrease() == 1) {
                sql.append(" AUTO_INCREMENT"); // 添加自增属性
            }

            // 检查是否必填
            if (field.getIsNullable() == 1) {
                sql.append(" NOT NULL");
            }

            // 检查默认值
            if (ObjUtilX.isNotEmpty(field.getDefaultVal())) {
                sql.append(" DEFAULT '").append(field.getDefaultVal()).append("'");;
            }

            //预留主键设置
            //if (field.getIsPrimaryKey() ==  1) {
            //    sql.append(" PRIMARY KEY");
            //}
            // 收集主键字段
            if (field.getIsPrimaryKey() == 1) {
                primaryKeyFields.add("`" + field.getFieldCode() + "`");
            }
//            if (ObjUtilX.isNotEmpty(field.getRemark())) {
//                sql.append(" COMMENT '").append(field.getRemark()).append("'");
//            }

            // 添加描述
            if (ObjUtilX.isNotEmpty(field.getFieldName())) {
                sql.append(" COMMENT '").append(field.getFieldName()).append("'");
            }

            // 添加逗号
            if(i < fields.size() - 1){
                sql.append(",");
                sql.append(lineSeparator);
            }
            //sql.append(", ");
        }

        // 移除最后的逗号和空格
        //sql.setLength(sql.length() - 2);
        // 设置主键，添加描述
//        sql.append("PRIMARY KEY (`id`) USING BTREE ");

        //添加索引
        //现有的索引
        List<ModelTableIndexDO> indexDOS = tableIndexMapper.selectList(LambdaQueryWrapperX.<ModelTableIndexDO>lambdaQueryX()
                .eqIfPresent(ModelTableIndexDO::getTableId, tableDO.getTableId()));
        if(ObjUtilX.isNotEmpty(indexDOS)){
            Map<String, List<ModelTableIndexDO>> indexMap = indexDOS.stream().collect(Collectors.groupingBy(s -> s.getIdxName()));
            sql.append(",");
            int i = 0;
            for (Map.Entry<String, List<ModelTableIndexDO>> entry : indexMap.entrySet()) {
                sql.append(lineSeparator);
                String key = entry.getKey();
                List<ModelTableIndexDO> value = entry.getValue();
                if (!"NORMAL".equals(value.get(0).getIdxType())) {
                    sql.append("  ");
                    sql.append(value.get(0).getIdxType());
                }
                sql.append("  KEY ");
                sql.append("`");
                sql.append(key);
                sql.append("`");
                sql.append("(");
                for (ModelTableIndexDO idxField : value) {
                    sql.append("`");
                    sql.append(idxField.getFieldCode());
                    sql.append("`");
                    sql.append(", ");
                }
                // 移除最后的逗号和空格
                sql.setLength(sql.length() - 2);
                sql.append(")");
                if (!"FULLTEXT".equals(value.get(0).getIdxType())) {
                    sql.append(" USING BTREE");
                }
//                sql.append(" USING ");
//                sql.append(value.get(0).getIdxWay());
                if(ObjUtilX.isNotEmpty(value.get(0).getRemark())) {
                    sql.append(" COMMENT '");
                    sql.append(value.get(0).getRemark());
                    sql.append("' ");
                }
                if (i < indexMap.size() - 1) {
                    sql.append(",");
                    sql.append(lineSeparator);
                }
                i++;
            }
            // 移除最后的逗号和空格
            //sql.setLength(sql.length() - 2);
        }
        // 如果有主键字段，则添加到 SQL 中
        if (!primaryKeyFields.isEmpty()) {
            sql.append(", ").append(lineSeparator);
            sql.append("  PRIMARY KEY (");
            sql.append(String.join(", ", primaryKeyFields)); // 使用逗号连接主键字段
            sql.append(") USING BTREE");
        }
        sql.append(lineSeparator);
        sql.append(") ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb3 ");
        sql.append("COLLATE=utf8mb3_bin ROW_FORMAT=COMPACT COMMENT='");
        sql.append(tableDO.getTableName());
        sql.append("';");

        //if(ObjUtilX.isNotEmpty(tableDO.getRemark())) {
        //    sql.append(" COMMENT='");
        //    sql.append(tableDO.getRemark());
        //    sql.append("'");
        //}
        //sql.append(";");

        log.info("生成建表SQL完毕:\n" + sql);
        return sql;
    }

    @Override
    //    @Transactional(rollbackFor = Exception.class)
    public void dropField(ModelFieldDO change, ModelTableDO tableDO) {
        // 删除数据库字段的逻辑
        //String prefix = dbUtil.getDBNameAllDynamic(tableDO.getDataSourceConfigId(),true);//mysql可以不加前缀
        StringBuilder sql = new StringBuilder("ALTER TABLE ");
        sql.append("`"+tableDO.getTableCode()+"` ");
        //sql.append(prefix).append(".`").append(tableDO.getTableCode()).append("` "); // 使用双引号
        sql.append(" DROP COLUMN ");
        sql.append(change.getFieldCode());
        String sqlStr = sql.toString();
        dbUtil.exeSQL(tableDO, sqlStr, DBOPTypeEnum.DEL_FIELD);
        //SqlLogDO sqlLog = new SqlLogDO(tableDO.getProjectId(), tableDO.getTableCode(), sqlStr, DBOPTypeEnum.DEL_FIELD.getType(), 1, DBOPTypeEnum.DEL_FIELD.getDesc());
        //try (Connection connection = dbUtil.getCon(tableDO.getDataSourceConfigId());
        //     Statement stmt = connection.createStatement()) {
        //    stmt.execute(sqlStr);
        //    stmt.close();
        //    log.info("执行表字段删除完毕:" + tableDO.getTableCode() + " - " + change.getFieldCode());
        //    log.info("执行表字段删除完毕：" + sql);
        //} catch (SQLException e) {
        //    sqlLog.setSucStatus(0);
        //    sqlLog.setErrMgs(e.getMessage());
        //    // 捕获 SQLException 并提供特定错误信息
        //    throw new BizException("5002", "数据库操作失败: " + e.getMessage());
        //} catch (Exception e) {
        //    log.error("执行表字段删除失败：" + sql);
        //    e.printStackTrace();
        //    throw new BizException("5001","执行表字段删除失败："+ tableDO.getTableCode() + " - " + change.getFieldCode());
        //} finally {
        //    try {
        //        sqlLogMapper.insert(sqlLog);
        //    } catch (Exception e) {
        //        log.error("插入日志失败:" + e.getMessage());
        //    }
        //}
    }

    public StringBuilder getMysqlFullCode(ModelFieldDO field, StringBuilder sql) {
        sql.append("`");
        sql.append(field.getFieldCode());
        sql.append("` ");
        sql.append(field.getFieldType());
        String upperStr = field.getFieldType().toUpperCase();
        // 如果字段是 VARCHAR 且未指定长度，则使用默认长度 50
        if ("VARCHAR".equalsIgnoreCase(upperStr) && (ObjUtilX.isEmpty(field.getLeng()) || field.getLeng() <= 0)) {
            sql.append("(50)");
        } if ("CHAR".equalsIgnoreCase(upperStr) && (ObjUtilX.isEmpty(field.getLeng()) || field.getLeng() <= 0)) {
            sql.append("(10)");
        } else if (!upperStr.equals("DATE")
                && !upperStr.equals("TIMESTAMP")
                && !upperStr.equals("INT2")
                && !upperStr.equals("INT4")
                && !upperStr.equals("INT8")
                && !upperStr.equals("TEXT")
                && ObjUtilX.isNotEmpty(field.getLeng()) && field.getLeng() > 0) {
            sql.append("(").append(field.getLeng());
            if (ObjUtilX.isNotEmpty(field.getFieldPrecision()) && field.getFieldPrecision() > 0) {
                sql.append(",").append(field.getFieldPrecision());
            }
            sql.append(")");
        }
        return sql;
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED, rollbackFor = Exception.class)
    public String addField(ModelFieldDO change, ModelTableDO tableDO) {
        // 新增字段到数据库的逻辑
        StringBuilder sql = new StringBuilder("ALTER TABLE ");
        sql.append("`"+tableDO.getTableCode()+"` ");
        sql.append(" ADD COLUMN ");
        getMysqlFullCode(change, sql);
        // 检查是否为自增字段
        if (ObjUtilX.isNotEmpty(change.getIsAutoIncrease()) && change.getIsAutoIncrease() == 1) {
            sql.append(" AUTO_INCREMENT"); // 添加 AUTO_INCREMENT 属性
        }
        // 检查是否必填
        if (change.getIsNullable() == 1) {
            sql.append(" NOT NULL");
        }
        //默认值
        if (ObjUtilX.isNotEmpty(change.getDefaultVal())) {
            if(("CURRENT_TIMESTAMP").equals(change.getDefaultVal())){
                sql.append(" DEFAULT ").append(change.getDefaultVal());
            }else {
                sql.append(" DEFAULT '").append(change.getDefaultVal()).append("'");
            }
        }
        //预留主键设置
        if (change.getIsPrimaryKey() ==  1) {
            sql.append(" PRIMARY KEY");
        }
        // 检查是否添加注释
        if (ObjUtilX.isNotEmpty(change.getFieldName())) {
            sql.append(" COMMENT '").append(change.getFieldName()).append("'");
        }
        // 拼接 after
        if (ObjUtilX.isNotEmpty(change.getUpFieldCode())) {
            sql.append(" AFTER `").append(change.getUpFieldCode()).append("` ");
        }
        sql.append(";");
        String sqlStr = sql.toString();
        SqlLogDO sqlLog = new SqlLogDO(tableDO.getProjectId(), tableDO.getTableId(), tableDO.getTableCode(), sqlStr, DBOPTypeEnum.ADD_FIELD.getType(), 1, DBOPTypeEnum.ADD_FIELD.getDesc());
        try (Connection connection = dbUtil.getCon(tableDO.getDataSourceConfigId());
             Statement stmt = connection.createStatement()) {
            if(change.getNeedExe()) {
                stmt.execute(sqlStr);
            }
        } catch (SQLException e) {
            sqlLog.setSucStatus(0);
            sqlLog.setErrMgs(e.getMessage());
            log.error("执行表字段新增失败：" + sql);
            // 捕获 SQLException 并提供特定错误信息
            throw new BizException("5002", "数据库操作失败: " + e.getMessage());
        } catch (BizException e) {
            sqlLog.setSucStatus(0);
            sqlLog.setErrMgs(e.getMessage());
            log.error("执行表字段新增失败：" + sql);
            // 捕获 SQLException 并提供特定错误信息
            throw new BizException("5002", "数据库操作失败: " + e.getMessage());
        } catch (Exception e) {
            sqlLog.setSucStatus(0);
            sqlLog.setErrMgs(e.getMessage());
            log.error("执行表字段新增失败：" + sql);
            e.printStackTrace();
            throw new BizException("5001", "执行表字段新增失败：" + tableDO.getTableCode() + " - " + change.getFieldCode());
        } finally {
            insertLog(sqlLog); // 调用独立方法处理日志插入
        }
        return sqlStr;
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED, rollbackFor = Exception.class)
    public void modifyField(ModelFieldDO change, ModelFieldDO oldField, ModelTableDO tableDO) {
        StringBuilder sql = new StringBuilder("ALTER TABLE ");
        sql.append("`").append(tableDO.getTableCode()).append("` ");

        // 如果有旧字段名且新旧名称不同，则使用 CHANGE COLUMN 来更改字段名
        if (ObjUtilX.isNotEmpty(change.getOldFieldCode()) && !change.getOldFieldCode().equals(change.getFieldCode())) {
            sql.append(" CHANGE COLUMN `").append(change.getOldFieldCode()).append("` `").append(change.getFieldCode()).append("` ");
        } else {
            sql.append(" MODIFY COLUMN `").append(change.getFieldCode()).append("` "); // 只需使用一次列名
        }

        // 添加字段类型及其长度与精度
        sql.append(change.getFieldType()).append(" ");

        String upperStr = change.getFieldType().toUpperCase();
        boolean hasLengthOrPrecision = false;

        // 处理 VARCHAR 类型的默认长度
        if ("VARCHAR".equalsIgnoreCase(upperStr)) {
            int length = ObjUtilX.isNotEmpty(change.getLeng()) ? change.getLeng() : 50; // 默认长度为50
            sql.append("(").append(length).append(") ");
            hasLengthOrPrecision = true;
        }
        // 处理其他类型的长度和精度
        else if (!isNotLeng(upperStr) && ObjUtilX.isNotEmpty(change.getLeng())) {
            sql.append("(").append(change.getLeng());
            if (ObjUtilX.isNotEmpty(change.getFieldPrecision())) {
                sql.append(", ").append(change.getFieldPrecision());
            }
            sql.append(") ");
            hasLengthOrPrecision = true;
        }

        // 检查自增属性
        if (change.getIsAutoIncrease() == 1) {
            sql.append(" AUTO_INCREMENT"); // 添加 AUTO_INCREMENT 属性
        }

        // 设置是否必填
        if (change.getIsNullable() == 1) { // 0 表示 NOT NULL
            sql.append(" NOT NULL");
        }

        // 设置默认值对比
        if (!areDefaultValuesEqual(change.getDefaultVal(), oldField.getDefaultVal())) {
            sql.append(" DEFAULT '").append(change.getDefaultVal()).append("'");
        }

        // 添加字段注释
        if (ObjUtilX.isNotEmpty(change.getFieldName())) {
            sql.append(" COMMENT '").append(change.getFieldName()).append("'");
        }

        // 拼接 after
        if (ObjUtilX.isNotEmpty(change.getUpFieldCode())) {
            sql.append(" AFTER '").append(change.getUpFieldCode()).append("'");
        }
        sql.append(";");
        String sqlStr = sql.toString();
        SqlLogDO sqlLog = new SqlLogDO(tableDO.getProjectId(), tableDO.getTableId(), tableDO.getTableCode(), sqlStr, DBOPTypeEnum.UPD_FIELD.getType(), 1, DBOPTypeEnum.UPD_FIELD.getDesc());
        try (Connection connection = dbUtil.getCon(tableDO.getDataSourceConfigId());
             Statement stmt = connection.createStatement()) {
            if(change.getNeedExe()) {
                stmt.execute(sqlStr);
            }
        } catch (BizException e) {
            sqlLog.setSucStatus(0);
            sqlLog.setErrMgs(e.getMessage());
            log.error("执行表字段更新失败：" + sql);
            throw new BizException("5002", "数据库操作失败: " + e.getMessage());
        } catch (Exception e) {
            sqlLog.setSucStatus(0);
            sqlLog.setErrMgs(e.getMessage());
            log.error("执行表字段更新失败：" + sql);
            throw new BizException("5001", "执行表字段更新失败：" + tableDO.getTableCode() + " - " + change.getFieldCode());
        } finally {
            insertLog(sqlLog); // 调用独立方法处理日志插入
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    @Override
    public void insertLog(SqlLogDO sqlLog) {
        // 独立处理日志插入
        try {
            sqlLogMapper.insert(sqlLog);
        } catch (Exception e) {
            log.error("插入日志失败: " + e.getMessage());
        }
    }

    // 判断字段类型是否为日期或时间戳
    private boolean isNotLeng(String fieldType) {
        return "DATE".equalsIgnoreCase(fieldType)
                || "TINYINT".equalsIgnoreCase(fieldType)
                || "SMALLINT".equalsIgnoreCase(fieldType)
                || "INT".equalsIgnoreCase(fieldType)
                || "BIGINT".equalsIgnoreCase(fieldType)
                || "TIMESTAMP".equalsIgnoreCase(fieldType)
                || "DATETIME".equalsIgnoreCase(fieldType)
                || "BOOL".equalsIgnoreCase(fieldType)
                || "BOOLEAN".equalsIgnoreCase(fieldType)
                || "FLOAT".equalsIgnoreCase(fieldType)
                || "DOUBLE".equalsIgnoreCase(fieldType)
                || "TIME".equalsIgnoreCase(fieldType)
                || "YEAR".equalsIgnoreCase(fieldType)
                || "TEXT".equalsIgnoreCase(fieldType)
                || "BLOB".equalsIgnoreCase(fieldType)
                || "MEDIUMTEXT".equalsIgnoreCase(fieldType)
                || "MEDIUMBLOB".equalsIgnoreCase(fieldType)
                || "LONGTEXT".equalsIgnoreCase(fieldType)
                || "LONGBLOB".equalsIgnoreCase(fieldType)
                || "JSON".equalsIgnoreCase(fieldType)
                || "ENUM".equalsIgnoreCase(fieldType)
                || "SET".equalsIgnoreCase(fieldType)
                || "GEOMETRY".equalsIgnoreCase(fieldType)
                || "POINT".equalsIgnoreCase(fieldType)
                || "LINESTRING".equalsIgnoreCase(fieldType)
                || "POLYGON".equalsIgnoreCase(fieldType);
    }

    @Override
    public void delIndex(ModelTableDO tableDO, String idxName) {
        String sql = "ALTER TABLE "+ dbUtil.getDBNameAllDynamic(tableDO.getDataSourceConfigId(), true)
                +".`"+tableDO.getTableCode()+"`" +" DROP INDEX " + idxName + ";";
        dbUtil.exeSQL(tableDO, sql, DBOPTypeEnum.DROP_INDEX);
    }

    @Override
    public void renameIndex(ModelTableDO tableDO, String oldIdxName, String idxName) {
        if(oldIdxName.equals(idxName)){
            return;
        }
        String sql = "ALTER TABLE "+ dbUtil.getDBNameAllDynamic(tableDO.getDataSourceConfigId(), true)
                +".`"+ tableDO.getTableCode() +"`" +" RENAME INDEX "
                + oldIdxName + " TO " + idxName + ";";
        dbUtil.exeSQL(tableDO, sql, DBOPTypeEnum.ALTER_INDEX);
    }

    /**
     * 支持索引修改
     * @param tableDO
     * @param oldIndex
     * @param newIndex
     */
    @Override
    public void modifyIndex(ModelTableDO tableDO, ModelTableIndexAditReqVO oldIndex, ModelTableIndexAditReqVO newIndex) {
        // 检查索引是否仅仅名称发生变化
        if (onlyNameChanged(oldIndex, newIndex)) {
            // 如果仅是名称变化，使用重命名逻辑
            if (!oldIndex.getIdxName().equals(newIndex.getIdxName())) {
                String renameSql = "ALTER TABLE " + dbUtil.getDBNameAllDynamic(tableDO.getDataSourceConfigId(), true)
                        + ".`" + tableDO.getTableCode() + "` RENAME INDEX `"
                        + oldIndex.getIdxName() + "` TO `" + newIndex.getIdxName() + "`;";
                dbUtil.exeSQL(tableDO, renameSql, DBOPTypeEnum.ALTER_INDEX);
            }
        } else {
            // 名称之外的其他属性发生变化，先删除旧索引，后创建新索引
            String dropSql = "ALTER TABLE " + dbUtil.getDBNameAllDynamic(tableDO.getDataSourceConfigId(), true)
                    + ".`" + tableDO.getTableCode() + "` DROP INDEX `" + oldIndex.getIdxName() + "`;";

            StringBuilder createSql = new StringBuilder();
            createSql.append("ALTER TABLE ").append(dbUtil.getDBNameAllDynamic(tableDO.getDataSourceConfigId(), true))
                    .append(".`").append(tableDO.getTableCode()).append("` ADD ");

            if ("UNIQUE".equalsIgnoreCase(newIndex.getIdxType())) {
                createSql.append("UNIQUE ");
            } else if ("FULLTEXT".equalsIgnoreCase(newIndex.getIdxType())) {
                createSql.append("FULLTEXT ");
            }

            createSql.append("INDEX `").append(newIndex.getIdxName()).append("`(");

            // 使用 fields 列表来处理复合索引字段
            List<ModelFieldAditReqVO> fields = newIndex.getFields();
            for (int i = 0; i < fields.size(); i++) {
                if (i > 0) {
                    createSql.append(", ");
                }
                ModelFieldAditReqVO field = fields.get(i);
                createSql.append("`").append(field.getFieldCode()).append("` ");
                //if ("DESC".equalsIgnoreCase(field.getSortType())) {//todo 暂时没有这个排序属性，后续加
                //    createSql.append("DESC");
                //} else {
                //    createSql.append("ASC");
                //}
            }
            createSql.append(") USING ").append(newIndex.getIdxWay());

            if (newIndex.getRemark() != null && !newIndex.getRemark().isEmpty()) {
                createSql.append(" COMMENT '").append(newIndex.getRemark()).append("'");
            }

            createSql.append(";");

            // 执行删除和创建索引的 SQL
            dbUtil.exeSQL(tableDO, dropSql + "\n" + createSql, DBOPTypeEnum.ALTER_INDEX);
        }
    }

    @Override
    public void addIndex(List<ModelTableIndexAditReqVO> indexAdds, Map<String, ModelTableIndexDO> exsitIdxMap, Map<String, ModelFieldDO> exsitFieldCodeMap, ModelTableDO exists) {
        for (ModelTableIndexAditReqVO indexAdd : indexAdds) {

            if (ObjUtilX.isEmpty(indexAdd.getFields())) {
                throw new BizException("5001", "索引：" + indexAdd.getIdxName() + "，没有指定字段");
            }
            if (exists.getNeedExe() && exsitIdxMap.containsKey(indexAdd.getIdxName())) {
                throw new BizException("5001", "索引名【" + indexAdd.getIdxName() + "】已存在");
            }
            List<ModelTableIndexDO> indexDOList = new ArrayList<>(indexAdd.getFields().size());
            StringBuilder fieldStr = new StringBuilder();
            Long idxId = IdWorker.getId();
            for (ModelFieldAditReqVO field : indexAdd.getFields()) {
//                    if(!exsitFieldIdMap.containsKey(field.getFieldId())){
//                        throw new BizException("5001","创建索引时【"+field.getFieldCode()+"】字段不存在");
//                    }
                if(!exsitFieldCodeMap.containsKey(field.getFieldCode())){
                    throw new BizException("5001","创建索引时【"+field.getFieldCode()+"】字段不存在");
                }
                field.setFieldId(exsitFieldCodeMap.get(field.getFieldCode()).getFieldId());
                int i = 1;
                fieldStr.append("`").append(field.getFieldCode()).append("`").append(", ");
                ModelTableIndexDO idx = new ModelTableIndexDO();
                idx.setIdxId(idxId);
                idx.setIdxName(indexAdd.getIdxName());
                idx.setTableId(exists.getTableId());
                idx.setTableCode(exists.getTableCode());
                if(indexAdd.getIdxName().equals("UNIQUE")){
                    idx.setNonUnique(0);
                }else{
                    idx.setNonUnique(1);
                }

                idx.setIdxSeq(i++);
                idx.setFieldId(field.getFieldId());
                idx.setFieldCode(field.getFieldCode());
                idx.setSortType(indexAdd.getSortType());
                idx.setIdxType(indexAdd.getIdxType());
                idx.setIdxWay("FULLTEXT".equalsIgnoreCase(indexAdd.getIdxType())?"":"BTREE");
                idx.setRemark(indexAdd.getRemark());

                indexDOList.add(idx);
            }
            //存在真实表
            if (exists.getIsGen() == 1) {
                // 移除最后的逗号和空格
                fieldStr.setLength(fieldStr.length() - 2);

                StringBuilder sql = new StringBuilder();
                sql.append("ALTER TABLE ");
                sql.append(dbUtil.getDBNameAllDynamic(exists.getDataSourceConfigId(), true));
                sql.append(".`");
                sql.append(exists.getTableCode());
                sql.append("`");
                sql.append(" ADD ");
                if(!"NORMAL".equals(indexAdd.getIdxType())) {
                    sql.append(indexAdd.getIdxType());
                }
                sql.append(" INDEX ");
                sql.append(indexAdd.getIdxName());
                sql.append("(");
                sql.append(fieldStr);
                sql.append(")");
//                    sql.append(indexAdd.getIdxWay());
                if(!"FULLTEXT".equals(indexAdd.getIdxType())) {
                    sql.append(" USING BTREE");
                }
                sql.append(" COMMENT '");
                sql.append(indexAdd.getRemark());
                sql.append("'");
                sql.append(";");

//                    String sql = "ALTER TABLE "+ dbUtil.getDBName(true)+".`"+exists.getTableCode()+"`"
//                            +" ADD " + indexAdd.getIdxType() + " INDEX " + indexAdd.getIdxName() + "(" + fieldStr + ")"
//                            + " USING "+indexAdd.getIdxWay()+" COMMENT '"
//                            + indexAdd.getRemark() + "'";
                if(exists.getNeedExe()) {
                    dbUtil.exeSQL(exists, sql.toString(), DBOPTypeEnum.ADD_INDEX);
                }
            }
            tableIndexMapper.insertBatch(indexDOList);
        }
    }

    //生成mysql建表SQL
    @Override
    public StringBuilder genCreateTempTableSQL(ModelQueryDO queryDO, String paramCode, List<ModelFieldDO> fields)  {

        String tableName = dbUtil.getDBNameAllDynamic(queryDO.getDataSourceConfigId(), true)+"."+paramCode.replaceAll("@", "")+" ";
        StringBuilder sql = new StringBuilder("DROP TEMPORARY TABLE IF EXISTS ");
        sql.append(tableName);
        sql.append(";");
        sql.append("CREATE TEMPORARY TABLE ");
        sql.append(tableName);
        sql.append(" (");
        //添加业务字段
        for (ModelFieldDO field : fields) {
            //拼接类型
            getMysqlFullCode(field, sql);
            if (field.getIsNullable() == 1) {
                sql.append(" NOT NULL");
            }
            if (ObjUtilX.isNotEmpty(field.getDefaultVal())) {
                sql.append(" DEFAULT '").append(field.getDefaultVal()).append("'");;
            }
            //预留主键设置
            if (field.getIsPrimaryKey() ==  1) {
                sql.append(" PRIMARY KEY");
            }
            sql.append(", ");
        }

        // 移除最后的逗号和空格
        sql.setLength(sql.length() - 2);
        sql.append(");");
        log.info("生成建临时表SQL完毕:\n" + sql.toString());
        return sql;
    }

    @Override
    public StringBuilder genCreateTempTableInsertSQL(ModelQueryDO queryDO, String paramCode, JSONArray paramArray, List<ModelFieldDO> fieldDOS)  {

        StringBuilder sql = new StringBuilder(" INSERT INTO ");
        sql.append(dbUtil.getDBNameAllDynamic(queryDO.getDataSourceConfigId(), true)
                +"."+paramCode.replaceAll("@", "")+" ");//

        //添加字段
        sql.append(" (");
        for (ModelFieldDO fieldDO : fieldDOS) {
            sql.append(fieldDO.getFieldCode());
            sql.append(",");
        }
        // 移除最后的逗号
        sql.setLength(sql.length() - 1);
        sql.append(") VALUES ");
        //添加业务字段
        for (int i = 0; i < paramArray.size(); i++) {
            JSONObject jsonObject = paramArray.getJSONObject(i);
            sql.append(" (");
            for (ModelFieldDO fieldDO : fieldDOS) {
                sql.append("'"+jsonObject.get(fieldDO.getFieldCode())+"'");
                sql.append(",");
            }
            // 移除最后的逗号
            sql.setLength(sql.length() - 1);
            sql.append("), ");
        }
        // 移除最后的逗号和空格
        sql.setLength(sql.length() - 2);
        sql.append(";");
        log.info("生成临时表插入SQL完毕:\n" + sql.toString());
        return sql;
    }

    @Override
    public TableStructure getTableStructure(ModelTableDO table) throws SQLException {
        Connection connection = dbUtil.getCon(table.getDataSourceConfigId());
        String tableName = table.getTableCode();
        DatabaseMetaData metaData = connection.getMetaData();

        ResultSet columns = metaData.getColumns(connection.getCatalog(), connection.getSchema(), tableName, null);
        // 获取表的信息，这里 third 参数是表名
        ResultSet tables = metaData.getTables(connection.getCatalog(), connection.getSchema(), tableName, null);
        String remarks = "";
        if (tables.next()) {
            // 获取表的类型
            String tableType = tables.getString("TABLE_TYPE");

            // 获取表的描述（如果存在）
            remarks = tables.getString("REMARKS"); // 表的描述
            if(ObjUtilX.isEmpty(remarks)){
                throw new BizException("5001", "表描述信息为空，请补充");
            }
            table.setTableName(remarks);
            table.setRemark(remarks);
        } else {
            throw new BizException("5001", tableName+"表未找到");
        }

        TableStructure tableStructure = new TableStructure();
        tableStructure.setTableId(table.getTableId());
        tableStructure.setTableCode(table.getTableCode());
        tableStructure.setTableName(remarks);
        Integer sort = 0;
        while (columns.next()) {
            String columnName = columns.getString("COLUMN_NAME");
//            if(columnName.equals("id")){
//                continue;
//            }
            ColumnInfo column = new ColumnInfo();
            column.setSort(sort++);
            column.setName(columnName);
            column.setType(columns.getString("TYPE_NAME")); // 数据类型名称
            column.setSize(columns.getInt("COLUMN_SIZE")); // 列大小
            column.setNullable(columns.getInt("NULLABLE") == DatabaseMetaData.columnNullable); // 是否可空

            column.setFieldPrecision(columns.getInt("DECIMAL_DIGITS"));

            // 默认值
            column.setDefaultValue(columns.getString("COLUMN_DEF"));

            // 字段描述
            column.setRemark(columns.getString("REMARKS")); // 获取字段描述
            if(ObjUtilX.isEmpty(column.getRemark())){
                throw new BizException("5001", columnName + ":字段描述为空，请补充");
            }
            if(column.getRemark().length() > 100){
                throw new BizException("5001", columnName + ":字段描述用于生成列中文名，请勿过长，其他描述请导入表之后复制到“描述”。");
            }
            column.setPrimaryKey(false);
            // 判断是否为主键
            ResultSet pkResultSet = metaData.getPrimaryKeys(null, null, tableName);
            while (pkResultSet.next()) {
                String pkColumnName = pkResultSet.getString("COLUMN_NAME");
                if (pkColumnName.equals(column.getName())) {
                    column.setPrimaryKey(true);
                    break;
                }
            }

            column.setIsAutoIncrease(0);
            //检查自增属性
            String query = "SELECT COLUMN_NAME, EXTRA FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ? AND COLUMN_NAME = ?";
            try (PreparedStatement stmt = connection.prepareStatement(query)) {
                stmt.setString(1, connection.getCatalog()); // 数据库名
                stmt.setString(2, tableName); // 表名
                stmt.setString(3, columnName); // 列名

                ResultSet rs = stmt.executeQuery();
                if (rs.next()) {
                    String extra = rs.getString("EXTRA");
                    if ("auto_increment".equalsIgnoreCase(extra)) {
                        column.setIsAutoIncrease(1); // 设定自增属性
                    } else {
                        column.setIsAutoIncrease(0);
                    }
                }
            }
            tableStructure.addColumn(column);
        }
        Statement stmt = connection.createStatement();
        stmt.execute("SHOW INDEX FROM " + tableName);
        ResultSet indexResultSet = stmt.getResultSet();
        while (indexResultSet.next()) {
            String indexName = indexResultSet.getString("Key_name");
            String columnName = indexResultSet.getString("Column_name");
            if(columnName.equals("id")){
                continue;
            }
            String indexType = indexResultSet.getString("Index_type");
            boolean nonUnique = indexResultSet.getBoolean("Non_unique");
            String ascOrDesc = indexResultSet.getString("Collation");  // 是否升序或降序（MySQL 5.7+ 支持）
            // 确定索引类型
            String indexCategory;
            if ("FULLTEXT".equalsIgnoreCase(indexType)) {
                indexCategory = "FULLTEXT";
            }else {
                if (nonUnique) {
                    indexCategory = "NORMAL"; // 普通索引
                } else {
                    indexCategory = "UNIQUE";  // 唯一索引
                }
                if ("A".equalsIgnoreCase(ascOrDesc)) {
                    ascOrDesc = "ASC";
                } else if ("D".equalsIgnoreCase(ascOrDesc)) {
                    ascOrDesc = "DESC";
                } else {
                    ascOrDesc = "";
                }
            }

            short cardinality = indexResultSet.getShort("Cardinality");
            Integer seq = indexResultSet.getInt("Seq_in_index");
            String comment = indexResultSet.getString("Index_comment");

            ModelTableIndexRespVO index = new ModelTableIndexRespVO();
            index.setFields(new ArrayList<>());
            index.setIdxId(0L);//新增时填充
            index.setIdxName(indexName);
            index.setTableId(table.getTableId());
            index.setTableCode(tableName);
            index.setNonUnique(nonUnique?1:0);
            index.setIdxSeq(seq);

            index.setFieldId(0L);//新增时填充
            index.setFieldCode(columnName);
            index.setSortType(ascOrDesc);
            index.setIdxType(indexCategory);
            index.setIdxWay("FULLTEXT".equalsIgnoreCase(indexType)?"":"BTREE");
            index.setRemark(comment);
            tableStructure.addIndex(index);
        }
        //关闭资源
        stmt.close();
        indexResultSet.close();
        return tableStructure;
    }
}
