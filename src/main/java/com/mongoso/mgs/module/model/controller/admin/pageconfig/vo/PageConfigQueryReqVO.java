package com.mongoso.mgs.module.model.controller.admin.pageconfig.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import jakarta.validation.constraints.NotNull;

/**
 * 页面配置 QueryReqVO
 *
 * <AUTHOR>
 */
@Data
public class PageConfigQueryReqVO {

    /** 名称 */
    @JsonProperty(value = "itemName")
    private String name;

    /** 内容 */
    @JsonProperty(value = "itemContent")
    private String content;

    @NotNull(message = "项目id不能为空")
    @JsonProperty(value = "projectId")
    private Long projectId;
}
