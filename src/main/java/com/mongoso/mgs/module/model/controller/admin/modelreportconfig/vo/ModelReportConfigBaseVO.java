package com.mongoso.mgs.module.model.controller.admin.modelreportconfig.vo;

import lombok.*;

import jakarta.validation.constraints.*;
import java.io.Serializable;

/**
 * 自定义报表配置 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class ModelReportConfigBaseVO implements Serializable {

    /** 条件id */
    private Long id;

    /** 报表id   */
    @NotNull(message = "报表id  不能为空")
    private Long reportId;

    /** 数据源   */
    @NotEmpty(message = "数据源  不能为空")
    private String dataTable;

    /** 字段编码 */
    @NotEmpty(message = "字段编码不能为空")
    private String fieldCode;

    /** 字段名称 */
    @NotEmpty(message = "字段名称不能为空")
    private String fieldName;

    /** 控件类型 */
    @NotNull(message = "控件类型不能为空")
    private Integer controlType;

    /** 字段类型   */
    @NotNull(message = "字段类型  不能为空")
    private String fieldType;

    /** 长度 */
    private Integer leng;

    /** 字段精度 */
    private Integer fieldPrecision;

    /** 正则表达式 */
    private String regExpression;

    /** 数据掩码 */
    private String dataMask;

    /** 可编辑 */
    @NotNull(message = "可编辑不能为空")
    private Integer isEdit;

    /** 是否显示 */
    @NotNull(message = "是否显示不能为空")
    private Integer isShow;

    /** 空值显示 */
    private String emptyShow;

    /** 是否必填 */
    @NotNull(message = "是否必填不能为空")
    private Integer isRequired;

    /** 默认值 */
    private String defaultVal;

    /** 备注   */
    private String remark;

    /** 版本号 */
    @NotNull(message = "版本号不能为空")
    private Integer versionNo;

    /** 0:查询条件，1:展示数据 */
    private Integer category;

}
