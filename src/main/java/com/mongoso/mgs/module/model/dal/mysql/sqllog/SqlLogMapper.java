package com.mongoso.mgs.module.model.dal.mysql.sqllog;

import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.module.model.dal.db.sqllog.SqlLogDO;
import com.mongoso.mgs.module.model.controller.admin.sqllog.vo.SqlLogPageReqVO;
import com.mongoso.mgs.module.model.controller.admin.sqllog.vo.SqlLogQueryReqVO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 脚本日志 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SqlLogMapper extends BaseMapperX<SqlLogDO> {

    default PageResult<SqlLogDO> selectPage(SqlLogPageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<SqlLogDO>lambdaQueryX()
                .eqIfPresent(SqlLogDO::getProjectId, reqVO.getProjectId())
                .eqIfPresent(SqlLogDO::getTableId, reqVO.getTableId())
                .likeIfPresent(SqlLogDO::getTableCode, reqVO.getTableCode())
                .likeIfPresent(SqlLogDO::getSql, reqVO.getSql())
                .eqIfPresent(SqlLogDO::getOpType, reqVO.getOpType())
                .eqIfPresent(SqlLogDO::getSucStatus, reqVO.getSucStatus())
                .likeIfPresent(SqlLogDO::getErrMgs, reqVO.getErrMgs())
                .orderByDesc(SqlLogDO::getCreatedDt));
    }




    default List<SqlLogDO> selectList(SqlLogQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<SqlLogDO>lambdaQueryX()
                .eqIfPresent(SqlLogDO::getProjectId, reqVO.getProjectId())
                .eqIfPresent(SqlLogDO::getTableId, reqVO.getTableId())
                .likeIfPresent(SqlLogDO::getTableCode, reqVO.getTableCode())
                .likeIfPresent(SqlLogDO::getSql, reqVO.getSql())
                .eqIfPresent(SqlLogDO::getOpType, reqVO.getOpType())
                .eqIfPresent(SqlLogDO::getSucStatus, reqVO.getSucStatus())
                .likeIfPresent(SqlLogDO::getErrMgs, reqVO.getErrMgs())
                    .orderByDesc(SqlLogDO::getCreatedDt));
    }


}