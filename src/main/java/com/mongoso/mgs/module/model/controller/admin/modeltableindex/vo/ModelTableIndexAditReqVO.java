package com.mongoso.mgs.module.model.controller.admin.modeltableindex.vo;

import com.mongoso.mgs.module.model.controller.admin.modelfield.vo.ModelFieldAditReqVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

/**
 * 图形建模主表索引 AditReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ModelTableIndexAditReqVO extends ModelTableIndexBaseVO {

    /** 索引字段列表 */
    private List<ModelFieldAditReqVO> fields = new ArrayList<>();
}
