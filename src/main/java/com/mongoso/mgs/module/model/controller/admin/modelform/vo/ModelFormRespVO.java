package com.mongoso.mgs.module.model.controller.admin.modelform.vo;

import com.mongoso.mgs.module.model.controller.admin.modelform.button.vo.ModelFormButtonRespVO;
import com.mongoso.mgs.module.model.controller.admin.modelform.nocopy.vo.ModelFormNoCopyRespVO;
import lombok.*;

import java.util.List;


 import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
  


/**
 * 单据建模主 RespVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ModelFormRespVO extends ModelFormBaseVO {

    /** 创建人 */
    private String createdBy;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createdDt;

    /** 更新人 */
    private String updatedBy;

    /** 更新时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime updatedDt;

    /** 单据建模按钮列表 */
    private List<ModelFormButtonRespVO> modelFormButtonList;

    /** 单据建模不可复制字段列表 */
    private List<ModelFormNoCopyRespVO> modelFormNoCopyList;

}
