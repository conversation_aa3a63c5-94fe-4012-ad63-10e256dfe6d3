package com.mongoso.mgs.module.model.controller.admin.modelquery;

import cn.hutool.db.Entity;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.model.controller.admin.modelquery.vo.*;
import com.mongoso.mgs.module.model.dal.db.modelquery.ModelQueryDO;
import com.mongoso.mgs.module.model.service.modelquery.ModelQueryService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

import static com.mongoso.mgs.framework.common.domain.ResultX.success;

/**
 * 自定义查询 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/model")
@Validated
public class ModelQueryController {

    @Resource
    private ModelQueryService queryService;

    @OperateLog("自定义查询添加或编辑")
    @PostMapping("/modelQueryAdit")
    @PreAuthorize("@ss.hasPermission('modelQuery:adit')")
    public ResultX<Long> modelQueryAdit(@Valid @RequestBody ModelQueryAditReqVO reqVO) {
        return success(reqVO.getQueryId() == null
                            ? queryService.modelQueryAdd(reqVO)
                            : queryService.modelQueryEdit(reqVO));
    }

    @OperateLog("自定义查询删除")
    @PostMapping("/modelQueryDel")
    @PreAuthorize("@ss.hasPermission('modelQuery:del')")
    public ResultX<Boolean> modelQueryDel(@Valid @RequestBody ModelQueryPrimaryReqVO reqVO) {
        queryService.modelQueryDel(reqVO.getQueryId());
        return success(true);
    }

    @OperateLog("图形建模主修改父节点")
    @PostMapping("/modelQueryDrag")
    @PreAuthorize("@ss.hasPermission('modelQuery:adit')")
    public ResultX<Integer> modelQueryDrag(@Valid @RequestBody ModelQueryDragReqVO reqVO) {
        return success(queryService.modelQueryDrag(reqVO));
    }

    @OperateLog("获取当前数据库")
    @PostMapping("/dbType")
    public ResultX<String> dbType(@Valid @RequestBody ModelQueryQueryReqVO reqVO) {
        return success(queryService.dbType(reqVO.getDataSourceConfigId()));
    }

    @OperateLog("自定义查询执行")
    @PostMapping("/modelQueryExc")
    @PreAuthorize("@ss.hasPermission('modelQuery:query')")
    public ResultX<PageResult<Map<String, Object>>> modelQueryExc(@Valid @RequestBody ModelQueryPrimaryReqVO reqVO) {
        return success(queryService.modelQueryExc(reqVO));
    }

    @OperateLog("自定义批量查询")
    @PostMapping("/modelQueryBatch")
    @PreAuthorize("@ss.hasPermission('modelQuery:query')")
    public ResultX<List<Map<String, Object>>> modelQueryBatch(@Valid @RequestBody ModelQueryBatchReqVO reqVO) {
        return success(queryService.modelQueryBatch(reqVO));
    }

    @OperateLog("自定义查询详情")
    @PostMapping("/modelQueryDetail")
    @PreAuthorize("@ss.hasPermission('modelQuery:query')")
    public ResultX<ModelQueryRespVO> modelQueryDetail(@Valid @RequestBody ModelQueryPrimaryReqVO reqVO) {
        return success(queryService.modelQueryDetail(reqVO.getQueryId()));
    }

    @OperateLog("查询Tree")
    @PostMapping("/modelQueryTree")
    @PreAuthorize("@ss.hasPermission('modelQuery:query')")
    public ResultX<List<ModelQueryRespVO>> modelQueryTree() {
        return success(queryService.modelQueryTree());
    }


    @OperateLog("自定义查询列表")
    @PostMapping("/modelQueryList")
    @PreAuthorize("@ss.hasPermission('modelQuery:query')")
    public ResultX<List<ModelQueryRespVO>> modelQueryList(@Valid @RequestBody ModelQueryQueryReqVO reqVO) {
        List<ModelQueryDO> list = queryService.modelQueryList(reqVO);
        return success(BeanUtilX.copyList(list, ModelQueryRespVO::new));
    }

    @OperateLog("自定义查询分页")
    @PostMapping("/modelQueryPage")
    @PreAuthorize("@ss.hasPermission('modelQuery:query')")
    public ResultX<PageResult<ModelQueryRespVO>> modelQueryPage(@Valid @RequestBody ModelQueryPageReqVO reqVO) {
        PageResult<ModelQueryDO> pageResult = queryService.modelQueryPage(reqVO);
        return success(BeanUtilX.copyPage(pageResult, ModelQueryRespVO::new));
    }

}
