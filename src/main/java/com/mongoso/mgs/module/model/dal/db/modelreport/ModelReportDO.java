package com.mongoso.mgs.module.model.dal.db.modelreport;

import lombok.*;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;

/**
 * 自定义报 DO
 *
 * <AUTHOR>
 */
@TableName("lowcode.sys_model_report")
//@KeySequence("sys_model_report_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ModelReportDO extends OperateDO {

    /** 报表id   */
    @TableId(type = IdType.ASSIGN_ID)
    private Long reportId;

    /** 报表名称 */
    private String reportName;

    /** 报表编号 */
    private String reportCode;

    /** 查询id   */
    private Long queryId;

    /** 备注   */
    private String remark;

    /** 版本号 */
    private Integer versionNo;

    /** 是否发布 */
    private Integer isPublish;

    /** 类型 */
    private Integer dirType;

    /** 父节点 */
    private Long parentId;
}
