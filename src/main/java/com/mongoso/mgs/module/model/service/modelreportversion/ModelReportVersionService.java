package com.mongoso.mgs.module.model.service.modelreportversion;

import java.util.*;
import jakarta.validation.*;
import com.mongoso.mgs.module.model.controller.admin.modelreportversion.vo.*;
import com.mongoso.mgs.module.model.dal.db.modelreportversion.ModelReportVersionDO;
import com.mongoso.mgs.framework.common.domain.PageResult;

/**
 * 自定义报表版本 Service 接口
 *
 * <AUTHOR>
 */
public interface ModelReportVersionService {

    /**
     * 创建自定义报表版本
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long modelReportVersionAdd(@Valid ModelReportVersionAditReqVO reqVO);

    /**
     * 更新自定义报表版本
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long modelReportVersionEdit(@Valid ModelReportVersionAditReqVO reqVO);

    /**
     * 删除自定义报表版本
     *
     * @param reportId 编号
     */
    void modelReportVersionDel(Long reportId);

    /**
     * 获得自定义报表版本信息
     *
     * @param reportId 编号
     * @return 自定义报表版本信息
     */
    ModelReportVersionDO modelReportVersionDetail(Long reportId);

    /**
     * 获得自定义报表版本列表
     *
     * @param reqVO 查询条件
     * @return 自定义报表版本列表
     */
    List<ModelReportVersionDO> modelReportVersionList(@Valid ModelReportVersionQueryReqVO reqVO);

    /**
     * 获得自定义报表版本分页
     *
     * @param reqVO 查询条件
     * @return 自定义报表版本分页
     */
    PageResult<ModelReportVersionDO> modelReportVersionPage(@Valid ModelReportVersionPageReqVO reqVO);

}
