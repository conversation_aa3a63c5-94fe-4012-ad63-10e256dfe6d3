package com.mongoso.mgs.module.model.controller.admin.modelfieldconf;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.model.controller.admin.modelfieldconf.vo.*;
import com.mongoso.mgs.module.model.controller.admin.modeltable.vo.ModelTableListifyReqVO;
import com.mongoso.mgs.module.model.dal.db.modelfieldconf.ModelFieldConfDO;
import com.mongoso.mgs.module.model.service.modelfieldconf.ModelFieldConfService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.mongoso.mgs.framework.common.domain.ResultX.success;

/**
 * 图形建模字段翻译配置 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/model")
@Validated
public class ModelFieldConfController {

    @Resource
    private ModelFieldConfService fieldConfService;

    @OperateLog("图形建模字段翻译配置添加或编辑")
    @PostMapping("/modelFieldConfAdit")
    @PreAuthorize("@ss.hasPermission('modelFieldConf:adit')")
    public ResultX<Long> modelFieldConfAdit(@Valid @RequestBody ModelFieldConfAditReqVO reqVO) {
        return success(reqVO.getId() == null
                            ? fieldConfService.modelFieldConfAdd(reqVO)
                            : fieldConfService.modelFieldConfEdit(reqVO));
    }

    @OperateLog("图形建模字段翻译配置删除")
    @PostMapping("/modelFieldConfDel")
    @PreAuthorize("@ss.hasPermission('modelFieldConf:del')")
    public ResultX<Boolean> modelFieldConfDel(@Valid @RequestBody ModelFieldConfPrimaryReqVO reqVO) {
        fieldConfService.modelFieldConfDel(reqVO.getId());
        return success(true);
    }

    @OperateLog("图形建模主批量删除")
    @PostMapping("/modelFieldConfDelBatch")
    @PreAuthorize("@ss.hasPermission('modelFieldConf:del')")
    public ResultX<Boolean> modelFieldConfDelBatch(@Valid @RequestBody ModelFieldConfDelBatchReqVO reqVO) {
        fieldConfService.modelFieldConfDelBatch(reqVO);
        return success(true);
    }

    @OperateLog("图形建模字段翻译配置详情")
    @PostMapping("/modelFieldConfDetail")
    @PreAuthorize("@ss.hasPermission('modelFieldConf:query')")
    public ResultX<ModelFieldConfRespVO> modelFieldConfDetail(@Valid @RequestBody ModelFieldConfPrimaryReqVO reqVO) {
        ModelFieldConfDO oldDO = fieldConfService.modelFieldConfDetail(reqVO.getId());
        return success(BeanUtilX.copy(oldDO, ModelFieldConfRespVO::new));
    }

    @OperateLog("图形建模字段翻译")
    @PostMapping("/modelFieldTrans")
    @PreAuthorize("@ss.hasPermission('modelFieldConf:adit')")
    public ResultX<List<ModelFieldConfDO>> modelFieldTrans(@Valid @RequestBody ModelFieldTransReqVO reqVO) {
        return success(fieldConfService.modelFieldTrans(reqVO));
    }

    @OperateLog("图形建模字段翻译并保存")
    @PostMapping("/modelFieldTransSave")
    @PreAuthorize("@ss.hasPermission('modelFieldConf:adit')")
    public ResultX<Boolean> modelFieldTransSave(@Valid @RequestBody ModelFieldTransReqVO reqVO) {
        return success(fieldConfService.modelFieldTransSave(reqVO));
    }

    @OperateLog("图形建模字段翻译配置列表")
    @PostMapping("/modelFieldConfList")
    @PreAuthorize("@ss.hasPermission('modelFieldConf:query')")
    public ResultX<List<ModelFieldConfRespVO>> modelFieldConfList(@Valid @RequestBody ModelFieldConfQueryReqVO reqVO) {
        List<ModelFieldConfDO> list = fieldConfService.modelFieldConfList(reqVO);
        return success(BeanUtilX.copyList(list, ModelFieldConfRespVO::new));
    }

    @OperateLog(value = "匹配字段")
    @RequestMapping(value = "/fieldTranslate", method = RequestMethod.POST)
    //public ResultX<String> fieldTranslate(@RequestBody Map<String, String> paramsMap) {
    public ResultX<String> fieldTranslate(@RequestBody ModelTableListifyReqVO reqVO) {
        ResultX<String> result = new ResultX();
        result.setResult(1);
        result.setCode(GlobalErrorCodeConstants.SUCCESS.getCode());

        Map<String, Integer> translateCountMap = new HashMap<String, Integer>();
        //String returnStr = fieldConfService.fieldTranslate(Integer.valueOf(paramsMap.get("textType")), paramsMap.get("itemContent"), translateCountMap);
        String returnStr = fieldConfService.fieldTranslate(reqVO, translateCountMap);
        if(translateCountMap.get("totalFiled") == translateCountMap.get("translateFiled")){
            result.setMsg(translateCountMap.get("totalFiled") + "个字段已全部翻译");
        }else{
            result.setMsg("已翻译"+ translateCountMap.get("translateFiled") + "个字段, 还有"
                    + translateCountMap.get("noTranslateFiled") + "个字段未翻译，未翻译字段已上传");
        }
        result.setData(returnStr);
        return result;
    }

    @OperateLog(value = "平台匹配")
    @RequestMapping(value = "/matchField", method = RequestMethod.POST)
    public ResultX<List<CreateInterfaceFieldParams>> matchField(@RequestBody List<CreateInterfaceFieldParams> list) {
        return success(fieldConfService.matchField(list));
    }

    @OperateLog("图形建模字段翻译配置分页")
    @PostMapping("/modelFieldConfPage")
    @PreAuthorize("@ss.hasPermission('modelFieldConf:query')")
    public ResultX<PageResult<ModelFieldConfRespVO>> modelFieldConfPage(@Valid @RequestBody ModelFieldConfPageReqVO reqVO) {
        return success(fieldConfService.modelFieldConfPage(reqVO));
    }

}
