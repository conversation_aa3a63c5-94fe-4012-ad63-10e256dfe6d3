package com.mongoso.mgs.module.model.controller.admin.sqllog.vo;

import lombok.*;

import com.mongoso.mgs.framework.common.domain.PageParam;


    


/**
 * 脚本日志 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SqlLogPageReqVO extends PageParam {

    /** 项目id */
    private Long projectId;
    private Long tableId;
    /** 表名 */
    private String tableCode;

    /** 脚本 */
    private String sql;

    /** 操作类别 [建表，新增字段，修改字段，删除字段，修改表，删除表，清空数据] */
    private Integer opType;

    /** 执行状态 [失败，成功] */
    private Integer sucStatus;

    /** 异常信息 */
    private String errMgs;

}
