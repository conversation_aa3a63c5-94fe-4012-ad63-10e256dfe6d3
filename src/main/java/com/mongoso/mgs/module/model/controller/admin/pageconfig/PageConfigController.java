package com.mongoso.mgs.module.model.controller.admin.pageconfig;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.model.controller.admin.pageconfig.vo.*;
import com.mongoso.mgs.module.model.dal.db.pageconfig.PageConfigDO;
import com.mongoso.mgs.module.model.service.pageconfig.PageConfigService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

import static com.mongoso.mgs.framework.common.domain.ResultX.success;

/**
 * 页面配置 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system")
@Validated
public class PageConfigController {

    @Resource
    private PageConfigService pageConfigService;

    /**
     * 添加或编辑页面配置
     * @param reqVO
     * @return
     */
    @OperateLog("页面配置添加或编辑")
    @PostMapping("/pageConfigAdit")
    @PreAuthorize("@ss.hasPermission('pageConfig:adit')")
    public ResultX<PageConfigRespVO> pageConfigAdit(@Valid @RequestBody PageConfigAditReqVO reqVO) {
        return success(reqVO.getId() == null
                            ? pageConfigService.pageConfigAdd(reqVO)
                            : pageConfigService.pageConfigEdit(reqVO));
    }

    /**
     * 删除页面配置
     * @param reqVO
     * @return
     */
    @OperateLog("页面配置删除")
    @PostMapping("/pageConfigDelete")
    @PreAuthorize("@ss.hasPermission('pageConfig:del')")
    public ResultX<Boolean> pageConfigDel(@Valid @RequestBody PageConfigPrimaryReqVO reqVO) {
        pageConfigService.pageConfigDel(reqVO.getId());
        return success(true);
    }

    /**
     * 页面配置详情
     * @param reqVO
     * @return
     */
    @OperateLog("页面配置详情")
    @PostMapping("/pageConfigDetail")
    @PreAuthorize("@ss.hasPermission('pageConfig:query')")
    public ResultX<PageConfigRespVO> pageConfigDetail(@Valid @RequestBody PageConfigDetailReqVO reqVO) {
        PageConfigDO oldDO = pageConfigService.pageConfigDetail(reqVO);
        return success(BeanUtilX.copy(oldDO, PageConfigRespVO::new));
    }

    /**
     * 页面配置列表
     * @param reqVO
     * @return
     */
    @OperateLog("页面配置列表")
    @PostMapping("/pageConfigList")
    @PreAuthorize("@ss.hasPermission('pageConfig:query')")
    public ResultX<List<PageConfigRespVO>> pageConfigList(@Valid @RequestBody PageConfigQueryReqVO reqVO) {
        List<PageConfigDO> list = pageConfigService.pageConfigList(reqVO);
        return success(BeanUtilX.copyList(list, PageConfigRespVO::new));
    }

    /**
     * 页面配置树
     * @param reqVO
     * @return
     */
    @OperateLog("页面配置树")
    @PostMapping("/pageConfigTree")
    @PreAuthorize("@ss.hasPermission('pageConfig:query')")
    public ResultX<List<PageConfigRespVO>> pageConfigTree(@Valid @RequestBody PageConfigQueryReqVO reqVO) {
        return success(pageConfigService.pageConfigTree(reqVO));
    }

    /**
     * 页面配置分页
     * @param reqVO
     * @return
     */
    @OperateLog("页面配置分页")
    @PostMapping("/pageConfigPage")
    @PreAuthorize("@ss.hasPermission('pageConfig:query')")
    public ResultX<PageResult<PageConfigRespVO>> pageConfigPage(@Valid @RequestBody PageConfigPageReqVO reqVO) {
        PageResult<PageConfigDO> pageResult = pageConfigService.pageConfigPage(reqVO);
        return success(BeanUtilX.copyPage(pageResult, PageConfigRespVO::new));
    }

    /**
     * 页面配置移动
     * @param reqVO
     * @return
     */
    @OperateLog("页面配置移动")
    @PostMapping("/pageConfigMove")
    @PreAuthorize("@ss.hasPermission('pageConfig:adit')")
    public ResultX pageConfigMove(@Valid @RequestBody PageConfigMoveReqVO reqVO) {
        pageConfigService.pageConfigMove(reqVO);
        return success();
    }

    /**
     * 页面配置拖动
     * @param reqVO
     * @return
     */
    @OperateLog("页面配置移动")
    @PostMapping("/pageConfigDrag")
    @PreAuthorize("@ss.hasPermission('pageConfig:adit')")
    public ResultX pageConfigDrag(@Valid @RequestBody PageConfigDragReqVO reqVO) {
        pageConfigService.pageConfigDrag(reqVO);
        return success();
    }

}
