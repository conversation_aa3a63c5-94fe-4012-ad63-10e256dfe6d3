package com.mongoso.mgs.module.model.service.model;

import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.ObjUtilX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.model.controller.admin.modeltable.vo.Field;
import com.mongoso.mgs.module.model.controller.admin.modeltable.vo.ModelTableExportReqVO;
import com.mongoso.mgs.module.model.controller.admin.modeltable.vo.ModelTablePrimaryReqVO;
import com.mongoso.mgs.module.model.controller.admin.modeltable.vo.SchemaReqVO;
import com.mongoso.mgs.module.model.dal.db.modelfield.ModelFieldDO;
import com.mongoso.mgs.module.model.dal.db.modelfieldalter.ModelFieldAlterDO;
import com.mongoso.mgs.module.model.dal.db.modeltable.ModelTableDO;
import com.mongoso.mgs.module.model.dal.mysql.modelfieldalter.ModelFieldAlterMapper;
import com.mongoso.mgs.module.model.dal.mysql.modeltable.ModelTableMapper;
import com.mongoso.mgs.module.model.service.dbfactory.DBCreator;
import com.mongoso.mgs.module.model.service.dbfactory.DBCreatorFactory;
import lombok.extern.log4j.Log4j2;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import javax.sql.DataSource;
import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.sql.Statement;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 建模 Service
 */
@Service
@Log4j2
public class SchemaServiceImpl implements SchemaService {

    @Autowired
    private DataSource dataSource;
    @Resource
    private ModelTableMapper tableMapper;
    //@Resource
    //private ModelFieldMapper fieldMapper;
    @Resource
    private ModelFieldAlterMapper fieldAlterMapper;
    @Autowired
    private DBCreatorFactory DBCreatorFactory;
    /*********************** 这里的方法不用了，自测玩了 *********************/
    @Override
    public String schemaAdd(SchemaReqVO reqVO) {
        StringBuilder sql = getDDL(reqVO);
        try (Connection connection = dataSource.getConnection();
             Statement stmt = connection.createStatement()) {
            stmt.execute(sql.toString());
            log.debug("完成建表:"+reqVO.getTableCode());
            return "succ";
        } catch (Exception e) {
            log.error("建表失败:"+reqVO.getTableCode());
            return "err";
        }
    }

    // 获取表的 DDL 语句
    @NotNull
    private static StringBuilder getDDL(SchemaReqVO reqVO) {
        List<Field> fields = reqVO.getFields();
        StringBuilder sql = new StringBuilder("CREATE TABLE IF NOT EXISTS ");
        sql.append(reqVO.getTableCode());
        sql.append(" (");
        //添加ID
        sql.append("`id` int NOT NULL AUTO_INCREMENT COMMENT 'id',");
        //添加业务字段
        for (Field field : fields) {
            sql.append(field.getFieldName()).append(" ").append(field.getFieldType());
            if (!field.isNullable()) {
                sql.append(" NOT NULL");
            }
            //预留主键设置
//            if (field.isPrimaryKey()) {
//                sql.append(" PRIMARY KEY");
//            }
            if (ObjUtilX.isNotEmpty(field.getFieldName())) {
                sql.append(" COMMENT '").append(field.getFieldName()).append("'");
            }
            sql.append(", ");
        }

        // 移除最后的逗号和空格
//        sql.setLength(sql.length() - 2);
        // 设置主键，添加描述
        sql.append("PRIMARY KEY (`id`) USING BTREE ");
        sql.append(") ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb3 ");
        sql.append("COLLATE=utf8mb3_bin ROW_FORMAT=COMPACT COMMENT='");
        sql.append(reqVO.getRemark());
        sql.append("';");
        return sql;
    }

    @Override
    public String schemaDel(String id) {
        return "";
    }

    /************************** 结合图形建模 ******************************/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String tableGenerate(ModelTablePrimaryReqVO reqVO) {
        ModelTableDO tableDO = tableMapper.selectById(reqVO.getTableId());
        if(ObjUtilX.isEmpty(tableDO)){
            throw new BizException("5001","找不到模型表");
        }
        // 查询对应的字段信息
//        Long fieldCount = fieldMapper.selectCount(LambdaQueryWrapperX.<ModelFieldDO>lambdaQueryX()
//                .eq(ModelFieldDO::getTableId, reqVO.getTableId()));
//
        String rst = "处理失败";
//        if (tableDO.getIsGen() != 1) {//第一次创建表
            rst = createTableDynamic(tableDO);
            tableDO.setIsGen(1);
            tableMapper.updateById(tableDO);
//        } else {//表已经创建过 增删改 弃用
//            //修改表备注
////            processFieldChanges(tableDO);
//            throw new BizException("5001","已经生成过表结构了");
//        }
        return rst;
    }

    @Override
    public String modelTableDDL(Long tableId) {
        ModelTableDO tableDO = tableMapper.selectById(tableId);
        if(ObjUtilX.isEmpty(tableDO)){
            throw new BizException("5001","找不到模型表");
        }
        if(tableDO.getDirType() == 0){//如果是目录
            return "";
        }
        try {
            DBCreator creator = DBCreatorFactory.getDBCreatorDynamic(tableDO.getDataSourceConfigId());
            StringBuilder stringBuilder = creator.genCreateTableSQL(tableDO);
            return stringBuilder.toString();
            //return stringBuilder.toString()//格式化
            //        .replaceFirst("\\(", "(\n  ")
            //        .replaceAll(";", ";\n")
            //        .replaceAll("\\)\\);", ")\n);")
            //        .replaceAll(", \"", ", \n  \"")
            //        .replaceAll(", CONSTRAINT", ", \n  \\CONSTRAINT")
            //        ;
        }catch (BizException e) {
            throw new BizException("5001", e.getMessage());
        }catch (Exception e) {
            log.error("生成建表SQL失败");
            e.printStackTrace();
            throw new BizException("5001","生成建表SQL失败");
        }
    }

    @Override
    public void modelTableExportSQL(ModelTableExportReqVO reqVO) {
        StringBuilder sql = new StringBuilder();
        // 防止重复
        Set<Long> tableIds = new HashSet<>(reqVO.getTableIds());
        for (Long tableId : tableIds) {
            sql.append(modelTableDDL(tableId));
            sql.append("\n\n");
        }
        HttpServletResponse response = reqVO.getResponse();
        response.setCharacterEncoding("utf-8");
        response.setContentType("application/octet-stream");
        try {
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + reqVO.getFileName() + ".sql"); // 使用请求中的文件名
            ServletOutputStream outputStream = response.getOutputStream();
            // 将生成的 SQL 内容写入输出流
            outputStream.write(sql.toString().getBytes(StandardCharsets.UTF_8));
            outputStream.flush(); // 刷新输出流
            response.flushBuffer();
        } catch (Exception var14) {
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR); // 500错误
            throw new BizException("5001","生成建表SQL失败");
        }
    }


    @NotNull
    private String createTableDynamic(ModelTableDO tableDO) {
        try {
            DBCreator creator = DBCreatorFactory.getDBCreatorDynamic(tableDO.getDataSourceConfigId());
            creator.createTableDynamic(tableDO);
            log.info("执行建表完毕");
            return "执行建表完毕";
        }catch (BizException e) {
            // 处理业务异常，并记录异常信息
            log.error("执行建表失败: {}", e.getMessage());
            throw e; // 重新抛出业务异常
        } catch (Exception e) {
            // 处理其他异常
            log.error("执行建表过程中发生意外错误: {}", e.getMessage());
            throw new BizException("5001", "执行建表失败: " + e.getMessage());
        }
    }


    @NotNull
    public static StringBuilder getFullCode(ModelFieldDO field, StringBuilder sql) {
        sql.append(field.getFieldCode());
        sql.append(" ");
        sql.append(field.getFieldType());
        if(ObjUtilX.isNotEmpty(field.getLeng()) && field.getLeng() > 0){
            sql.append("(").append(field.getLeng());
            if(ObjUtilX.isNotEmpty(field.getFieldPrecision())  && field.getFieldPrecision() > 0){
                sql.append(",").append(field.getFieldPrecision());
            }
            sql.append(")");
        }
        return sql;
    }

    @NotNull
    private static StringBuilder getFullCode(ModelFieldAlterDO field, StringBuilder sql) {
        sql.append(field.getFieldCode());
        sql.append(" ");
        sql.append(field.getFieldType());
        if(ObjUtilX.isNotEmpty(field.getLeng())){
            sql.append("(").append(field.getLeng());
            if(ObjUtilX.isNotEmpty(field.getFieldPrecision())){
                sql.append(",").append(field.getFieldPrecision());
            }
            sql.append(")");
        }
        return sql;
    }

    /**
     * 写个反射的，暂时没用
     * @param field
     * @param sql
     * @return
     * @param <T>
     */
    public static <T> StringBuilder getFullCodeT(T field, StringBuilder sql) {
        try {
            // 使用反射获取字段值
            Method getFieldCode = field.getClass().getMethod("getFieldCode");
            Method getFieldType = field.getClass().getMethod("getFieldType");
            Method getLeng = field.getClass().getMethod("getLeng");
            Method getFieldPrecision = field.getClass().getMethod("getFieldPrecision");

            sql.append(getFieldCode.invoke(field)).append(" ")
                    .append(getFieldType.invoke(field));

            if (getLeng.invoke(field) != null && (Integer) getLeng.invoke(field) > 0) {
                sql.append("(").append(getLeng.invoke(field));

                if (getFieldPrecision.invoke(field) != null && (Integer) getFieldPrecision.invoke(field) > 0) {
                    sql.append(",").append(getFieldPrecision.invoke(field));
                }
                sql.append(")");
            }
        } catch (Exception e) {
            throw new RuntimeException("Error while generating SQL: " + e.getMessage(), e);
        }

        return sql;
    }

    //执行字段变更
    private void processFieldChanges(ModelTableDO tableDO) {
        List<ModelFieldAlterDO> changes = fieldAlterMapper.selectList(LambdaQueryWrapperX.<ModelFieldAlterDO>lambdaQueryX()
                .eqIfPresent(ModelFieldAlterDO::getTableId, tableDO.getTableId()));
        changes.forEach(change -> {
            switch (change.getOpType()) {
                case 0:
                    // 删除字段
                    dropField(change, tableDO);
                    break;
                case 1:
                    // 新增字段
                    addField(change, tableDO);
                    break;
                case 2:
                    // 修改字段
                    modifyField(change, tableDO);
                    break;
            }
            change.setIsProcessed(1);
        });
        fieldAlterMapper.updateBatch(changes);
    }

    /**************************** 字段修改 ***************************/
    public void addField(ModelFieldAlterDO change, ModelTableDO tableDO) {
        // 新增字段到数据库的逻辑
        StringBuilder sql = new StringBuilder("ALTER TABLE ");
        sql.append(tableDO.getTableCode());
        sql.append(" ADD COLUMN ");
        sql.append(change.getFieldCode()).append(" ").append(getFullCode(change, sql));
        if (change.getIsNullable() == 1) {
            sql.append(" NOT NULL");
        }
        //预留主键设置
//            if (field.isPrimaryKey()) {
//                sql.append(" PRIMARY KEY");
//            }
        if (ObjUtilX.isNotEmpty(change.getFieldName())) {
            sql.append(" COMMENT '").append(change.getFieldName()).append("'");
        }
        try (Connection connection = dataSource.getConnection();
             Statement stmt = connection.createStatement()) {
            stmt.execute(sql.toString());
            log.info("执行表字段新增完毕:" + tableDO.getTableCode() + " - " + change.getFieldCode());
        } catch (Exception e) {
            log.error("执行表字段新增失败：" + sql);
            throw new BizException("5001","执行表字段新增失败："+ tableDO.getTableCode() + " - " + change.getFieldCode());
        }
    }

    public void dropField(ModelFieldAlterDO change, ModelTableDO tableDO) {
        // 删除数据库字段的逻辑
        StringBuilder sql = new StringBuilder("ALTER TABLE ");
        sql.append(tableDO.getTableCode());
        sql.append(" DROP COLUMN IF EXISTS ");
        sql.append(change.getFieldCode());
        try (Connection connection = dataSource.getConnection();
             Statement stmt = connection.createStatement()) {
            stmt.execute(sql.toString());
            log.info("执行表字段删除完毕:" + tableDO.getTableCode() + " - " + change.getFieldCode());
        } catch (Exception e) {
            log.error("执行表字段删除失败：" + sql);
            throw new BizException("5001","执行表字段删除失败："+ tableDO.getTableCode() + " - " + change.getFieldCode());
        }
    }

    public void modifyField(ModelFieldAlterDO change, ModelTableDO tableDO) {
        // 修改数据库字段的逻辑
        // ALTER TABLE `mgs-low-code`.`sys_model_table`
        //CHANGE COLUMN `type` `type1` enum NULL COMMENT '类型11' AFTER `is_gen`;
        StringBuilder sql = new StringBuilder("ALTER TABLE ");
        sql.append(tableDO.getTableCode());
        sql.append(" CHANGE COLUMN ");
        sql.append(change.getOldFieldCode()).append(" ");
        sql.append(change.getFieldCode()).append(" ").append(getFullCode(change, sql));
        if (change.getIsNullable() == 1) {
            sql.append(" NOT NULL");
        }
        //预留主键设置
//            if (field.isPrimaryKey()) {
//                sql.append(" PRIMARY KEY");
//            }
        if (ObjUtilX.isNotEmpty(change.getFieldName())) {
            sql.append(" COMMENT '").append(change.getFieldName()).append("'");
        }
        try (Connection connection = dataSource.getConnection();
             Statement stmt = connection.createStatement()) {
            stmt.execute(sql.toString());
            log.info("执行表字段修改完毕:" + tableDO.getTableCode() + " - " + change.getFieldCode());
        } catch (Exception e) {
            log.error("执行表字段修改失败：" + sql);
            throw new BizException("5001","执行表字段修改失败："+ tableDO.getTableCode() + " - " + change.getFieldCode());
        }
    }
}
