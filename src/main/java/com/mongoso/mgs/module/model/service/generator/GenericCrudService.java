package com.mongoso.mgs.module.model.service.generator;

import cn.hutool.db.Db;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.ObjUtilX;
import com.mongoso.mgs.framework.common.util.StrUtilX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import com.mongoso.mgs.module.business.service.BusinessNodeService;
import com.mongoso.mgs.module.codegen.common.util.StringUtils;
import com.mongoso.mgs.module.model.dal.db.generator.BizRelationDO;
import com.mongoso.mgs.module.model.dal.db.modelfield.ModelFieldDO;
import com.mongoso.mgs.module.model.dal.mysql.generator.BizRelationMapper;
import com.mongoso.mgs.module.model.dal.mysql.generator.MyGenericMapper;
import com.mongoso.mgs.module.model.dal.mysql.modelfield.ModelFieldMapper;
import com.mongoso.mgs.module.model.dal.mysql.modeltable.ModelTableMapper;
import com.mongoso.mgs.module.rule.domain.RuleValidationResult;
import com.mongoso.mgs.module.rule.service.RuleEngineService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/2/14
 * @description 通用的实现建模之后的数据库操作服务
 */
@Slf4j
@Service
public class GenericCrudService {

    @Autowired
    private ModelTableMapper modelTableMapper;
    @Autowired
    private ModelFieldMapper fieldMapper;

    @Autowired
    private BizRelationMapper bizRelationMapper;

    @Autowired
    private MyGenericMapper myGenericMapper; // 假设这是您的通用 Mapper

    @Autowired
    private RuleEngineService ruleEngineService;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private BusinessNodeService businessNodeService;

    private final String CREATED_BY = "created_by";
    private final String CREATED_DT = "created_dt";
    private final String UPDATED_BY = "updated_by";
    private final String UPDATED_DT = "updated_dt";
    private final String TENANT_ID = "tenant_id";
    private final String PATH = "path";
    private final String CHILDREN = "children";
    private final String DELETED = "deleted";

    public Long create(Long tableId, Map<String, Object> data) {
        String tableCode = modelTableMapper.getTableCodeById(tableId); // 获取 tableCode
        if (tableCode == null) {
            throw new BizException("5001", "未找到对应表代码，table ID: " + tableId);
        }
        List<ModelFieldDO> fieldList = fieldMapper.selectList(LambdaQueryWrapperX.<ModelFieldDO>lambdaQueryX()
                .eq(ModelFieldDO::getTableId, tableId)
                .orderByAsc(ModelFieldDO::getSort));
        if(ObjUtilX.isEmpty(fieldList)){
            throw new BizException("5001","找不到模型表字段列表");
        }
        //boolean havePrimaryKey = false;
        String primaryKey = "";
        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        LocalDateTime now = LocalDateTime.now();
        for (ModelFieldDO field : fieldList) {
            if (field.getIsPrimaryKey() == 1) {
                //havePrimaryKey = true;
                primaryKey = field.getFieldCode();
            }
            switch (field.getFieldCode()) {
                case CREATED_BY:
                case UPDATED_BY:
                    data.put(field.getFieldCode(), loginUser.getFullUserName());
                    break;
                case CREATED_DT:
                case UPDATED_DT:
                    data.put(field.getFieldCode(), now);
                    break;
                case TENANT_ID:
                    data.put(field.getFieldCode(), ObjUtilX.isEmpty(loginUser.getTenantId()) ? 0L : loginUser.getTenantId());
                    break;
            }
        }
        Long id = IdWorker.getId();
        data.put(primaryKey, id);

        // 单次遍历以过滤值为空字符串的键值对，并生成 fields 和 values
        String[] result = data.entrySet().stream()
                .filter(entry -> !(entry.getValue() instanceof String && ((String) entry.getValue()).isEmpty()))
                .map(entry -> new String[]{
                        StringUtils.strHumpToBar(entry.getKey()), // 驼峰转换下划线
                        entry.getValue() != null ? "'" + entry.getValue().toString() + "'" : "NULL" // 处理值
                })
                .reduce(new String[]{"", ""}, (acc, current) -> {
                    acc[0] += (acc[0].isEmpty() ? "" : ", ") + current[0]; // 构建 fields 字符串
                    acc[1] += (acc[1].isEmpty() ? "" : ", ") + current[1]; // 构建 values 字符串
                    return acc;
                });

        String fields = result[0];
        String values = result[1];
        try {
            myGenericMapper.insertData(tableCode, fields, values);
        }catch (Exception e){
            throw new BizException("5001", "数据库操作失败，" + e.getMessage());
        }
        return id;
    }

    @Resource
    private DataSource dataSource;

    public Boolean executeBatchQueries(String sql) throws SQLException {
        Connection conn = Db.use(dataSource).getConnection();
        try {
            conn.setAutoCommit(false); // 关闭自动提交，手动管理事务
            // 先不用分号拆分，直接运行试试
            Db.use(dataSource).execute(sql);
            conn.commit(); // 提交事务
        } catch (SQLException e) {
            conn.rollback(); // 发生异常时回滚事务
            throw new BizException("5001", "操作失败，请检查原因: " + e.getMessage());
            //return false;
        } finally {
            try {
                conn.close(); // 关闭连接
            } catch (Exception e) {

            }
        }
        return true;
    }

    public Boolean executeBatchQueriesNotTrans(String sql) throws SQLException {
        // 先不用分号拆分，直接运行试试
        Db.use(dataSource).execute(sql);
        return true;
    }


    /**
     * 根据 Map 格式的入参生成树插入 SQL 语句
     * @param mainId 主表ID（传入0L时会自动生成）
     * @param bizId 业务ID
     * @param dataMap Map 格式的入参
     * @return 主表ID
     */
    public Long generateTreeInsertSQL(Long mainId, Long bizId, Map<String, Object> dataMap) throws SQLException {
        log.info("开始生成树形插入SQL, bizId: {}", bizId);

        // 1. 先执行业务级规则校验
        RuleValidationResult validation = ruleEngineService.validateBusinessRules(bizId, null, dataMap);
        if (!validation.isValid()) {
            throw new BizException("5002", "业务规则校验失败: " + validation.getErrorMessage());
        }

        // 2. 执行业务级规则计算（自动填充字段）
        Map<String, Object> calculatedDataMap = ruleEngineService.executeCalculationRules(bizId, dataMap);

        // 3. 获取业务关联配置
        Map<Long, BizRelationDO> relationMap = bizRelationMapper.selectByBizId(bizId).stream()
                .collect(Collectors.toMap(BizRelationDO::getTableId, relation -> relation));

        // 4. 开始生成 SQL，使用包装器保存mainId
        MainIdWrapper mainIdWrapper = new MainIdWrapper(mainId);
        String sql = generateInsertSQLFromMapWithMainId(mainIdWrapper, bizId, calculatedDataMap, null, null, relationMap);
        log.info("生成SQL完成, bizId: {}, SQL长度: {}, mainId: {}", bizId, sql.length(), mainIdWrapper.getMainId());

        // 5.执行SQL - 使用JdbcTemplate（自动参与Spring事务）
        try {
            jdbcTemplate.execute(sql);
            log.info("SQL执行成功，长度: {}", sql.length());
        } catch (Exception e) {
            log.error("SQL执行失败: {}", e.getMessage(), e);
            throw new BizException("5001", "业务执行失败: " + e.getMessage());
        }

        // 6. 返回主表ID
        return mainIdWrapper.getMainId();
    }
    
    /**
     * 主表ID包装器，用于在递归过程中保存和传递mainId
     */
    private static class MainIdWrapper {
        private Long mainId;
        
        public MainIdWrapper(Long mainId) {
            this.mainId = mainId;
        }
        
        public Long getMainId() {
            return mainId;
        }
        
        public void setMainId(Long mainId) {
            if (this.mainId == null || this.mainId == 0L) {
                this.mainId = mainId;
            }
        }
    }
    
    /**
     * 生成插入SQL的递归方法（带主表ID保存）
     */
    private String generateInsertSQLFromMapWithMainId(MainIdWrapper mainIdWrapper, Long bizId, Map<String, Object> dataMap, 
                                                     String parentField, Long parentFieldValue, Map<Long, BizRelationDO> relationMap) {
        if (dataMap == null || dataMap.isEmpty()) {
            return "";
        }

        StringBuilder sqlBuilder = new StringBuilder();

        // 获取当前层级的表ID和数据
        Long tableId = Long.parseLong(dataMap.get("table_id").toString());
        List<Map<String, Object>> dataList = (List<Map<String, Object>>) dataMap.get("data");

        if (parentField != null) {
            for (Map<String, Object> data : dataList) {
                data.put(parentField, parentFieldValue);
            }
        }

        // 处理每条数据
        for (Map<String, Object> data : dataList) {
            // 对每条数据执行表级规则校验
            RuleValidationResult dataValidation = ruleEngineService.validateBusinessRules(tableId, data);
            if (!dataValidation.isValid()) {
                throw new BizException("5002", tableId+" - 数据校验失败: " + dataValidation.getErrorMessage());
            }
            
            // 执行表级规则计算
            Map<String, Object> calculatedData = ruleEngineService.executeCalculationRules(tableId, data);
            
            // 生成当前记录的插入SQL
            Long currentRecordId = createSQL(tableId, calculatedData, sqlBuilder);
            
            // 保存主表ID（第一次生成的记录ID作为主表ID）
            mainIdWrapper.setMainId(currentRecordId);
            
            // 处理子级数据
            if (calculatedData.containsKey(CHILDREN)) {
                List<Map<String, Object>> childrenList = (List<Map<String, Object>>) calculatedData.get(CHILDREN);

                if (childrenList != null && !childrenList.isEmpty()) {
                    for (Map<String, Object> childMap : childrenList) {
                        if (childMap.containsKey("table_id")) {
                            Long childTableId = Long.parseLong(childMap.get("table_id").toString());
                            BizRelationDO relation = relationMap.get(childTableId);

                            if (relation != null) {
                                String childRelationField = relation.getFieldCode();
                                // 递归处理子级数据
                                String childrenSQL = generateInsertSQLFromMapWithMainId(mainIdWrapper, bizId, childMap, childRelationField, currentRecordId, relationMap);
                                if (!childrenSQL.isEmpty()) {
                                    sqlBuilder.append(childrenSQL);
                                }
                            } else {
                                log.warn("未找到表[{}]的关联配置", childTableId);
                            }
                        }
                    }
                }
            }
        }

        return sqlBuilder.toString();
    }

    public Long createSQL(Long tableId, Map<String, Object> data, StringBuilder sb) {
        String tableCode = modelTableMapper.getTableCodeById(tableId);
        if (tableCode == null) {
            throw new RuntimeException("未找到对应表代码，table ID: " + tableId);
        }
        List<ModelFieldDO> fieldList = fieldMapper.selectList(LambdaQueryWrapperX.<ModelFieldDO>lambdaQueryX()
                .eq(ModelFieldDO::getTableId, tableId)
                .orderByAsc(ModelFieldDO::getSort));
        if (ObjUtilX.isEmpty(fieldList)) {
            throw new RuntimeException("找不到模型表字段列表");
        }

        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        LocalDateTime now = LocalDateTime.now();
        String primaryKey = "";
        
        // 创建字段代码集合，用于过滤
        Set<String> validFieldCodes = fieldList.stream()
                .map(ModelFieldDO::getFieldCode)
                .collect(Collectors.toSet());

        for (ModelFieldDO field : fieldList) {
            if (field.getIsPrimaryKey() == 1) {
                primaryKey = field.getFieldCode();
            }
            switch (field.getFieldCode()) {
                case CREATED_BY:
                case UPDATED_BY:
                    data.put(field.getFieldCode(), loginUser.getFullUserName());
                    break;
                case CREATED_DT:
                case "updated_dt":
                    data.put(field.getFieldCode(), now.toString());
                    break;
                case TENANT_ID:
                    data.put(field.getFieldCode(), ObjUtilX.isEmpty(loginUser.getTenantId()) ? 0L : loginUser.getTenantId());
                    break;
            }
        }
        
        Long id = 0L;
        StringBuilder sqlBuilder = new StringBuilder();
        if (data.containsKey(primaryKey) && data.get(primaryKey) != null) {
            id = Long.valueOf(data.get(primaryKey).toString());
            if (data.containsKey(DELETED) && "1".equals(data.get(DELETED))) {
                if (sqlBuilder.length() > 0) {
                    sqlBuilder.append("; ");
                }
                sqlBuilder.append("DELETE FROM lowcode.").append(tableCode).append(" WHERE ").append(primaryKey).append(" = ").append(id);
            } else {
                // 有主键值，执行更新操作 - 只更新表中存在的字段
                String finalPrimaryKey = primaryKey;
                String updateFields = data.entrySet().stream()
                        .filter(entry -> {
                            String key = entry.getKey();
                            return validFieldCodes.contains(key) // 只包含表中存在的字段
                                    && !key.equals(finalPrimaryKey)
                                    && !key.equals(CREATED_BY)
                                    && !key.equals(CREATED_DT)
                                    && !key.equals(TENANT_ID)
                                    && !key.equals(PATH)
                                    && !key.equals(CHILDREN);
                        })
                        .map(entry -> StringUtils.strHumpToBar(entry.getKey()) + " = '" + entry.getValue().toString().replace("'", "''") + "'")
                        .collect(Collectors.joining(", "));

                if (sqlBuilder.length() > 0) {
                    sqlBuilder.append("; ");
                }
                sqlBuilder.append("UPDATE lowcode.").append(tableCode).append(" SET ").append(updateFields).append(" WHERE ").append(primaryKey).append(" = ").append(id);
            }
        } else {
            id = IdWorker.getId();
            data.put(primaryKey, id);

            // 只包含表中存在的字段
            List<String> validFields = data.keySet().stream()
                    .filter(key -> validFieldCodes.contains(key) && !key.equals(CHILDREN))
                    .toList();

            StringBuilder fieldsBuilder = new StringBuilder();
            fieldsBuilder.append(String.join(", ", validFields.stream()
                    .map(StringUtils::strHumpToBar)
                    .toList()));

            String values = String.join(", ", validFields.stream()
                    .map(key -> {
                        Object value = data.get(key);
                        return value != null ? "'" + value.toString().replace("'", "''") + "'" : "NULL";
                    })
                    .toList());

            if (!sqlBuilder.isEmpty()) {
                sqlBuilder.append("; ");
            }
            sqlBuilder.append("INSERT INTO lowcode.").append(tableCode).append(" (").append(fieldsBuilder).append(") VALUES (").append(values).append(")");
        }
        sb.append(sqlBuilder).append("; ");
        return id;
    }

    /**
     * 生成批量插入或更新的 SQL 语句
     * @param tableId 表 ID
     * @param dataList 要插入或更新的数据列表
     * @return 生成的 SQL 语句，多条 SQL 用分号分隔
     */
    public String batchCreateSQL(Long tableId, List<Map<String, Object>> dataList) {
        String tableCode = modelTableMapper.getTableCodeById(tableId);
        if (tableCode == null) {
            throw new RuntimeException("未找到对应表代码，table ID: " + tableId);
        }
        List<ModelFieldDO> fieldList = fieldMapper.selectList(LambdaQueryWrapperX.<ModelFieldDO>lambdaQueryX()
                .eq(ModelFieldDO::getTableId, tableId)
                .orderByAsc(ModelFieldDO::getSort));
        if (ObjUtilX.isEmpty(fieldList)) {
            throw new RuntimeException("找不到模型表字段列表");
        }

        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        LocalDateTime now = LocalDateTime.now();
        String primaryKey = "";
        
        // 创建字段代码集合，用于过滤
        Set<String> validFieldCodes = fieldList.stream()
                .map(ModelFieldDO::getFieldCode)
                .collect(Collectors.toSet());

        for (ModelFieldDO field : fieldList) {
            if (field.getIsPrimaryKey() == 1) {
                primaryKey = field.getFieldCode();
            }
            for (Map<String, Object> data : dataList) {
                switch (field.getFieldCode()) {
                    case CREATED_BY:
                    case UPDATED_BY:
                        data.put(field.getFieldCode(), loginUser.getFullUserName());
                        break;
                    case CREATED_DT:
                    case UPDATED_DT:
                        data.put(field.getFieldCode(), now.toString());
                        break;
                    case TENANT_ID:
                        data.put(field.getFieldCode(), ObjUtilX.isEmpty(loginUser.getTenantId()) ? 0L : loginUser.getTenantId());
                        break;
                }
            }
        }

        StringBuilder sqlBuilder = new StringBuilder();
        for (Map<String, Object> data : dataList) {
            if (data.containsKey(primaryKey) && data.get(primaryKey) != null) {
                Long id = Long.valueOf(data.get(primaryKey).toString());
                if ("1".equals(data.get(DELETED))) {
                    if (sqlBuilder.length() > 0) {
                        sqlBuilder.append("; ");
                    }
                    sqlBuilder.append("DELETE FROM lowcode.").append(tableCode).append(" WHERE ").append(primaryKey).append(" = ").append(id);
                } else {
                    // 有主键值，执行更新操作 - 只更新表中存在的字段
                    String finalPrimaryKey = primaryKey;
                    String updateFields = data.entrySet().stream()
                            .filter(entry -> validFieldCodes.contains(entry.getKey()) // 只包含表中存在的字段
                                    && !entry.getKey().equals(finalPrimaryKey)
                                    && !entry.getKey().equals(CREATED_BY)
                                    && !entry.getKey().equals(CREATED_DT)
                                    && !entry.getKey().equals(TENANT_ID)
                            )
                            .map(entry -> StringUtils.strHumpToBar(entry.getKey()) + " = '" + entry.getValue().toString().replace("'", "''") + "'")
                            .collect(Collectors.joining(", "));

                    if (sqlBuilder.length() > 0) {
                        sqlBuilder.append("; ");
                    }
                    sqlBuilder.append("UPDATE lowcode.").append(tableCode).append(" SET ").append(updateFields).append(" WHERE ").append(primaryKey).append(" = ").append(id);
                }
            } else {
                Long id = IdWorker.getId();
                data.put(primaryKey, id);

                // 只包含表中存在的字段
                List<String> validFields = data.keySet().stream()
                        .filter(key -> validFieldCodes.contains(key) && !key.equals(CHILDREN))
                        .collect(Collectors.toList());

                StringBuilder fieldsBuilder = new StringBuilder();
                fieldsBuilder.append(String.join(", ", validFields.stream()
                        .map(StringUtils::strHumpToBar)
                        .collect(Collectors.toList())));

                String values = String.join(", ", validFields.stream()
                        .map(key -> {
                            Object value = data.get(key);
                            return value != null ? "'" + value.toString().replace("'", "''") + "'" : "NULL";
                        })
                        .collect(Collectors.toList()));

                if (sqlBuilder.length() > 0) {
                    sqlBuilder.append("; ");
                }
                sqlBuilder.append("INSERT INTO lowcode.").append(tableCode).append(" (").append(fieldsBuilder).append(") VALUES (").append(values).append(")");
            }
        }

        return sqlBuilder.toString();
    }

    public Map<String, Object> read(Long tableId, Map<String, Object> params) {
        String tableCode = modelTableMapper.getTableCodeById(tableId); // 获取 tableCode
        if (tableCode == null) {
            throw new BizException("5001", "未找到对应表代码，table ID: " + tableId);
        }
        // 获取建模主键字段
        ModelFieldDO pkField = fieldMapper.selectOne(LambdaQueryWrapperX.<ModelFieldDO>lambdaQueryX()
                .eq(ModelFieldDO::getTableId, tableId)
                .eq(ModelFieldDO::getIsPrimaryKey, 1)
        );
        if (pkField == null) {
            throw new BizException("5001", "未找到对应表主键，table ID: " + tableId);
        }

        String pkCode = StringUtils.strBarToHump(pkField.getFieldCode());
        if (!params.containsKey(pkCode)) {
            throw new BizException("5001", "请选择一条记录");
        }
        Long id = Long.valueOf(params.get(pkCode).toString()); // 转换为 Long 类型
        //return convertKeys(myGenericMapper.selectData(tableCode, pkField.getFieldCode(), id));
        return myGenericMapper.selectData(tableCode, pkField.getFieldCode(), id);
    }

    public void update(Long tableId, Map<String, Object> params) {
        String tableCode = modelTableMapper.getTableCodeById(tableId); // 获取 tableCode
        if (tableCode == null) {
            throw new BizException("5001", "未找到对应表代码，table ID: " + tableId);
        }

        List<ModelFieldDO> fields = fieldMapper.selectList(LambdaQueryWrapperX.<ModelFieldDO>lambdaQueryX()
                .eq(ModelFieldDO::getTableId, tableId)
                .orderByAsc(ModelFieldDO::getSort));
        if(ObjUtilX.isEmpty(fields)){
            throw new BizException("5001","找不到模型表字段列表");
        }
        boolean havePrimaryKey = false;
        String primaryKey = "";
        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        LocalDateTime now = LocalDateTime.now();
        for (ModelFieldDO field : fields) {
            if(field.getIsPrimaryKey() == 1){
                havePrimaryKey = true;
                primaryKey = field.getFieldCode();
            }
            switch (field.getFieldCode()) {
                //case CREATED_BY:
                case UPDATED_BY:
                    params.put(field.getFieldCode(), loginUser.getFullUserName());
                    break;
                //case CREATED_DT:
                case "updated_dt":
                    params.put(field.getFieldCode(), now);
                    break;
            }
        }
        if (!havePrimaryKey) {
            throw new BizException("5001", "未找到对应表主键，table ID: " + tableId);
        }
        String pkCode = StringUtils.strBarToHump(primaryKey);
        if (!params.containsKey(pkCode)) {
            throw new BizException("5001", "请选择一条记录");
        }

        Long id = Long.valueOf(params.get(pkCode).toString()); // 转换为 Long 类型

        String updateFields = params.entrySet().stream()
                .filter(entry -> !entry.getKey().equals(pkCode))
                .map(entry -> StringUtils.strHumpToBar(entry.getKey()) + " = '" + entry.getValue() + "'")
                .reduce((a, b) -> a + ", " + b)
                .orElse("");

        try {
            myGenericMapper.updateData(tableCode, primaryKey, id, updateFields);
        }catch (Exception e){
            throw new BizException("5001", "数据库操作失败，" + e.getMessage());
        }
    }

    public void delete(Long tableId, Map<String, Object> params) {
        String tableCode = modelTableMapper.getTableCodeById(tableId); // 获取 tableCode
        if (tableCode == null) {
            throw new BizException("5001", "未找到对应表代码，table ID: " + tableId);
        }
        ModelFieldDO pkField = fieldMapper.selectOne(LambdaQueryWrapperX.<ModelFieldDO>lambdaQueryX()
                .eq(ModelFieldDO::getTableId, tableId)
                .eq(ModelFieldDO::getIsPrimaryKey, 1)
        );
        if (pkField == null) {
            throw new BizException("5001", "未找到对应表主键，table ID: " + tableId);
        }
        String pkCode = StringUtils.strBarToHump(pkField.getFieldCode());
        if (!params.containsKey(pkCode)) {
            throw new BizException("5001", "请选择一条记录");
        }
        Long id = Long.valueOf(params.get(pkCode).toString()); // 转换为 Long 类型
        try {
            myGenericMapper.deleteData(tableCode, pkField.getFieldCode(), id);
        }catch (Exception e){
            throw new BizException("5001", "数据库操作失败，" + e.getMessage());
        }
    }

    public List<Map<String, Object>> getPagedData(Long tableId, String filter, int currentPage, int pageSize) {
        String tableCode = modelTableMapper.getTableCodeById(tableId); // 获取 tableCode
        if (tableCode == null) {
            throw new BizException("5001", "未找到对应表代码，table ID: " + tableId);
        }

        int offset = (currentPage - 1) * pageSize; // 正确的逻辑
        if(ObjUtilX.isEmpty(filter)){
            filter = null;
        }else{
            //filter = StringUtils.strHumpToBar(filter);
        }
        //return convertKeysToCamelCase(myGenericMapper.selectPage(tableCode, filter, offset, pageSize));
        return myGenericMapper.selectPage(tableCode, filter, offset, pageSize);
    }

    public List<Map<String, Object>> getAllData(Long tableId, String filter) {
        String tableCode = modelTableMapper.getTableCodeById(tableId); // 获取 tableCode
        if (tableCode == null) {
            throw new BizException("5001", "未找到对应表代码，table ID: " + tableId);
        }
        //return convertKeysToCamelCase(myGenericMapper.selectAll(tableCode, filter));
        return myGenericMapper.selectAll(tableCode, filter);
    }

    public Long getCount(Long tableId, String filter) {
        String tableCode = modelTableMapper.getTableCodeById(tableId); // 获取 tableCode
        if (tableCode == null) {
            throw new BizException("5001", "未找到对应表代码，table ID: " + tableId);
        }
        return myGenericMapper.selectCount(tableCode, filter);
    }

    // 转换 Map 的键从下划线格式到驼峰格式
    private List<Map<String, Object>> convertKeysToCamelCase(List<Map<String, Object>> originalList) {
        if(ObjUtilX.isEmpty(originalList)){
            return new ArrayList<>();
        }
        return originalList.stream()
                .map(this::convertKeys)
                .collect(Collectors.toList());
    }

    // Helper method to convert each map's keys
    private Map<String, Object> convertKeys(Map<String, Object> originalMap) {
        if(ObjUtilX.isEmpty(originalMap)){
            throw new BizException("5001", "未找到对应信息");
        }
        return originalMap.entrySet().stream()
                .collect(Collectors.toMap(
                        entry -> StringUtils.strBarToHump(entry.getKey()), // 假设已实现该方法
                        Map.Entry::getValue
                ));
    }

    public Map<String, Object> batchRead(Long bizId, Long mainId) {
        if (bizId == null) {
            throw new BizException("5001", "请选择业务模型");
        }
        // 通过 bizId 查询所有相关的 BizRelationDO 记录
        List<BizRelationDO> bizRelationList = bizRelationMapper.selectByBizId(bizId);
        if (bizRelationList.isEmpty()) {
            return new HashMap<>();
        }

        // 找到根表
        BizRelationDO rootRelation = findRootRelation(bizRelationList);
        if (rootRelation == null) {
            throw new BizException("5001", "未找到业务关联配置信息");
        }

        Long rootTableId = rootRelation.getTableId();
        // 在构建树时就进行规则计算
        Map<String, Object> treeData = buildTreeWithCalculation(rootTableId, mainId, bizRelationList, "", bizId);
        
        return treeData;
    }

    /**
     * 找到根表对应的 BizRelationDO 记录
     * @param bizRelationList 业务关系记录列表
     * @return 根表对应的 BizRelationDO 记录
     */
    private BizRelationDO findRootRelation(List<BizRelationDO> bizRelationList) {
        for (BizRelationDO relation : bizRelationList) {
            boolean isRoot = true;
            for (BizRelationDO check : bizRelationList) {
                if (Objects.equals(check.getTableId(), relation.getParentTableId())) {
                    isRoot = false;
                    break;
                }
            }
            if (isRoot) {
                return relation;
            }
        }
        return null;
    }

    /**
     * 递归构建树形结构（带规则计算）
     * @param tableId 当前表 ID
     * @param parentId 父记录 ID
     * @param bizRelationList 业务关系记录列表
     * @param parentPath 父路径
     * @param bizId 业务ID
     * @return 包含树形结构数据的 Map
     */
    private Map<String, Object> buildTreeWithCalculation(Long tableId, Long parentId, List<BizRelationDO> bizRelationList, String parentPath, Long bizId) {
        String tableCode = modelTableMapper.getTableCodeById(tableId);
        if (tableCode == null) {
            throw new BizException("5001", "未找到对应表代码，table ID: " + tableId);
        }

        // 获取建模主键字段
        ModelFieldDO pkField = fieldMapper.selectOne(LambdaQueryWrapperX.<ModelFieldDO>lambdaQueryX()
                .eq(ModelFieldDO::getTableId, tableId)
                .eq(ModelFieldDO::getIsPrimaryKey, 1)
        );
        if (pkField == null) {
            throw new BizException("5001", "未找到对应表主键，table ID: " + tableId);
        }

        String pkCode = pkField.getFieldCode();
        StringBuilder filter = new StringBuilder();
        if (parentId != null) {
            // 找到关联字段
            BizRelationDO relation = bizRelationList.stream()
                    .filter(r -> Objects.equals(r.getTableId(), tableId))
                    .findFirst()
                    .orElse(null);
            if (relation != null && StrUtilX.isNotEmpty(relation.getFieldCode())) {
                filter.append(relation.getFieldCode())
                        .append(" = '").append(parentId).append("'");
            } else {
                // 根节点使用主键查
                filter.append(pkCode)
                        .append(" = '").append(parentId).append("'");
            }
        }

        // 查询该表的所有数据
        List<Map<String, Object>> tableData = myGenericMapper.selectAll(tableCode, filter.toString());
        log.info("查询到{}条数据，tableId: {}, tableCode: {}", tableData.size(), tableId, tableCode);

        List<Map<String, Object>> children = new ArrayList<>();
        if (tableData.isEmpty()) {
            // 处理空数据的情况...
            Map<String, Object> emptyRecord = new HashMap<>();
            Long recordId = 0L;
            String currentPath = StrUtilX.isEmpty(parentPath) ? recordId.toString() : parentPath + "." + recordId;
            emptyRecord.put(PATH, currentPath);
            emptyRecord.put(pkCode, recordId);

            // 查找子表
            List<BizRelationDO> childRelations = bizRelationList.stream()
                    .filter(r -> Objects.equals(r.getParentTableId(), tableId))
                    .collect(Collectors.toList());
            for (BizRelationDO childRelation : childRelations) {
                Map<String, Object> childTree = buildTreeWithCalculation(childRelation.getTableId(), recordId, bizRelationList, currentPath, bizId);
                if (!childTree.isEmpty()) {
                    if (!emptyRecord.containsKey(CHILDREN)) {
                        emptyRecord.put(CHILDREN, new ArrayList<Map<String, Object>>());
                    }
                    ((List<Map<String, Object>>) emptyRecord.get(CHILDREN)).add(childTree);
                }
            }
            children.add(emptyRecord);
        } else {
            for (Map<String, Object> record : tableData) {
                log.info("处理记录前: {}", record.keySet());
                
                // 关键：这里需要确保调用正确的方法
                Map<String, Object> calculatedRecord;
                try {
                    calculatedRecord = ruleEngineService.executeCalculationRules(bizId, tableId, record);
                    log.info("规则计算后: {}", calculatedRecord.keySet());
                    log.info("是否包含device_status_str: {}", calculatedRecord.containsKey("device_status_str"));
                } catch (Exception e) {
                    log.error("规则计算失败: {}", e.getMessage(), e);
                    calculatedRecord = new HashMap<>(record);
                }
                
                Long recordId = (Long) calculatedRecord.get(pkCode);
                // 构建当前节点的 path
                String currentPath = StrUtilX.isEmpty(parentPath) ? recordId.toString() : parentPath + "." + recordId;
                calculatedRecord.put(PATH, currentPath);

                // 查找子表
                List<BizRelationDO> childRelations = bizRelationList.stream()
                        .filter(r -> Objects.equals(r.getParentTableId(), tableId))
                        .collect(Collectors.toList());
                for (BizRelationDO childRelation : childRelations) {
                    Map<String, Object> childTree = buildTreeWithCalculation(childRelation.getTableId(), recordId, bizRelationList, currentPath, bizId);
                    if (!childTree.isEmpty()) {
                        if (!calculatedRecord.containsKey(CHILDREN)) {
                            calculatedRecord.put(CHILDREN, new ArrayList<Map<String, Object>>());
                        }
                        ((List<Map<String, Object>>) calculatedRecord.get(CHILDREN)).add(childTree);
                    }
                }
                
                log.info("最终添加到children的记录: {}", calculatedRecord.keySet());
                children.add(calculatedRecord);
            }
        }

        Map<String, Object> result = new HashMap<>();
        result.put("table_id", tableId);
        result.put("data", children);
        log.info("返回结果，data包含{}条记录", children.size());
        return result;
    }

    //private Map<String, Object> buildTreeHaveEmpty(Long tableId, Long parentId, List<BizRelationDO> bizRelationList, String parentPath) {
    //    String tableCode = modelTableMapper.getTableCodeById(tableId);
    //    if (tableCode == null) {
    //        throw new BizException("5001", "未找到对应表代码，table ID: " + tableId);
    //    }
    //
    //    // 获取建模主键字段
    //    ModelFieldDO pkField = fieldMapper.selectOne(LambdaQueryWrapperX.<ModelFieldDO>lambdaQueryX()
    //            .eq(ModelFieldDO::getTableId, tableId)
    //            .eq(ModelFieldDO::getIsPrimaryKey, 1)
    //    );
    //    if (pkField == null) {
    //        throw new BizException("5001", "未找到对应表主键，table ID: " + tableId);
    //    }
    //
    //    String pkCode = pkField.getFieldCode();
    //    StringBuilder filter = new StringBuilder();
    //    if (parentId != null) {
    //        // 找到关联字段
    //        BizRelationDO relation = bizRelationList.stream()
    //                .filter(r -> Objects.equals(r.getTableId(), tableId))
    //                .findFirst()
    //                .orElse(null);
    //        if (relation != null && StrUtilX.isNotEmpty(relation.getFieldCode())) {
    //            filter.append(relation.getFieldCode())
    //                    .append(" = '").append(parentId).append("'");
    //        } else {
    //            // 根节点使用主键查
    //            filter.append(pkCode)
    //                    .append(" = '").append(parentId).append("'");
    //        }
    //    }
    //
    //    // 查询该表的所有数据
    //    List<Map<String, Object>> tableData = myGenericMapper.selectAll(tableCode, filter.toString());
    //
    //    List<Map<String, Object>> children = new ArrayList<>();
    //    for (Map<String, Object> record : tableData) {
    //        Long recordId = (Long) record.get(pkCode);
    //        // 构建当前节点的 path
    //        String currentPath = StrUtilX.isEmpty(parentPath) ? recordId.toString() : parentPath + "." + recordId;
    //        record.put(PATH, currentPath);
    //
    //        // 查找子表
    //        List<BizRelationDO> childRelations = bizRelationList.stream()
    //                .filter(r -> Objects.equals(r.getParentTableId(), tableId))
    //                .collect(Collectors.toList());
    //        for (BizRelationDO childRelation : childRelations) {
    //            Map<String, Object> childTree = buildTreeHaveEmpty(childRelation.getTableId(), recordId, bizRelationList, currentPath);
    //            if (!childTree.isEmpty()) {
    //                if (!record.containsKey(CHILDREN)) {
    //                    record.put(CHILDREN, new ArrayList<Map<String, Object>>());
    //                }
    //                ((List<Map<String, Object>>) record.get(CHILDREN)).add(childTree);
    //            }
    //        }
    //        children.add(record);
    //    }
    //
    //    Map<String, Object> result = new HashMap<>();
    //    result.put("table_id", tableId);
    //    result.put("data", children);
    //    return result;
    //}

    /**
     * 根据业务 ID 查询相关表的数据
     * @param bizId 业务 ID
     * @return 包含各表数据的 Map，键为表 ID，值为对应表的数据列表
     */
    public Map<Long, List<Map<String, Object>>> batchReadOld(Long bizId) {
        Map<Long, List<Map<String, Object>>> resultMap = new HashMap<>();

        // 通过 bizId 查询所有相关的 BizRelationDO 记录
        List<BizRelationDO> bizRelationList = bizRelationMapper.selectByBizId(bizId);
        if (bizRelationList.isEmpty()) {
            return resultMap;
        }

        // 提取所有的 tableId
        Set<Long> tableIds = new HashSet<>();
        bizRelationList.forEach(bizRelation -> {
            tableIds.add(bizRelation.getParentTableId());
            tableIds.add(bizRelation.getTableId());
        });
        tableIds.remove(null);
        for (Long tableId : tableIds) {
            String tableCode = modelTableMapper.getTableCodeById(tableId);
            if (tableCode == null) {
                throw new BizException("5001", "未找到对应表代码，table ID: " + tableId);
            }

            // 获取建模主键字段
            ModelFieldDO pkField = fieldMapper.selectOne(LambdaQueryWrapperX.<ModelFieldDO>lambdaQueryX()
                    .eq(ModelFieldDO::getTableId, tableId)
                    .eq(ModelFieldDO::getIsPrimaryKey, 1)
            );
            if (pkField == null) {
                throw new BizException("5001", "未找到对应表主键，table ID: " + tableId);
            }

            // 查询该表的所有数据
            List<Map<String, Object>> tableData = myGenericMapper.selectAll(tableCode, null);
            // 转驼峰
            //List<Map<String, Object>> convertedData = convertKeysToCamelCase(tableData);
            //resultMap.put(tableId, convertedData);
            resultMap.put(tableId, tableData);
        }

        return resultMap;
    }

    /**
     * 将字符串从下划线格式转换为驼峰格式
     * @param input 输入字符串
     * @return 转换后的驼峰格式字符串
     */
    private String convertToCamelCase(String input) {
        StringBuilder result = new StringBuilder();
        boolean capitalizeNext = false;
        for (char c : input.toCharArray()) {
            if (c == '_') {
                capitalizeNext = true;
            } else {
                if (capitalizeNext) {
                    result.append(Character.toUpperCase(c));
                    capitalizeNext = false;
                } else {
                    result.append(Character.toLowerCase(c));
                }
            }
        }
        return result.toString();
    }

    /**
     * 生成树形插入SQL并执行（包含后置脚本，统一Spring事务管理）
     */
    @Transactional(rollbackFor = Exception.class)
    public Long generateTreeInsertSQLWithScript(Long mainId, Long bizId, Map<String, Object> dataMap) {
        log.info("开始生成树形插入SQL（含脚本）, bizId: {}", bizId);

        try {
            // 1. 先执行业务级规则校验
            RuleValidationResult validation = ruleEngineService.validateBusinessRules(bizId, null, dataMap);
            if (!validation.isValid()) {
                throw new BizException("5002", "业务规则校验失败: " + validation.getErrorMessage());
            }
            
            // 2. 执行业务级规则计算
            Map<String, Object> calculatedDataMap = ruleEngineService.executeCalculationRules(bizId, dataMap);
            
            // 3. 获取业务关联配置
            Map<Long, BizRelationDO> relationMap = bizRelationMapper.selectByBizId(bizId).stream()
                    .collect(Collectors.toMap(BizRelationDO::getTableId, relation -> relation));

            // 4. 生成SQL
            MainIdWrapper mainIdWrapper = new MainIdWrapper(mainId);
            String sql = generateInsertSQLFromMapWithMainId(mainIdWrapper, bizId, calculatedDataMap, null, null, relationMap);
            log.info("生成SQL完成, bizId: {}, SQL长度: {}, mainId: {}", bizId, sql.length(), mainIdWrapper.getMainId());

            // 5. 执行SQL - 使用Spring事务管理
            try {
                jdbcTemplate.execute(sql);
                log.info("SQL执行成功，长度: {}", sql.length());
            } catch (Exception e) {
                log.error("SQL执行失败: {}", e.getMessage(), e);
                throw new BizException("5001", "业务执行失败: " + e.getMessage());
            }

            // 6. 执行后置脚本（在同一Spring事务中）
            try {
                executePostScripts(bizId, mainIdWrapper.getMainId(), calculatedDataMap);
                log.info("后置脚本执行成功, bizId: {}, mainId: {}", bizId, mainIdWrapper.getMainId());
            } catch (Exception e) {
                log.error("后置脚本执行失败，事务将回滚, bizId: {}, mainId: {}, error: {}",
                         bizId, mainIdWrapper.getMainId(), e.getMessage(), e);
                // 直接抛出异常，让Spring事务回滚
                throw new BizException("5001", "后置脚本执行失败: " + e.getMessage());
            }

            return mainIdWrapper.getMainId();
            
        } catch (Exception e) {
            log.error("生成树形插入SQL失败，事务将回滚, bizId: {}, error: {}", bizId, e.getMessage(), e);
            throw e; // 重新抛出异常，确保事务回滚
        }
    }

    /**
     * 执行后置脚本
     */
    private void executePostScripts(Long bizId, Long mainId, Map<String, Object> dataMap) {
        log.info("开始执行后置脚本, bizId: {}, mainId: {}", bizId, mainId);
        
        try {
            // 准备脚本参数
            Map<String, Object> scriptParams = new HashMap<>(dataMap);
            scriptParams.put("bizId", bizId);
            scriptParams.put("mainId", mainId);
            scriptParams.put("operation", "SAVE_BATCH");
            
            // 执行后置节点（包含脚本节点）
            businessNodeService.executeNodesByEvent(bizId, "AFTER", "SAVE_BATCH", mainId, scriptParams);
            
            log.info("后置脚本执行完成, bizId: {}, mainId: {}", bizId, mainId);
            
        } catch (Exception e) {
            log.error("后置脚本执行失败, bizId: {}, mainId: {}, error: {}", bizId, mainId, e.getMessage(), e);
            throw e; // 直接抛出异常，让调用方处理
        }
    }
}
