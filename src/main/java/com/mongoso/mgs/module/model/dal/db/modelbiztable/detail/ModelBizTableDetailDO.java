package com.mongoso.mgs.module.model.dal.db.modelbiztable.detail;

import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;
import lombok.*;

import com.baomidou.mybatisplus.annotation.*;

/**
 * 子表明细 DO
 *
 * <AUTHOR>
 */
@TableName(value = "lowcode.sys_model_biz_table_detail", autoResultMap = true)
//@KeySequence("sys_model_biz_table_detail_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ModelBizTableDetailDO extends OperateDO {

    /** 数据id */
        @TableId(type = IdType.ASSIGN_ID)
    private Long dataId;

    /** 明细表id */
    private Long tableId;

    /** 主表业务编码 */
    private String bizCode;

    /** 明细表 */
    private String tableCode;

    /** 级别 */
    private Integer level;

    /** 子表类型[0:表格 1:表格树] */
    private Integer tableType;

    /** 是否必有一行 */
    private Short isNullAble;

    /** 父数据id */
    private Long parentDataId;

    /** 备注 */
    private String remark;

    /** 父行路径 */
    private String parentRowPath;

    /** 父行号 (只有当子表类型是树结构才有数据)*/
    private Integer parentRowNo;

    /** 行号 */
    private Integer rowNo;
}
