package com.mongoso.mgs.module.model.service.modelfieldconf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.mongoso.mgs.common.util.HttpRequestUtil;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.common.util.ObjUtilX;
import com.mongoso.mgs.framework.common.util.StrUtilX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.codegen.common.util.StringUtils;
import com.mongoso.mgs.module.enums.DBTypeEnum;
import com.mongoso.mgs.module.enums.DataTypeEnum;
import com.mongoso.mgs.module.model.controller.admin.modelfieldconf.vo.*;
import com.mongoso.mgs.module.model.controller.admin.modeltable.vo.ModelTableListifyReqVO;
import com.mongoso.mgs.module.model.dal.db.modelfieldconf.ModelFieldConfDO;
import com.mongoso.mgs.module.model.dal.mysql.modelfieldconf.ModelFieldConfMapper;
import com.mongoso.mgs.module.model.service.modeltable.ModelTableService;
import jakarta.annotation.Resource;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringEscapeUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;
// import static com.mongoso.mgs.module.model.enums.ErrorCodeConstants.*;


/**
 * 图形建模字段翻译配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Log4j2
public class ModelFieldConfServiceImpl implements ModelFieldConfService {

    @Resource
    private ModelFieldConfMapper fieldConfMapper;

    @Lazy
    @Resource
    private ModelTableService tableService;

    @Value("${remote.translate.url}")
    private String translateUrl;

    @Override
    public Long modelFieldConfAdd(ModelFieldConfAditReqVO reqVO) {
        //验重
        repeatValid(reqVO);
        // 插入
        ModelFieldConfDO fieldConf = BeanUtilX.copy(reqVO, ModelFieldConfDO::new);
        fieldConfMapper.insert(fieldConf);
        // 返回
        return fieldConf.getId();
    }

    @Override
    public Long modelFieldConfEdit(ModelFieldConfAditReqVO reqVO) {
        // 校验存在
        this.modelFieldConfValidateExists(reqVO.getId());
        // 更新
        ModelFieldConfDO fieldConf = BeanUtilX.copy(reqVO, ModelFieldConfDO::new);
        if(ObjUtilX.isEmpty(fieldConf.getFieldName()) || ObjUtilX.isEmpty(fieldConf.getEnglishName())){
            throw new BizException("5001", "中/英文名不能为空");
        }
        //验重
        repeatValid(reqVO);
        //fieldConf.setEnglishName(StringUtils.strBarToHump(fieldConf.getFieldCode()));
        fieldConf.setFieldCode(StringUtils.strHumpToBar(fieldConf.getEnglishName()));
        fieldConfMapper.updateById(fieldConf);
        // 返回
        return fieldConf.getId();
    }

    public Long repeatValid(ModelFieldConfAditReqVO reqVO) {
        long count = 0L;
        long id = reqVO.getId();
        // 根据数据源判重
        if (StrUtilX.isEmpty(reqVO.getId())) {
            // 新增验重
            if (StrUtilX.isNotEmpty(reqVO.getFieldName())) {
                count = fieldConfMapper.selectCountByName(reqVO.getFieldName(),null);
                if (count>0){
                    throw new BizException("5001", "您输入的中文名称值在项目中重复，请重新输入：" + reqVO.getFieldName());
                }
            }
            if (StrUtilX.isNotEmpty(reqVO.getFieldCode())) {
                count = fieldConfMapper.selectCountByCode(reqVO.getFieldCode(),null);
                if (count>0){
                    throw new BizException("5001", "您输入的英文名称值在项目中重复，请重新输入：" + reqVO.getFieldCode());
                }
            }
        } else {
            // 编辑验重
            if (StrUtilX.isNotEmpty(reqVO.getFieldName())) {
                count = fieldConfMapper.selectCountByName(reqVO.getFieldName(), reqVO.getId());
                if (count>0){
                    throw new BizException("5001", "您输入的中文名称值在项目中重复，请重新输入：" + reqVO.getFieldName());
                }
            }
            if (StrUtilX.isNotEmpty(reqVO.getEnglishName())) {
                count = fieldConfMapper.selectCountByEnglishName(reqVO.getEnglishName(), reqVO.getId());
                if (count>0){
                    throw new BizException("5001", "您输入的英文名称值在项目中重复，请重新输入：" + reqVO.getEnglishName());
                }
            }
        }
        return count;
    }

    @Override
    public void modelFieldConfDel(Long id) {
        // 校验存在
        this.modelFieldConfValidateExists(id);
        // 删除
        fieldConfMapper.deleteById(id);
    }

    private ModelFieldConfDO modelFieldConfValidateExists(Long id) {
        ModelFieldConfDO fieldConf = fieldConfMapper.selectById(id);
        if (fieldConf == null) {
            // throw exception(FIELD_CONF_NOT_EXISTS);
            throw new BizException("5001", "图形建模字段翻译配置不存在");
        }
        return fieldConf;
    }

    @Override
    public ModelFieldConfDO modelFieldConfDetail(Long id) {
        return fieldConfMapper.selectById(id);
    }

    @Override
    public List<ModelFieldConfDO> modelFieldConfList(ModelFieldConfQueryReqVO reqVO) {

        if(ObjUtilX.isEmpty(reqVO.getFieldName())){
            return new ArrayList<>();
        }
        List<ModelFieldConfDO> confs = fieldConfMapper.selectList(
                new LambdaQueryWrapper<ModelFieldConfDO>()
//                        .eq(ModelFieldConfDO::getProjectId, reqVO.getProjectId())
                        .and(wrapper ->
                                wrapper.like(ModelFieldConfDO::getFieldName, reqVO.getFieldName())
                                        .or().like(ModelFieldConfDO::getFieldCode, reqVO.getFieldName())
                                        .or().like(ModelFieldConfDO::getEnglishName, reqVO.getFieldName())
                        )
        );
        if(reqVO.getTextType() == 1){//接口文档返回
            confs.stream().forEach(conf -> {
                //conf.setEnglishName(StringUtils.strBarToHump(conf.getEnglishName()));
                conf.setEnglishName(conf.getEnglishName());
                if(StrUtilX.isNotEmpty(conf.getFieldType())){
                    if("DECIMAL".equals(conf.getFieldType()) || conf.getFieldType().startsWith("INT")){
                        conf.setDataType(DataTypeEnum.NUMBER.getValue());
                    }else{
                        conf.setDataType(DataTypeEnum.STRING.getValue());
                    }
                }else{
                    conf.setDataType(DataTypeEnum.STRING.getValue());
                }
            });
        }
        return confs;
    }

    @Override
    public PageResult<ModelFieldConfRespVO> modelFieldConfPage(ModelFieldConfPageReqVO reqVO) {

        PageResult<ModelFieldConfDO> rst = fieldConfMapper.selectPage(reqVO);
        List<ModelFieldConfRespVO> list = rst.getList().stream().map((item) -> {
            ModelFieldConfRespVO conf = BeanUtilX.copy(item, ModelFieldConfRespVO::new);
            // 查询车间产线
            if(reqVO.getTextType() == 1){//接口文档返回
                conf.setEnglishName(StringUtils.strBarToHump(conf.getEnglishName()));
                if(StrUtilX.isNotEmpty(conf.getFieldType())){
                    if("DECIMAL".equals(conf.getFieldType()) || conf.getFieldType().startsWith("INT")){
                        conf.setDataType(DataTypeEnum.NUMBER.getValue());
                    }else{
                        conf.setDataType(DataTypeEnum.STRING.getValue());
                    }
                }else{
                    conf.setDataType(DataTypeEnum.STRING.getValue());
                }
            }
            return conf;
        }).collect(Collectors.toList());
        return PageResult.init(rst, list);
    }

    @Override
    public List<ModelFieldConfDO> modelFieldTrans(ModelFieldTransReqVO reqVO) {
        if(ObjUtilX.isEmpty(reqVO.getProjectId())){
            throw new BizException("5001", "请选择一个项目");
        }
        List<String> fieldNameList = new ArrayList<>(reqVO.getFields().size());
        Map<String, String> reqMap = new HashMap<>(reqVO.getFields().size());
        for (ModelFieldConfAditReqVO req : reqVO.getFields()) {
            if(ObjUtilX.isEmpty(req.getItemName())){
                continue;
            }
            fieldNameList.add(req.getItemName());
            reqMap.put(req.getItemName(), ObjUtilX.isEmpty(req.getEnglishName())?"":req.getEnglishName());
        }
        if(ObjUtilX.isEmpty(fieldNameList)){
            throw new BizException("5001", "请传入要翻译的字段");
        }
        String param = "中国|" + String.join("|", fieldNameList);
        return getModelFieldConfDOS(reqVO.getProjectId(), param, fieldNameList);
    }

    @Override
    public List<ModelFieldConfDO> getModelFieldConfDOS(Long projectId, String param, List<String> fieldNameList) {
        //区分数据库
        String dbType = tableService.modelTableDbType(projectId);
        //String dbTypePrefix = "";
        if (dbType.toLowerCase().equals(DBTypeEnum.PG.getValue())) {
            //dbTypePrefix = "fieldType请选择一个字段类型，选项包括：[INT2, INT4, INT8, NUMERIC, CHAR, VARCHAR, TEXT, DATE, TIMESTAMP]；要翻译的字段：中国|";
        } else if (dbType.toLowerCase().equals(DBTypeEnum.MYSQL.getValue())) {
            //dbTypePrefix = "fieldType请选择一个字段类型，选项包括：[INT,BIGINT,DECIMAL,CHAR,VARCHAR,TEXT,DATE,DATETIME]；要翻译的字段：中国|";
        } else {
            throw new BizException("5001", "暂不支持该数据库类型");
        }
        JSONObject req = new JSONObject();
        req.put("field_list", param);
        req.put("db_type", dbType);
        JSONObject exeRst;
        String s = "";
        try {
            s = HttpRequestUtil.sendPost(translateUrl, req.toJSONString(), null, 30000, 30000);
            exeRst = JSONObject.parseObject(s);
        }catch (IOException e){
            throw new BizException("5001", "连接翻译服务器失败：" + e.getMessage());
        }catch (Exception e){
            throw new BizException("5001", "翻译失败：" + s);
        }
        if(exeRst.containsKey("error")){
            throw new BizException("5001", "翻译失败：" + exeRst.getString("error"));
        }
        // 解析 JSON 翻译的字符串
        JSONArray fieldsArray = exeRst.getJSONArray("fields");
        if(ObjUtilX.isEmpty(fieldsArray) || fieldsArray.size() == 0){
            throw new BizException("5001", "AI翻译失败，请重试");
        }
        // 创建 ModelFieldConfDO
        List<ModelFieldConfDO> modelFieldConfList = new ArrayList<>(fieldsArray.size());

//        List<ModelFieldConfDO> confDOS = fieldConfMapper.selectListOld(new ModelFieldConfQueryReqVO());
//        List<String> fieldNames = confDOS.stream()
//                .map(ModelFieldConfDO::getFieldName) // 提取 fieldName
//                .filter(fieldName -> ObjUtilX.isNotEmpty(fieldName )) // 过滤掉空值
//                .collect(Collectors.toList()); // 收集结果到 List

        // 查询已有的字段配置
        List<ModelFieldConfDO> fieldConfList = fieldConfMapper.selectList(LambdaQueryWrapperX.<ModelFieldConfDO>lambdaQueryX()
                .in(ModelFieldConfDO::getFieldName, fieldNameList)
        );

        // 使用 Map 存储字段配置以便快速查找
        //Map<String, ModelFieldConfDO> fieldConfMap = fieldConfList.stream()
        //        .filter(fieldConf -> StrUtilX.isNotEmpty(fieldConf.getEnglishName()))
        //        .collect(Collectors.toMap(ModelFieldConfDO::getFieldName, fieldConf -> fieldConf));

        Map<String, ModelFieldConfDO> fieldConfMap = fieldConfList.stream()
                .filter(fieldConf -> StrUtilX.isNotEmpty(fieldConf.getEnglishName()))
                .collect(Collectors.toMap(
                        ModelFieldConfDO::getFieldName,
                        fieldConf -> fieldConf,
                        (existing, replacement) -> existing // 保留现有的值
                ));

        // 遍历请求字段并更新字段代码
        //for (ModelFieldAditReqVO params : aditReq.getFields()) {
        //    ModelFieldConfDO fieldConf = fieldConfMap.get(params.getFieldName());
        //    if (fieldConf != null) {
        //        // 更新关联字段的内容
        //        params.setFieldCode(fieldConf.getFieldCode()); // 更新字段代码
        //        // 如有需要，还可以更新其他字段，例如：
        //        // params.setOtherField(fieldConf.getOtherFieldValue());
        //    }
        //}

        // 使用 for 循环遍历 fieldsArray
        for (int i = 0; i < fieldsArray.size(); i++) {
            JSONObject fieldJson = fieldsArray.getJSONObject(i);

            String fieldName = StringEscapeUtils.unescapeJava(fieldJson.getString("original_name"));
            if(fieldName.equalsIgnoreCase("中国")){
                continue;
            }
//            if(fieldNames.contains(fieldName)) {// 已经翻译过的过滤掉
//                continue;
//            }
            ModelFieldConfDO fieldConf = new ModelFieldConfDO();

            // 转换 Unicode 字符串为普通文本
            fieldConf.setFieldName(fieldName);
            // 设置属性
            String fieldCode = fieldJson.getString("english_name");// english_name -> field_codee
            String fieldType = fieldJson.getString("field_type");
            if (dbType.toLowerCase().equals(DBTypeEnum.PG.getValue())){
                // 纠正AI翻译的字段类型
                if (fieldType.equalsIgnoreCase("INTEGER")){
                    fieldType = "INT4";
                }
                if (fieldType.equalsIgnoreCase("DECIMAL")){
                    fieldType = "NUMERIC";
                }
                if (fieldType.equalsIgnoreCase("DOUBLE PRECISION")){
                    fieldType = "NUMERIC";
                }
            }
            Integer leng = fieldJson.getInteger("field_length");
            Integer precision = Integer.valueOf(fieldJson.getOrDefault("field_precision", "0").toString());
            // 如果当前中文名保存过，就取保存过的。
            ModelFieldConfDO existConf = fieldConfMap.get(fieldName);
            if (existConf != null) {
                // 更新关联字段的内容
                fieldCode = existConf.getFieldCode();
                fieldType = ObjUtilX.isNotEmpty(existConf.getFieldType())?existConf.getFieldType():fieldType;
                leng = existConf.getLeng();
                precision = existConf.getFieldPrecision();
            }

            String dataType = "";
            if (fieldType.equalsIgnoreCase("DECIMAL")
                    || fieldType.contains("NUMERIC")
                    || fieldType.contains("INT")
                    || fieldType.equalsIgnoreCase("FLOAT")
                    || fieldType.contains("DOUBLE")) {
                dataType = DataTypeEnum.NUMBER.getValue();
            } else if (fieldType.equalsIgnoreCase("VARCHAR")) {
                dataType = DataTypeEnum.STRING.getValue();
            } else {
                dataType = DataTypeEnum.OBJECT.getValue();
            }

            fieldConf.setDataType(dataType);   // field_type
            fieldConf.setFieldType(fieldType);   // field_type
            fieldConf.setFieldTypeName(fieldType);   // field_type
            if (leng == null || leng == 0) {
                switch (fieldType.toUpperCase()) { // 使用 toUpperCase() 进行不区分大小写比较
                    case "VARCHAR" -> leng = 50;
                    case "CHAR" -> leng = 10;
                    case "FLOAT" -> {
                        leng = 10;
                        precision = 2;
                    }
                    case "DOUBLE" -> {
                        leng = 12;
                        precision = 2;
                    }
                    case "NUMERIC", "DECIMAL" -> {
                        leng = 18;
                        precision = 2;
                    }
                    default -> leng = 10; // 默认值
                }
            }
            fieldConf.setLeng(leng);     // field_length -> leng
            fieldConf.setFieldLength(leng);
            fieldConf.setFieldPrecision(precision); // field_precision // original_name
            fieldConf.setItemName(fieldName);
            //String englishName = fieldJson.getString("english_name");

            // 已经存在的字段，就取保存过的。
            //String oldName = reqMap.get(fieldName);
            //fieldCode = ObjUtilX.isEmpty(oldName)?fieldCode:oldName;
            fieldConf.setFieldCode(fieldCode);
            fieldConf.setEnglishName(StringUtils.strBarToHump(fieldCode));

            // 添加到集合中
            modelFieldConfList.add(fieldConf);
        }
        return modelFieldConfList;
    }

    @Override
    @Transactional
    public Boolean modelFieldTransSave(ModelFieldTransReqVO reqVO) {
        if(ObjUtilX.isEmpty(reqVO.getProjectId())){
            throw new BizException("5001", "请选择一个项目");
        }
        List<String> fieldNames = new ArrayList<>();
        if(reqVO.getTextType() == 1){
            fieldNames = reqVO.getFields().stream()
                    .map(ModelFieldConfAditReqVO::getItemName) // 提取 itemName
                    .toList(); // 收集结果到 List
        }else{
            fieldNames = reqVO.getFields().stream()
                    .map(ModelFieldConfAditReqVO::getFieldName) // 提取 fieldName
                    .toList(); // 收集结果到 List
        }
        List<ModelFieldConfDO> confDOS = fieldConfMapper.selectList(LambdaQueryWrapperX.<ModelFieldConfDO>lambdaQueryX()
                .in(ModelFieldConfDO::getFieldName, fieldNames)
        );

        Map<String, Long> fieldNameMap = confDOS.stream()
                .collect(Collectors.toMap(ModelFieldConfDO::getItemName, ModelFieldConfDO::getId,
                        (existingValue, newValue) -> newValue
                ));

        List<ModelFieldConfDO> insertList = new ArrayList<>();//插入的集合
        List<ModelFieldConfDO> updateList = new ArrayList<>();//更新的集合
        for (ModelFieldConfAditReqVO aditReq : reqVO.getFields()) {
            if(ObjUtilX.isEmpty(aditReq.getItemName())){
                continue;
            }
            // 都为空就不保存配置
            if(ObjUtilX.isEmpty(aditReq.getEnglishName()) && ObjUtilX.isEmpty(aditReq.getFieldCode())){
                continue;
            }
            if(ObjUtilX.isEmpty(aditReq.getDataType())){
                //throw new BizException("5001", "请配置json字段类型");
                aditReq.setDataType(DataTypeEnum.STRING.getValue());
            }
            // 这里是翻译json配置时，没有fieldType
            if(ObjUtilX.isEmpty(aditReq.getFieldType())){
                if(aditReq.getDataType().equals(DataTypeEnum.NUMBER.getValue())){
                    aditReq.setFieldType("DECIMAL");
                    aditReq.setLeng(18);
                    aditReq.setFieldLength(18);
                    aditReq.setFieldPrecision(2);
                }else {//(aditReq.getDataType().equals("String"))
                    aditReq.setFieldType("VARCHAR");
                    aditReq.setLeng(50);
                    aditReq.setFieldLength(50);
                    aditReq.setFieldPrecision(0);
                }
            }

            ModelFieldConfDO temp = BeanUtilX.copy(aditReq, ModelFieldConfDO::new);
            temp.setFieldName(temp.getItemName());
            String fieldCode = temp.getFieldCode();
            String englishName = temp.getEnglishName();
            if(reqVO.getTextType() == 0) {
                if (ObjUtilX.isNotEmpty(temp.getFieldCode())) {
                    englishName = StringUtils.strBarToHump(fieldCode.toLowerCase());//转成驼峰
                    temp.setEnglishName(englishName);
                }else{
                    throw new BizException("5001", "请配置字段名:" + temp.getItemName());
                }
            }
            if(reqVO.getTextType() == 1) {
                if (ObjUtilX.isNotEmpty(temp.getEnglishName())) {
                    fieldCode = StringUtils.strHumpToBar(englishName);//转成下划线
                    temp.setFieldCode(fieldCode);
                }else{
                    throw new BizException("5001", "请配置英文名");
                }
            }

            temp.setLeng(temp.getFieldLength());
            // 检查 fieldName 是否存在于 fieldNameMap 中
            if (fieldNameMap.containsKey(temp.getItemName())) {
                // 如果存在，则添加到更新列表，并赋值 ID
                temp.setId(fieldNameMap.get(temp.getItemName())); // 设置 ID
                updateList.add(temp); // 添加到更新列表
            } else {
                // 如果不存在，则添加到插入列表
                insertList.add(temp); // 添加到插入列表
            }
        }
        fieldConfMapper.insertBatch(insertList);
        if(ObjUtilX.isNotEmpty(updateList)) {
            fieldConfMapper.updateBatch(updateList);
        }
        return true;
    }

    @Override
    public String fieldTranslate(ModelTableListifyReqVO reqVO, Map<String, Integer> translateCountMap) {
        Integer textType = reqVO.getTextType();
        String itemContent = reqVO.getItemContent();
        Long projectId = reqVO.getProjectId();
        if(ObjUtilX.isEmpty(projectId)){
            throw new BizException("5001", "项目ID不能为空！");
        }
        try {
            List<Map> contentMap = new ArrayList<>();
            translateCountMap.put("totalFiled", 0);
            translateCountMap.put("translateFiled", 0);
            translateCountMap.put("noTranslateFiled", 0);
            translateCountMap.put("textType", textType);
            JSONArray jasonArray = JSON.parseArray(itemContent);
            JSONObject jsonObject = jasonArray.getJSONObject(0);
            Object mindType = jsonObject.get("mindType");
            //Long projectId = (Long) jsonObject.get("projectId");
            //if (!StringUtils.isEmpty(itemId)) {
            //    projectId = taskMapper.queryTaskProjectId(Integer.valueOf(taskId));
            //}

            iteraJson(itemContent, contentMap, translateCountMap, mindType);
            String jsonString = JSON.toJSONString(contentMap);
            return jsonString;
        } catch (Exception e) {
            log.error("[字段翻译]字段翻译失败！用户ID[" + itemContent + "].", e);
            throw new BizException("000002", e.getMessage());
        }
    }

    @Override
    public List<CreateInterfaceFieldParams> matchField(List<CreateInterfaceFieldParams> list) {
        if(ObjUtilX.isEmpty(list)){
            return list;
        }
        List<String> itemNames = list.stream().map(CreateInterfaceFieldParams::getItemName).collect(Collectors.toList());
        //这里先不区分项目
        List<ModelFieldConfDO> fieldConfList = fieldConfMapper.selectList(LambdaQueryWrapperX.<ModelFieldConfDO>lambdaQueryX()
                .in(ModelFieldConfDO::getFieldName, itemNames)
        );
        if(ObjUtilX.isEmpty(fieldConfList)){
            return list;
        }

        for(CreateInterfaceFieldParams params: list){
            // 已翻译字段不匹配平台字段
            if(!StringUtils.isEmpty(params.getEnglishName())){
                continue;
            }

            for(ModelFieldConfDO fieldConf : fieldConfList){
                if(StringUtils.isEmpty(fieldConf.getEnglishName())){
                    continue;
                }
                if(params.getItemName().equals(fieldConf.getFieldName())){
                    //params.setEnglishName(StringUtils.strHumpToBar(fieldConf.getEnglishName()));
                    params.setEnglishName(fieldConf.getEnglishName());
                }
            }
        }
        return list;
    }

    @Override
    public void modelFieldConfDelBatch(ModelFieldConfDelBatchReqVO reqVO) {
        fieldConfMapper.delete(LambdaQueryWrapperX.<ModelFieldConfDO>lambdaQueryX()
                .in(ModelFieldConfDO::getId, reqVO.getIds()));
    }

    public boolean iteraJson(String str, List<Map> res, Map<String, Integer> translateCountMap, Object mindType) throws BizException {
        //因为json串中不一定有逗号，但是一定有：号，所以这里判断没有则已经value了
        if (str.toString().indexOf("\"itemName\"") == -1) {
            return true;
        }
        // todo 前端传当前表id，搞字段（jsonFields）和配置处理，放方法外层，不要递归里面
        //Map<String, ModelFieldConfDO> fieldConfMap = new HashMap<>();
        //List<ModelFieldConfDO> fieldConfList = fieldConfMapper.selectList(LambdaQueryWrapperX.<ModelFieldConfDO>lambdaQueryX()
        //        .eq(ModelFieldConfDO::getProjectId, projectId)
        //);
        //for (ModelFieldConfDO fieldConf : fieldConfList) {
        //    fieldConfMap.put(fieldConf.getFieldName(), fieldConf); // 根据实际的字段名进行映射
        //}
        JSONArray jasonArray = JSON.parseArray(str);
        for (int i = 0; i < jasonArray.size(); i++) {
            Map map = new LinkedHashMap<>();
            JSONObject jsonObject = jasonArray.getJSONObject(i);
            Object translated = 0;
            for (Map.Entry entry : jsonObject.entrySet()) {
                String key = entry.getKey().toString();
                Object value = entry.getValue() == null ? "" : entry.getValue();
                //System.out.println("key************:" + key);
                //System.out.println("value************" + value);

                List<Map> list = new ArrayList<>();
                if (iteraJson(value.toString(), list, translateCountMap, mindType)) {
                    if (!jsonObject.containsKey("mindType") && key.equals("itemName")) {
                        translateCountMap.put("totalFiled", translateCountMap.get("totalFiled") + 1);
                        if (translated != null && translated.toString().equals("1")) {
                            translateCountMap.put("translateFiled", translateCountMap.get("translateFiled") + 1);
                            map.put(key, value);
                        } else {
                            map.put(key, value);

                            String itemName = value.toString();
                            ModelFieldConfDO projectFieldDO = fieldConfMapper.selectOne(new QueryWrapper<ModelFieldConfDO>()
                                   // .eq("project_id", projectId)
                                    .eq("field_name", itemName)
                                    .orderByDesc("updated_dt")
                                    .last("limit 1"));
                            if(projectFieldDO == null){
                                continue;
                            }
                            String englishName = projectFieldDO.getEnglishName();
                            String fieldCode = projectFieldDO.getFieldCode();
                            map.put("fieldCode", fieldCode);
                            map.put("englishName", englishName);
                            map.put("defaultVal", projectFieldDO.getDefaultVal());
                            map.put("remark", projectFieldDO.getRemark());
                            if (!StringUtils.isEmpty(fieldCode)) {
                                map.put("fieldType", projectFieldDO.getFieldType());
                                map.put("fieldTypeName", projectFieldDO.getFieldTypeName());
                                translateCountMap.put("translateFiled", translateCountMap.get("translateFiled") + 1);
                                // 建模表格匹配
                                if (mindType != null && mindType.toString().equals("table")) {
                                    //数据库文档
                                    //map.put("fieldCode", fieldCode);
                                    //String en = StringUtils.strHumpToBar(fieldCode);
                                    //map.put("englishName", en);

                                    String dataType = jsonObject.get("dataType") != null ? jsonObject.get("dataType").toString() : "";
                                    String dataLength = jsonObject.get("dataLength") != null ? jsonObject.get("dataLength").toString() : "";

                                    if(StringUtils.isEmpty(dataType) ){
                                        map.put("dataType", projectFieldDO.getDataType());
                                    }
                                    if(StringUtils.isEmpty(dataLength)){
                                        Integer oldDataLength = projectFieldDO.getFieldLength();
                                        Integer fieldPrecision = projectFieldDO.getFieldPrecision();
                                        if(ObjUtilX.isNotEmpty(oldDataLength)){
                                            map.put("hasLength", 1);
                                            map.put("dataLength", oldDataLength);
                                            if(ObjUtilX.isNotEmpty(fieldPrecision)){
                                                map.put("hasPoint", 1);
                                                map.put("dataPoint", fieldPrecision);
                                            }

                                        }
                                    }
                                    //if(StringUtils.isEmpty(remark) ){
                                    //    map.put("remark", projectFieldDO.getFieldRemark());
                                    //    if(jsonObject.get("remark") != null && projectFieldDO.getFieldRemark() !=null){
                                    //        jsonObject.put("remark", projectFieldDO.getFieldRemark());
                                    //    }
                                    //}
                                    map.put("compositeName", itemName + StringUtils.strBarToHump(englishName));
                                } else {
                                    //接口文档
                                    if (key.equals("接口地址") || key.equals("请求地址") || key.equals("响应地址")) {
                                        continue;
                                    }
                                    //String fieldStyle = projectMapper.queryProjectInterfaceFieldStyle(String.valueOf(projectId), userId); textType
                                    //Integer fieldStyle = translateCountMap.get("textType");
                                    //if (fieldStyle == 1) {
                                    //    englishName = StringUtils.strBarToHump(englishName);
                                    //}

                                    String dataType = jsonObject.get("dataType") != null ? jsonObject.get("dataType").toString() : "";
                                    String dataLength = jsonObject.get("dataLength") != null ? jsonObject.get("dataLength").toString() : "";
                                    //String defaultValue = jsonObject.get("defaultValue") != null ? jsonObject.get("defaultValue").toString() : "";
                                    Object defaultValue = jsonObject.get("defaultValue");

                                    if(StringUtils.isEmpty(dataType)){
                                        if(StringUtils.isEmpty(projectFieldDO.getFieldType())){
                                            map.put("dataType", DataTypeEnum.STRING.getValue());
                                        }else{
                                            if("DECIMAL".equals(projectFieldDO.getFieldType())
                                                    || projectFieldDO.getFieldType().contains("NUMBER")
                                                    || projectFieldDO.getFieldType().contains("INT")
                                                    || projectFieldDO.getFieldType().contains("FLOAT")
                                                    || projectFieldDO.getFieldType().contains("DOUBLE")
                                            ){
                                                map.put("dataType", DataTypeEnum.NUMBER.getValue());
                                            }else{
                                                map.put("dataType", DataTypeEnum.STRING.getValue());
                                            }
                                        }
                                    }else{
                                        map.put("dataType", dataType);
                                    }
                                    if(StringUtils.isEmpty(dataLength) ){
                                        map.put("dataLength", projectFieldDO.getFieldLength());
                                    }
                                    if(ObjUtilX.isEmpty(defaultValue) ){
                                        map.put("defaultValue", projectFieldDO.getDefaultVal());
                                    }
                                    //if(StringUtils.isEmpty(remark) ){
                                    //    map.put("remark", projectFieldDO.getFieldRemark());
                                    //    if(jsonObject.get("remark") != null && projectFieldDO.getFieldRemark() !=null){
                                    //        jsonObject.put("remark", projectFieldDO.getFieldRemark());
                                    //    }
                                    //}
                                    map.put("compositeName", itemName + StringUtils.strBarToHump(englishName));
                                }
                            } else {
                                translateCountMap.put("noTranslateFiled", translateCountMap.get("noTranslateFiled") + 1);
                                map.put(key, value);
                                map.put("translated", 0);
                            }
                        }
                    } else {
                        if(!map.containsKey(key)  || map.get(key) == null || map.get(key) == "") {
                            map.put(key, value);
                        }
                    }
                } else {
                    map.put(key, list);
                }
            }
            res.add(map);
        }
        return false;
    }

}
