package com.mongoso.mgs.module.model.dal.mysql.modelform;

import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.module.model.dal.db.modelform.ModelFormDO;
import org.apache.ibatis.annotations.Mapper;
import com.mongoso.mgs.module.model.controller.admin.modelform.vo.*;

/**
 * 单据建模主 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ModelFormMapper extends BaseMapperX<ModelFormDO> {

    default PageResult<ModelFormDO> selectPage(ModelFormPageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<ModelFormDO>lambdaQueryX()
                .likeIfPresent(ModelFormDO::getDataCode, reqVO.getDataCode())
                .eqIfPresent(ModelFormDO::getFormType, reqVO.getFormType())
                .likeIfPresent(ModelFormDO::getModelBizCode, reqVO.getModelBizCode())
                .eqIfPresent(ModelFormDO::getModelBizType, reqVO.getModelBizType())
                .likeIfPresent(ModelFormDO::getFormName, reqVO.getFormName())
                .eqIfPresent(ModelFormDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(ModelFormDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .orderByDesc(ModelFormDO::getDataId));
    }




    default List<ModelFormDO> selectList(ModelFormQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<ModelFormDO>lambdaQueryX()
                .eqIfPresent(ModelFormDO::getDataId, reqVO.getDataId())
                .likeIfPresent(ModelFormDO::getDataCode, reqVO.getDataCode())
                .eqIfPresent(ModelFormDO::getFormType, reqVO.getFormType())
                .likeIfPresent(ModelFormDO::getModelBizCode, reqVO.getModelBizCode())
                .eqIfPresent(ModelFormDO::getModelBizType, reqVO.getModelBizType())
                .likeIfPresent(ModelFormDO::getFormName, reqVO.getFormName())
                .eqIfPresent(ModelFormDO::getRemark, reqVO.getRemark())
                .eqIfPresent(ModelFormDO::getCreatedBy, reqVO.getCreatedBy())
                .betweenIfPresent(ModelFormDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .eqIfPresent(ModelFormDO::getUpdatedBy, reqVO.getUpdatedBy())
                .betweenIfPresent(ModelFormDO::getUpdatedDt, reqVO.getStartUpdatedDt(), reqVO.getEndUpdatedDt())
                .orderByDesc(ModelFormDO::getDataId));
    }

    /**
     * 查询最大的单据对象编码
     *
     * @return 最大的单据对象编码
     */
    String selectMaxDataCode();


}