package com.mongoso.mgs.module.model.controller.admin.sqllog.vo;

import lombok.*;

import jakarta.validation.constraints.*;
import java.io.Serializable;

  

/**
 * 脚本日志 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class SqlLogBaseVO implements Serializable {

    /** 主键 */
    private Long id;

    /** 项目id */
    @NotNull(message = "项目id不能为空")
    private Long projectId;
    @NotNull(message = "表id不能为空")
    private Long tableId;

    /** 表名 */
    private String tableCode;

    /** 脚本 */
    @NotEmpty(message = "脚本不能为空")
    private String sql;

    /** 操作类别 [建表，新增字段，修改字段，删除字段，修改表，删除表，清空数据] */
    private Integer opType;

    /** 执行状态 [失败，成功] */
    private Integer sucStatus;

    /** 异常信息 */
    private String errMgs;

}
