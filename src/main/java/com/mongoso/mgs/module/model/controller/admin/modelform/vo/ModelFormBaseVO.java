package com.mongoso.mgs.module.model.controller.admin.modelform.vo;

import lombok.*;

import jakarta.validation.constraints.*;
import java.io.Serializable;

  

/**
 * 单据建模主 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class ModelFormBaseVO implements Serializable {

    /** 主键id */
    private Long dataId;

    /** 单据对象编码 */
    private String dataCode;

    /** 单据对象类型 */
    private Short formType;

    /** 业务建模编码 */
    private String modelBizCode;

    /** 业务建模类型 */
    private Short modelBizType;

    /** 单据对象名称 */
    private String formName;

    /** 备注 */
    private String remark;

}
