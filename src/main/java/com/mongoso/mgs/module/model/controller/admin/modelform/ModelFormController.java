package com.mongoso.mgs.module.model.controller.admin.modelform;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import jakarta.validation.*;
import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import static com.mongoso.mgs.framework.common.domain.ResultX.success;

import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.model.controller.admin.modelform.vo.*;
import com.mongoso.mgs.module.model.dal.db.modelform.ModelFormDO;
import com.mongoso.mgs.module.model.service.modelform.ModelFormService;

/**
 * 单据建模主 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/model")
@Validated
public class ModelFormController {

    @Resource
    private ModelFormService formService;

    @OperateLog("单据建模主添加或编辑")
    @PostMapping("/modelFormAdit")
    @PreAuthorize("@ss.hasPermission('modelForm:adit')")
    public ResultX<Long> modelFormAdit(@Valid @RequestBody ModelFormAditReqVO reqVO) {
        return success(reqVO.getDataId() == null
                            ? formService.modelFormAdd(reqVO)
                            : formService.modelFormEdit(reqVO));
    }

    @OperateLog("单据建模主删除")
    @PostMapping("/modelFormDelete")
    @PreAuthorize("@ss.hasPermission('modelForm:delete')")
    public ResultX<Boolean> modelFormDelete(@Valid @RequestBody ModelFormPrimaryReqVO reqVO) {
        formService.modelFormDelete(reqVO.getDataId());
        return success(true);
    }

    @OperateLog("单据建模主详情")
    @PostMapping("/modelFormDetail")
    @PreAuthorize("@ss.hasPermission('modelForm:query')")
    public ResultX<ModelFormRespVO> modelFormDetail(@Valid @RequestBody ModelFormPrimaryReqVO reqVO) {
        return success(formService.modelFormDetail(reqVO.getDataId()));
    }

    @OperateLog("单据建模主列表")
    @PostMapping("/modelFormList")
    @PreAuthorize("@ss.hasPermission('modelForm:query')")
    public ResultX<List<ModelFormRespVO>> modelFormList(@Valid @RequestBody ModelFormQueryReqVO reqVO) {
        return success(formService.modelFormList(reqVO));
    }

    @OperateLog("单据建模主分页")
    @PostMapping("/modelFormPage")
    @PreAuthorize("@ss.hasPermission('modelForm:query')")
    public ResultX<PageResult<ModelFormRespVO>> modelFormPage(@Valid @RequestBody ModelFormPageReqVO reqVO) {
        return success(formService.modelFormPage(reqVO));
    }

}
