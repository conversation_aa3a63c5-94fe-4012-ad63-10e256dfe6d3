package com.mongoso.mgs.module.model.controller.admin.modelfunctionhistory;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import jakarta.validation.*;
import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import static com.mongoso.mgs.framework.common.domain.ResultX.success;

import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.model.controller.admin.modelfunctionhistory.vo.*;
import com.mongoso.mgs.module.model.dal.db.modelfunctionhistory.ModelFunctionHistoryDO;
import com.mongoso.mgs.module.model.service.modelfunctionhistory.ModelFunctionHistoryService;

/**
 * 自定义函数历史 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/model")
@Validated
public class ModelFunctionHistoryController {

    @Resource
    private ModelFunctionHistoryService functionHistoryService;

    @OperateLog("自定义函数历史添加或编辑")
    @PostMapping("/modelFunctionHistoryAdit")
    @PreAuthorize("@ss.hasPermission('modelFunctionHistory:adit')")
    public ResultX<Long> modelFunctionHistoryAdit(@Valid @RequestBody ModelFunctionHistoryAditReqVO reqVO) {
        return success(reqVO.getDataId() == null
                            ? functionHistoryService.modelFunctionHistoryAdd(reqVO)
                            : functionHistoryService.modelFunctionHistoryEdit(reqVO));
    }

    @OperateLog("自定义函数历史删除")
    @PostMapping("/modelFunctionHistoryDel")
    @PreAuthorize("@ss.hasPermission('modelFunctionHistory:del')")
    public ResultX<Boolean> modelFunctionHistoryDel(@Valid @RequestBody ModelFunctionHistoryPrimaryReqVO reqVO) {
        functionHistoryService.modelFunctionHistoryDel(reqVO.getDataId());
        return success(true);
    }

    @OperateLog("自定义函数历史详情")
    @PostMapping("/modelFunctionHistoryDetail")
    @PreAuthorize("@ss.hasPermission('modelFunctionHistory:query')")
    public ResultX<ModelFunctionHistoryRespVO> modelFunctionHistoryDetail(@Valid @RequestBody ModelFunctionHistoryPrimaryReqVO reqVO) {
        ModelFunctionHistoryDO oldDO = functionHistoryService.modelFunctionHistoryDetail(reqVO.getDataId());
        return success(BeanUtilX.copy(oldDO, ModelFunctionHistoryRespVO::new));
    }

    @OperateLog("自定义函数历史列表")
    @PostMapping("/modelFunctionHistoryList")
    @PreAuthorize("@ss.hasPermission('modelFunctionHistory:query')")
    public ResultX<List<ModelFunctionHistoryRespVO>> modelFunctionHistoryList(@Valid @RequestBody ModelFunctionHistoryQueryReqVO reqVO) {
        List<ModelFunctionHistoryDO> list = functionHistoryService.modelFunctionHistoryList(reqVO);
        return success(BeanUtilX.copyList(list, ModelFunctionHistoryRespVO::new));
    }

    @OperateLog("自定义函数历史分页")
    @PostMapping("/modelFunctionHistoryPage")
    @PreAuthorize("@ss.hasPermission('modelFunctionHistory:query')")
    public ResultX<PageResult<ModelFunctionHistoryRespVO>> modelFunctionHistoryPage(@Valid @RequestBody ModelFunctionHistoryPageReqVO reqVO) {
        PageResult<ModelFunctionHistoryDO> pageResult = functionHistoryService.modelFunctionHistoryPage(reqVO);
        return success(BeanUtilX.copyPage(pageResult, ModelFunctionHistoryRespVO::new));
    }

}
