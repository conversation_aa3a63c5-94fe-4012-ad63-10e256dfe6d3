package com.mongoso.mgs.module.model.controller.admin.item.vo;

import com.mongoso.mgs.framework.common.domain.PageParam;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 导出API文档
 *
 * @Auther: daijinbiao
 * @Date: 2025-4-2 10:50:50
 */
@Data
public class QueryApiParams extends PageParam {

    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	/** 项目Id */
	@NotNull(message = "项目Id不能为空")
    private Long projectId;

	/** 接口文档名称 */
	//@NotEmpty(message = "接口文档名称不能为空")
	private String apiName;

	HttpServletResponse response;
}
