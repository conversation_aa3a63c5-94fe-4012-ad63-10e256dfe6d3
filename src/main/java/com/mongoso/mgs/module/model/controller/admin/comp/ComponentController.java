package com.mongoso.mgs.module.model.controller.admin.comp;

import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.model.controller.admin.comp.vo.ComponentBaseVO;
import com.mongoso.mgs.module.model.dal.db.comp.ComponentDO;
import com.mongoso.mgs.module.model.service.comp.ComponentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import static com.mongoso.mgs.framework.common.domain.ResultX.success;

/**
 * 前端组件 Controller
 *
 * <AUTHOR>
 */

@RestController
@RequestMapping("/component")
@Validated
public class ComponentController {

    @Autowired
    private ComponentService componentService;

    @RequestMapping(value = "/saveProjectComp", method = RequestMethod.POST)
    @OperateLog(value="保存项目组件")
    public ResultX<Boolean> saveProjectComp(@RequestBody ComponentBaseVO projectComp) {
        return success(componentService.saveProjectComp(projectComp));
    }

    /**
     * 查询项目组件
     * @return
     */
    @RequestMapping(value = "/queryProjectComp", method = RequestMethod.POST)
    @OperateLog(value="查询项目组件")
    public ResultX<ComponentDO> queryProjectComp() {
        return success(componentService.queryProjectComp());
    }
}
