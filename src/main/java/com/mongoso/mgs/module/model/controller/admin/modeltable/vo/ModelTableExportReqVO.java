package com.mongoso.mgs.module.model.controller.admin.modeltable.vo;

import lombok.Data;
import lombok.ToString;

import jakarta.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.List;

/**
 * 图形建模主 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
public class ModelTableExportReqVO implements Serializable {

    HttpServletResponse response;
    /** 模型表id集合 */
    private List<Long> tableIds;

    private String fileName;
}
