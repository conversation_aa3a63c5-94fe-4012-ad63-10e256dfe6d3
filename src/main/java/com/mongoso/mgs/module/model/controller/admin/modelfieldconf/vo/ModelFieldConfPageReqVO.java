package com.mongoso.mgs.module.model.controller.admin.modelfieldconf.vo;

import com.alibaba.fastjson.JSONObject;
import com.mongoso.mgs.framework.common.domain.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * 图形建模字段翻译配置 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ModelFieldConfPageReqVO extends PageParam {
    /** 模型字段表中文名 */
    private String fieldName;
    /** 模型字段表配置 */
    private String fieldConf;
    /** 字段类型 */
    private Integer textType;

    /** 模型字段表实名 */
    private String fieldCode;

    /** 字段类型 */
    private String fieldType;

    /** 长度 */
    private Integer leng;

    /** 精度 */
    private Integer fieldPrecision;

    /** 是否必填 */
    private Integer isNullable;

    /** 是否为主键 */
    private Integer isPrimaryKey;

    /** 默认值 */
    private String defaultVal;

    /** 排序 */
    private Integer sort;

    /** 属性类型，0：系统，1：用户 */
    private Integer propType;

    /** 备注描述 */
    private String remark;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startCreatedDt;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endCreatedDt;

    /** 字段类型名称 */
    private String fieldTypeName;

    /** json字段配置 */
    private List<JSONObject> jsonFields;

    /** 节点名 */
    private String itemName;

    /** 英文名 */
    private String englishName;

    /** 字段长度 */
    private Integer fieldLength;
}
