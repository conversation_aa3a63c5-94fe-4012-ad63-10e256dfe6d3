package com.mongoso.mgs.module.model.dal.db.item;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

/**
 * 文件夹表
 * 表的实体类，参数和表字段一一对应
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-03-22 11:52:28
 */
@Data
@TableName("lowcode.sys_item")
public class ItemDO extends Model<ItemDO> {

	/**
	 * 主键
	 */
	@TableId(value = "item_id", type = IdType.ASSIGN_ID)
	private Long itemId;
	/**
	 * 文件夹名称
	 */
	private String itemName;
	/**
	 * 父文件夹Id
	 */
	private Long parentItemId;
	/**
	 * 项目ID
	 */
	private Long projectId;
	/**
	 * 文件夹（0文件夹 1项目文件夹 2项目内文件夹 3文件集 5:思维导图 6:数据导图）
	 */
	private Integer itemType;
	/**
	 * 文件排序号
	 */
	private Integer itemSeq;
	/**
	 * 用户Id
	 */
	private Long userId;
	/**
	 * 有效性（0无效 1有效 2回收站）
	 */
	private Integer active;
	/**
	 * 是否默认（0否 1是）
	 */
	private Integer isDefault;
	/**
	 * 创建人
	 */
	private String creator;
	/**
	 * 创建时间
	 */
	private Date createdDt;
	/**
	 * 操作人
	 */
	private String updator;
	/**
	 * 更新时间
	 */
	private Date updatedDt;

	/**
	 * 任务集类型 ["工作",  "问题", "信息架构图导图", "数据库导图", "接口导图"]
	 */
	private Integer taskSetType;
	/**
	 * 应用类型 0-MAX 1-测试任务 2-用例库
	 */
	private Integer queryType;

	/** 大类别 0:架构，1：接口 */
	private Integer category;
	private String content;
	private String description;
	private String apiUrl;

}
