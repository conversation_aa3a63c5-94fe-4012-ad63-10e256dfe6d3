package com.mongoso.mgs.module.model.controller.admin.modelreport.vo;

import com.mongoso.mgs.framework.common.domain.BaseTree;
import com.mongoso.mgs.module.model.controller.admin.modelquery.vo.ModelQueryRespVO;
import com.mongoso.mgs.module.model.dal.db.modelreportconfig.ModelReportConfigDO;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 自定义报 RespVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ModelReportRespVO extends BaseTree {

    List<ModelReportRespVO> children;
    List<ModelReportConfigDO> paramList;
    List<ModelReportConfigDO> resultList;

    /** 报表id  */
    private Long reportId;

    /** 报表名称 */
    @NotEmpty(message = "报表名称不能为空")
    private String reportName;

    /** 报表编号 */
//    @NotEmpty(message = "报表编号不能为空")
    private String reportCode;

    /** 查询id   */
    @NotNull(message = "查询id不能为空")
    private Long queryId;

    /** 备注   */
    private String remark;

    /** 版本号 */
//    @NotNull(message = "版本号不能为空")
    private Integer versionNo;

    /** 是否发布 */
//    @NotNull(message = "是否发布不能为空")
    private Integer isPublish;

    /** 类型 */
    private Integer dirType = 1;
    /** 父节点 */
    private Long parentId;
    /** 创建人   */
    private String createdBy;

    /** 创建时间   */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createdDt;

    /** 更新人   */
    private String updatedBy;

    /** 更新时间   */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime updatedDt;

}
