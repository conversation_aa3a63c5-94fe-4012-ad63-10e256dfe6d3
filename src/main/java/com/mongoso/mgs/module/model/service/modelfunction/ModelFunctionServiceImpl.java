package com.mongoso.mgs.module.model.service.modelfunction;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mongoso.mgs.common.util.HttpRequestUtil;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.common.util.ObjUtilX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.framework.mybatis.core.util.IDUtilX;
import com.mongoso.mgs.module.model.controller.admin.modelfunction.vo.*;
import com.mongoso.mgs.module.model.dal.db.modelfunction.ModelFunctionDO;
import com.mongoso.mgs.module.model.dal.db.modelfunctionhistory.ModelFunctionHistoryDO;
import com.mongoso.mgs.module.model.dal.mysql.modelfunction.ModelFunctionMapper;
import com.mongoso.mgs.module.model.dal.mysql.modelfunctionhistory.ModelFunctionHistoryMapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;
// import static com.mongoso.mgs.module.model.enums.ErrorCodeConstants.*;


/**
 * 自定义函数 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ModelFunctionServiceImpl implements ModelFunctionService {

    @Resource
    private ModelFunctionMapper functionMapper;
    @Resource
    private ModelFunctionHistoryMapper functionHistoryMapper;


    @Value("${remote.python.executeurl}")
    String pythonExe;

//    @Resource
//    private ModelFunctionVersionMapper functionVersionMapper;

    @Override
//    @Transactional(rollbackFor = Exception.class)
    public Long modelFunctionAdd(ModelFunctionAditReqVO reqVO) {
        // 插入
        ModelFunctionDO function = BeanUtilX.copy(reqVO, ModelFunctionDO::new);
        Long funId = IDUtilX.getId();
        function.setFunId(funId);
        functionMapper.insert(function);
        // 函数添加，上面是目录添加
        if(reqVO.getDirType() == 1){
//            if(ObjUtilX.isEmpty(reqVO.getFunBody())){
//                throw new BizException("5001", "函数主体不能为空");
//            }
            if(ObjUtilX.isEmpty(reqVO.getRunEnv())){
                throw new BizException("5001", "运行环境不能为空");
            }
        }
        // 返回
        return function.getFunId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long modelFunctionEdit(ModelFunctionAditReqVO reqVO) {
        // 校验存在
        ModelFunctionDO functionDO = this.modelFunctionValidateExists(reqVO.getFunId());
        if(functionDO.getIsLock() == 1){
            throw new BizException("5001", "当前函数已被锁定，不可编辑");
        }
        if(functionDO.getPropType() == 0){
            throw new BizException("5001", "系统函数，不可编辑");
        }
        if(reqVO.getDirType() == 1) {
            if (ObjUtilX.isEmpty(reqVO.getFunBody())) {
                throw new BizException("5001", "函数主体不能为空");
            }
            if (ObjUtilX.isEmpty(reqVO.getRunEnv())) {
                throw new BizException("5001", "运行环境不能为空");
            }
        }
        // 更新
        ModelFunctionDO function = BeanUtilX.copy(reqVO, ModelFunctionDO::new);
        if(reqVO.getDirType() == 1) {
            // 查询版本号+1
            Integer version = functionHistoryMapper.selectObjs(new LambdaQueryWrapper<ModelFunctionHistoryDO>()
                            .select(ModelFunctionHistoryDO::getVersionNo)
                            .eq(ModelFunctionHistoryDO::getFunId, functionDO.getFunId())
                            .orderByDesc(ModelFunctionHistoryDO::getVersionNo) // 选择降序排列
                            .last("LIMIT 1")) // 只取第一条记录，即最大值
                    .stream()
                    .map(obj -> (Integer) obj)
                    .findFirst()
                    .orElse(0);

            version = version + 1;//发布操作，保存时+1
            if(reqVO.getIsPublish() == 1) {
                // 插入历史版本，这个表可以不用了
//                ModelFunctionVersionDO versionDO = new ModelFunctionVersionDO();
//                versionDO.setFunId(function.getFunId());
//                versionDO.setVersionNo(version);
//                functionVersionMapper.insert(versionDO);
//
//                // 只保留10条记录
//                functionVersionMapper.delete(LambdaQueryWrapperX.<ModelFunctionVersionDO>lambdaQueryX()
//                        .eq(ModelFunctionVersionDO::getFunId, functionDO.getFunId())
//                        .le(ModelFunctionVersionDO::getVersionNo, version - 10)
//                );

                // 搞两张表存，这里历史版本也可以存在主表，用一个当前启动标识处理
                ModelFunctionHistoryDO history = BeanUtilX.copy(function, ModelFunctionHistoryDO::new);
                history.setVersionNo(version);
                functionHistoryMapper.insert(history);

                functionHistoryMapper.delete(LambdaQueryWrapperX.<ModelFunctionHistoryDO>lambdaQueryX()
                        .eq(ModelFunctionHistoryDO::getFunId, functionDO.getFunId())
                        .le(ModelFunctionHistoryDO::getVersionNo, version - 11)
                );
            }
            function.setVersionNo(version);

        }else{
            function.setVersionNo(0);
        }
        functionMapper.updateById(function);
        // 返回
        return function.getFunId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modelFunctionDel(Long funId) {
        // 校验存在
        ModelFunctionDO functionDO = this.modelFunctionValidateExists(funId);
        if(functionDO.getIsLock() == 1){
            throw new BizException("5001", "当前函数已被锁定，不可删除");
        }
        // 如果是目录，判断下面有没有子节点
        if(functionDO.getDirType() == 0){
            Long count = functionMapper.selectCount(LambdaQueryWrapperX.<ModelFunctionDO>lambdaQueryX().eq(ModelFunctionDO::getParentId, funId));
            if(count > 0){
                throw new BizException("5001", "当前目录下存在数据，不可删除");
            }
        }
        // 删除
        functionMapper.deleteById(funId);

        // 删除版本
        functionHistoryMapper.delete(LambdaQueryWrapperX.<ModelFunctionHistoryDO>lambdaQueryX().eq(ModelFunctionHistoryDO::getFunId, funId));
    }

    private ModelFunctionDO modelFunctionValidateExists(Long funId) {
        ModelFunctionDO function = functionMapper.selectById(funId);
        if (function == null) {
            // throw exception(FUNCTION_NOT_EXISTS);
            throw new BizException("5001", "自定义函数不存在");
        }
        return function;
    }

    private ModelFunctionDO modelFunctionValidateExists(String code) {
        ModelFunctionDO function = functionMapper.selectOne(LambdaQueryWrapperX.<ModelFunctionDO>lambdaQueryX()
                .eqIfPresent(ModelFunctionDO::getFunCode, code).last("limit 1"));
        if (function == null) {
            // throw exception(FUNCTION_NOT_EXISTS);
            throw new BizException("5001", "自定义函数不存在");
        }
        return function;
    }

    @Override
    public ModelFunctionDO modelFunctionDetail(Long funId) {
        return functionMapper.selectById(funId);
    }

    @Override
    public List<ModelFunctionDO> modelFunctionList(ModelFunctionQueryReqVO reqVO) {
        return functionMapper.selectList(reqVO);
    }

    @Override
    public PageResult<ModelFunctionDO> modelFunctionPage(ModelFunctionPageReqVO reqVO) {
        return functionMapper.selectPage(reqVO);
    }

    @Override
    public List<ModelFunctionRespVO> modelFunctionTree(ModelFunctionQueryReqVO reqVO) {
//        ModelFunctionQueryReqVO reqVO = new ModelFunctionQueryReqVO();
        //todo 添加条件
        List<ModelFunctionDO> list = functionMapper.selectList(reqVO);
        // 正序
//        Collections.sort(list);

        List<ModelFunctionRespVO> collect = list.stream().map(item -> {
            ModelFunctionRespVO convert = BeanUtilX.copy(item, ModelFunctionRespVO::new);
            convert.setItemId(convert.getFunId().toString());
            convert.setItemName(convert.getFunName());
            convert.setItemCode(convert.getFunCode());
            convert.setParentItemId(convert.getParentId().toString());
//            convert.setPath(null);
            return convert;
        }).collect(Collectors.toList());

        List<ModelFunctionRespVO> authMenuResps = this.listToTree(collect);

        return authMenuResps;
    }

    @Override
    public Integer modelFunctionDrag(ModelFunctionDragReqVO reqVO) {
        // 校验存在
        ModelFunctionDO exists = this.modelFunctionValidateExists(reqVO.getFunId());
        // 更新
        // 校验父节点存在
        ModelFunctionDO parent = functionMapper.selectById(reqVO.getParentId());
        if (parent == null) {
            throw new BizException("5001","父节点不存在");
        }
        if (parent.getDirType() != 0) {
            throw new BizException("5001","父节点必须是目录");
        }
        exists.setParentId(reqVO.getParentId());
        return functionMapper.updateById(exists);
    }

    @Override
    public void modelFunctionLock(ModelFunctionLockReqVO reqVO) {
        // 校验存在
        ModelFunctionDO exists = this.modelFunctionValidateExists(reqVO.getFunId());
        exists.setIsLock(reqVO.getIsLock());
        functionMapper.updateById(exists);
    }

    @Override
    public JSONObject modelFunctionExecute(ModelFunctionPrimaryReqVO reqVO) {
//        Long scriptId = reqVO.getFunId();
        String scriptContent = reqVO.getFunBody();
        // 如果前端没有传脚本，就找发布的最新的脚本执行，
        if(ObjUtilX.isEmpty(scriptContent)) {
            // 校验存在
            ModelFunctionDO exists;
            if(ObjUtilX.isNotEmpty(reqVO.getFunId())) {
                exists = this.modelFunctionValidateExists(reqVO.getFunId());
                // 查询最新发布生效的版本
                ModelFunctionHistoryDO latestVersion = functionHistoryMapper.findLatestVersionByFunId(exists.getFunId());
                if (null == latestVersion) {
                    throw new BizException("5001", "当前没有发布生效的版本，请先发布函数");
                }
                scriptContent = latestVersion.getFunBody();
            }else if(ObjUtilX.isNotEmpty(reqVO.getFunCode())) {
                exists = this.modelFunctionValidateExists(reqVO.getFunCode());
                // 查询最新发布生效的版本
                ModelFunctionHistoryDO latestVersion = functionHistoryMapper.findLatestVersionByFunCode(exists.getFunCode());
                if (null == latestVersion) {
                    throw new BizException("5001", "当前没有发布生效的版本，请先发布函数");
                }
                scriptContent = latestVersion.getFunBody();
            }else {
                throw new BizException("5001","请传入执行代码或选择一个函数执行");
            }
            if (exists.getDirType() != 1) {
                throw new BizException("5001","请选择函数节点执行");
            }
        }else{
            ModelFunctionDO exists;
            if(ObjUtilX.isNotEmpty(reqVO.getFunId())) {
                exists = this.modelFunctionValidateExists(reqVO.getFunId());
                // 保存代码
                exists.setFunBody(scriptContent);
                ModelFunctionAditReqVO reqSave = BeanUtilX.copy(exists, ModelFunctionAditReqVO::new);
                reqSave.setFunBody(scriptContent);
                modelFunctionEdit(reqSave);
            }else if(ObjUtilX.isNotEmpty(reqVO.getFunCode())) {
                exists = this.modelFunctionValidateExists(reqVO.getFunCode());
                // 保存代码
                exists.setFunBody(scriptContent);
                ModelFunctionAditReqVO reqSave = BeanUtilX.copy(exists, ModelFunctionAditReqVO::new);
                reqSave.setFunBody(scriptContent);
                modelFunctionEdit(reqSave);
            }
        }

        // 请求参数
        JSONObject params = new JSONObject();
        String s = null;
        try {
            JSONObject pm = new JSONObject();
            params.put("code", scriptContent);
//            params.put("endpoint", exists.getFunCode());
            if(ObjUtilX.isNotEmpty(scriptContent)){
                pm = JSONObject.parseObject(reqVO.getParams());
            }
            params.put("params", pm);
            String ps = params.toJSONString();
            s = HttpRequestUtil.sendPost(pythonExe, ps, null, null, null);
//            s = HttpUtil.post(pythonExe, params.toJSONString());
            JSONObject exeRst = JSONObject.parseObject(s);
//            if("success".equals(exeRst.get("status"))){
//                return "执行成功：" + exeRst.get("output");
//            }
//            return "执行失败：" + exeRst.get("output");
            return exeRst;
        } catch (Exception e) {
            throw new BizException("5001","执行失败，结果："+s +"，信息：" +e.getMessage());
        }

//        try {
//            // 创建一个临时 Python 文件，用于本地执行
//            File tempScript = File.createTempFile("script", ".py");
//            Files.write(tempScript.toPath(), scriptContent.getBytes());
//
//            // 设置执行权限（Linux 环境下）
//            tempScript.setExecutable(true);
//
//            // 构建命令
//            ProcessBuilder processBuilder = new ProcessBuilder("python3", tempScript.getAbsolutePath());
//            processBuilder.redirectErrorStream(true);
//            Process process = processBuilder.start();
//
//            // 等待执行完成并读取输出
//            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
//            StringBuilder result = new StringBuilder();
//            String line;
//            while ((line = reader.readLine()) != null) {
//                result.append(line).append("\n");
//            }
//
//            process.waitFor();
////            return ResponseEntity.ok(ResultX.success(result.toString()));
////            result.setLength(result.length() - 2);//去掉最后一行的\n
//            return result.toString();
//        } catch (Exception e) {
////            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
////                    .body(ResultX.error("500", e.getMessage()));
//            e.printStackTrace();
//            throw new BizException("5001", "脚本执行出错："+e.getMessage());
//        }
    }

    public static List<ModelFunctionRespVO> listToTree(List<ModelFunctionRespVO> list) {
        //用递归找子。
        List<ModelFunctionRespVO> treeList = new CopyOnWriteArrayList<>();
        for (ModelFunctionRespVO tree : list) {
            if ("0".equals(tree.getParentItemId())) {
                treeList.add(findChildren(tree, list));
            }
        }
        return treeList;
    }

    //寻找子节点
    private static ModelFunctionRespVO findChildren(ModelFunctionRespVO tree, List<ModelFunctionRespVO> list) {
        for (ModelFunctionRespVO node : list) {
            if (node.getParentItemId().equals(tree.getFunId().toString())) {
                if (tree.getChildren() == null) {
                    tree.setChildren(new CopyOnWriteArrayList<>());
                }
                tree.getChildren().add(findChildren(node, list));
            }
        }
        return tree;
    }

}
