package com.mongoso.mgs.module.model.controller.admin.modelfunctionversion.vo;

import lombok.*;

import jakarta.validation.constraints.*;
import java.io.Serializable;

/**
 * 自定义报表版本 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class ModelFunctionVersionBaseVO implements Serializable {

    /** 主键ID   */
    private Long id;

    /** 函数id   */
    @NotNull(message = "函数id  不能为空")
    private Long funId;

    /** 版本号 */
    @NotNull(message = "版本号不能为空")
    private Integer versionNo;

}
