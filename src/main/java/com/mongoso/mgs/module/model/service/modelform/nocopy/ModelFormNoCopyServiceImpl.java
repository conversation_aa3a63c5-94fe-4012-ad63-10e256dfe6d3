package com.mongoso.mgs.module.model.service.modelform.nocopy;

import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.mongoso.mgs.module.model.controller.admin.modelform.nocopy.vo.*;
import com.mongoso.mgs.module.model.dal.db.modelform.nocopy.ModelFormNoCopyDO;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.module.model.dal.mysql.modelform.nocopy.ModelFormNoCopyMapper;

// import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exception;
import com.mongoso.mgs.framework.common.exception.BizException;
// import static com.mongoso.mgs.module.model.enums.ErrorCodeConstants.*;


/**
 * 单据不可复制字段 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ModelFormNoCopyServiceImpl implements ModelFormNoCopyService {

    @Resource
    private ModelFormNoCopyMapper formNoCopyMapper;

    @Override
    public Long modelFormNoCopyAdd(ModelFormNoCopyAditReqVO reqVO) {
        // 插入
        ModelFormNoCopyDO formNoCopy = BeanUtilX.copy(reqVO, ModelFormNoCopyDO::new);
        formNoCopyMapper.insert(formNoCopy);
        // 返回
        return formNoCopy.getDataId();
    }

    @Override
    public Long modelFormNoCopyEdit(ModelFormNoCopyAditReqVO reqVO) {
        // 校验存在
        this.modelFormNoCopyValidateExists(reqVO.getDataId());
        // 更新
        ModelFormNoCopyDO formNoCopy = BeanUtilX.copy(reqVO, ModelFormNoCopyDO::new);
        formNoCopyMapper.updateById(formNoCopy);
        // 返回
        return formNoCopy.getDataId();
    }

    @Override
    public void modelFormNoCopyDelete(Long dataId) {
        // 校验存在
        this.modelFormNoCopyValidateExists(dataId);
        // 删除
        formNoCopyMapper.deleteById(dataId);
    }

    private ModelFormNoCopyDO modelFormNoCopyValidateExists(Long dataId) {
        ModelFormNoCopyDO formNoCopy = formNoCopyMapper.selectById(dataId);
        if (formNoCopy == null) {
            // throw exception(FORM_NO_COPY_NOT_EXISTS);
            throw new BizException("5001", "单据不可复制字段不存在");
        }
        return formNoCopy;
    }

    @Override
    public ModelFormNoCopyRespVO modelFormNoCopyDetail(Long dataId) {
        ModelFormNoCopyDO data = formNoCopyMapper.selectById(dataId);
        return BeanUtilX.copy(data, ModelFormNoCopyRespVO::new);
    }

    @Override
    public List<ModelFormNoCopyRespVO> modelFormNoCopyList(ModelFormNoCopyQueryReqVO reqVO) {
        List<ModelFormNoCopyDO> data = formNoCopyMapper.selectList(reqVO);
        return BeanUtilX.copy(data, ModelFormNoCopyRespVO::new);
    }

    @Override
    public PageResult<ModelFormNoCopyRespVO> modelFormNoCopyPage(ModelFormNoCopyPageReqVO reqVO) {
        PageResult<ModelFormNoCopyDO> data = formNoCopyMapper.selectPage(reqVO);
        return BeanUtilX.copy(data, ModelFormNoCopyRespVO::new);
    }

    @Override
    public void modelFormNoCopyBatchAdd(List<ModelFormNoCopyDO> noCopyDOList) {
        if (noCopyDOList != null && !noCopyDOList.isEmpty()) {
            formNoCopyMapper.insertBatch(noCopyDOList);
        }
    }

    @Override
    public void modelFormNoCopyBatchDeleteByCode(String modelFormCode) {
        if (modelFormCode != null && !modelFormCode.trim().isEmpty()) {
            formNoCopyMapper.deleteByModelFormCode(modelFormCode);
        }
    }

}
