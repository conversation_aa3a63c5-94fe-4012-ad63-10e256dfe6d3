package com.mongoso.mgs.module.model.dal.mysql.modelreportversion;

import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.module.model.dal.db.modelreportversion.ModelReportVersionDO;
import org.apache.ibatis.annotations.Mapper;
import com.mongoso.mgs.module.model.controller.admin.modelreportversion.vo.*;

/**
 * 自定义报表版本 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ModelReportVersionMapper extends BaseMapperX<ModelReportVersionDO> {

    default PageResult<ModelReportVersionDO> selectPage(ModelReportVersionPageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<ModelReportVersionDO>lambdaQueryX()
                .eqIfPresent(ModelReportVersionDO::getVersionNo, reqVO.getVersionNo())
                .betweenIfPresent(ModelReportVersionDO::getCreatedDt, reqVO.getCreatedDt())
                .orderByDesc(ModelReportVersionDO::getCreatedDt));
    }

    default List<ModelReportVersionDO> selectList(ModelReportVersionQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<ModelReportVersionDO>lambdaQueryX()
                .eqIfPresent(ModelReportVersionDO::getVersionNo, reqVO.getVersionNo())
                .betweenIfPresent(ModelReportVersionDO::getCreatedDt, reqVO.getCreatedDt())
                    .orderByDesc(ModelReportVersionDO::getCreatedDt));
    }

}