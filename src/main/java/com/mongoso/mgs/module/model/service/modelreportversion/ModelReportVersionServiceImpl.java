package com.mongoso.mgs.module.model.service.modelreportversion;

import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.mongoso.mgs.module.model.controller.admin.modelreportversion.vo.*;
import com.mongoso.mgs.module.model.dal.db.modelreportversion.ModelReportVersionDO;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.module.model.dal.mysql.modelreportversion.ModelReportVersionMapper;

// import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exception;
import com.mongoso.mgs.framework.common.exception.BizException;
// import static com.mongoso.mgs.module.model.enums.ErrorCodeConstants.*;


/**
 * 自定义报表版本 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ModelReportVersionServiceImpl implements ModelReportVersionService {

    @Resource
    private ModelReportVersionMapper reportVersionMapper;

    @Override
    public Long modelReportVersionAdd(ModelReportVersionAditReqVO reqVO) {
        // 插入
        ModelReportVersionDO reportVersion = BeanUtilX.copy(reqVO, ModelReportVersionDO::new);
        reportVersionMapper.insert(reportVersion);
        // 返回
        return reportVersion.getReportId();
    }

    @Override
    public Long modelReportVersionEdit(ModelReportVersionAditReqVO reqVO) {
        // 校验存在
        this.modelReportVersionValidateExists(reqVO.getReportId());
        // 更新
        ModelReportVersionDO reportVersion = BeanUtilX.copy(reqVO, ModelReportVersionDO::new);
        reportVersionMapper.updateById(reportVersion);
        // 返回
        return reportVersion.getReportId();
    }

    @Override
    public void modelReportVersionDel(Long reportId) {
        // 校验存在
        this.modelReportVersionValidateExists(reportId);
        // 删除
        reportVersionMapper.deleteById(reportId);
    }

    private ModelReportVersionDO modelReportVersionValidateExists(Long reportId) {
        ModelReportVersionDO reportVersion = reportVersionMapper.selectById(reportId);
        if (reportVersion == null) {
            // throw exception(REPORT_VERSION_NOT_EXISTS);
            throw new BizException("5001", "自定义报表版本不存在");
        }
        return reportVersion;
    }

    @Override
    public ModelReportVersionDO modelReportVersionDetail(Long reportId) {
        return reportVersionMapper.selectById(reportId);
    }

    @Override
    public List<ModelReportVersionDO> modelReportVersionList(ModelReportVersionQueryReqVO reqVO) {
        return reportVersionMapper.selectList(reqVO);
    }

    @Override
    public PageResult<ModelReportVersionDO> modelReportVersionPage(ModelReportVersionPageReqVO reqVO) {
        return reportVersionMapper.selectPage(reqVO);
    }

}
