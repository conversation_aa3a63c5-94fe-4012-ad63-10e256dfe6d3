package com.mongoso.mgs.module.model.dal.mysql.modeltable;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.model.controller.admin.modeltable.vo.ModelTablePageReqVO;
import com.mongoso.mgs.module.model.controller.admin.modeltable.vo.ModelTableQueryReqVO;
import com.mongoso.mgs.module.model.dal.db.modeltable.ModelTableDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 图形建模主 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ModelTableMapper extends BaseMapperX<ModelTableDO> {

    default PageResult<ModelTableDO> selectPage(ModelTablePageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<ModelTableDO>lambdaQueryX()
                .eqIfPresent(ModelTableDO::getDataSourceConfigId, reqVO.getDataSourceConfigId())
                .eqIfPresent(ModelTableDO::getProjectId, reqVO.getProjectId())
                .eqIfPresent(ModelTableDO::getTableCode, reqVO.getTableCode())
                .eqIfPresent(ModelTableDO::getRemark, reqVO.getRemark())
                .eqIfPresent(ModelTableDO::getUpgradeFlag, reqVO.getUpgradeFlag())
                .eqIfPresent(ModelTableDO::getIsGen, reqVO.getIsGen())
                .eqIfPresent(ModelTableDO::getDirType, reqVO.getDirType())
                .eqIfPresent(ModelTableDO::getParentId, reqVO.getParentId())
                .likeIfPresent(ModelTableDO::getTableName, reqVO.getTableName())
                .betweenIfPresent(ModelTableDO::getCreatedDt, reqVO.getCreatedDt())
                .orderByDesc(ModelTableDO::getCreatedDt));
    }

    default List<ModelTableDO> selectList(ModelTableQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<ModelTableDO>lambdaQueryX()
                .eqIfPresent(ModelTableDO::getTableCode, reqVO.getTableCode())
                .eqIfPresent(ModelTableDO::getProjectId, reqVO.getProjectId())
                .eqIfPresent(ModelTableDO::getDataSourceConfigId, reqVO.getDataSourceConfigId())
                .eqIfPresent(ModelTableDO::getRemark, reqVO.getRemark())
                .eqIfPresent(ModelTableDO::getUpgradeFlag, reqVO.getUpgradeFlag())
                .eqIfPresent(ModelTableDO::getIsGen, reqVO.getIsGen())
                .eqIfPresent(ModelTableDO::getDirType, reqVO.getDirType())
                .eqIfPresent(ModelTableDO::getParentId, reqVO.getParentId())
                .likeIfPresent(ModelTableDO::getTableName, reqVO.getTableName())
                .betweenIfPresent(ModelTableDO::getCreatedDt, reqVO.getCreatedDt())
                    .orderByAsc(ModelTableDO::getSeq)
                    .orderByDesc(ModelTableDO::getCreatedDt)
        );
    }

    default long selectCountByName(String tableName, Long tableId, Long dsId, Long projectId){
        return selectCount(LambdaQueryWrapperX.<ModelTableDO>lambdaQueryX()
//                .eqIfPresent(ModelTableDO::getParentId, parentItemId)
                .eq(ModelTableDO::getProjectId, projectId)
                .eq(ModelTableDO::getDataSourceConfigId, dsId)
                .eq(ModelTableDO::getTableName, tableName)
                .eq(ModelTableDO::getDirType, 1)
                .neIfPresent(ModelTableDO::getTableId, tableId));
    }

    default long selectCountByCode(String tableCode, Long tableId, Long dsId, Long projectId){
        return selectCount(LambdaQueryWrapperX.<ModelTableDO>lambdaQueryX()
                .eq(ModelTableDO::getTableCode, tableCode)
                .eq(ModelTableDO::getProjectId, projectId)
                .eq(ModelTableDO::getDataSourceConfigId, dsId)
                .eq(ModelTableDO::getDirType, 1)
                .neIfPresent(ModelTableDO::getTableId, tableId));
    }

    default ModelTableDO getModelByCode(String tableCode){
        return selectOne(LambdaQueryWrapperX.<ModelTableDO>lambdaQueryX()
                .eq(ModelTableDO::getTableCode, tableCode)
                .last(" limit 1")
        );
    }

    default String getTableCodeById(Long tableId){
        return selectOne(LambdaQueryWrapperX.<ModelTableDO>lambdaQueryX().eq(ModelTableDO::getTableId, tableId)).getTableCode();
    }

    Integer updateItemSeqAddOne(@Param("parentId")Long parentId, @Param("seq")Integer seq);

    void batchUpdateItemSeq(@Param("items") List<ModelTableDO> siblings);
}