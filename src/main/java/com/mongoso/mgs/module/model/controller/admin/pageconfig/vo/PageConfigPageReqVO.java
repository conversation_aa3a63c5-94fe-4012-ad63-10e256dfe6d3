package com.mongoso.mgs.module.model.controller.admin.pageconfig.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.mongoso.mgs.framework.common.domain.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import jakarta.validation.constraints.NotNull;

/**
 * 页面配置 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PageConfigPageReqVO extends PageParam {

    /** 名称 */
    @JsonProperty(value = "itemName")
    private String name;

    /** 内容 */
    @JsonProperty(value = "itemContent")
    private String content;

    @NotNull(message = "项目id不能为空")
    @JsonProperty(value = "projectId")
    private Long projectId;

}
