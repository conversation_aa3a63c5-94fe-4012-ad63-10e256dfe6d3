package com.mongoso.mgs.module.model.service.modelfunctionversion;

import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.mongoso.mgs.module.model.controller.admin.modelfunctionversion.vo.*;
import com.mongoso.mgs.module.model.dal.db.modelfunctionversion.ModelFunctionVersionDO;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.module.model.dal.mysql.modelfunctionversion.ModelFunctionVersionMapper;

// import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exception;
import com.mongoso.mgs.framework.common.exception.BizException;
// import static com.mongoso.mgs.module.model.enums.ErrorCodeConstants.*;


/**
 * 自定义报表版本 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ModelFunctionVersionServiceImpl implements ModelFunctionVersionService {

    @Resource
    private ModelFunctionVersionMapper functionVersionMapper;

    @Override
    public Long modelFunctionVersionAdd(ModelFunctionVersionAditReqVO reqVO) {
        // 插入
        ModelFunctionVersionDO functionVersion = BeanUtilX.copy(reqVO, ModelFunctionVersionDO::new);
        functionVersionMapper.insert(functionVersion);
        // 返回
        return functionVersion.getFunId();
    }

    @Override
    public Long modelFunctionVersionEdit(ModelFunctionVersionAditReqVO reqVO) {
        // 校验存在
        this.modelFunctionVersionValidateExists(reqVO.getFunId());
        // 更新
        ModelFunctionVersionDO functionVersion = BeanUtilX.copy(reqVO, ModelFunctionVersionDO::new);
        functionVersionMapper.updateById(functionVersion);
        // 返回
        return functionVersion.getFunId();
    }

    @Override
    public void modelFunctionVersionDel(Long funId) {
        // 校验存在
        this.modelFunctionVersionValidateExists(funId);
        // 删除
        functionVersionMapper.deleteById(funId);
    }

    private ModelFunctionVersionDO modelFunctionVersionValidateExists(Long funId) {
        ModelFunctionVersionDO functionVersion = functionVersionMapper.selectById(funId);
        if (functionVersion == null) {
            // throw exception(FUNCTION_VERSION_NOT_EXISTS);
            throw new BizException("5001", "自定义报表版本不存在");
        }
        return functionVersion;
    }

    @Override
    public ModelFunctionVersionDO modelFunctionVersionDetail(Long funId) {
        return functionVersionMapper.selectById(funId);
    }

    @Override
    public List<ModelFunctionVersionDO> modelFunctionVersionList(ModelFunctionVersionQueryReqVO reqVO) {
        return functionVersionMapper.selectList(reqVO);
    }

    @Override
    public PageResult<ModelFunctionVersionDO> modelFunctionVersionPage(ModelFunctionVersionPageReqVO reqVO) {
        return functionVersionMapper.selectPage(reqVO);
    }

}
