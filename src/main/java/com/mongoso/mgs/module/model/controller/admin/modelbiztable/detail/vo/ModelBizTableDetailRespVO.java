package com.mongoso.mgs.module.model.controller.admin.modelbiztable.detail.vo;

import lombok.*;

import java.util.List;





/**
 * 子表明细 RespVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ModelBizTableDetailRespVO extends ModelBizTableDetailBaseVO {

    /** 模型表名称（从sys_model_table表关联查询得到） */
    private String tableName;

    /** 子集列表（根据父行路径组装的树结构） */
    private List<ModelBizTableDetailRespVO> children;

}
