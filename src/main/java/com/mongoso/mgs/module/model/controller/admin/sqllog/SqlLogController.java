package com.mongoso.mgs.module.model.controller.admin.sqllog;

import com.mongoso.mgs.module.model.controller.admin.sqllog.vo.*;
import com.mongoso.mgs.module.model.service.sqllog.SqlLogService;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import jakarta.validation.*;
import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;

import static com.mongoso.mgs.framework.common.domain.ResultX.success;

import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;

/**
 * 脚本日志 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/sql")
@Validated
public class SqlLogController {

    @Resource
    private SqlLogService logService;

    @OperateLog("脚本日志添加或编辑")
    @PostMapping("/sqlLogAdit")
    @PreAuthorize("@ss.hasPermission('sqlLog:adit')")
    public ResultX<Long> sqlLogAdit(@Valid @RequestBody SqlLogAditReqVO reqVO) {
        return success(reqVO.getId() == null
                            ? logService.sqlLogAdd(reqVO)
                            : logService.sqlLogEdit(reqVO));
    }

    @OperateLog("脚本日志删除")
    @PostMapping("/sqlLogDel")
    @PreAuthorize("@ss.hasPermission('sqlLog:delete')")
    public ResultX<Boolean> sqlLogDel(@Valid @RequestBody SqlLogPrimaryReqVO reqVO) {
        logService.sqlLogDel(reqVO.getId());
        return success(true);
    }

    @OperateLog("脚本日志详情")
    @PostMapping("/sqlLogDetail")
    @PreAuthorize("@ss.hasPermission('sqlLog:query')")
    public ResultX<SqlLogRespVO> sqlLogDetail(@Valid @RequestBody SqlLogPrimaryReqVO reqVO) {
        return success(logService.sqlLogDetail(reqVO.getId()));
    }

    @OperateLog("脚本日志列表")
    @PostMapping("/sqlLogList")
    @PreAuthorize("@ss.hasPermission('sqlLog:query')")
    public ResultX<List<SqlLogRespVO>> sqlLogList(@Valid @RequestBody SqlLogQueryReqVO reqVO) {
        return success(logService.sqlLogList(reqVO));
    }

    @OperateLog("脚本日志分页")
    @PostMapping("/sqlLogPage")
    @PreAuthorize("@ss.hasPermission('sqlLog:query')")
    public ResultX<PageResult<SqlLogRespVO>> sqlLogPage(@Valid @RequestBody SqlLogPageReqVO reqVO) {
        return success(logService.sqlLogPage(reqVO));
    }

}
