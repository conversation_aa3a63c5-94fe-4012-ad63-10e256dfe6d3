package com.mongoso.mgs.module.model.service.modelqueryresult;

import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.mongoso.mgs.module.model.controller.admin.modelqueryresult.vo.*;
import com.mongoso.mgs.module.model.dal.db.modelqueryresult.ModelQueryResultDO;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.module.model.dal.mysql.modelqueryresult.ModelQueryResultMapper;

// import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exception;
import com.mongoso.mgs.framework.common.exception.BizException;
// import static com.mongoso.mgs.module.model.enums.ErrorCodeConstants.*;


/**
 * 自定义查询结果 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ModelQueryResultServiceImpl implements ModelQueryResultService {

    @Resource
    private ModelQueryResultMapper queryResultMapper;

    @Override
    public Long modelQueryResultAdd(ModelQueryResultAditReqVO reqVO) {
        // 插入
        ModelQueryResultDO queryResult = BeanUtilX.copy(reqVO, ModelQueryResultDO::new);
        queryResultMapper.insert(queryResult);
        // 返回
        return queryResult.getId();
    }

    @Override
    public Long modelQueryResultEdit(ModelQueryResultAditReqVO reqVO) {
        // 校验存在
        this.modelQueryResultValidateExists(reqVO.getId());
        // 更新
        ModelQueryResultDO queryResult = BeanUtilX.copy(reqVO, ModelQueryResultDO::new);
        queryResultMapper.updateById(queryResult);
        // 返回
        return queryResult.getId();
    }

    @Override
    public void modelQueryResultDel(Long id) {
        // 校验存在
        this.modelQueryResultValidateExists(id);
        // 删除
        queryResultMapper.deleteById(id);
    }

    private ModelQueryResultDO modelQueryResultValidateExists(Long id) {
        ModelQueryResultDO queryResult = queryResultMapper.selectById(id);
        if (queryResult == null) {
            // throw exception(QUERY_RESULT_NOT_EXISTS);
            throw new BizException("5001", "自定义查询结果不存在");
        }
        return queryResult;
    }

    @Override
    public ModelQueryResultDO modelQueryResultDetail(Long id) {
        return queryResultMapper.selectById(id);
    }

    @Override
    public List<ModelQueryResultDO> modelQueryResultList(ModelQueryResultQueryReqVO reqVO) {
        return queryResultMapper.selectList(reqVO);
    }

    @Override
    public PageResult<ModelQueryResultDO> modelQueryResultPage(ModelQueryResultPageReqVO reqVO) {
        return queryResultMapper.selectPage(reqVO);
    }

}
