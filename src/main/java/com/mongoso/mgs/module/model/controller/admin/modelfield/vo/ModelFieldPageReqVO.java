package com.mongoso.mgs.module.model.controller.admin.modelfield.vo;

import lombok.*;

import com.mongoso.mgs.framework.common.domain.PageParam;







































import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 图形建模字段 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ModelFieldPageReqVO extends PageParam {

    /** 模型字段表id中文名 */
    private String fieldName;

    /** 模型字段表id实名 */
    private String fieldCode;

    /** 字段类型 */
    private String fieldType;

    /** 模型表id */
    private Long tableId;

    /** 是否为空 */
    private Integer isNullable;

    /** 是否为主键 */
    private Integer isPrimaryKey;

    /** 默认值 */
    private String defaultVal;

    /** 排序 */
    private Integer sort;

    /** 属性类型，0：系统，1：用户 */
    private Integer propType;

    /** 备注描述 */
    private String remark;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createdDt;

}
