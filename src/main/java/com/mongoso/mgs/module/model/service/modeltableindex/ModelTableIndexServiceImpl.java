package com.mongoso.mgs.module.model.service.modeltableindex;

import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.mongoso.mgs.module.model.controller.admin.modeltableindex.vo.*;
import com.mongoso.mgs.module.model.dal.db.modeltableindex.ModelTableIndexDO;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.module.model.dal.mysql.modeltableindex.ModelTableIndexMapper;

import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exception;
import static com.mongoso.mgs.module.model.enums.ErrorCodeConstants.*;

/**
 * 图形建模主表索引 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ModelTableIndexServiceImpl implements ModelTableIndexService {

    @Resource
    private ModelTableIndexMapper tableIndexMapper;

    @Override
    public Long modelTableIndexAdd(ModelTableIndexAditReqVO reqVO) {
        // 插入
        ModelTableIndexDO tableIndex = BeanUtilX.copy(reqVO, ModelTableIndexDO::new);
        tableIndexMapper.insert(tableIndex);
        // 返回
        return tableIndex.getId();
    }

    @Override
    public Long modelTableIndexEdit(ModelTableIndexAditReqVO reqVO) {
        // 校验存在
        this.modelTableIndexValidateExists(reqVO.getId());
        // 更新
        ModelTableIndexDO tableIndex = BeanUtilX.copy(reqVO, ModelTableIndexDO::new);
        tableIndexMapper.updateById(tableIndex);
        // 返回
        return tableIndex.getId();
    }

    @Override
    public void modelTableIndexDel(Long id) {
        // 校验存在
        this.modelTableIndexValidateExists(id);
        // 删除
        tableIndexMapper.deleteById(id);
    }

    private ModelTableIndexDO modelTableIndexValidateExists(Long id) {
        ModelTableIndexDO tableIndex = tableIndexMapper.selectById(id);
        if (tableIndex == null) {
            throw exception(TABLE_INDEX_NOT_EXISTS);
        }
        return tableIndex;
    }

    @Override
    public ModelTableIndexDO modelTableIndexDetail(Long id) {
        return tableIndexMapper.selectById(id);
    }

    @Override
    public List<ModelTableIndexDO> modelTableIndexList(ModelTableIndexQueryReqVO reqVO) {
        return tableIndexMapper.selectList(reqVO);
    }

    @Override
    public PageResult<ModelTableIndexDO> modelTableIndexPage(ModelTableIndexPageReqVO reqVO) {
        return tableIndexMapper.selectPage(reqVO);
    }

}
