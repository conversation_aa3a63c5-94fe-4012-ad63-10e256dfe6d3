package com.mongoso.mgs.module.model.enums;


import com.mongoso.mgs.framework.common.exception.ErrorCode;

/**
 * Infra 错误码枚举类
 *
 * infra 系统，使用 1-001-000-000 段
 */
public interface ErrorCodeConstants {
    // ========== 图形建模主 ==========
    ErrorCode TABLE_NOT_EXISTS = new ErrorCode("5001", "图形建模主表不存在");

    // ========== 图形建模字段 ==========
    ErrorCode FIELD_NOT_EXISTS = new ErrorCode("5001", "图形建模字段不存在");

    // ========== 图形建模字段 ==========
    ErrorCode FIELD_ALTER_NOT_EXISTS = new ErrorCode("5001", "图形建模字段不存在");

    // ========== 图形建模主表索引 ==========
    ErrorCode TABLE_INDEX_NOT_EXISTS = new ErrorCode("5001", "图形建模主表索引不存在");
}
