package com.mongoso.mgs.module.model.controller.admin.modelquery.vo;

import com.mongoso.mgs.module.model.controller.admin.modelqueryparam.vo.ModelQueryParamAditReqVO;
import com.mongoso.mgs.module.model.controller.admin.modelqueryresult.vo.ModelQueryResultAditReqVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

/**
 * 自定义查询 AditReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ModelQueryAditReqVO extends ModelQueryBaseVO {

    /** 参数 */
    List<ModelQueryParamAditReqVO> params = new ArrayList<ModelQueryParamAditReqVO>();
    /** 结果 */
    List<ModelQueryResultAditReqVO> results = new LinkedList<ModelQueryResultAditReqVO>();

}
