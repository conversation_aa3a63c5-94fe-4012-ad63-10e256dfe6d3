package com.mongoso.mgs.module.model.controller.admin.modelquery.vo;

import com.mongoso.mgs.module.model.controller.admin.modelqueryparam.vo.ModelQueryParamAditReqVO;
import lombok.Data;
import lombok.ToString;

import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 自定义查询 PrimaryReqVO
 *
 * <AUTHOR>
 */
@Data
public class ModelQueryPrimaryReqVO implements Serializable {

    @NotNull(message = "主键ID不能为空")
    private Long queryId;

    /** 查询语句 */
//    @NotEmpty(message = "SQL语句不能为空")
    private String queryStatment;

    //参数
    List<ModelQueryParamAditReqVO> params;
}
