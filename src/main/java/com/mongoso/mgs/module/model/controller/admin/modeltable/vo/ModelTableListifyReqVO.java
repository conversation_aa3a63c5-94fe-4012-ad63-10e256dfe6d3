package com.mongoso.mgs.module.model.controller.admin.modeltable.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 这个是listfy的JSON请求格式
 * daijinbiao
 * 2024-11-11
 */
@Data
public class ModelTableListifyReqVO implements Serializable {

    /** 模型表id */
    private Long tableId;
    private Long projectId;

    /** 文本类型 */
    private Integer textType;

//    @JSONField(name = "itemContent")
//    private List<ModelTableJsonReqVO> itemContent;
    /** 内容数据 */
    private String itemContent;
}
