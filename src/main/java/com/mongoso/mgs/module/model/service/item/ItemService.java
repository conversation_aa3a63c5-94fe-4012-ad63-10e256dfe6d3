package com.mongoso.mgs.module.model.service.item;

import com.mongoso.mgs.framework.file.core.domain.FileResp;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.model.controller.admin.item.vo.*;
import com.mongoso.mgs.module.model.controller.admin.item.vo.CreateItemParams;
import com.mongoso.mgs.module.model.dal.db.item.ItemDO;

import jakarta.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/6
 * @description
 */
public interface ItemService {

    ItemDO getOne(LambdaQueryWrapperX qw);

    /**
     * 新增文件信息
     * @param params
     * @return
     */
    Long insertItemInfo(CreateItemParams params);

    List<ItemTreeVo> queryItemTreeList(QueryItemParams params);

    void deleteItemInfo(Long itemId);

    void updateItemInfo(UpdateItemParams params);

    ItemDO itemDetail(Long itemId);

    Boolean editItemInfo(CreateItemParams params);

    Integer itemDrag(@Valid UpdateItemParams params);

    Integer dragItemInfo(DragItemParams params);

    void apiExport(@Valid ApiExportReqVO reqVO);

    void deleteItemInfoBatch(DeleteBatchItemParams params);

    FileResp exportApiDoc(QueryApiParams reqVO);

    String saveItems(CreateItemParamsBatch params);
}
