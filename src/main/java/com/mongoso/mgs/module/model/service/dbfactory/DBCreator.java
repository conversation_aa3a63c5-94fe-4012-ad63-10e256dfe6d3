package com.mongoso.mgs.module.model.service.dbfactory;

import cn.hutool.json.JSONArray;
import com.mongoso.mgs.common.util.TableStructure;
import com.mongoso.mgs.framework.common.util.ObjUtilX;
import com.mongoso.mgs.module.model.controller.admin.modelfield.vo.ModelFieldAditReqVO;
import com.mongoso.mgs.module.model.controller.admin.modeltableindex.vo.ModelTableIndexAditReqVO;
import com.mongoso.mgs.module.model.dal.db.modelfield.ModelFieldDO;
import com.mongoso.mgs.module.model.dal.db.modelfieldalter.ModelFieldAlterDO;
import com.mongoso.mgs.module.model.dal.db.modelquery.ModelQueryDO;
import com.mongoso.mgs.module.model.dal.db.modeltable.ModelTableDO;
import com.mongoso.mgs.module.model.dal.db.modeltableindex.ModelTableIndexDO;
import com.mongoso.mgs.module.model.dal.db.sqllog.SqlLogDO;
import org.jetbrains.annotations.NotNull;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据库工厂接口
 * 搞个接口用来扩展适配更多数据库
 * daijinbiao
 * 2024-10-12
 */
public interface DBCreator {

    /**
     * 执行建表
     *
     * @param tableDO
     * @param stmt
     * @throws SQLException
     */
    void createTable(ModelTableDO tableDO, Statement stmt) throws SQLException;

    /**
     * 生成建表SQL
     *
     * @param tableDO
     * @return
     */
    StringBuilder genCreateTableSQL(ModelTableDO tableDO);

    /**
     * 添加字段
     *
     * @param change
     * @param tableDO
     * @throws SQLException
     */
    String addField(ModelFieldDO change, ModelTableDO tableDO);

    /**
     * 修改字段
     *
     * @param change
     * @param oldField
     * @param tableDO
     */
    void modifyField(ModelFieldDO change, ModelFieldDO oldField, ModelTableDO tableDO);

    /**
     * 删除字段
     *
     * @param change
     * @param tableDO
     */
    void dropField(ModelFieldDO change, ModelTableDO tableDO);

    @Transactional(propagation = Propagation.NOT_SUPPORTED, rollbackFor = Exception.class)
    void insertLog(SqlLogDO sqlLog);

    /**
     * 删除索引
     *
     * @param table
     * @param idxName
     */
    void delIndex(ModelTableDO table, String idxName);

    /**
     * 修改索引名称
     *
     * @param table
     * @param oldIdxName
     * @param idxName
     */
    void renameIndex(ModelTableDO table, String oldIdxName, String idxName);

    /**
     * 修改索引信息
     *
     * @param table
     * @param oldIndex
     * @param newIndex
     */
    void modifyIndex(ModelTableDO table, ModelTableIndexAditReqVO oldIndex, ModelTableIndexAditReqVO newIndex);



    default boolean onlyNameChanged(ModelTableIndexAditReqVO oldIndex, ModelTableIndexAditReqVO newIndex) {
        return fieldListsAreEqual(oldIndex.getFields(), newIndex.getFields()) &&
                oldIndex.getIdxType().equals(newIndex.getIdxType()) &&
                oldIndex.getIdxWay().equals(newIndex.getIdxWay()) &&
                oldIndex.getRemark().equals(newIndex.getRemark()) &&
                !oldIndex.getIdxName().equals(newIndex.getIdxName());
    }

    // Helper method to compare two lists of ModelFieldAditReqVO
    default boolean fieldListsAreEqual(List<ModelFieldAditReqVO> oldFields, List<ModelFieldAditReqVO> newFields) {
        if (oldFields.size() != newFields.size()) {
            return false;
        }

        for (int i = 0; i < oldFields.size(); i++) {
            ModelFieldAditReqVO oldField = oldFields.get(i);
            ModelFieldAditReqVO newField = newFields.get(i);
            if (!fieldsAreEqual(oldField, newField)) {
                return false;
            }
        }

        return true;
    }

    // Helper method to compare two ModelFieldAditReqVO objects
    private boolean fieldsAreEqual(ModelFieldAditReqVO oldField, ModelFieldAditReqVO newField) {
        return oldField.getFieldCode().equals(newField.getFieldCode());
        // 如果 ModelFieldAditReqVO 有其他需要比较的字段属性，可以继续加在这里
    }

    /**
     * 判断默认值是否相等
     *
     * @param newDefaultVal
     * @param existingDefaultVal
     * @return
     */
    default boolean areDefaultValuesEqual(String newDefaultVal, String existingDefaultVal) {
        return (ObjUtilX.isNotEmpty(newDefaultVal) && ObjUtilX.isNotEmpty(existingDefaultVal) && newDefaultVal.equals(existingDefaultVal))
                || (ObjUtilX.isEmpty(newDefaultVal) && ObjUtilX.isEmpty(existingDefaultVal)); // 当两个都是空或空字符串时，也认为相等
    }

    /**
     * 添加索引
     *
     * @param indexAdds
     * @param exsitIdxMap
     * @param exsitFieldCodeMap
     * @param exists
     */
    void addIndex(List<ModelTableIndexAditReqVO> indexAdds, Map<String, ModelTableIndexDO> exsitIdxMap, Map<String, ModelFieldDO> exsitFieldCodeMap, ModelTableDO exists);

    void createTableDynamic(ModelTableDO tableDO) throws SQLException;



    // 将 ResultSet 转换为 Map
    default Map<String, Object> convertResultSetToMap(ResultSet rs) throws SQLException {
        Map<String, Object> map = new HashMap<>();
        ResultSetMetaData metaData = rs.getMetaData();
        int columnCount = metaData.getColumnCount();
        for (int i = 1; i <= columnCount; i++) {
            String columnName = metaData.getColumnName(i);
            Object value = rs.getObject(i);
            map.put(columnName, value);
        }
        return map;
    }

    /**
     * 拼接 字段（长度，精度）
     *
     * @param field
     * @param sql
     * @return
     */
    @NotNull
    default StringBuilder getFullCode(ModelFieldDO field, StringBuilder sql) {
        sql.append(field.getFieldCode());
        sql.append(" ");
        sql.append(field.getFieldType());
        String upperStr = field.getFieldType().toUpperCase();
        // 如果字段是 VARCHAR 且未指定长度，则使用默认长度 50
        if ("VARCHAR".equalsIgnoreCase(upperStr) && (ObjUtilX.isEmpty(field.getLeng()) || field.getLeng() <= 0)) {
            sql.append("(50)");
        } if ("CHAR".equalsIgnoreCase(upperStr) && (ObjUtilX.isEmpty(field.getLeng()) || field.getLeng() <= 0)) {
            sql.append("(10)");
        } else if (!upperStr.equals("DATE")
                && !upperStr.equals("TIMESTAMP")
                && !upperStr.equals("INT2")
                && !upperStr.equals("INT4")
                && !upperStr.equals("INT8")
                && !upperStr.equals("TEXT")
                && ObjUtilX.isNotEmpty(field.getLeng()) && field.getLeng() > 0) {
            sql.append("(").append(field.getLeng());
            if (ObjUtilX.isNotEmpty(field.getFieldPrecision()) && field.getFieldPrecision() > 0) {
                sql.append(",").append(field.getFieldPrecision());
            }
            sql.append(")");
        }
        return sql;
    }

    @NotNull
    default StringBuilder getFullType(ModelFieldAlterDO field, StringBuilder sql) {
        sql.append(" ");
        sql.append(field.getFieldType());
        if (ObjUtilX.isNotEmpty(field.getLeng())) {
            sql.append("(").append(field.getLeng());
            if (ObjUtilX.isNotEmpty(field.getFieldPrecision())) {
                sql.append(",").append(field.getFieldPrecision());
            }
            sql.append(")");
        }
        return sql;
    }


    /**
     * 判断字符串是否为数字
     * @param str
     * @return
     */
    default boolean isNumeric(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }
        try {
            Double.parseDouble(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 判断字符串是否包含一些关键字
     * @param str
     * @param keywords
     * @return
     */
    default boolean containsAny(String str, String[] keywords) {
        for (String keyword : keywords) {
            if (str.contains(keyword)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断字符串是否是一些关键字开头
     * @param str
     * @param keywords
     * @return
     */
    default boolean startWith(String str, String[] keywords) {
        for (String keyword : keywords) {
            if (str.trim().toUpperCase().startsWith(keyword)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取表结构
     * @param table
     * @return
     * @throws SQLException
     */
    TableStructure getTableStructure(ModelTableDO table) throws SQLException;

    /**
     * 创建临时表
     *
     * @param queryDO
     * @param paramCode
     * @param fields
     * @return
     */
    StringBuilder genCreateTempTableSQL(ModelQueryDO queryDO, String paramCode, List<ModelFieldDO> fields);

    /**
     * 插入临时表数据
     *
     * @param paramCode
     * @param paramArray
     * @param fieldDOS
     * @return
     */
    StringBuilder genCreateTempTableInsertSQL(ModelQueryDO queryDO,String paramCode, JSONArray paramArray, List<ModelFieldDO> fieldDOS);
}

