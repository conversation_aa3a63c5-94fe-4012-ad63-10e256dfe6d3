package com.mongoso.mgs.module.model.controller.admin.modelfunction.vo;

import lombok.Data;

import jakarta.validation.constraints.NotNull;

/**
 * 自定义函数 PrimaryReqVO
 *
 * <AUTHOR>
 */
@Data
public class ModelFunctionPrimaryReqVO {

    /** 函数id  */
    private Long funId;

    /** 函数编号 */
    private String funCode;

    /** 版本号 */
    private Integer versionNo;

    /** 函数主体 */
    private String funBody;


    private String params;
}
