package com.mongoso.mgs.module.model.dal.mysql.modelreport;

import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.module.model.dal.db.modelreport.ModelReportDO;
import org.apache.ibatis.annotations.Mapper;
import com.mongoso.mgs.module.model.controller.admin.modelreport.vo.*;

/**
 * 自定义报 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ModelReportMapper extends BaseMapperX<ModelReportDO> {

    default PageResult<ModelReportDO> selectPage(ModelReportPageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<ModelReportDO>lambdaQueryX()
                .likeIfPresent(ModelReportDO::getReportName, reqVO.getReportName())
                .eqIfPresent(ModelReportDO::getReportCode, reqVO.getReportCode())
                .eqIfPresent(ModelReportDO::getQueryId, reqVO.getQueryId())
                .eqIfPresent(ModelReportDO::getRemark, reqVO.getRemark())
                .eqIfPresent(ModelReportDO::getVersionNo, reqVO.getVersionNo())
                .betweenIfPresent(ModelReportDO::getCreatedDt, reqVO.getCreatedDt())
                .eqIfPresent(ModelReportDO::getIsPublish, reqVO.getIsPublish())
                .orderByDesc(ModelReportDO::getCreatedDt));
    }

    default List<ModelReportDO> selectList(ModelReportQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<ModelReportDO>lambdaQueryX()
                .likeIfPresent(ModelReportDO::getReportName, reqVO.getReportName())
                .eqIfPresent(ModelReportDO::getReportCode, reqVO.getReportCode())
                .eqIfPresent(ModelReportDO::getQueryId, reqVO.getQueryId())
                .eqIfPresent(ModelReportDO::getRemark, reqVO.getRemark())
                .eqIfPresent(ModelReportDO::getVersionNo, reqVO.getVersionNo())
                .betweenIfPresent(ModelReportDO::getCreatedDt, reqVO.getCreatedDt())
                .eqIfPresent(ModelReportDO::getIsPublish, reqVO.getIsPublish())
                    .orderByDesc(ModelReportDO::getCreatedDt));
    }

}