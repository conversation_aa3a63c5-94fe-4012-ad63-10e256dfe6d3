package com.mongoso.mgs.module.model.controller.admin.modelreportversion.vo;

import lombok.*;

import com.mongoso.mgs.framework.common.domain.PageParam;












import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 自定义报表版本 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ModelReportVersionPageReqVO extends PageParam {

    /** 版本号 */
    private Integer versionNo;

    /** 创建时间   */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createdDt;

}
