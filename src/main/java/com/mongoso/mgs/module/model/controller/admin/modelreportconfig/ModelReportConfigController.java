package com.mongoso.mgs.module.model.controller.admin.modelreportconfig;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import jakarta.validation.*;
import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import static com.mongoso.mgs.framework.common.domain.ResultX.success;

import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.model.controller.admin.modelreportconfig.vo.*;
import com.mongoso.mgs.module.model.dal.db.modelreportconfig.ModelReportConfigDO;
import com.mongoso.mgs.module.model.service.modelreportconfig.ModelReportConfigService;

/**
 * 自定义报表配置 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/model")
@Validated
public class ModelReportConfigController {

    @Resource
    private ModelReportConfigService reportConfigService;

    @OperateLog("自定义报表配置添加或编辑")
    @PostMapping("/modelReportConfigAdit")
    @PreAuthorize("@ss.hasPermission('modelReportConfig:adit')")
    public ResultX<Long> modelReportConfigAdit(@Valid @RequestBody ModelReportConfigAditReqVO reqVO) {
        return success(reqVO.getId() == null
                            ? reportConfigService.modelReportConfigAdd(reqVO)
                            : reportConfigService.modelReportConfigEdit(reqVO));
    }

    @OperateLog("自定义报表配置删除")
    @PostMapping("/modelReportConfigDel")
    @PreAuthorize("@ss.hasPermission('modelReportConfig:del')")
    public ResultX<Boolean> modelReportConfigDel(@Valid @RequestBody ModelReportConfigPrimaryReqVO reqVO) {
        reportConfigService.modelReportConfigDel(reqVO.getId());
        return success(true);
    }

    @OperateLog("自定义报表配置详情")
    @PostMapping("/modelReportConfigDetail")
    @PreAuthorize("@ss.hasPermission('modelReportConfig:query')")
    public ResultX<ModelReportConfigRespVO> modelReportConfigDetail(@Valid @RequestBody ModelReportConfigPrimaryReqVO reqVO) {
        ModelReportConfigDO oldDO = reportConfigService.modelReportConfigDetail(reqVO.getId());
        return success(BeanUtilX.copy(oldDO, ModelReportConfigRespVO::new));
    }

    @OperateLog("自定义报表配置列表")
    @PostMapping("/modelReportConfigList")
    @PreAuthorize("@ss.hasPermission('modelReportConfig:query')")
    public ResultX<List<ModelReportConfigRespVO>> modelReportConfigList(@Valid @RequestBody ModelReportConfigQueryReqVO reqVO) {
        List<ModelReportConfigDO> list = reportConfigService.modelReportConfigList(reqVO);
        return success(BeanUtilX.copyList(list, ModelReportConfigRespVO::new));
    }

    @OperateLog("自定义报表配置分页")
    @PostMapping("/modelReportConfigPage")
    @PreAuthorize("@ss.hasPermission('modelReportConfig:query')")
    public ResultX<PageResult<ModelReportConfigRespVO>> modelReportConfigPage(@Valid @RequestBody ModelReportConfigPageReqVO reqVO) {
        PageResult<ModelReportConfigDO> pageResult = reportConfigService.modelReportConfigPage(reqVO);
        return success(BeanUtilX.copyPage(pageResult, ModelReportConfigRespVO::new));
    }

}
