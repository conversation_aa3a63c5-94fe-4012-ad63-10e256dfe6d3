package com.mongoso.mgs.module.model.dal.mysql.modelform.button;

import java.util.*;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.module.model.dal.db.modelform.button.ModelFormButtonDO;
import org.apache.ibatis.annotations.Mapper;
import com.mongoso.mgs.module.model.controller.admin.modelform.button.vo.*;

/**
 * 单据建模按钮配置 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ModelFormButtonMapper extends BaseMapperX<ModelFormButtonDO> {

    default PageResult<ModelFormButtonDO> selectPage(ModelFormButtonPageReqVO reqVO) {
        return selectPage(reqVO, LambdaQueryWrapperX.<ModelFormButtonDO>lambdaQueryX()
                .likeIfPresent(ModelFormButtonDO::getModelFormCode, reqVO.getModelFormCode())
                .eqIfPresent(ModelFormButtonDO::getButtonType, reqVO.getButtonType())
                .likeIfPresent(ModelFormButtonDO::getButtonName, reqVO.getButtonName())
                .eqIfPresent(ModelFormButtonDO::getIsSelect, reqVO.getIsSelect())
                .betweenIfPresent(ModelFormButtonDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .orderByDesc(ModelFormButtonDO::getDataId));
    }




    default List<ModelFormButtonDO> selectList(ModelFormButtonQueryReqVO reqVO) {
        return selectList(LambdaQueryWrapperX.<ModelFormButtonDO>lambdaQueryX()
                .eqIfPresent(ModelFormButtonDO::getDataId, reqVO.getDataId())
                .likeIfPresent(ModelFormButtonDO::getModelFormCode, reqVO.getModelFormCode())
                .eqIfPresent(ModelFormButtonDO::getButtonType, reqVO.getButtonType())
                .likeIfPresent(ModelFormButtonDO::getButtonName, reqVO.getButtonName())
                .eqIfPresent(ModelFormButtonDO::getIsSelect, reqVO.getIsSelect())
                .eqIfPresent(ModelFormButtonDO::getCreatedBy, reqVO.getCreatedBy())
                .betweenIfPresent(ModelFormButtonDO::getCreatedDt, reqVO.getStartCreatedDt(), reqVO.getEndCreatedDt())
                .eqIfPresent(ModelFormButtonDO::getUpdatedBy, reqVO.getUpdatedBy())
                .betweenIfPresent(ModelFormButtonDO::getUpdatedDt, reqVO.getStartUpdatedDt(), reqVO.getEndUpdatedDt())
                .orderByDesc(ModelFormButtonDO::getDataId));
    }

    /**
     * 根据单据建模编码批量删除按钮
     *
     * @param modelFormCode 单据建模编码
     * @return 删除的记录数
     */
    default int deleteByModelFormCode(String modelFormCode) {
        return delete(LambdaQueryWrapperX.<ModelFormButtonDO>lambdaQueryX()
                .eq(ModelFormButtonDO::getModelFormCode, modelFormCode));
    }


}