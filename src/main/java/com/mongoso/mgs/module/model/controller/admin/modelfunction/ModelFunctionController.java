package com.mongoso.mgs.module.model.controller.admin.modelfunction;

import com.alibaba.fastjson.JSONObject;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.module.model.controller.admin.modelfunction.vo.*;
import com.mongoso.mgs.module.model.dal.db.modelfunction.ModelFunctionDO;
import com.mongoso.mgs.module.model.service.modelfunction.ModelFunctionService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

import static com.mongoso.mgs.framework.common.domain.ResultX.error;
import static com.mongoso.mgs.framework.common.domain.ResultX.success;

/**
 * 自定义函数 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/model")
@Validated
public class ModelFunctionController {

    @Resource
    private ModelFunctionService functionService;

    @OperateLog("自定义函数添加或编辑")
    @PostMapping("/modelFunctionAdit")
    @PreAuthorize("@ss.hasPermission('modelFunction:adit')")
    public ResultX<Long> modelFunctionAdit(@Valid @RequestBody ModelFunctionAditReqVO reqVO) {
        return success(reqVO.getFunId() == null
                            ? functionService.modelFunctionAdd(reqVO)
                            : functionService.modelFunctionEdit(reqVO));
    }

    @OperateLog("自定义函数删除")
    @PostMapping("/modelFunctionDel")
    @PreAuthorize("@ss.hasPermission('modelFunction:del')")
    public ResultX<Boolean> modelFunctionDel(@Valid @RequestBody ModelFunctionPrimaryReqVO reqVO) {
        functionService.modelFunctionDel(reqVO.getFunId());
        return success(true);
    }

    @OperateLog("自定义函数锁定")
    @PostMapping("/modelFunctionLock")
    @PreAuthorize("@ss.hasPermission('modelFunction:del')")
    public ResultX<Boolean> modelFunctionLock(@Valid @RequestBody ModelFunctionLockReqVO reqVO) {
        functionService.modelFunctionLock(reqVO);
        return success(true);
    }

    @OperateLog("自定义函数详情")
    @PostMapping("/modelFunctionDetail")
    @PreAuthorize("@ss.hasPermission('modelFunction:query')")
    public ResultX<ModelFunctionRespVO> modelFunctionDetail(@Valid @RequestBody ModelFunctionPrimaryReqVO reqVO) {
        ModelFunctionDO oldDO = functionService.modelFunctionDetail(reqVO.getFunId());
        return success(BeanUtilX.copy(oldDO, ModelFunctionRespVO::new));
    }

    @OperateLog("自定义函数列表")
    @PostMapping("/modelFunctionList")
    @PreAuthorize("@ss.hasPermission('modelFunction:query')")
    public ResultX<List<ModelFunctionRespVO>> modelFunctionList(@Valid @RequestBody ModelFunctionQueryReqVO reqVO) {
        List<ModelFunctionDO> list = functionService.modelFunctionList(reqVO);
        return success(BeanUtilX.copyList(list, ModelFunctionRespVO::new));
    }

    @OperateLog("自定义函数分页")
    @PostMapping("/modelFunctionPage")
    @PreAuthorize("@ss.hasPermission('modelFunction:query')")
    public ResultX<PageResult<ModelFunctionRespVO>> modelFunctionPage(@Valid @RequestBody ModelFunctionPageReqVO reqVO) {
        PageResult<ModelFunctionDO> pageResult = functionService.modelFunctionPage(reqVO);
        return success(BeanUtilX.copyPage(pageResult, ModelFunctionRespVO::new));
    }

    @OperateLog("查询Tree")
    @PostMapping("/modelFunctionTree")
    @PreAuthorize("@ss.hasPermission('modelFunction:query')")
    public ResultX<List<ModelFunctionRespVO>> modelFunctionTree(@RequestBody ModelFunctionQueryReqVO reqVO) {
        return success(functionService.modelFunctionTree(reqVO));
    }

    @OperateLog("自定义函数修改父节点")
    @PostMapping("/modelFunctionDrag")
    @PreAuthorize("@ss.hasPermission('modelFunction:adit')")
    public ResultX<Integer> modelFunctionDrag(@Valid @RequestBody ModelFunctionDragReqVO reqVO) {
        return success(functionService.modelFunctionDrag(reqVO));
    }

    @OperateLog("执行函数")
    @PostMapping("/modelFunctionExecute")
    public ResultX<?> modelFunctionExecute(@Valid @RequestBody ModelFunctionPrimaryReqVO reqVO) {
        JSONObject exeRst = functionService.modelFunctionExecute(reqVO);
        return success(exeRst);
//        if("success".equals(exeRst.get("status"))){
//            return success(exeRst);
//        }
//        return error("5001", exeRst.toJSONString());
    }

}
