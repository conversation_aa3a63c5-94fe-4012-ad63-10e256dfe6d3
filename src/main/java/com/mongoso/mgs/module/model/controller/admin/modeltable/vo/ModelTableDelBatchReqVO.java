package com.mongoso.mgs.module.model.controller.admin.modeltable.vo;

import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * 图形建模主 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
public class ModelTableDelBatchReqVO implements Serializable {

    /** 模型表id集合 */
    @NotEmpty(message = "模型表集合不能为空")
    private List<Long> tableIds;
}
