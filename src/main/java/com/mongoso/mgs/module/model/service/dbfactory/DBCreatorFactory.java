package com.mongoso.mgs.module.model.service.dbfactory;

import com.mongoso.mgs.common.util.DatabaseUtil;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.module.enums.DBTypeEnum;
import com.mongoso.mgs.module.model.service.dbfactory.creator.MySQLCreator;
import com.mongoso.mgs.module.model.service.dbfactory.creator.PostgreSQLCreator;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

/**
 * 数据库操作工程类
 * daijinbiao
 * 2024-10-12 11:08:42
 */
@Component
public class DBCreatorFactory {
    @Resource
    private DatabaseUtil dbUtil;

    private final ApplicationContext applicationContext;

    public DBCreatorFactory(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    /**
     * 获取数据库空间
     * @param config
     * @return
     */
    public DBCreator getDBCreatorDynamic(Long config) {
        try {
            String dbType = dbUtil.getDbTypeDynamic(config);
            if (dbType.toLowerCase().contains(DBTypeEnum.MYSQL.getValue())) {
                return applicationContext.getBean(MySQLCreator.class);
            } else if (dbType.toLowerCase().contains(DBTypeEnum.PG.getValue())) {
                return applicationContext.getBean(PostgreSQLCreator.class);
            } else {
                throw new BizException(5001, "暂不支持当前数据库: " + dbType);
            }
        }catch (BizException e) {
            throw e;
        }catch (Exception e) {
            throw new BizException(5001, "连接失败: " + e.getMessage());
        }
    }
}
