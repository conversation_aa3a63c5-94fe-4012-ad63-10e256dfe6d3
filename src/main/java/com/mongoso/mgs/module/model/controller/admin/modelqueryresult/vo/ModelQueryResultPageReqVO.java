package com.mongoso.mgs.module.model.controller.admin.modelqueryresult.vo;

import lombok.*;

import com.mongoso.mgs.framework.common.domain.PageParam;






























import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 自定义查询结果 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ModelQueryResultPageReqVO extends PageParam {

    /** 查询id */
    private Long queryId;

    /** 结果字段 */
    private String resultField;

    /** 结果字段别名 */
    private String resultFieldName;

    /** 数据类型 */
    private String dataType;

    /** 长度 */
    private Integer leng;

    /** 精度 */
    private Integer fieldPrecision;

    /** 备注 */
    private String remark;

    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createdDt;

}
