package com.mongoso.mgs.module.model.controller.admin.modelbiztable.detail.vo;

import lombok.*;

import jakarta.validation.constraints.*;
import java.io.Serializable;

  

/**
 * 子表明细 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class ModelBizTableDetailBaseVO implements Serializable {

    /** 数据id */
    private Long dataId;

    /** 明细表id */
    @NotNull(message = "明细表id不能为空")
    private Long tableId;

    /** 主表业务编码 */
    @NotEmpty(message = "主表业务编码不能为空")
    private String bizCode;

    /** 明细表 */
    @NotEmpty(message = "明细表不能为空")
    private String tableCode;

    /** 级别 */
    @NotNull(message = "级别不能为空")
    private Integer level;

    /** 子表类型[0:表格 1:表格树] */
    @NotNull(message = "子表类型[0:表格 1:表格树]不能为空")
    private Integer tableType;

    /** 是否必有一行 */
    private Short isNullAble;

    /** 父数据id */
    private Long parentDataId;

    /** 备注 */
    private String remark;

    /** 父行路径 */
    private String parentRowPath;

    /** 父行号 (只有当子表类型是树结构才有数据)*/
    private Integer parentRowNo;

    /** 行号 */
    private Integer rowNo;

    /** 表名称 */
    private String tableName;
}
