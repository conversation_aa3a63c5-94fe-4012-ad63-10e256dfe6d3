package com.mongoso.mgs.module.model.controller.admin.modelquery.vo;

import lombok.*;

import com.mongoso.mgs.framework.common.domain.PageParam;





















import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 自定义查询 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ModelQueryPageReqVO extends PageParam {

    private Long dataSourceConfigId;
    /** 查询编码 */
    private String queryCode;

    /** 查询中文名 */
    private String queryName;

    /** 查询语句 */
    private String queryStatment;

    /** 备注 */
    private String remark;
    /** 类型 */
    private Integer dirType;

    /** 父节点 */
    private Long parentId;
    /** 创建时间 */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createdDt;

}
