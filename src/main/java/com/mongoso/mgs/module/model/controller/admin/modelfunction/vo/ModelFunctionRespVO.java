package com.mongoso.mgs.module.model.controller.admin.modelfunction.vo;

import com.mongoso.mgs.framework.common.domain.BaseTree;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

import static com.mongoso.mgs.framework.common.util.DateUtilX.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 自定义函数 RespVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ModelFunctionRespVO extends BaseTree {

    List<ModelFunctionRespVO> children;

    /** 函数id   */
    private Long funId;

    /** 函数名称 */
    @NotEmpty(message = "函数名称不能为空")
    private String funName;

    /** 函数编号 */
    @NotEmpty(message = "函数编号不能为空")
    private String funCode;

    /** 函数主体 */
    @NotEmpty(message = "函数主体不能为空")
    private String funBody;

    /** 运行环境   */
    @NotEmpty(message = "运行环境  不能为空")
    private String runEnv;

    /** 超时时间 */
    private Integer timeout;

    /** 备注   */
    private String remark;

    /** 版本号 */
    @NotNull(message = "版本号不能为空")
    private Integer versionNo;

    /** 是否发布 */
    @NotNull(message = "是否发布不能为空")
    private Integer isPublish;
    private Integer isLock;

    /** 目录类型 */
    @NotNull(message = "目录类型不能为空")
    private Integer dirType;

    /** 父节点 */
    @NotNull(message = "父节点不能为空")
    private Long parentId;

    /** 属性类型，0：系统，1：用户 */
    @NotNull(message = "属性类型，0：系统，1：用户不能为空")
    private Integer propType;

    /** 更新时间   */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime updatedDt;

    /** 创建人   */
    private String createdBy;

    /** 创建时间   */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createdDt;

    /** 更新人   */
    private String updatedBy;

}
