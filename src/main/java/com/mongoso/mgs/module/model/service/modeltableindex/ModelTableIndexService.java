package com.mongoso.mgs.module.model.service.modeltableindex;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.module.model.controller.admin.modeltableindex.vo.ModelTableIndexAditReqVO;
import com.mongoso.mgs.module.model.controller.admin.modeltableindex.vo.ModelTableIndexPageReqVO;
import com.mongoso.mgs.module.model.controller.admin.modeltableindex.vo.ModelTableIndexQueryReqVO;
import com.mongoso.mgs.module.model.dal.db.modeltableindex.ModelTableIndexDO;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 图形建模主表索引 Service 接口
 *
 * <AUTHOR>
 */
public interface ModelTableIndexService {

    /**
     * 创建图形建模主表索引
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long modelTableIndexAdd(@Valid ModelTableIndexAditReqVO reqVO);

    /**
     * 更新图形建模主表索引
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long modelTableIndexEdit(@Valid ModelTableIndexAditReqVO reqVO);

    /**
     * 删除图形建模主表索引
     *
     * @param id 编号
     */
    void modelTableIndexDel(Long id);

    /**
     * 获得图形建模主表索引信息
     *
     * @param id 编号
     * @return 图形建模主表索引信息
     */
    ModelTableIndexDO modelTableIndexDetail(Long id);

    /**
     * 获得图形建模主表索引列表
     *
     * @param reqVO 查询条件
     * @return 图形建模主表索引列表
     */
    List<ModelTableIndexDO> modelTableIndexList(@Valid ModelTableIndexQueryReqVO reqVO);

    /**
     * 获得图形建模主表索引分页
     *
     * @param reqVO 查询条件
     * @return 图形建模主表索引分页
     */
    PageResult<ModelTableIndexDO> modelTableIndexPage(@Valid ModelTableIndexPageReqVO reqVO);

}
