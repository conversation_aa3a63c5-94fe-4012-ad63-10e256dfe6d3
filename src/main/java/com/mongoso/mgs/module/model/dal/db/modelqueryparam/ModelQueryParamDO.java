package com.mongoso.mgs.module.model.dal.db.modelqueryparam;

import lombok.*;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;

/**
 * 自定义查询参数 DO
 *
 * <AUTHOR>
 */
@TableName("lowcode.sys_model_query_param")
//@KeySequence("sys_model_query_param_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ModelQueryParamDO extends OperateDO {

    /** 主键ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /** 查询id */
    private Long queryId;

    /** 参数英文名 */
    private String paramCode;

    /** 参数中文名 */
    private String paramName;

    /** 参数模式 */
    private Integer paramType;

    /** 数据类型 */
    private String dataType;

    /** 长度 */
    private Integer leng;

    /** 精度 */
    private Integer fieldPrecision;

    /** 数据源 */
    private String dataTable;

    private Long tableId;
    /** 数据列 */
    private String dataFields;
    private String dataFieldStr;

    /** 备注 */
    private String remark;


}
