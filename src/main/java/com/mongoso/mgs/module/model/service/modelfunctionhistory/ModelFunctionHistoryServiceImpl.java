package com.mongoso.mgs.module.model.service.modelfunctionhistory;

import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.mongoso.mgs.module.model.controller.admin.modelfunctionhistory.vo.*;
import com.mongoso.mgs.module.model.dal.db.modelfunctionhistory.ModelFunctionHistoryDO;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.module.model.dal.mysql.modelfunctionhistory.ModelFunctionHistoryMapper;

// import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exception;
import com.mongoso.mgs.framework.common.exception.BizException;
// import static com.mongoso.mgs.module.model.enums.ErrorCodeConstants.*;


/**
 * 自定义函数历史 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ModelFunctionHistoryServiceImpl implements ModelFunctionHistoryService {

    @Resource
    private ModelFunctionHistoryMapper functionHistoryMapper;

    @Override
    public Long modelFunctionHistoryAdd(ModelFunctionHistoryAditReqVO reqVO) {
        // 插入
        ModelFunctionHistoryDO functionHistory = BeanUtilX.copy(reqVO, ModelFunctionHistoryDO::new);
        functionHistoryMapper.insert(functionHistory);
        // 返回
        return functionHistory.getDataId();
    }

    @Override
    public Long modelFunctionHistoryEdit(ModelFunctionHistoryAditReqVO reqVO) {
        // 校验存在
        this.modelFunctionHistoryValidateExists(reqVO.getDataId());
        // 更新
        ModelFunctionHistoryDO functionHistory = BeanUtilX.copy(reqVO, ModelFunctionHistoryDO::new);
        functionHistoryMapper.updateById(functionHistory);
        // 返回
        return functionHistory.getDataId();
    }

    @Override
    public void modelFunctionHistoryDel(Long dId) {
        // 校验存在
        this.modelFunctionHistoryValidateExists(dId);
        // 删除
        functionHistoryMapper.deleteById(dId);
    }

    private ModelFunctionHistoryDO modelFunctionHistoryValidateExists(Long dId) {
        ModelFunctionHistoryDO functionHistory = functionHistoryMapper.selectById(dId);
        if (functionHistory == null) {
            // throw exception(FUNCTION_HISTORY_NOT_EXISTS);
            throw new BizException("5001", "自定义函数历史不存在");
        }
        return functionHistory;
    }

    @Override
    public ModelFunctionHistoryDO modelFunctionHistoryDetail(Long dId) {
        return functionHistoryMapper.selectById(dId);
    }

    @Override
    public List<ModelFunctionHistoryDO> modelFunctionHistoryList(ModelFunctionHistoryQueryReqVO reqVO) {
        return functionHistoryMapper.selectList(reqVO);
    }

    @Override
    public PageResult<ModelFunctionHistoryDO> modelFunctionHistoryPage(ModelFunctionHistoryPageReqVO reqVO) {
        return functionHistoryMapper.selectPage(reqVO);
    }

}
