package com.mongoso.mgs.module.model.controller.admin.item.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by Fashion on 2020/2/19.
 */
@Data
public class CreateItemParamsBatch implements Serializable {
	
    private static final long serialVersionUID = 1L;

    private List<CreateItemParams> apiList;

    private Integer itemType;

    private Long parentItemId;

    private Long projectId;

    private Integer category;

    private Integer overite;//是否覆盖 0-不覆盖 1-覆盖
}
