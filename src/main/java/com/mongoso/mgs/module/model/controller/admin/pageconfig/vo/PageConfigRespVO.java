package com.mongoso.mgs.module.model.controller.admin.pageconfig.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

/**
 * 页面配置 RespVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PageConfigRespVO extends PageConfigBaseVO {

    /** 子集 */
    List<PageConfigRespVO> children = new ArrayList<>();
}
