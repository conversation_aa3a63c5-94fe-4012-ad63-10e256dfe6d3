package com.mongoso.mgs.module.model.dal.db.modelfunctionhistory;

import lombok.*;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;

/**
 * 自定义函数历史 DO
 *
 * <AUTHOR>
 */
@TableName("lowcode.sys_model_function_history")
//@KeySequence("sys_model_function_history_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ModelFunctionHistoryDO extends OperateDO {

    /** 函数id   */
    private Long funId;

    /** 函数名称 */
    private String funName;

    /** 函数编号 */
    private String funCode;

    /** 函数主体 */
    private String funBody;

    /** 运行环境   */
    private String runEnv;

    /** 超时时间 */
    private Integer timeout;

    /** 备注   */
    private String remark;

    /** 版本号 */
    private Integer versionNo;

    /** 是否发布 */
    private Integer isPublish;

    /** 是否锁定 */
    private Integer isLock;

    /** 目录类型 */
    private Integer dirType;

    /** 父节点 */
    private Long parentId;

    /** 属性类型，0：系统，1：用户 */
    private Integer propType;

    /** 数据id */
    @TableId(type = IdType.ASSIGN_ID)
    private Long dataId;


}
