package com.mongoso.mgs.module.model.controller.admin.modelbiztable.vo;

import com.mongoso.mgs.module.model.controller.admin.modelbiztable.detail.vo.ModelBizTableDetailRespVO;
import lombok.*;

import java.util.List;





/**
 * 主 RespVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ModelBizTableRespVO extends ModelBizTableBaseVO {

    /** 模型表名称（从sys_model_table表关联查询得到） */
    private String tableName;

    /** 子表明细列表（树结构） */
    private List<ModelBizTableDetailRespVO> modelBizTableDetailList;

}
