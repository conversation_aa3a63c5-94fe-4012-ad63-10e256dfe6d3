package com.mongoso.mgs.module.model.controller.admin.modelfieldconf.vo;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.util.List;

/**
 * 图形建模字段翻译配置 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class ModelFieldConfBaseVO implements Serializable {

    /** 模型字段表id */
    private Long id;

    /** 模型字段表中文名 */
    private String fieldName;

    /** 模型字段表实名 */
    @NotEmpty(message = "模型字段表实名不能为空")
    private String fieldCode;

    /** 字段类型 */
    private String fieldType;

    /** 文档类型 */
    private String dataType;

    /** 长度 */
    private Integer leng;

    /** 精度 */
    private Integer fieldPrecision;

    /** 是否必填 */
    @NotNull(message = "是否必填不能为空")
    private Integer isNullable;

    /** 是否为主键 */
    @NotNull(message = "是否为主键不能为空")
    private Integer isPrimaryKey;

    /** 默认值 */
    private String defaultVal;

    /** 排序 */
    @NotNull(message = "排序不能为空")
    private Integer sort;

    /** 属性类型，0：系统，1：用户 */
    @NotNull(message = "属性类型，0：系统，1：用户不能为空")
    private Integer propType;

    /** 备注描述 */
    private String remark;

    /** 节点名 */
    private String itemName;

    /** 英文名 */
    private String englishName;

    /** 字段长度 */
    private Integer fieldLength;
}
