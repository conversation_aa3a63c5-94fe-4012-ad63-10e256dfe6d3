package com.mongoso.mgs.module.model.service.sqllog;

import com.mongoso.mgs.module.model.controller.admin.sqllog.vo.SqlLogAditReqVO;
import com.mongoso.mgs.module.model.controller.admin.sqllog.vo.SqlLogPageReqVO;
import com.mongoso.mgs.module.model.controller.admin.sqllog.vo.SqlLogQueryReqVO;
import com.mongoso.mgs.module.model.controller.admin.sqllog.vo.SqlLogRespVO;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.mongoso.mgs.module.model.dal.db.sqllog.SqlLogDO;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.module.model.dal.mysql.sqllog.SqlLogMapper;

// import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exception;
import com.mongoso.mgs.framework.common.exception.BizException;
// import static com.mongoso.mgs.module.sql.enums.ErrorCodeConstants.*;


/**
 * 脚本日志 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SqlLogServiceImpl implements SqlLogService {

    @Resource
    private SqlLogMapper logMapper;

    @Override
    public Long sqlLogAdd(SqlLogAditReqVO reqVO) {
        // 插入
        SqlLogDO log = BeanUtilX.copy(reqVO, SqlLogDO::new);
        logMapper.insert(log);
        // 返回
        return log.getId();
    }

    @Override
    public Long sqlLogEdit(SqlLogAditReqVO reqVO) {
        // 校验存在
        this.sqlLogValidateExists(reqVO.getId());
        // 更新
        SqlLogDO log = BeanUtilX.copy(reqVO, SqlLogDO::new);
        logMapper.updateById(log);
        // 返回
        return log.getId();
    }

    @Override
    public void sqlLogDel(Long id) {
        // 校验存在
        this.sqlLogValidateExists(id);
        // 删除
        logMapper.deleteById(id);
    }

    private SqlLogDO sqlLogValidateExists(Long id) {
        SqlLogDO log = logMapper.selectById(id);
        if (log == null) {
            // throw exception(LOG_NOT_EXISTS);
            throw new BizException("5001", "脚本日志不存在");
        }
        return log;
    }

    @Override
    public SqlLogRespVO sqlLogDetail(Long id) {
        SqlLogDO data = logMapper.selectById(id);
        return BeanUtilX.copy(data, SqlLogRespVO::new);
    }

    @Override
    public List<SqlLogRespVO> sqlLogList(SqlLogQueryReqVO reqVO) {
        List<SqlLogDO> data = logMapper.selectList(reqVO);
        return BeanUtilX.copy(data, SqlLogRespVO::new);
    }

    @Override
    public PageResult<SqlLogRespVO> sqlLogPage(SqlLogPageReqVO reqVO) {
        PageResult<SqlLogDO> data = logMapper.selectPage(reqVO);
        return BeanUtilX.copy(data, SqlLogRespVO::new);
    }

}
