package com.mongoso.mgs.module.model.controller.admin.item.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by Fashion on 2019/2/18.
 */
@Data
public class DragItemParams implements Serializable {
	
    private static final long serialVersionUID = 1L;

    //@ApiModelProperty(value = "文件ID", required = false)
    private Long itemId;
    
    //@ApiModelProperty(value = "目标文件ID", required = false)
    private Long destItemId;
    
    //@ApiModelProperty(value = "拖动类型 0-目标之前  1-目标之后", required = false)
    private int dragType;
    
    //@ApiModelProperty(value = "更新用户", required = true, hidden = true)
    private String updateUser;

    //@ApiModelProperty(value = "更新用户Id", required = true, hidden = true)
    private String updateUserId;
    
    //@ApiModelProperty(value = "更新时间", required = true, hidden = true)
    private Date updateTime;

    //@ApiModelProperty(value = "项目ID", required = false)
    private Long projectId;

    //@ApiModelProperty(value = "文件类型 0-文件夹 1-项目")
    private Integer itemType;
}
