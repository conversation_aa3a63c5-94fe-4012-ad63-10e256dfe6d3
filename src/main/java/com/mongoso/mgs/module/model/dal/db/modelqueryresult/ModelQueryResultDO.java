package com.mongoso.mgs.module.model.dal.db.modelqueryresult;

import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;

/**
 * 自定义查询结果 DO
 *
 * <AUTHOR>
 */
@TableName("lowcode.sys_model_query_result")
//@KeySequence("sys_model_query_result_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ModelQueryResultDO extends OperateDO {

    /** 主键ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /** 查询id */
    private Long queryId;

    /** 结果字段 */
    private String resultField;

    /** 结果字段别名 */
    private String resultFieldName;

    /** 数据类型 */
    private String dataType;

    /** 长度 */
    private Integer leng;

    /** 精度 */
    private Integer fieldPrecision;
    /**
     * 排序
     */
    private Integer sort;
    /** 备注 */
    private String remark;

    /**
     * 是否显示
     */
    private Integer isShow;
}
