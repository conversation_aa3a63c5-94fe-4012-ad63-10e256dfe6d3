package com.mongoso.mgs.module.model.controller.admin.modelfieldconf.vo;

import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 图形建模字段翻译配置 Base VO，提供给添加、修改、返回的子 VO 使用
 *
 * <AUTHOR>
 */
@Data
public class ModelFieldTransReqVO implements Serializable {

    /** 模型字段表中文名集合 */
    @NotEmpty(message = "请传入字段集合")
    private List<ModelFieldConfAditReqVO> fields;

    /** 文本类型 */
    private Integer textType = 0;

    /** 数据源id */
    //@NotNull(message = "请传入数据源")
    private Long dataSourceConfigId;
    /** 项目id */
    private Long projectId;
}
