package com.mongoso.mgs.module.model.dal.db.generator;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 业务建模 DO
 *
 * <AUTHOR>
 */
@TableName(value = "lowcode.sys_model_biz_relation", autoResultMap = true)
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BizRelationDO {

    /** 主键 ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 业务 ID
     */
    private Long bizId;

    /**
     * 父表 ID
     */
    private Long parentTableId;

    /**
     * 子表 ID
     */
    private Long tableId;

    /**
     * 父表主键字段
     */
    private String parentField;

    /**
     * 子表外键字段
     */
    private String fieldCode;
}
