package com.mongoso.mgs.module.model.service.modelbiztable;

import java.util.*;
import jakarta.validation.*;
import com.mongoso.mgs.module.model.controller.admin.modelbiztable.vo.*;
import com.mongoso.mgs.module.model.dal.db.modelbiztable.ModelBizTableDO;
import com.mongoso.mgs.framework.common.domain.PageResult;

/**
 * 主 Service 接口
 *
 * <AUTHOR>
 */
public interface ModelBizTableService {

    /**
     * 创建主
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long modelBizTableAdd(@Valid ModelBizTableAditReqVO reqVO);

    /**
     * 更新主
     *
     * @param reqVO 更新信息
     * @return 编号
     */
    Long modelBizTableEdit(@Valid ModelBizTableAditReqVO reqVO);

    /**
     * 删除主
     *
     * @param dataId 编号
     */
    void modelBizTableDelete(Long dataId);

    /**
     * 获得主信息
     *
     * @param dataId 编号
     * @return 主信息
     */
    ModelBizTableRespVO modelBizTableDetail(Long dataId);

    /**
     * 获得主列表
     *
     * @param reqVO 查询条件
     * @return 主列表
     */
    List<ModelBizTableRespVO> modelBizTableList(@Valid ModelBizTableQueryReqVO reqVO);

    /**
     * 获得主分页
     *
     * @param reqVO 查询条件
     * @return 主分页
     */
    PageResult<ModelBizTableRespVO> modelBizTablePage(@Valid ModelBizTablePageReqVO reqVO);

}
