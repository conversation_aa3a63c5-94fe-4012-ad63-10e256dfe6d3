package com.mongoso.mgs.module.business.service;

import java.util.Map;

/**
 * 业务流程服务
 */
public interface BusinessProcessService {
    
    /**
     * 获取业务对应的流程定义Key
     * @param bizId 业务ID
     * @return 流程定义Key
     */
    String getProcessKeyByBizId(Long bizId);
    
    /**
     * 保存业务流程配置
     * @param bizId 业务ID
     * @param processKey 流程定义Key
     * @param config 流程配置
     */
    void saveProcessConfig(Long bizId, String processKey, Map<String, Object> config);
    
    /**
     * 获取业务流程配置
     * @param bizId 业务ID
     * @return 流程配置
     */
    Map<String, Object> getProcessConfig(Long bizId);
}