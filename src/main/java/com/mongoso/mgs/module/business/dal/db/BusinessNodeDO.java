package com.mongoso.mgs.module.business.dal.db;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 业务节点 DO
 */
@TableName("lowcode.sys_lowcode_business_node")
@Data
@EqualsAndHashCode(callSuper = true)
public class BusinessNodeDO extends OperateDO {

    @TableId
    private Long id;
    /**
     * 业务ID
     */
    private Long bizId;
    
    /**
     * 节点ID
     */
    private String nodeId;
    
    /**
     * 节点名称
     */
    private String nodeName;
    
    /**
     * 节点类型
     */
    private String nodeType;
    
    /**
     * 触发事件
     */
    private String triggerEvent;
    
    /**
     * 任务定义Key
     */
    private String taskDefinitionKey;
    
    /**
     * 节点配置 (JSON)
     */
    private String config;
    
    /**
     * 执行条件 (JSON)
     */
    private String condition;
    
    /**
     * 执行顺序
     */
    private Integer orderNum;
    
    /**
     * 是否启用
     */
    private Boolean enabled;
}