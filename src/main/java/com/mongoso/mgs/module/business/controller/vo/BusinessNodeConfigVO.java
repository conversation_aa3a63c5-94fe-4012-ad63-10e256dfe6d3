package com.mongoso.mgs.module.business.controller.vo;

import lombok.Data;
import java.util.Map;

/**
 * 业务节点配置VO
 */
@Data
public class BusinessNodeConfigVO {
    
    /**
     * 节点ID
     */
    private String nodeId;
    
    /**
     * 节点名称
     */
    private String nodeName;
    
    /**
     * 节点类型
     * SCRIPT - 脚本节点
     * API - API调用节点
     * RULE - 规则节点
     * NOTIFICATION - 通知节点
     * WORKFLOW - 工作流节点
     */
    private String nodeType;
    
    /**
     * 触发事件
     * BEFORE - 操作前执行
     * AFTER - 操作后执行
     */
    private String triggerEvent;
    
    /**
     * 任务定义Key（工作流相关）
     */
    private String taskDefinitionKey;
    
    /**
     * 节点配置
     */
    private Map<String, Object> config;
    
    /**
     * 执行条件
     */
    private Map<String, Object> condition;
    
    /**
     * 执行顺序
     */
    private Integer orderNum;
    
    /**
     * 是否启用
     */
    private Boolean enabled;
}