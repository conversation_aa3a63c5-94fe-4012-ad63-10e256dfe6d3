package com.mongoso.mgs.module.business.dal.mysql;

import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.business.dal.db.BusinessProcessDO;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface BusinessProcessMapper extends BaseMapperX<BusinessProcessDO> {

    /**
     * 根据 bizId 获取业务流程
     *
     * @param bizId 业务 ID
     * @return 业务流程
     */
    default BusinessProcessDO selectByBizId(Long bizId) {
        return selectOne(LambdaQueryWrapperX.<BusinessProcessDO>lambdaQueryX()
                .eq(BusinessProcessDO::getBizId, bizId)
                .last(" limit 1")
        );
    }
}