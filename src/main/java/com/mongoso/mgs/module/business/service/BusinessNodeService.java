package com.mongoso.mgs.module.business.service;

import com.mongoso.mgs.module.business.controller.vo.BusinessNodeConfigVO;

import java.util.List;
import java.util.Map;

public interface BusinessNodeService {

    /**
     * 根据业务ID获取节点列表
     */
    List<BusinessNodeConfigVO> getNodesByBizId(Long bizId);

    /**
     * 保存业务节点配置
     */
    void saveNodes(Long bizId, List<BusinessNodeConfigVO> nodes);

    /**
     * 添加业务节点
     */
    void addNode(Long bizId, BusinessNodeConfigVO node);

    /**
     * 更新业务节点
     */
    void updateNode(Long bizId, String nodeId, BusinessNodeConfigVO node);

    /**
     * 删除业务节点
     */
    void deleteNode(Long bizId, String nodeId);

    /**
     * 测试业务节点
     */
    Map<String, Object> testNode(Long bizId, String nodeId, Map<String, Object> testData);

    /**
     * 获取节点配置模板
     */
    List<Map<String, Object>> getNodeTemplates();

    /**
     * 更新节点执行顺序
     */
    void updateNodeOrder(Long bizId, List<Map<String, Object>> sortData);

    /**
     * 启用/禁用节点
     */
    void toggleNodeStatus(Long bizId, String nodeId, Boolean enabled);

    /**
     * 根据事件和操作执行业务节点
     */
    Map<String, Object> executeNodesByEvent(Long bizId, String event, String operation, Long mainId, Map<String, Object> data);
}
