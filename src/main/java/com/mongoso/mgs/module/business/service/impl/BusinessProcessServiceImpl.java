package com.mongoso.mgs.module.business.service.impl;

import com.alibaba.fastjson.JSON;
import com.mongoso.mgs.framework.common.util.ObjUtilX;
import com.mongoso.mgs.module.business.dal.db.BusinessProcessDO;
import com.mongoso.mgs.module.business.dal.mysql.BusinessProcessMapper;
import com.mongoso.mgs.module.business.service.BusinessProcessService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class BusinessProcessServiceImpl implements BusinessProcessService {

    @Resource
    private BusinessProcessMapper businessProcessMapper;

    @Override
    public String getProcessKeyByBizId(Long bizId) {
        log.info("获取业务流程定义Key, bizId: {}", bizId);
        
        BusinessProcessDO process = businessProcessMapper.selectByBizId(bizId);
        if (process == null) {
            log.warn("未找到业务流程配置, bizId: {}", bizId);
            return null;
        }
        
        return process.getProcessKey();
    }

    @Override
    public void saveProcessConfig(Long bizId, String processKey, Map<String, Object> config) {
        log.info("保存业务流程配置, bizId: {}, processKey: {}", bizId, processKey);
        
        BusinessProcessDO process = businessProcessMapper.selectByBizId(bizId);
        if (process == null) {
            // 新增
            process = new BusinessProcessDO();
            process.setBizId(bizId);
            process.setProcessKey(processKey);
            process.setConfig(JSON.toJSONString(config));
            businessProcessMapper.insert(process);
        } else {
            // 更新
            process.setProcessKey(processKey);
            process.setConfig(JSON.toJSONString(config));
            businessProcessMapper.updateById(process);
        }
    }

    @Override
    public Map<String, Object> getProcessConfig(Long bizId) {
        log.info("获取业务流程配置, bizId: {}", bizId);
        
        BusinessProcessDO process = businessProcessMapper.selectByBizId(bizId);
        if (process == null) {
            return new HashMap<>();
        }
        
        if (ObjUtilX.isEmpty(process.getConfig())) {
            return new HashMap<>();
        }
        
        return JSON.parseObject(process.getConfig(), Map.class);
    }
}