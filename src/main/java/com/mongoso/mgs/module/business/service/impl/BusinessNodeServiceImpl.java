package com.mongoso.mgs.module.business.service.impl;

import com.alibaba.fastjson.JSON;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.ObjUtilX;
import com.mongoso.mgs.framework.mybatis.core.util.IDUtilX;
import com.mongoso.mgs.module.business.controller.vo.BusinessNodeConfigVO;
import com.mongoso.mgs.module.business.dal.db.BusinessNodeDO;
import com.mongoso.mgs.module.business.dal.mysql.BusinessNodeMapper;
import com.mongoso.mgs.module.business.service.BusinessNodeService;
import com.mongoso.mgs.module.script.service.ScriptExecutionService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BusinessNodeServiceImpl implements BusinessNodeService {

    @Resource
    private BusinessNodeMapper businessNodeMapper;
    
    @Resource
    private ScriptExecutionService scriptExecutionService;

    @Override
    public List<BusinessNodeConfigVO> getNodesByBizId(Long bizId) {
        log.info("获取业务节点配置, bizId: {}", bizId);
        
        List<BusinessNodeDO> nodes = businessNodeMapper.selectByBizId(bizId);
        if (ObjUtilX.isEmpty(nodes)) {
            return new ArrayList<>();
        }
        
        return nodes.stream().map(this::convertToVO).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveNodes(Long bizId, List<BusinessNodeConfigVO> nodes) {
        log.info("保存业务节点配置, bizId: {}, 节点数: {}", bizId, nodes.size());
        
        // 先删除原有节点
        businessNodeMapper.deleteByBizId(bizId);
        
        // 保存新节点
        for (BusinessNodeConfigVO node : nodes) {
            BusinessNodeDO nodeDO = convertToDO(bizId, node);
            businessNodeMapper.insert(nodeDO);
        }
    }

    @Override
    public void addNode(Long bizId, BusinessNodeConfigVO node) {
        log.info("添加业务节点, bizId: {}, nodeId: {}", bizId, node.getNodeId());
        
        // 检查节点ID是否已存在
        List<BusinessNodeDO> existingNodes = businessNodeMapper.selectByBizId(bizId);
        boolean exists = existingNodes.stream()
                .anyMatch(n -> n.getNodeId().equals(node.getNodeId()));
        
        if (exists) {
            throw new BizException("5001", "节点ID已存在: " + node.getNodeId());
        }
        
        BusinessNodeDO nodeDO = convertToDO(bizId, node);
        businessNodeMapper.insert(nodeDO);
    }

    @Override
    public void updateNode(Long bizId, String nodeId, BusinessNodeConfigVO node) {
        log.info("更新业务节点, bizId: {}, nodeId: {}", bizId, nodeId);
        
        // 查找现有节点
        List<BusinessNodeDO> nodes = businessNodeMapper.selectByBizId(bizId);
        BusinessNodeDO existingNode = nodes.stream()
                .filter(n -> n.getNodeId().equals(nodeId))
                .findFirst()
                .orElseThrow(() -> new BizException("5001", "节点不存在: " + nodeId));
        
        // 更新节点信息
        existingNode.setNodeName(node.getNodeName());
        existingNode.setNodeType(node.getNodeType());
        existingNode.setTriggerEvent(node.getTriggerEvent());
        existingNode.setTaskDefinitionKey(node.getTaskDefinitionKey());
        existingNode.setConfig(JSON.toJSONString(node.getConfig()));
        existingNode.setCondition(JSON.toJSONString(node.getCondition()));
        existingNode.setOrderNum(node.getOrderNum());
        existingNode.setEnabled(node.getEnabled());
        
        businessNodeMapper.updateById(existingNode);
    }

    @Override
    public void deleteNode(Long bizId, String nodeId) {
        log.info("删除业务节点, bizId: {}, nodeId: {}", bizId, nodeId);
        
        // 查找并删除节点
        List<BusinessNodeDO> nodes = businessNodeMapper.selectByBizId(bizId);
        BusinessNodeDO nodeToDelete = nodes.stream()
                .filter(n -> n.getNodeId().equals(nodeId))
                .findFirst()
                .orElseThrow(() -> new BizException("5001", "节点不存在: " + nodeId));
        
        businessNodeMapper.deleteById(nodeToDelete.getId());
    }

    @Override
    public Map<String, Object> testNode(Long bizId, String nodeId, Map<String, Object> testData) {
        log.info("测试业务节点, bizId: {}, nodeId: {}", bizId, nodeId);
        
        // 查找节点
        List<BusinessNodeDO> nodes = businessNodeMapper.selectByBizId(bizId);
        BusinessNodeDO node = nodes.stream()
                .filter(n -> n.getNodeId().equals(nodeId))
                .findFirst()
                .orElseThrow(() -> new BizException("5001", "节点不存在: " + nodeId));
        
        try {
            // 检查条件
            if (!checkNodeCondition(node, testData)) {
                Map<String, Object> result = new HashMap<>();
                result.put("success", false);
                result.put("message", "节点条件不满足");
                return result;
            }
            
            // 执行节点
            Map<String, Object> result = executeNode(node, null, testData);
            Map<String, Object> testResult = new HashMap<>();
            testResult.put("success", true);
            testResult.put("result", result);
            return testResult;
            
        } catch (BizException e) {
          throw new BizException("5001", "测试节点失败: " + e.getMessage());
        }catch (Exception e) {
            log.error("测试节点失败: {}, error: {}", nodeId, e.getMessage(), e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "执行失败: " + e.getMessage());
            return result;
        }
    }

    @Override
    public List<Map<String, Object>> getNodeTemplates() {
        log.info("获取节点配置模板");
        
        List<Map<String, Object>> templates = new ArrayList<>();
        
        // 脚本节点模板
        Map<String, Object> scriptTemplate = new HashMap<>();
        scriptTemplate.put("nodeType", "SCRIPT");
        scriptTemplate.put("name", "脚本节点");
        scriptTemplate.put("description", "执行自定义脚本");
        Map<String, Object> scriptConfig = new HashMap<>();
        scriptConfig.put("scriptId", "");
        scriptConfig.put("timeout", 30000);
        scriptTemplate.put("configTemplate", scriptConfig);
        templates.add(scriptTemplate);
        
        // API调用节点模板
        Map<String, Object> apiTemplate = new HashMap<>();
        apiTemplate.put("nodeType", "API");
        apiTemplate.put("name", "API调用节点");
        apiTemplate.put("description", "调用外部API接口");
        Map<String, Object> apiConfig = new HashMap<>();
        apiConfig.put("url", "");
        apiConfig.put("method", "POST");
        apiConfig.put("headers", new HashMap<>());
        apiConfig.put("timeout", 30000);
        apiTemplate.put("configTemplate", apiConfig);
        templates.add(apiTemplate);
        
        // 规则节点模板
        Map<String, Object> ruleTemplate = new HashMap<>();
        ruleTemplate.put("nodeType", "RULE");
        ruleTemplate.put("name", "规则节点");
        ruleTemplate.put("description", "执行业务规则");
        Map<String, Object> ruleConfig = new HashMap<>();
        ruleConfig.put("ruleId", "");
        ruleTemplate.put("configTemplate", ruleConfig);
        templates.add(ruleTemplate);
        
        // 通知节点模板
        Map<String, Object> notificationTemplate = new HashMap<>();
        notificationTemplate.put("nodeType", "NOTIFICATION");
        notificationTemplate.put("name", "通知节点");
        notificationTemplate.put("description", "发送通知消息");
        Map<String, Object> notificationConfig = new HashMap<>();
        notificationConfig.put("type", "EMAIL");
        notificationConfig.put("recipients", new ArrayList<>());
        notificationConfig.put("template", "");
        notificationTemplate.put("configTemplate", notificationConfig);
        templates.add(notificationTemplate);
        
        return templates;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateNodeOrder(Long bizId, List<Map<String, Object>> sortData) {
        log.info("更新节点执行顺序, bizId: {}, 节点数: {}", bizId, sortData.size());
        
        for (Map<String, Object> item : sortData) {
            String nodeId = (String) item.get("nodeId");
            Integer orderNum = (Integer) item.get("orderNum");
            
            // 查找节点
            List<BusinessNodeDO> nodes = businessNodeMapper.selectByBizId(bizId);
            BusinessNodeDO node = nodes.stream()
                    .filter(n -> n.getNodeId().equals(nodeId))
                    .findFirst()
                    .orElse(null);
            
            if (node != null) {
                node.setOrderNum(orderNum);
                businessNodeMapper.updateById(node);
            }
        }
    }

    @Override
    public void toggleNodeStatus(Long bizId, String nodeId, Boolean enabled) {
        log.info("切换节点状态, bizId: {}, nodeId: {}, enabled: {}", bizId, nodeId, enabled);
        
        // 查找节点
        List<BusinessNodeDO> nodes = businessNodeMapper.selectByBizId(bizId);
        BusinessNodeDO node = nodes.stream()
                .filter(n -> n.getNodeId().equals(nodeId))
                .findFirst()
                .orElseThrow(() -> new BizException("5001", "节点不存在: " + nodeId));
        
        node.setEnabled(enabled);
        businessNodeMapper.updateById(node);
    }

    @Override
    public Map<String, Object> executeNodesByEvent(Long bizId, String triggerEvent, String taskDefinitionKey, 
                                                  Long mainId, Map<String, Object> data) {
        log.info("执行业务节点, bizId: {}, triggerEvent: {}, taskDefinitionKey: {}", bizId, triggerEvent, taskDefinitionKey);
        
        Map<String, Object> processedData = new HashMap<>(data);
        
        // 获取符合条件的节点
        List<BusinessNodeDO> nodes = businessNodeMapper.selectByBizIdEventAndOperation(bizId, triggerEvent, taskDefinitionKey);
        if (ObjUtilX.isEmpty(nodes)) {
            log.info("未找到匹配的业务节点, bizId: {}, triggerEvent: {}, taskDefinitionKey: {}", bizId, triggerEvent, taskDefinitionKey);
            return processedData;
        }
        
        // 按顺序执行节点
        for (BusinessNodeDO node : nodes) {
            if (!node.getEnabled()) {
                log.info("节点已禁用, 跳过执行: {}", node.getNodeName());
                continue;
            }
            
            // 检查条件是否满足
            if (!checkNodeCondition(node, processedData)) {
                log.info("节点条件不满足, 跳过执行: {}", node.getNodeName());
                continue;
            }
            
            // 执行节点
            try {
                log.info("开始执行节点: {}, 类型: {}", node.getNodeName(), node.getNodeType());
                Map<String, Object> nodeResult = executeNode(node, mainId, processedData);
                
                if (nodeResult != null) {
                    // 处理动态参数 - 直接合并到processedData中
                    if (nodeResult.containsKey("dynamicParams")) {
                        Map<String, Object> dynamicParams = (Map<String, Object>) nodeResult.get("dynamicParams");
                        processedData.putAll(dynamicParams);
                        log.info("动态参数已合并到数据中: {}", dynamicParams.keySet());
                    }
                    
                    // 合并其他业务数据到processedData中
                    for (Map.Entry<String, Object> entry : nodeResult.entrySet()) {
                        if (!"dynamicParams".equals(entry.getKey()) && 
                            !"input_params".equals(entry.getKey()) && 
                            !"success".equals(entry.getKey()) && 
                            !"message".equals(entry.getKey())) {
                            processedData.put(entry.getKey(), entry.getValue());
                            log.info("合并节点数据: {} = {}", entry.getKey(), entry.getValue());
                        }
                    }
                }
            } catch (Exception e) {
                log.error("执行节点失败: {}, error: {}", node.getNodeName(), e.getMessage(), e);
                // 节点执行失败，抛出异常终止整个流程
                throw new BizException("5001", "节点执行失败[" + node.getNodeName() + "]: " + e.getMessage());
            }
        }
        
        log.info("节点执行完成，最终数据字段: {}", processedData.keySet());
        return processedData;
    }

    /**
     * 检查节点条件是否满足
     */
    private boolean checkNodeCondition(BusinessNodeDO node, Map<String, Object> data) {
        if (ObjUtilX.isEmpty(node.getCondition())) {
            return true;
        }
        
        try {
            Map<String, Object> condition = JSON.parseObject(node.getCondition(), Map.class);
            
            // 支持多种条件检查方式
            if (condition.containsKey("field") && condition.containsKey("value")) {
                String field = (String) condition.get("field");
                Object expectedValue = condition.get("value");
                Object actualValue = data.get(field);
                String operator = (String) condition.getOrDefault("operator", "equals");
                
                return checkConditionByOperator(actualValue, expectedValue, operator);
            }
            
            // 支持多条件组合（AND/OR）
            if (condition.containsKey("conditions")) {
                List<Map<String, Object>> conditions = (List<Map<String, Object>>) condition.get("conditions");
                String logic = (String) condition.getOrDefault("logic", "AND");
                
                if ("OR".equals(logic)) {
                    return conditions.stream().anyMatch(cond -> checkSingleCondition(cond, data));
                } else {
                    return conditions.stream().allMatch(cond -> checkSingleCondition(cond, data));
                }
            }
            
            return true;
        } catch (Exception e) {
            log.error("检查节点条件失败: {}, error: {}", node.getNodeName(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 检查单个条件
     */
    private boolean checkSingleCondition(Map<String, Object> condition, Map<String, Object> data) {
        String field = (String) condition.get("field");
        Object expectedValue = condition.get("value");
        Object actualValue = data.get(field);
        String operator = (String) condition.getOrDefault("operator", "equals");
        
        return checkConditionByOperator(actualValue, expectedValue, operator);
    }

    /**
     * 根据操作符检查条件
     */
    private boolean checkConditionByOperator(Object actualValue, Object expectedValue, String operator) {
        if (actualValue == null && expectedValue == null) {
            return true;
        }
        if (actualValue == null || expectedValue == null) {
            return "is_null".equals(operator) ? expectedValue == null : 
                   "is_not_null".equals(operator) ? actualValue != null : false;
        }
        
        switch (operator) {
            case "equals":
            case "eq":
                return expectedValue.equals(actualValue);
            case "not_equals":
            case "ne":
                return !expectedValue.equals(actualValue);
            case "contains":
                return actualValue.toString().contains(expectedValue.toString());
            case "not_contains":
                return !actualValue.toString().contains(expectedValue.toString());
            case "starts_with":
                return actualValue.toString().startsWith(expectedValue.toString());
            case "ends_with":
                return actualValue.toString().endsWith(expectedValue.toString());
            case "is_empty":
                return actualValue.toString().trim().isEmpty();
            case "is_not_empty":
                return !actualValue.toString().trim().isEmpty();
            case "gt":
                return compareNumbers(actualValue, expectedValue) > 0;
            case "gte":
                return compareNumbers(actualValue, expectedValue) >= 0;
            case "lt":
                return compareNumbers(actualValue, expectedValue) < 0;
            case "lte":
                return compareNumbers(actualValue, expectedValue) <= 0;
            case "in":
                if (expectedValue instanceof List) {
                    return ((List<?>) expectedValue).contains(actualValue);
                }
                return false;
            case "not_in":
                if (expectedValue instanceof List) {
                    return !((List<?>) expectedValue).contains(actualValue);
                }
                return true;
            default:
                log.warn("不支持的操作符: {}", operator);
                return expectedValue.equals(actualValue);
        }
    }

    /**
     * 数字比较
     */
    private int compareNumbers(Object value1, Object value2) {
        try {
            BigDecimal num1 = new BigDecimal(value1.toString());
            BigDecimal num2 = new BigDecimal(value2.toString());
            return num1.compareTo(num2);
        } catch (Exception e) {
            // 如果不是数字，按字符串比较
            return value1.toString().compareTo(value2.toString());
        }
    }
    
    /**
     * 执行节点
     */
    private Map<String, Object> executeNode(BusinessNodeDO node, Long mainId, Map<String, Object> data) {
        String nodeType = node.getNodeType();
        Map<String, Object> config = JSON.parseObject(node.getConfig(), Map.class);
        
        switch (nodeType) {
            case "SCRIPT":
                return executeScriptNode(config, mainId, data);
            case "API":
                return executeApiNode(config, mainId, data);
            case "RULE":
                return executeRuleNode(config, mainId, data);
            case "NOTIFICATION":
                return executeNotificationNode(config, mainId, data);
            default:
                log.warn("不支持的节点类型: {}", nodeType);
                return null;
        }
    }

    /**
     * 执行脚本节点
     */
    private Map<String, Object> executeScriptNode(Map<String, Object> config, Long mainId, Map<String, Object> data) {
        log.info("执行脚本节点, config: {}", config);
        String scriptId = (String) config.get("scriptId");
        Long bizId = Long.valueOf(config.get("bizId").toString());
        
        Map<String, Object> params = new HashMap<>(data);
        params.put("mainId", mainId);
        
        Object result = scriptExecutionService.executeScript(bizId, scriptId, params);
        
        // 处理脚本返回结果
        Map<String, Object> nodeResult = new HashMap<>();
        
        if (result instanceof String) {
            try {
                // 尝试解析JSON字符串
                Map<String, Object> jsonResult = JSON.parseObject((String) result, Map.class);
                
                // 检查是否包含动态参数
                if (jsonResult.containsKey("dynamicParams")) {
                    nodeResult.put("dynamicParams", jsonResult.get("dynamicParams"));
                    log.info("脚本返回动态参数: {}", jsonResult.get("dynamicParams"));
                }
                
                // 保留其他返回数据
                for (Map.Entry<String, Object> entry : jsonResult.entrySet()) {
                    if (!"dynamicParams".equals(entry.getKey())) {
                        nodeResult.put(entry.getKey(), entry.getValue());
                    }
                }
                
            } catch (Exception e) {
                log.warn("解析脚本返回结果失败，作为普通字符串处理: {}", e.getMessage());
                nodeResult.put("scriptResult", result);
            }
        } else if (result instanceof Map) {
            Map<String, Object> mapResult = (Map<String, Object>) result;
            nodeResult.putAll(mapResult);
        } else {
            nodeResult.put("scriptResult", result);
        }
        
        return nodeResult;
    }
    
    /**
     * 执行API调用节点
     */
    private Map<String, Object> executeApiNode(Map<String, Object> config, Long mainId, Map<String, Object> data) {
        log.info("执行API调用节点, config: {}", config);
        // TODO: 实现API调用逻辑
        return new HashMap<>();
    }
    
    /**
     * 执行规则节点
     */
    private Map<String, Object> executeRuleNode(Map<String, Object> config, Long mainId, Map<String, Object> data) {
        log.info("执行规则节点, config: {}", config);
        // TODO: 实现规则执行逻辑
        return new HashMap<>();
    }
    
    /**
     * 执行通知节点
     */
    private Map<String, Object> executeNotificationNode(Map<String, Object> config, Long mainId, Map<String, Object> data) {
        log.info("执行通知节点, config: {}", config);
        // TODO: 实现通知发送逻辑
        return new HashMap<>();
    }
    
    /**
     * 将DO转换为VO
     */
    private BusinessNodeConfigVO convertToVO(BusinessNodeDO nodeDO) {
        BusinessNodeConfigVO vo = new BusinessNodeConfigVO();
        vo.setNodeId(nodeDO.getNodeId());
        vo.setNodeName(nodeDO.getNodeName());
        vo.setNodeType(nodeDO.getNodeType());
        vo.setTriggerEvent(nodeDO.getTriggerEvent());
        vo.setTaskDefinitionKey(nodeDO.getTaskDefinitionKey());
        
        // 解析JSON配置
        if (ObjUtilX.isNotEmpty(nodeDO.getConfig())) {
            vo.setConfig(JSON.parseObject(nodeDO.getConfig(), Map.class));
        }
        if (ObjUtilX.isNotEmpty(nodeDO.getCondition())) {
            vo.setCondition(JSON.parseObject(nodeDO.getCondition(), Map.class));
        }
        
        vo.setOrderNum(nodeDO.getOrderNum());
        vo.setEnabled(nodeDO.getEnabled());
        return vo;
    }
    
    /**
     * 将VO转换为DO
     */
    private BusinessNodeDO convertToDO(Long bizId, BusinessNodeConfigVO vo) {
        BusinessNodeDO nodeDO = new BusinessNodeDO();
        nodeDO.setId(IDUtilX.getId());
        nodeDO.setBizId(bizId);
        nodeDO.setNodeId(vo.getNodeId());
        nodeDO.setNodeName(vo.getNodeName());
        nodeDO.setNodeType(vo.getNodeType());
        nodeDO.setTriggerEvent(vo.getTriggerEvent());
        nodeDO.setTaskDefinitionKey(vo.getTaskDefinitionKey());
        
        // 转换为JSON字符串
        if (vo.getConfig() != null) {
            nodeDO.setConfig(JSON.toJSONString(vo.getConfig()));
        }
        if (vo.getCondition() != null) {
            nodeDO.setCondition(JSON.toJSONString(vo.getCondition()));
        }
        
        nodeDO.setOrderNum(vo.getOrderNum() != null ? vo.getOrderNum() : 0);
        nodeDO.setEnabled(vo.getEnabled() != null ? vo.getEnabled() : true);
        return nodeDO;
    }
}
