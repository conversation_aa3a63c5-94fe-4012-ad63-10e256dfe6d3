package com.mongoso.mgs.module.business.dal.mysql;

import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.business.dal.db.BusinessNodeDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface BusinessNodeMapper extends BaseMapperX<BusinessNodeDO> {

    default List<BusinessNodeDO> selectByBizIdAndEvent(Long bizId, String event) {
        return selectList(LambdaQueryWrapperX.<BusinessNodeDO>lambdaQueryX()
                .eq(BusinessNodeDO::getBizId, bizId)
                .eq(BusinessNodeDO::getTriggerEvent, event)
                .eq(BusinessNodeDO::getEnabled, true)
                .orderByAsc(BusinessNodeDO::getOrderNum)
        );
    }

    default List<BusinessNodeDO> selectByBizIdEventAndOperation(Long bizId, String event, String operation) {
        LambdaQueryWrapperX<BusinessNodeDO> wrapper = LambdaQueryWrapperX.<BusinessNodeDO>lambdaQueryX()
                .eq(BusinessNodeDO::getBizId, bizId)
                .eq(BusinessNodeDO::getTriggerEvent, event)
                .eq(BusinessNodeDO::getEnabled, true)
                .eqIfPresent(BusinessNodeDO::getTaskDefinitionKey, operation);

        
        return selectList(wrapper.orderByAsc(BusinessNodeDO::getOrderNum));
    }

    default void deleteByBizId(Long bizId) {
        delete(LambdaQueryWrapperX.<BusinessNodeDO>lambdaQueryX()
                .eq(BusinessNodeDO::getBizId, bizId));
    }

    default List<BusinessNodeDO> selectByBizId(Long bizId) {
        return selectList(LambdaQueryWrapperX.<BusinessNodeDO>lambdaQueryX()
                .eq(BusinessNodeDO::getBizId, bizId)
                .orderByAsc(BusinessNodeDO::getOrderNum)
        );
    }
}