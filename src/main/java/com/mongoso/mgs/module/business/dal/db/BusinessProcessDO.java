package com.mongoso.mgs.module.business.dal.db;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 业务流程 DO
 */
@TableName("lowcode.sys_lowcode_business_process")
@Data
@EqualsAndHashCode(callSuper = true)
public class BusinessProcessDO extends OperateDO {

    @TableId
    private Long id;
    /**
     * 业务ID
     */
    private Long bizId;
    
    /**
     * 流程定义Key
     */
    private String processKey;
    
    /**
     * 流程配置 (JSON)
     */
    private String config;
    
    /**
     * 是否启用
     */
    private Boolean enabled;
}