package com.mongoso.mgs.module.business.controller.admin;

import com.mongoso.mgs.module.business.controller.vo.BusinessNodeConfigVO;
import com.mongoso.mgs.module.business.service.BusinessNodeService;
import com.mongoso.mgs.module.codegen.dal.pojo.ResultX;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/admin/business/node")
@Slf4j
public class BusinessNodeController {

    @Resource
    private BusinessNodeService businessNodeService;

    @GetMapping("/list/{bizId}")
    public ResultX<List<BusinessNodeConfigVO>> getBusinessNodes(@PathVariable Long bizId) {
        List<BusinessNodeConfigVO> nodes = businessNodeService.getNodesByBizId(bizId);
        return ResultX.success(nodes);
    }

    @PostMapping("/save/{bizId}")
    public ResultX<Boolean> saveBusinessNodes(
            @PathVariable Long bizId,
            @RequestBody List<BusinessNodeConfigVO> nodes) {
        businessNodeService.saveNodes(bizId, nodes);
        return ResultX.success(true);
    }

    @PostMapping("/add/{bizId}")
    public ResultX<Boolean> addBusinessNode(
            @PathVariable Long bizId,
            @RequestBody BusinessNodeConfigVO node) {
        businessNodeService.addNode(bizId, node);
        return ResultX.success(true);
    }

    @PutMapping("/update/{bizId}/{nodeId}")
    public ResultX<Boolean> updateBusinessNode(
            @PathVariable Long bizId,
            @PathVariable String nodeId,
            @RequestBody BusinessNodeConfigVO node) {
        businessNodeService.updateNode(bizId, nodeId, node);
        return ResultX.success(true);
    }

    @DeleteMapping("/delete/{bizId}/{nodeId}")
    public ResultX<Boolean> deleteBusinessNode(
            @PathVariable Long bizId,
            @PathVariable String nodeId) {
        businessNodeService.deleteNode(bizId, nodeId);
        return ResultX.success(true);
    }

    @PostMapping("/test/{bizId}/{nodeId}")
    public ResultX<Map<String, Object>> testBusinessNode(
            @PathVariable Long bizId,
            @PathVariable String nodeId,
            @RequestBody Map<String, Object> testData) {
        Map<String, Object> result = businessNodeService.testNode(bizId, nodeId, testData);
        return ResultX.success(result);
    }

    @GetMapping("/templates")
    public ResultX<List<Map<String, Object>>> getNodeTemplates() {
        List<Map<String, Object>> templates = businessNodeService.getNodeTemplates();
        return ResultX.success(templates);
    }

    @PostMapping("/sort/{bizId}")
    public ResultX<Boolean> sortNodes(
            @PathVariable Long bizId,
            @RequestBody List<Map<String, Object>> sortData) {
        businessNodeService.updateNodeOrder(bizId, sortData);
        return ResultX.success(true);
    }

    @PostMapping("/enable/{bizId}/{nodeId}")
    public ResultX<Boolean> toggleNodeStatus(
            @PathVariable Long bizId,
            @PathVariable String nodeId,
            @RequestParam Boolean enabled) {
        businessNodeService.toggleNodeStatus(bizId, nodeId, enabled);
        return ResultX.success(true);
    }
}