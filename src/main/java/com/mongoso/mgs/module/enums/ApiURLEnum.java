package com.mongoso.mgs.module.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * API接口常量
 * daijinbiao
 * 2025-3-28 17:32:26
 */
@AllArgsConstructor
@Getter
public enum ApiURLEnum {

    // 基本接口
    ADD("Add"),     //预留
    UPDATE("Update"),   //预留
    ADIT("Adit"),
    DELETE("Delete"),
    DETAIL("Detail"),
    LIST("List"),
    PAGE("Page"),

    // 通用接口
    BASE_PREFIX("baseapi"),     //通用接口前缀

    ;

    /**
     * 条件
     */
    private final String suffix;

}
