package com.mongoso.mgs.module.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 禁止DML的关键字枚举
 * <AUTHOR>
 * @date 2025/4/30
 * @description
 */
@AllArgsConstructor
@Getter
public enum ForbidKeyWordEnum {
    DELETE("DELETE"),
    DROP("DROP"),
    TRUNCATE("TRUNCATE"),
    ALTER("ALTER");

    /** 禁止关键字 */
    private final String keyword;

    /**
     * 检查给定 SQL 是否以此关键字开头
     */
    public boolean startWith(String sql) {
        return sql.trim().toUpperCase().startsWith(this.keyword);
    }
}
