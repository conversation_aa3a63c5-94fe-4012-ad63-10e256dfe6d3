package com.mongoso.mgs.module.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 数据源类型枚举
 * <AUTHOR>
 * @date 2020-03-11
 */
@Getter
@AllArgsConstructor
public enum DataTypeEnum {

    OBJECT("Object"),

    JSONOBJECT("Json"),

    STRING("String"),

    NUMBER("Number"),

    ARRAY("Array"),

    BOOL("Boolean"),

    OTHER("Other"),




    ;

    /**
     * 数据库类型
     */
    private final String value;
}
