package com.mongoso.mgs.module.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 数据源类型枚举
 * <AUTHOR>
 * @date 2020-03-11
 */
@Getter
@AllArgsConstructor
public enum DBOPTypeEnum {

    //操作类别 [建表，新增字段，修改字段，删除字段，修改表，删除表，清空数据]'
    CREATE_TABLE(0,"新增表"),
    ADD_FIELD(1,"新增字段"),
    UPD_FIELD(2,"修改字段"),
    DEL_FIELD(3,"删除字段"),
    ALTER_TABLE(4,"修改表"),//不支持修改
    DROP_TABLE(5,"删除表"),//无引用
    EMPTY_TABLE(6,"清空数据"),
    ADD_INDEX(7,"新增索引"),
    DROP_INDEX(8,"删除索引"),
    ALTER_INDEX(9,"修改索引"),

    ;

    /**
     * 数据库类型
     */
    private final Integer type;
    private final String desc;
}
