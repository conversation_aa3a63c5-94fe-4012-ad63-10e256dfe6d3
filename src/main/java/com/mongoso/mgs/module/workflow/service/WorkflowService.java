package com.mongoso.mgs.module.workflow.service;

import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.module.workflow.controller.vo.WorkflowStartReqVO;
import com.mongoso.mgs.module.workflow.controller.vo.WorkflowTaskRespVO;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.HistoryService;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.Task;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class WorkflowService {
    
    @Autowired
    private RuntimeService runtimeService;
    
    @Autowired
    private TaskService taskService;
    
    @Autowired
    private HistoryService historyService;
    
    @Autowired
    private RepositoryService repositoryService;
    
    /**
     * 启动流程
     */
    public String startProcess(String processKey, WorkflowStartReqVO reqVO) {
        try {
            log.info("启动工作流, processKey: {}, businessKey: {}", processKey, reqVO.getBusinessKey());
            
            // 验证流程定义是否存在
            if (!isProcessDefinitionExists(processKey)) {
                throw new BizException("5001", "流程定义不存在: " + processKey);
            }
            
            // 启动流程时关联业务数据
            Map<String, Object> variables = new HashMap<>();
            variables.put("tableId", reqVO.getTableId());
            variables.put("businessId", reqVO.getBusinessId());
            variables.put("formData", reqVO.getFormData());
            
            ProcessInstance processInstance = runtimeService.startProcessInstanceByKey(
                processKey, reqVO.getBusinessKey(), variables);
            
            log.info("工作流启动成功, processInstanceId: {}", processInstance.getId());
            return processInstance.getId();
            
        } catch (Exception e) {
            log.error("启动工作流失败, processKey: {}, error: {}", processKey, e.getMessage(), e);
            throw new BizException("5001", "启动工作流失败: " + e.getMessage());
        }
    }
    
    /**
     * 完成任务
     */
    public void completeTask(String taskId, Map<String, Object> variables) {
        try {
            log.info("完成任务, taskId: {}", taskId);
            
            // 验证任务是否存在
            Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
            if (task == null) {
                throw new BizException("5001", "任务不存在: " + taskId);
            }
            
            taskService.complete(taskId, variables);
            log.info("任务完成成功, taskId: {}", taskId);
            
        } catch (Exception e) {
            log.error("完成任务失败, taskId: {}, error: {}", taskId, e.getMessage(), e);
            throw new BizException("5001", "完成任务失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取用户待办任务
     */
    public List<WorkflowTaskRespVO> getUserTasks(String assignee) {
        if (!StringUtils.hasText(assignee)) {
            throw new BizException("5001", "用户ID不能为空");
        }
        
        List<Task> tasks = taskService.createTaskQuery()
                .taskAssignee(assignee)
                .orderByTaskCreateTime()
                .desc()
                .list();
        
        return tasks.stream().map(this::convertToTaskRespVO).collect(Collectors.toList());
    }
    
    /**
     * 获取流程实例的所有任务
     */
    public List<WorkflowTaskRespVO> getProcessTasks(String processInstanceId) {
        if (!StringUtils.hasText(processInstanceId)) {
            throw new BizException("5001", "流程实例ID不能为空");
        }
        
        List<Task> tasks = taskService.createTaskQuery()
                .processInstanceId(processInstanceId)
                .orderByTaskCreateTime()
                .desc()
                .list();
        
        return tasks.stream().map(this::convertToTaskRespVO).collect(Collectors.toList());
    }
    
    /**
     * 获取历史任务
     */
    public List<WorkflowTaskRespVO> getHistoryTasks(String processInstanceId) {
        if (!StringUtils.hasText(processInstanceId)) {
            throw new BizException("5001", "流程实例ID不能为空");
        }
        
        List<HistoricTaskInstance> historicTasks = historyService.createHistoricTaskInstanceQuery()
                .processInstanceId(processInstanceId)
                .finished()
                .orderByHistoricTaskInstanceEndTime()
                .desc()
                .list();
        
        return historicTasks.stream().map(this::convertHistoricToTaskRespVO).collect(Collectors.toList());
    }
    
    /**
     * 暂停流程实例
     */
    public void suspendProcessInstance(String processInstanceId) {
        try {
            runtimeService.suspendProcessInstanceById(processInstanceId);
            log.info("流程实例暂停成功, processInstanceId: {}", processInstanceId);
        } catch (Exception e) {
            log.error("暂停流程实例失败, processInstanceId: {}, error: {}", processInstanceId, e.getMessage(), e);
            throw new BizException("5001", "暂停流程实例失败: " + e.getMessage());
        }
    }
    
    /**
     * 激活流程实例
     */
    public void activateProcessInstance(String processInstanceId) {
        try {
            runtimeService.activateProcessInstanceById(processInstanceId);
            log.info("流程实例激活成功, processInstanceId: {}", processInstanceId);
        } catch (Exception e) {
            log.error("激活流程实例失败, processInstanceId: {}, error: {}", processInstanceId, e.getMessage(), e);
            throw new BizException("5001", "激活流程实例失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除流程实例
     */
    public void deleteProcessInstance(String processInstanceId, String deleteReason) {
        try {
            runtimeService.deleteProcessInstance(processInstanceId, deleteReason);
            log.info("流程实例删除成功, processInstanceId: {}, reason: {}", processInstanceId, deleteReason);
        } catch (Exception e) {
            log.error("删除流程实例失败, processInstanceId: {}, error: {}", processInstanceId, e.getMessage(), e);
            throw new BizException("5001", "删除流程实例失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查流程定义是否存在
     */
    private boolean isProcessDefinitionExists(String processKey) {
        return repositoryService.createProcessDefinitionQuery()
                .processDefinitionKey(processKey)
                .latestVersion()
                .singleResult() != null;
    }
    
    /**
     * 转换Task为VO
     */
    private WorkflowTaskRespVO convertToTaskRespVO(Task task) {
        WorkflowTaskRespVO vo = new WorkflowTaskRespVO();
        vo.setTaskId(task.getId());
        vo.setTaskName(task.getName());
        vo.setAssignee(task.getAssignee());
        vo.setProcessInstanceId(task.getProcessInstanceId());
        vo.setCreateTime(task.getCreateTime().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDateTime());
        
        // 获取任务变量
        try {
            Map<String, Object> variables = taskService.getVariables(task.getId());
            vo.setVariables(variables);
        } catch (Exception e) {
            log.warn("获取任务变量失败, taskId: {}, error: {}", task.getId(), e.getMessage());
            vo.setVariables(new HashMap<>());
        }
        
        return vo;
    }
    
    /**
     * 转换HistoricTaskInstance为VO
     */
    private WorkflowTaskRespVO convertHistoricToTaskRespVO(HistoricTaskInstance historicTask) {
        WorkflowTaskRespVO vo = new WorkflowTaskRespVO();
        vo.setTaskId(historicTask.getId());
        vo.setTaskName(historicTask.getName());
        vo.setAssignee(historicTask.getAssignee());
        vo.setProcessInstanceId(historicTask.getProcessInstanceId());
        vo.setCreateTime(historicTask.getCreateTime().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDateTime());
        
        // 历史任务的变量获取
        try {
            Map<String, Object> variables = historyService.createHistoricVariableInstanceQuery()
                    .taskId(historicTask.getId())
                    .list()
                    .stream()
                    .collect(Collectors.toMap(
                            var -> var.getVariableName(),
                            var -> var.getValue(),
                            (existing, replacement) -> existing
                    ));
            vo.setVariables(variables);
        } catch (Exception e) {
            log.warn("获取历史任务变量失败, taskId: {}, error: {}", historicTask.getId(), e.getMessage());
            vo.setVariables(new HashMap<>());
        }
        
        return vo;
    }

    /**
     * 获取任务变量
     */
    public Map<String, Object> getTaskVariables(String taskId) {
        try {
            // 验证任务是否存在
            Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
            if (task == null) {
                throw new BizException("5001", "任务不存在: " + taskId);
            }
            
            // 获取任务变量
            Map<String, Object> variables = taskService.getVariables(taskId);
            return variables != null ? variables : new HashMap<>();
            
        } catch (Exception e) {
            log.error("获取任务变量失败, taskId: {}, error: {}", taskId, e.getMessage(), e);
            throw new BizException("5001", "获取任务变量失败: " + e.getMessage());
        }
    }
}
