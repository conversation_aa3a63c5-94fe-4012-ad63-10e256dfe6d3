package com.mongoso.mgs.module.workflow.controller.vo;

import jakarta.validation.constraints.NotBlank;
import java.util.Map;

public class WorkflowTaskReqVO {
    
    @NotBlank(message = "任务ID不能为空")
    private String taskId;
    
    private Map<String, Object> variables;
    
    private String comment;
    
    // getters and setters
    public String getTaskId() { return taskId; }
    public void setTaskId(String taskId) { this.taskId = taskId; }
    
    public Map<String, Object> getVariables() { return variables; }
    public void setVariables(Map<String, Object> variables) { this.variables = variables; }
    
    public String getComment() { return comment; }
    public void setComment(String comment) { this.comment = comment; }
}