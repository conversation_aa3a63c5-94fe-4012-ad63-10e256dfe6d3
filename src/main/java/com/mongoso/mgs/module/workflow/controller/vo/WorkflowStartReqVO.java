package com.mongoso.mgs.module.workflow.controller.vo;

import jakarta.validation.constraints.NotNull;
import java.util.Map;

public class WorkflowStartReqVO {
    
    @NotNull(message = "表ID不能为空")
    private Long tableId;
    
    @NotNull(message = "业务ID不能为空")
    private String businessId;
    
    private String businessKey;
    
    private Map<String, Object> formData;
    
    // getters and setters
    public Long getTableId() { return tableId; }
    public void setTableId(Long tableId) { this.tableId = tableId; }
    
    public String getBusinessId() { return businessId; }
    public void setBusinessId(String businessId) { this.businessId = businessId; }
    
    public String getBusinessKey() { return businessKey; }
    public void setBusinessKey(String businessKey) { this.businessKey = businessKey; }
    
    public Map<String, Object> getFormData() { return formData; }
    public void setFormData(Map<String, Object> formData) { this.formData = formData; }
}