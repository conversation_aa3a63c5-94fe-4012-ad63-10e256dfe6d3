package com.mongoso.mgs.module.workflow.controller;

import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.module.workflow.controller.vo.WorkflowStartReqVO;
import com.mongoso.mgs.module.workflow.controller.vo.WorkflowTaskReqVO;
import com.mongoso.mgs.module.workflow.controller.vo.WorkflowTaskRespVO;
import com.mongoso.mgs.module.workflow.service.WorkflowService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/workflow")
@Validated
public class WorkflowController {
    
    @Autowired
    private WorkflowService workflowService;
    
    // 启动工作流
    @PostMapping("/start/{processKey}")
    public ResultX<String> startProcess(@PathVariable String processKey,
                                       @RequestBody @Valid WorkflowStartReqVO reqVO) {
        String processInstanceId = workflowService.startProcess(processKey, reqVO);
        return ResultX.success(processInstanceId);
    }
    
    // 完成任务
    @PostMapping("/task/complete")
    public ResultX<Boolean> completeTask(@RequestBody @Valid WorkflowTaskReqVO reqVO) {
        workflowService.completeTask(reqVO.getTaskId(), reqVO.getVariables());
        return ResultX.success(true);
    }
    
    // 获取用户待办任务
    @GetMapping("/tasks/user/{assignee}")
    public ResultX<List<WorkflowTaskRespVO>> getUserTasks(@PathVariable String assignee) {
        List<WorkflowTaskRespVO> tasks = workflowService.getUserTasks(assignee);
        return ResultX.success(tasks);
    }
    
    // 获取流程任务列表
    @GetMapping("/tasks/process/{processInstanceId}")
    public ResultX<List<WorkflowTaskRespVO>> getProcessTasks(@PathVariable String processInstanceId) {
        List<WorkflowTaskRespVO> tasks = workflowService.getProcessTasks(processInstanceId);
        return ResultX.success(tasks);
    }
    
    // 获取历史任务
    @GetMapping("/tasks/history/{processInstanceId}")
    public ResultX<List<WorkflowTaskRespVO>> getHistoryTasks(@PathVariable String processInstanceId) {
        List<WorkflowTaskRespVO> tasks = workflowService.getHistoryTasks(processInstanceId);
        return ResultX.success(tasks);
    }
}
