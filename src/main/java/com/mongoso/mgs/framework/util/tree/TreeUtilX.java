package com.mongoso.mgs.framework.util.tree;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Auther: wangzhong
 * @Date: 2019/9/12 15:21
 * @Description: 树形结构
 */
public class TreeUtilX {

    // 将 List 转换为树形结构
    public static <T extends TreeItem<T>> List<T> listToTree(List<T> list) {
        return listToTree(list, null);
    }

    // 将 List 转换为树形结构
    public static <T extends TreeItem<T>> List<T> listToTree(List<T> list, String parentItemId) {
        if (parentItemId == null || parentItemId.isEmpty()) {
            parentItemId = "0";
        }
        // 用递归找子节点
        List<T> treeList = new ArrayList<>();
        for (T node : list) {
            if (parentItemId.equals(node.getParentItemId())) {
                node.setLevel(0); // 设置顶级节点的层级
                treeList.add(findChildren(node, list, 1));
            }
        }
        return treeList;
    }

    // 寻找子节点
    private static <T extends TreeItem<T>> T findChildren(T tree, List<T> list, int level) {
        for (T node : list) {
            if (node.getParentItemId().equals(tree.getItemId())) {
                node.setLevel(level); // 设置当前节点的层级
                tree.getChildren().add(findChildren(node, list, level + 1));
            }
        }
        return tree;
    }

    public static <T extends TreeItem<T>> List<T> getListByPid(List<T> tree, String pid) {
        List<T> list = new ArrayList<>();
        if (pid == null || pid.isEmpty()) {
            return list;
        }
        for (T t : tree) {
            if (t.getParentItemId().equals(pid)) {
                list.add(t);
            }
        }
        return list;
    }

    // 查询当前节点的所有父级名称（默认包括自己），使用 "/" 拼接
    public static <T extends TreeItem<T>> String getParentNames(List<T> tree, String itemId) {
        return getParentNames(tree, itemId, true);
    }

    // 查询当前节点的所有父级名称（可选择包括自己），使用 "/" 拼接
    public static <T extends TreeItem<T>> String getParentNames(List<T> tree, String itemId, boolean includeSelf) {
        // 获取当前节点的所有父级
        List<T> parents = getParent(tree, itemId);

        // 构建父级名称列表
        List<String> parentNames = new ArrayList<>();
        for (T parent : parents) {
            parentNames.add(parent.getItemName()); // 假设你的 TreeItem<T> 有一个 getName() 方法返回名称
        }

        // 如果需要包括自身的名称，则获取当前节点并添加
        if (includeSelf) {
            Map<String, T> nodeMap = new HashMap<>();
            buildNodeMap(tree, nodeMap); // 通过辅助方法构建节点映射
            T currentNode = nodeMap.get(itemId);
            if (currentNode != null) {
                parentNames.add(currentNode.getItemName());
            }
        }

        // 使用 "/" 拼接父级名称
        return String.join("/", parentNames);
    }


    // 查询当前节点的所有父级（不包括自己）
    public static <T extends TreeItem<T>> List<String> getParentIdList(List<T> tree, String itemId) {
        List<T> parent = getParent(tree, itemId);
        return parent.stream().map(TreeItem::getItemId).collect(Collectors.toList());
    }

    // 查询当前节点的所有父级（不包括自己）
    public static <T extends TreeItem<T>> List<T> getParent(List<T> tree, String itemId) {
        List<T> list = new ArrayList<>();
        if (itemId == null || itemId.isEmpty()) {
            return list;
        }

        Map<String, T> nodeMap = new HashMap<>();

        // 创建一个映射，以便快速查找节点
        buildNodeMap(tree, nodeMap);

        // 查找目标节点
        T currentNode = nodeMap.get(itemId);

        // 从目标节点开始，往上查找父级
        while (currentNode != null && currentNode.getParentItemId() != null) {
            currentNode = nodeMap.get(currentNode.getParentItemId());
            if (currentNode != null) {
                list.add(currentNode); // 添加父节点到结果列表
            }
        }

        // 倒序
        Collections.reverse(list);

        return list;
    }

    // 查询当前节点的所有子集（不包括自己）
    public static <T extends TreeItem<T>> List<String> getChildrenIdList(List<T> tree, String itemId) {
        List<T> list = getChildren(tree, itemId);
        return list.stream().map(TreeItem::getItemId).collect(Collectors.toList());
    }

    // 查询当前节点的所有子集（不包括自己）
    public static <T extends TreeItem<T>> List<T> getChildren(List<T> tree, String itemId) {

        List<T> list = new ArrayList<>();
        if (itemId == null || itemId.isEmpty()) {
            return list;
        }

        Map<String, T> nodeMap = new HashMap<>();
        // 创建一个映射，以便快速查找节点
        buildNodeMap(tree, nodeMap);

        // 查找目标节点
        T currentNode = nodeMap.get(itemId);
        if (currentNode != null) {
            // 收集子节点，排除自身
            collectChildren(currentNode, list);
        }
        return list;
    }

    // 收集子节点的辅助方法
    private static <T extends TreeItem<T>> void collectChildren(T node, List<T> childList) {
        for (T child : node.getChildren()) {
            childList.add(child); // 添加直接子节点
            // 递归收集间接子节点
            collectChildren(child, childList);
        }
    }

    // 辅助方法：构建节点映射
    private static <T extends TreeItem<T>> void buildNodeMap(List<T> tree, Map<String, T> nodeMap) {
        for (T node : tree) {
            nodeMap.put(node.getItemId(), node);
            buildNodeMap(node.getChildren(), nodeMap); // 递归处理子节点
        }
    }

    // 删除指定节点
    public static <T extends TreeItem<T>> void deleteNode(List<T> nodes, Collection<String> targets) {
        if (nodes == null || nodes.isEmpty() || targets.isEmpty()) {
            return;
        }

        // 遍历每个节点
        for (T node : nodes) {
            List<T> toRemove = new ArrayList<>(); // 用于保存要删除的子节点
            List<T> parentsToRemove = new ArrayList<>(); // 用于保存需要删除的父节点

            // 检查当前节点的子节点是否在目标列表中
            for (T child : node.getChildren()) {
                if (targets.contains(child.getItemId())) {
                    toRemove.add(child); // 找到要删除的节点
                    parentsToRemove.add(node); // 找到父节点，标记为删除
                }
            }

            // 移除找到的子节点
            node.getChildren().removeAll(toRemove);

            // 如果父节点在待删除列表中，也要移除父节点自身
            if (parentsToRemove.contains(node) && !targets.contains(node.getItemId())) {
                // 这个条件可以确保不会误删除根节点
                continue;
            }

            // 递归删除剩余子节点中的目标节点
            deleteNode(node.getChildren(), targets);
        }
    }


    /**
     * 计算树的最深深度
     */
    public static <T extends TreeItem<T>> int getMaxLevelByCode(List<T> nodes, String itemCode) {
        int maxDepth = -1;
        for (T node : nodes) {
            int depth = findDepth(node, itemCode, 0);
            if (depth > maxDepth) {
                maxDepth = depth; // 更新最大深度
            }
        }
        return maxDepth; // 返回最大深度
    }

    /**
     * 递归
     * 计算树的最深深度
     */
    private static <T extends TreeItem<T>> int findDepth(T node, String itemCode, int currentDepth) {
        if (node == null) {
            return -1; // 如果节点为空，返回-1表示未找到
        }

        int maxDepth = -1; // 初始化为-1，以处理未找到的情况

        // 检查当前节点的 itemId 是否与目标 itemId 匹配
        if (node.getItemCode().equals(itemCode)) {
            maxDepth = currentDepth; // 找到匹配项，更新最大深度
        }

        // 遍历子节点，递归计算深度
        for (T child : node.getChildren()) {
            int childDepth = findDepth(child, itemCode, currentDepth + 1);
            if (childDepth > maxDepth) {
                maxDepth = childDepth; // 更新最大深度
            }
        }

        return maxDepth; // 返回最大深度
    }

}
