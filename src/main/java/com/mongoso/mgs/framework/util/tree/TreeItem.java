package com.mongoso.mgs.framework.util.tree;

import java.util.ArrayList;
import java.util.List;

public class TreeItem<T> {

    private String itemId; // 节点ID
    private String itemCode; // 节点编码
    private String itemName; // 节点名称
    private Integer itemType;// 节点类型
    private String parentItemId; // 父节点ID
    private Integer level; // 层级
    private List<T> children = new ArrayList<>(); // 子节点列表

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }


//    private String itemId;
//    private String itemCode;
//    private String itemName;
//    private String parentItemId;
//    private String parentItemIds;
//    private Integer itemType;
//    private String itemNumber;


    public void setItemId(String itemId) {
        this.itemId = itemId;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public void setParentItemId(String parentItemId) {
        this.parentItemId = parentItemId;
    }

    public void setChildren(List<T> children) {
        this.children = children;
    }

    public void setItemType(Integer itemType) {
        this.itemType = itemType;
    }

    public String getItemId() {
        return itemId;
    }

    public String getItemCode() {
        return itemCode;
    }

    public String getItemName() {
        return itemName;
    }

    public Integer getItemType() {
        return itemType;
    }

    public String getParentItemId() {
        return parentItemId;
    }

    public List<T> getChildren() {
        return children;
    }


}
