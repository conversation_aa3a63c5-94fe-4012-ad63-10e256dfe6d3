package com.mongoso.mgs.framework.dal.db.permission;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 菜单表
 * 表的实体类，参数和表字段一一对应
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-21 15:04:16
 */
@Data
@TableName("s_app_auth_menu")
public class AppAuthMenuDO implements Comparable<Object>{

    private static final long serialVersionUID = 1L;

    /**
     * 菜单id
     */
    @TableId
	private String menuId;
	private Long menuSeq;
    /**
     * 菜单名称
     */
	private String menuName;
    /**
     * 父级ID
     */
	private String parentItemId;
    /**
     * 父级IDS
     */
	private String parentItemIds;
    /**
     * 前端按钮
     */
    private String buttonName;
    /**
     * 后端权限
     */
	private String permissions;
    /**
     * 前端路由
     */
	private String path;
    private String component;//前端组件地址
    private String componentName;//前端组件name
    /**
     * 是否隐藏
     */
	private Integer hidden;
    /**
     * 类型
     */
	private Integer type;
    /**
     * 排序
     */
	private Integer sort;
    /**
     * 图标
     */
	private String icon;

    /**
     * 创建时间
     */
    private LocalDateTime createdDt;
    /**
     * 更新时间
     */
    private LocalDateTime updatedDt;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 更新人
     */
    private String updatedBy;
    /**
     * 项目名
     */
    private String projectName;
    private Integer category;


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        AppAuthMenuDO menuEntity = (AppAuthMenuDO) o;
        return Objects.equals(menuId, menuEntity.menuId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(menuId);
    }

    @Override
    public int compareTo(Object arg0) {
        AppAuthMenuDO sysMenu = (AppAuthMenuDO)arg0;
        // 根据 sort 排序
        return sort.compareTo(sysMenu.getSort());
    }

}
