package com.mongoso.mgs.framework.dal.mysql.permission;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mongoso.mgs.framework.dal.db.permission.AuthRoleMenuDO;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 角色菜单表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-21 15:04:16
 */
@Mapper
public interface AuthRoleMenuMapper extends BaseMapperX<AuthRoleMenuDO> {

    int addAll(@Param("roleId") Long roleId, @Param("menuIdList") List<String> menuIdList);

    default int deleteByRoleId(Long roleId){
        return this.delete(new LambdaQueryWrapper<AuthRoleMenuDO>()
                        .eq(AuthRoleMenuDO::getRoleId,roleId));
    }

    default List<AuthRoleMenuDO> selectList(Long roleId){
        return this.selectList(new LambdaQueryWrapper<AuthRoleMenuDO>()
                .eq(AuthRoleMenuDO::getRoleId,roleId));
    }

    default AuthRoleMenuDO selectOneBy(Long roleId, String menuId){
        return selectOne(new LambdaQueryWrapper<AuthRoleMenuDO>()
                .eq(AuthRoleMenuDO::getRoleId,roleId)
                .eq(AuthRoleMenuDO::getMenuId,menuId));
    }

    default List<AuthRoleMenuDO> selectListByRoleId(Long copyRoleId){
        return selectList(new LambdaQueryWrapper<AuthRoleMenuDO>()
                .eq(AuthRoleMenuDO::getRoleId,copyRoleId));
    }

}
