package com.mongoso.mgs.framework.dal.db.permission;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.mongoso.mgs.framework.mybatis.core.pojo.BaseDO;
import lombok.Data;

import java.util.List;

/**
 * 角色表
 * 表的实体类，参数和表字段一一对应
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-21 15:04:16
 */
@Data
@TableName("s_auth_role")
//@KeySequence("s_auth_role_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
public class AuthRoleDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 角色ID
     */
    @TableId(type = IdType.ASSIGN_ID)
	private Long roleId;
    /**
     * 角色名称
     */
	private String roleName;
    /**
     * 角色编码
     */
	private String roleCode;
    /**
     * 备注
     */
	private String remark;
    /**
     * 是否启用
     */
	private Integer enable;


    /**
     * 角色对应的菜单
     */
    @JsonIgnore
    @TableField(exist = false)
    private List<AuthMenuDO> menus;
}
