package com.mongoso.mgs.framework.dal.mysql.permission;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mongoso.mgs.framework.dal.db.permission.AppAuthRoleMenuDO;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 角色菜单表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-21 15:04:16
 */
@Mapper
public interface AppAuthRoleMenuMapper extends BaseMapperX<AppAuthRoleMenuDO> {

    int addAll(@Param("roleId") Long roleId, @Param("menuIdList") List<String> menuIdList);

    default int deleteByRoleId(Long roleId){
        return this.delete(new LambdaQueryWrapper<AppAuthRoleMenuDO>()
                        .eq(AppAuthRoleMenuDO::getRoleId,roleId));
    }

    default List<AppAuthRoleMenuDO> selectList(Long roleId){
        return this.selectList(new LambdaQueryWrapper<AppAuthRoleMenuDO>()
                .eq(AppAuthRoleMenuDO::getRoleId,roleId));
    }
}
