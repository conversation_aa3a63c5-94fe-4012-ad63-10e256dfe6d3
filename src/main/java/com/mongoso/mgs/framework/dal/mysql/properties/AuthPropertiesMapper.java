package com.mongoso.mgs.framework.dal.mysql.properties;

import com.mongoso.mgs.framework.dal.db.properties.AuthPropertiesDO;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface AuthPropertiesMapper extends BaseMapperX<AuthPropertiesDO> {
    default List<AuthPropertiesDO> selectList() {
        return selectList(LambdaQueryWrapperX.<AuthPropertiesDO>lambdaQueryX()
                .orderByDesc(AuthPropertiesDO::getPropPkId));
    }

}
