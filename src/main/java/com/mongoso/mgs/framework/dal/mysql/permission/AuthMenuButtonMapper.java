package com.mongoso.mgs.framework.dal.mysql.permission;

import com.mongoso.mgs.framework.dal.db.permission.AuthMenuButtonDO;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 菜单表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-21 15:04:16
 */
@Mapper
public interface AuthMenuButtonMapper extends BaseMapperX<AuthMenuButtonDO> {

    default int deleteByMenuId(String menuId){
        return delete(LambdaQueryWrapperX.<AuthMenuButtonDO>lambdaQueryX()
                .eq(AuthMenuButtonDO::getMenuId, menuId));
    }

    default List<AuthMenuButtonDO> selectByMenuId(String menuId){
        return selectList(LambdaQueryWrapperX.<AuthMenuButtonDO>lambdaQueryX()
                .eq(AuthMenuButtonDO::getMenuId, menuId));
    }

    default List<AuthMenuButtonDO> selectListByCategory(Integer category){
        return selectList(LambdaQueryWrapperX.<AuthMenuButtonDO>lambdaQueryX()
                .eq(AuthMenuButtonDO::getCategory, category));
    }


    default List<AuthMenuButtonDO> selectListByCategorys(List<Integer> categorys){
        return selectList(LambdaQueryWrapperX.<AuthMenuButtonDO>lambdaQueryX()
                .in(AuthMenuButtonDO::getCategory, categorys));
    }

}
