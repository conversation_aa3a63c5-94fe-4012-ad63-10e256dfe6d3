package com.mongoso.mgs.framework.dal.db.appitem;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mongoso.mgs.framework.mybatis.core.pojo.BaseDO;
import lombok.*;

/**
 * 应用详情 DO
 *
 * <AUTHOR>
 */
@TableName("a_app_item")
//@KeySequence("a_app_item_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AppItem1DO extends BaseDO {

    /** 应用id */
    @TableId(type = IdType.ASSIGN_ID)
    private Integer appId;

    /** 应用名称 */
    private String appName;

    /** 项目名 */
    private String projectName;

    /** 域名 */
    private String domain;

    /** 应用类型 */
    private String appType;

    /** 应用版本id */
    private Integer appItemVersionId;

    /** 环境 */
    private String environment;

    /** 应用图标 */
    private String appIcon;

    /** 是否启用 */
    private Integer enable;

    /** 备注 */
    private String remark;


}
