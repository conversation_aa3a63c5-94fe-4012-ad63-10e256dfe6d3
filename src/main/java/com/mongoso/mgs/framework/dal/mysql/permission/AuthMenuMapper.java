package com.mongoso.mgs.framework.dal.mysql.permission;

import com.mongoso.mgs.framework.controller.admin.permission.vo.menu.AuthMenuQueryReqVO;
import com.mongoso.mgs.framework.dal.db.permission.AuthMenuDO;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * 菜单表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-21 15:04:16
 */
@Mapper
public interface AuthMenuMapper extends BaseMapperX<AuthMenuDO> {

    Collection<AuthMenuDO> getMenuByRoleId(@Param("roleIds") List<Long> roleIds,
                                              @Param("category") Integer category);

    Long max();

    default List<AuthMenuDO> selectList(AuthMenuQueryReqVO req){
        return selectList(LambdaQueryWrapperX.<AuthMenuDO>lambdaQueryX()
                .eqIfPresent(AuthMenuDO::getParentItemId, req.getMenuId())
                .inIfPresent(AuthMenuDO::getMenuId, req.getPidSet())
                .inIfPresent(AuthMenuDO::getType, req.getTypeList())
                .orderByDesc(AuthMenuDO::getSort));
    }

    default long countByPid(String menuId){
        return selectCount(LambdaQueryWrapperX.<AuthMenuDO>lambdaQueryX()
                .eq(AuthMenuDO::getParentItemId, menuId));
    }

    List<String> selectChildrenMenuList(@Param("menuId") String menuId,
                                        @Param("projectName") String projectName,
                                        @Param("category") Integer category);

    default List<AuthMenuDO> selectListInId(Set<String> pidSet,String projectName,Integer category){
        return selectList(LambdaQueryWrapperX.<AuthMenuDO>lambdaQueryX()
                .eq(AuthMenuDO::getProjectName, projectName)
                .eq(AuthMenuDO::getCategory, category)
                .eq(AuthMenuDO::getHidden, 0)
                .in(AuthMenuDO::getMenuId, pidSet));
    }

    List<String> selectListByButton(@Param("type") int type);

    /**
     * 查询菜单数量
     * @param projectName
     * @param userId
     * @return
     */
    Integer queryMenuCountByUserId(@Param("projectName") String projectName, @Param("userId") Long userId);

    default List<AuthMenuDO> selectListOrderSort(Integer type,String projectName,Integer category){
        return selectList(LambdaQueryWrapperX.<AuthMenuDO>lambdaQueryX()
                .eqIfPresent(AuthMenuDO::getProjectName, projectName)
                .eq(AuthMenuDO::getCategory, category)
                .eq(AuthMenuDO::getHidden, 0)
                .le(AuthMenuDO::getType, type)
                .orderByAsc(AuthMenuDO::getSort));
    }

    default long selectCountByName(String projectName){
        return selectCount(LambdaQueryWrapperX.<AuthMenuDO>lambdaQueryX()
                .eq(AuthMenuDO::getMenuName, projectName));
    }

    /**
     * 查询最顶级的不隐藏的菜单
     */
    default List<AuthMenuDO> selectListByPid(String pid){
        return selectList(LambdaQueryWrapperX.<AuthMenuDO>lambdaQueryX()
                .eq(AuthMenuDO::getParentItemId, pid)
                .eq(AuthMenuDO::getHidden, 0)
                .orderByAsc(AuthMenuDO::getSort)
        );
    }

    /**
     * 查询不隐藏的菜单
     */
    default List<AuthMenuDO> selectListByNotHidden(Integer category){
        return selectList(LambdaQueryWrapperX.<AuthMenuDO>lambdaQueryX()
                .eq(AuthMenuDO::getCategory, category)
                .eq(AuthMenuDO::getHidden, 0)
                .orderByAsc(AuthMenuDO::getSort));
    }

    default int deleteByMenuId(String menuId){
        return delete(LambdaQueryWrapperX.<AuthMenuDO>lambdaQueryX()
                .eq(AuthMenuDO::getMenuId, menuId));
    }

    default int deleteByCategory(Integer category){
        return delete(LambdaQueryWrapperX.<AuthMenuDO>lambdaQueryX()
                .eq(AuthMenuDO::getCategory, category));
    }

    default List<AuthMenuDO> selectListByCategory(Integer category){
        return selectList(LambdaQueryWrapperX.<AuthMenuDO>lambdaQueryX()
                .eq(AuthMenuDO::getCategory, category));
    }

    default List<AuthMenuDO> selectListByCategorys(List<Integer> categorys){
        return selectList(LambdaQueryWrapperX.<AuthMenuDO>lambdaQueryX()
                .in(AuthMenuDO::getCategory, categorys));
    }

    Integer sort(@Param("category") Integer category);

    default Long selectCountByCode(String menuCode){
        return selectCount(LambdaQueryWrapperX.<AuthMenuDO>lambdaQueryX()
                .eq(AuthMenuDO::getMenuCode, menuCode));
    }
}
