package com.mongoso.mgs.framework.dal.db.permission;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;
import lombok.Data;

/**
 * 角色菜单表
 * 表的实体类，参数和表字段一一对应
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-21 15:04:16
 */
@Data
@TableName("s_auth_role_menu")
//@KeySequence("s_auth_role_menu_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
public class AuthRoleMenuDO extends OperateDO {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
	private Long id;
    /**
     * 角色ID
     */
	private Long roleId;
    /**
     * 菜单ID
     */
	private String menuId;

	private Integer dataScope;
	private String buttonCodes;

}
