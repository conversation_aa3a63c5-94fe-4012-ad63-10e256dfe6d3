package com.mongoso.mgs.framework.dal.db.permission;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 菜单表
 * 表的实体类，参数和表字段一一对应
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-21 15:04:16
 */
@Data
@TableName("s_auth_menu_button")
public class AuthMenuButtonDO {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
	private Long id;

    private String buttonName;
	private String buttonCode;

    private String menuId;

    /**
     * 前端路由
     */
	private String path;
    /**
     * 排序
     */
	private Integer sort;

	private String buttonParam;
    private Integer category;
}
