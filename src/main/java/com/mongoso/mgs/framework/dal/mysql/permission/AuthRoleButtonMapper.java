package com.mongoso.mgs.framework.dal.mysql.permission;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mongoso.mgs.framework.dal.db.permission.AuthRoleButtonDO;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 角色按钮表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-21 15:04:16
 */
@Mapper
public interface AuthRoleButtonMapper extends BaseMapperX<AuthRoleButtonDO> {


    default int deleteByRoleId(Long roleId){
        return this.delete(new LambdaQueryWrapper<AuthRoleButtonDO>()
                        .eq(AuthRoleButtonDO::getRoleId,roleId));
    }

    default List<AuthRoleButtonDO> selectList(Long roleId){
        return this.selectList(new LambdaQueryWrapper<AuthRoleButtonDO>()
                .eq(AuthRoleButtonDO::getRoleId,roleId));
    }


    default List<AuthRoleButtonDO> selectList(Long roleId, Integer category){
        return this.selectList(new LambdaQueryWrapper<AuthRoleButtonDO>()
                .eq(AuthRoleButtonDO::getRoleId, roleId)
                .eq(AuthRoleButtonDO::getCategory, category));
    }
}
