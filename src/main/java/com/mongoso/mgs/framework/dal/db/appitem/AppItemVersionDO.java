package com.mongoso.mgs.framework.dal.db.appitem;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;
import lombok.*;

/**
 * 应用详情 DO
 *
 * <AUTHOR>
 */
@TableName("a_app_item_version")
//@KeySequence("a_app_item_version_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AppItemVersionDO extends OperateDO {

    /** 应用id */
    @TableId(type = IdType.ASSIGN_ID)
    private Integer appItemVersionId;

    private Integer appId;

    /** 应用版本 */
    private String appVersion;

    /** 应用版本url */
    private String appVersionUrl;

    /** zip包名 */
    private String zipName;

}
