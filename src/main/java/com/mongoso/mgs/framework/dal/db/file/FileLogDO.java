package com.mongoso.mgs.framework.dal.db.file;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mongoso.mgs.framework.mybatis.core.pojo.OperateDO;
import lombok.*;

/**
 * API 访问日志
 *
 */
@TableName("s_file_log")
//@KeySequence("s_file_log_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileLogDO extends OperateDO {

    /**
     * 文件id
     */
    @TableId
    private String fileId;
    /**
     * 文件名称
     */
    private String fileName;
    /**
     * 文件url
     */
    private String fileUrl;
    /**
     * 缩略图url
     */
    private String thumbnailUrl;
    /**
     * 文件格式
     */
    private String fileFormat;

    /**
     * 文件大小
     */
    private Long fileSize;
    /**
     * 图片宽度
     */
    private Integer fileWidth;
    /**
     * 图片高度
     */
    private Integer fileHeight;
    /**
     * 对象id
     */
    private String objId;
    /**
     *  表名
     */
    private String tableName;
    /**
     * 字段名称
     */
    private String fieldName;
    /**
     * 第三方类型
     */
    private String ossType;

    private String domain;

}
