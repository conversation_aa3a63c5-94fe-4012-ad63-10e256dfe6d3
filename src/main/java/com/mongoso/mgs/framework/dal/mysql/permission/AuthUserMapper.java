package com.mongoso.mgs.framework.dal.mysql.permission;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.controller.admin.permission.vo.user.AuthUserPageReqVO;
import com.mongoso.mgs.framework.controller.admin.permission.vo.user.AuthUserQueryReqVO;
import com.mongoso.mgs.framework.dal.db.permission.AuthRoleDO;
import com.mongoso.mgs.framework.dal.db.permission.AuthUserDO;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-21 15:04:16
 */
@Mapper
public interface AuthUserMapper extends BaseMapperX<AuthUserDO> {

    List<AuthRoleDO> getRoleIdByUserId(@Param("userId") Long userId);

    public default long countByUserAccount(String userAccount){
        return selectCount(new LambdaQueryWrapperX<AuthUserDO>()
                .eq(AuthUserDO::getUserAccount, userAccount));
    }

    public default List<AuthUserDO> selectList(AuthUserQueryReqVO req){
        return selectList(new LambdaQueryWrapperX<AuthUserDO>()
                .eqIfPresent(AuthUserDO::getEnable, req.getEnable()));
    }

    public default PageResult<AuthUserDO> selectPage(AuthUserPageReqVO req) {
        return selectPage(req, new LambdaQueryWrapperX<AuthUserDO>()
                .likeIfPresent(AuthUserDO::getUserName, req.getUserName())
                .eqIfPresent(AuthUserDO::getEnable, req.getEnable())
                .eqIfPresent(AuthUserDO::getIsAdmin, req.getIsAdmin())
                .orderByDesc(AuthUserDO::getCreatedDt));
    }


    default long selectCountByUserAccount(String userAccount){
        return selectCount(LambdaQueryWrapperX.<AuthUserDO>lambdaQueryX()
                .eq(AuthUserDO::getUserAccount, userAccount));
    }

    default long selectCountByUserPhoneNumber(String phoneNumber){
        return selectCount(LambdaQueryWrapperX.<AuthUserDO>lambdaQueryX()
                .eq(AuthUserDO::getPhoneNumber, phoneNumber));
    }

    default long selectCountByName(String userName){
        return selectCount(LambdaQueryWrapperX.<AuthUserDO>lambdaQueryX()
                .eq(AuthUserDO::getUserName, userName));
    }

    default AuthUserDO selectOneByUserAccount(String userAccount){
        return selectOne(new LambdaQueryWrapperX<AuthUserDO>()
                .eq(AuthUserDO::getUserAccount, userAccount));
    }

    default AuthUserDO selectOneByUserAccount(String userAccount, Long tenantId){
        return selectOne(LambdaQueryWrapperX.<AuthUserDO>lambdaQueryX()
                .eq(AuthUserDO :: getUserAccount, userAccount)
                .eq(AuthUserDO::getTenantId, tenantId));
    }

    default AuthUserDO selectOneByAccountOrPhone(String userAccount){
        return selectOne(LambdaQueryWrapperX.<AuthUserDO>lambdaQueryX()
                .and(i -> i.eq(AuthUserDO::getUserAccount, userAccount).or().eq(AuthUserDO::getPhoneNumber, userAccount)));
    }

}
