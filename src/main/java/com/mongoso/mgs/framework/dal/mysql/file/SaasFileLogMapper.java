package com.mongoso.mgs.framework.dal.mysql.file;


import com.mongoso.mgs.framework.dal.db.file.FileLogDO;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * API 访问日志 Mapper
 *
 */
@Mapper
public interface SaasFileLogMapper extends BaseMapperX<FileLogDO> {

    default List<FileLogDO> selectList(String objId, String tableName, String fieldName ){
        return selectList(new LambdaQueryWrapperX<FileLogDO>()
                .eq(FileLogDO::getObjId,objId)
                .eqIfPresent(FileLogDO::getTableName,tableName)
                .eqIfPresent(FileLogDO::getFieldName,fieldName));
    }

    default List<FileLogDO> selectListIn(List<String> objIds, String tableName, String fieldName){
        return selectList(new LambdaQueryWrapperX<FileLogDO>()
                .in(FileLogDO::getObjId,objIds)
                .eqIfPresent(FileLogDO::getTableName,tableName)
                .eqIfPresent(FileLogDO::getFieldName,fieldName));
    }

    default int updateInId(FileLogDO newDO,List<String> fileIdList){
        return update(newDO,new LambdaQueryWrapperX<FileLogDO>()
                .in(FileLogDO::getFileId,fileIdList));
    }

    default int deleteBy(String objId, String tableName, String fieldName){
        return delete(new LambdaQueryWrapperX<FileLogDO>()
                .eq(FileLogDO::getObjId,objId)
                .eqIfPresent(FileLogDO::getTableName,tableName)
                .eqIfPresent(FileLogDO::getFieldName,fieldName));
    }

}
