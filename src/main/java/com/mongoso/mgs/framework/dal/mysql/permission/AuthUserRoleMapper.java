package com.mongoso.mgs.framework.dal.mysql.permission;

import com.mongoso.mgs.framework.dal.db.permission.AuthUserRoleDO;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * 用户角色表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-21 15:04:16
 */
@Mapper
public interface AuthUserRoleMapper extends BaseMapperX<AuthUserRoleDO> {

    int addAll(@Param("userId") Long userId, @Param("roleIdList") List<Long> roleIdList);

    default void deleteByUserId(Long userId){
        this.delete(new LambdaQueryWrapperX<AuthUserRoleDO>()
                .eq(AuthUserRoleDO::getUserId,userId));
    }

    default void delete(AuthUserRoleDO authUserRoleDO){
        this.delete(new LambdaQueryWrapperX<AuthUserRoleDO>()
                .eqIfPresent(AuthUserRoleDO::getRoleId,authUserRoleDO.getRoleId()));
    }

    default List<AuthUserRoleDO> selectListByUserId(Long userId){
        return selectList(new LambdaQueryWrapperX<AuthUserRoleDO>()
                .eq(AuthUserRoleDO::getUserId,userId));
    }

    default List<AuthUserRoleDO> selectListByRoleIds(Collection<Long> roleIds) {
        return selectList(AuthUserRoleDO::getRoleId, roleIds);
    }

    default Long selectCountByRoleId(Long roleId){
        return selectCount(AuthUserRoleDO::getRoleId, roleId);
    }

}
