package com.mongoso.mgs.framework.dal.mysql.permission;


import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.controller.admin.permission.vo.role.AuthRolePageReqVO;
import com.mongoso.mgs.framework.controller.admin.permission.vo.role.AuthRoleQueryReqVO;
import com.mongoso.mgs.framework.dal.db.permission.AuthRoleDO;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 角色表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-21 15:04:16
 */
@Mapper
public interface AuthRoleMapper extends BaseMapperX<AuthRoleDO> {

    default List<AuthRoleDO> selectList(AuthRoleQueryReqVO req){
        return this.selectList(new LambdaQueryWrapperX<AuthRoleDO>()
                .eqIfPresent(AuthRoleDO::getEnable,req.getEnable()));
    }


    default PageResult<AuthRoleDO> selectPage(AuthRolePageReqVO req) {
        return selectPage(req, new LambdaQueryWrapperX<AuthRoleDO>()
                .likeIfPresent(AuthRoleDO::getRoleName, req.getRoleName())
                .eqIfPresent(AuthRoleDO::getEnable, req.getEnable())
                .orderByDesc(AuthRoleDO::getCreatedDt));
    }


    default long selectCountByName(String roleName){
        return selectCount(LambdaQueryWrapperX.<AuthRoleDO>lambdaQueryX()
                .eq(AuthRoleDO::getRoleName,roleName));
    }

    List<AuthRoleDO> getRoleIdByUserId(@Param("userId") Long userId);

}

