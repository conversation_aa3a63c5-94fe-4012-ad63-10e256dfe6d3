package com.mongoso.mgs.framework.dal.db.permission;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mongoso.mgs.framework.mybatis.core.pojo.BaseDO;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户表
 * 表的实体类，参数和表字段一一对应
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-21 15:04:16
 */
@Data
@TableName("s_auth_user")
public class AuthUserDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
	@TableId(type = IdType.ASSIGN_ID)
	private Long userId;

    /**
     * 用户名
     */
	private String userName;

    /**
     * 用户账号
     */
	private String userAccount;

    /**
     * 用户密码
     */
	private String userPassword;

    /**
     * 手机号
     */
	private String phoneNumber;

    /**
     * 邮箱
     */
	private String email;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否管理员 0-否 1-是
     */
    private Integer isAdmin;

    /**
     * 是否启用
     */
    private Integer enable;

    /**
     * 登陆锁定状态  0-正常，1-锁定
     */
    private Integer loginStatus = 0;

    /**
     * 最后登陆错误时间
     */
    private LocalDateTime lastLoginErrorTime;

    /**
     * 登陆错误次数
     */
    private Integer loginErrorCount = 0;

    /**
     * 租户ID
     */
    private Long tenantId;
}
