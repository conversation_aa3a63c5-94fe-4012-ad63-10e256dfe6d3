package com.mongoso.mgs.framework.dal.mysql.permission;

import com.mongoso.mgs.framework.dal.db.permission.AppAuthMenuDO;
import com.mongoso.mgs.framework.mybatis.core.mapper.BaseMapperX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * 菜单表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-21 15:04:16
 */
@Mapper
public interface AppAuthMenuMapper extends BaseMapperX<AppAuthMenuDO> {

    Long max();

    Collection<AppAuthMenuDO> getMenuByRoleId(@Param("roleIds") List<Long> roleIds,
                                              @Param("category") Integer category);
    List<String> selectChildrenMenuList(@Param("menuId") String menuId,
                                        @Param("projectName") String projectName,
                                        @Param("category") Integer category);

    default List<AppAuthMenuDO> selectListOrderSort(Integer type,String projectName,Integer category){
        return selectList(LambdaQueryWrapperX.<AppAuthMenuDO>lambdaQueryX()
                .eqIfPresent(AppAuthMenuDO::getProjectName, projectName)
                .le(AppAuthMenuDO::getType, type)
                .eq(AppAuthMenuDO::getHidden, 0)
                .eq(AppAuthMenuDO::getCategory, category)
                .orderByAsc(AppAuthMenuDO::getSort));
    }


    /**
     * 查询不隐藏的菜单
     * @return
     */
    default List<AppAuthMenuDO> selectListByNotHidden(Integer category){
        return selectList(LambdaQueryWrapperX.<AppAuthMenuDO>lambdaQueryX()
                .eq(AppAuthMenuDO::getCategory, category)
                .eq(AppAuthMenuDO::getHidden, 0));
    }

    default List<AppAuthMenuDO> selectListInId(Set<String> pidSet,String projectName,Integer category){
        return selectList(LambdaQueryWrapperX.<AppAuthMenuDO>lambdaQueryX()
                .eq(AppAuthMenuDO::getProjectName, projectName)
                .eq(AppAuthMenuDO::getCategory, category)
                .eq(AppAuthMenuDO::getHidden, 0)
                .in(AppAuthMenuDO::getMenuId, pidSet));
    }

    default List<AppAuthMenuDO> selectListByCategory(Integer category){
        return selectList(LambdaQueryWrapperX.<AppAuthMenuDO>lambdaQueryX()
                .eq(AppAuthMenuDO::getCategory, category));
    }


    default int deleteByCategory(Integer category){
        return delete(LambdaQueryWrapperX.<AppAuthMenuDO>lambdaQueryX()
                .eq(AppAuthMenuDO::getCategory, category));
    }

    default List<AppAuthMenuDO> selectListByCategorys(List<Integer> categorys){
        return selectList(LambdaQueryWrapperX.<AppAuthMenuDO>lambdaQueryX()
                .in(AppAuthMenuDO::getCategory, categorys));
    }

}
