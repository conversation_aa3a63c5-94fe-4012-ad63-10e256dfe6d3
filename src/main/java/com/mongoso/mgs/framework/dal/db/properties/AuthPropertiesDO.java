package com.mongoso.mgs.framework.dal.db.properties;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("s_properties")
@KeySequence("s_properties_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
public class AuthPropertiesDO {
    /**
     * 系统属性ID
     */
    @TableId(value = "prop_pk_id", type = IdType.AUTO)
    private Long propPkId;
    /**
     * 系统属性编码
     */
    private String propCode;
    /**
     * 系统属性值
     */
    private String propValue;
    /**
     * 系统属性描述
     */
    private String propDesc;
}
