package com.mongoso.mgs.framework.api;

//import com.mongoso.mgs.framework.api.dto.ApiAccessLogAddReqDTO;
//import com.mongoso.mgs.framework.api.dto.ApiErrorLogAddReqDTO;
//import com.mongoso.mgs.framework.api.dto.LoginLogCreateReqDTO;
//import com.mongoso.mgs.framework.api.dto.OperateLogAddReqDTO;

import com.mongoso.mgs.framework.api.dto.ApiAccessLogDTO;
import com.mongoso.mgs.framework.api.dto.ApiErrorLogDTO;
import com.mongoso.mgs.framework.api.dto.LoginLogDTO;
import com.mongoso.mgs.framework.api.dto.OperateLogDTO;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.service.auth.AuthService;
import com.mongoso.mgs.framework.tenant.core.service.TenantFrameworkService;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.Collection;
import java.util.HashSet;
import java.util.Set;

import static com.mongoso.mgs.framework.common.domain.ResultX.success;

/**
 *  框架依赖的视线，和业务无关
 *
 */
@Service
public class FrameworkApiImpl implements SingleFrameworkApi {

    //@Resource
    //private ApiAccessLogService apiAccessLogService;
    //
    //@Resource
    //private ApiErrorLogService apiErrorLogService;
    //
    //@Resource
    //private OperateLogMapper operateLogMapper;

    //@Resource
    private TenantFrameworkService tenantService;

    @Resource
    private AuthService authService;


    @Override
    public ResultX<LoginUser> checkAccessToken(String accessToken) {
        return success(authService.checkToken(accessToken));
    }

    @Override
    public void apiAccessLogAdd(ApiAccessLogDTO apiAccessLogDTO) {

    }

    @Override
    public void apiErrorLogAdd(ApiErrorLogDTO apiErrorLogDTO) {

    }

    @Override
    public void loginLogAdd(LoginLogDTO loginLogDTO) {

    }

    @Override
    public void operateLogAdd(OperateLogDTO operateLogDTO) {

    }

    @Override
    public ResultX<Boolean> hasAnyPermissions(String... permissions) {
        return success(doHasAnyPermissions(permissions));
    }

    @Override
    public ResultX<Boolean> hasAnyRoles(String... roles) {
        return this.hasAnyPermissions(roles);
    }

    @Override
    public ResultX<Collection<Long>> getTenantIds() {
        Collection<Long> tenantIds = tenantService.getTenantIds();
        Set<Long> collection = new HashSet<>(tenantIds);
        return success(collection);
        //return success(new ArrayList<>());
    }

    @Override
    public ResultX<Boolean> validTenant(Long id) {
        tenantService.validTenant(id);
        return success(true);
    }

    private boolean doHasAnyPermissions(String... permissions) {
        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        if (loginUser == null) {
            // 没登陆，返回没权限
            return false;
        }
        return loginUser.hasPermission(permissions);
    }

}
