package com.mongoso.mgs.framework.config;

import org.flowable.engine.ProcessEngine;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.HistoryService;
import org.flowable.engine.ManagementService;
import org.flowable.engine.IdentityService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.beans.factory.annotation.Autowired;

@Configuration
public class FlowableConfig {

    @Autowired
    private ProcessEngine processEngine;

    @Bean
    public RepositoryService repositoryService() {
        return processEngine.getRepositoryService();
    }

    @Bean
    public RuntimeService runtimeService() {
        return processEngine.getRuntimeService();
    }

    @Bean
    public TaskService taskService() {
        return processEngine.getTaskService();
    }

    @Bean
    public HistoryService historyService() {
        return processEngine.getHistoryService();
    }

    @Bean
    public ManagementService managementService() {
        return processEngine.getManagementService();
    }

    @Bean
    public IdentityService identityService() {
        return processEngine.getIdentityService();
    }
}