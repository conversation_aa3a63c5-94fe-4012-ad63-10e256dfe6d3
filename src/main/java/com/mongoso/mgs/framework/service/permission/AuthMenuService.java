package com.mongoso.mgs.framework.service.permission;

import cn.hutool.json.JSONObject;
import com.mongoso.mgs.framework.controller.admin.permission.vo.menu.*;
import com.mongoso.mgs.framework.dal.db.permission.AuthMenuDO;
import com.mongoso.mgs.framework.util.tree.TreeDrag;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 菜单表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-21 15:04:16
 */
public interface AuthMenuService {

    /**
     * 菜单表新增
     */
    String authMenuAdd(AuthMenuAddReqVO req);

    String authMenuEdit(AuthMenuEditReqVO reqVO);

    String authMenuDel(AuthMenuPrimaryReqVO reqVO);

    AuthMenuDetailRespVO authMenuDetail(String menuId);

    List<AuthMenuRespVO> authMenuTree(AuthMenuTreeReqVO reqVO);

    /**
     * 查询菜单列表
     * @param roleIds 角色ids
     * @return
     */
    Collection<AuthMenuDO> selectListInRoleId(List<Long> roleIds,Integer category);

    /**
     * 查询左侧菜单栏
     * @return
     */
    List<Map<String, Object>> authMenuRouter(JSONObject jsonString);

    /**
     * 查询按钮权限列表
     * @return
     */
    List<String> permissionConfigList();

    int insert(String appName, String projectName);

    List<Map<String, Object>> authMenuRouterAll(JSONObject jsonString);

    List<String> authMenuDrag(TreeDrag reqVO);

    Integer authButtonInit(AuthMenuTreeReqVO reqVO);
}

