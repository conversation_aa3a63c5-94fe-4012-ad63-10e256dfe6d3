package com.mongoso.mgs.framework.service.file;

import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.CollUtilX;
import com.mongoso.mgs.framework.common.util.StrUtilX;
import com.mongoso.mgs.framework.dal.db.file.FileLogDO;
import com.mongoso.mgs.framework.dal.mysql.file.SaasFileLogMapper;
import com.mongoso.mgs.framework.file.core.client.ali.AliClient;
import com.mongoso.mgs.framework.file.core.client.local.LocalClient;
import com.mongoso.mgs.framework.file.core.client.qiniu.QiNiuClient;
import com.mongoso.mgs.framework.file.core.client.upyun.UpYunClient;
import com.mongoso.mgs.framework.file.core.domain.FileEnum;
import com.mongoso.mgs.framework.file.core.domain.FileItem;
import com.mongoso.mgs.framework.file.core.domain.FileReq;
import com.mongoso.mgs.framework.file.core.domain.FileResp;
import com.mongoso.mgs.framework.mybatis.core.util.IDUtilX;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/12 14:36
 */
@Service
public class SaasFileServiceImpl implements SaasFileService {

    @Resource
    HttpServletResponse response;

    @Resource
    private LocalClient localClient;

    @Resource
    private QiNiuClient qiNiuClient;

    @Resource
    private AliClient aliClient;

    @Resource
    private UpYunClient upYunClient;

    @Resource
    private SaasFileLogMapper saasFileLogMapper;

    @Override
    public FileResp upload(MultipartFile file,FileReq req) throws IOException {
        String ossType = req.getOssType();
        // 上传文件
        FileResp resp;
        if (FileEnum.ali.getKey().equals(ossType)){
            resp = aliClient.upload(file.getBytes(), file.getOriginalFilename());
        }else  if (FileEnum.qiniu.getKey().equals(ossType)){
            resp = qiNiuClient.upload(file.getBytes(), file.getOriginalFilename());
        }else  if (FileEnum.upyun.getKey().equals(ossType)){
            resp = upYunClient.upload(file.getBytes(), file.getOriginalFilename());
        }else {
            resp = localClient.upload(file.getBytes(), file.getOriginalFilename());
        }

        // 保存到数据库
        FileLogDO fileLogDO = build(resp);

        String idStr = IDUtilX.getIdStr();
        fileLogDO.setFileId(idStr);
        fileLogDO.setObjId(req.getObjId());
        fileLogDO.setFieldName(req.getFieldName());
        saasFileLogMapper.insert(fileLogDO);

        resp.setFileId(idStr);
        if (resp.getFileUrl() != null){
            resp.setFileUrl(resp.getDomain()+resp.getFileUrl());
        }
        if (resp.getFileUrl() != null){
            resp.setThumbnailUrl(resp.getDomain()+resp.getThumbnailUrl());
        }
        // 返回
        return resp;
    }


    @Override
    public FileResp upload(FileReq req, String fileName, byte[] bytes) {
        String ossType = req.getOssType();
        // 上传文件
        FileResp resp;
        if (FileEnum.ali.getKey().equals(ossType)){
            resp = aliClient.upload(bytes, fileName);
        }else  if (FileEnum.qiniu.getKey().equals(ossType)){
            resp = qiNiuClient.upload(bytes, fileName);
        }else  if (FileEnum.upyun.getKey().equals(ossType)){
            resp = upYunClient.upload(bytes, fileName);
        }else {
            resp = localClient.upload(bytes, fileName);
        }

        // 保存到数据库
        FileLogDO fileLogDO = build(resp);

        String idStr = IDUtilX.getIdStr();
        fileLogDO.setFileId(idStr);
        fileLogDO.setFileName(fileName);
        fileLogDO.setObjId(req.getObjId());
        fileLogDO.setTableName(req.getTableName());
        fileLogDO.setFieldName(req.getFieldName());
        this.saasFileLogMapper.insert(fileLogDO);
        if (resp.getFileUrl() != null) {
            resp.setFileUrl(resp.getDomain() + resp.getFileUrl());
        }

        if (resp.getFileUrl() != null) {
            resp.setThumbnailUrl(resp.getFileUrl());
        }
        // 返回
        return resp;
    }

    @Override
    public Object del(FileReq req) {
        String ossType = req.getOssType();

        FileLogDO fileLogDO = saasFileLogMapper.selectById(req.getFileId());
        if (fileLogDO == null){
            throw new BizException("1001003001", "文件不存在");
        }
        // 删除文件日志
        saasFileLogMapper.deleteById(req.getFileId());


        // 删除第三方服务的文件
        if (FileEnum.ali.getKey().equals(ossType)){
            aliClient.del(fileLogDO.getFileUrl());
        }else  if (FileEnum.qiniu.getKey().equals(ossType)){
            qiNiuClient.del(fileLogDO.getFileUrl());
        }else  if (FileEnum.upyun.getKey().equals(ossType)){
            upYunClient.del(fileLogDO.getFileUrl());
        }else {
            localClient.del(fileLogDO.getFileUrl());
        }
        return 1;
    }

    @Override
    public void download(FileReq req) {
        // 查询文件
        String ossType = req.getOssType();

        FileLogDO fileLogDO = saasFileLogMapper.selectById(req.getFileId());
        if (fileLogDO == null){
            throw new BizException("1001003001", "文件不存在");
        }

        FileItem fileItem;
        if (FileEnum.ali.getKey().equals(ossType)){
            fileItem = aliClient.dowload(fileLogDO.getFileUrl());
        }else  if (FileEnum.qiniu.getKey().equals(ossType)){
            fileItem = qiNiuClient.dowload(fileLogDO.getFileUrl());
        }else  if (FileEnum.upyun.getKey().equals(ossType)){
            fileItem = upYunClient.dowload(fileLogDO.getFileUrl());
        }else {
            fileItem = localClient.dowload(fileLogDO.getFileUrl());
        }
        fileItem.setFileFormat(fileLogDO.getFileFormat());
        fileLogDO.setFileName(fileLogDO.getFileName());
        download(response,fileItem);
    }


    @Override
    public FileItem downloadFileItem(FileReq req) {
        // 查询文件
        String ossType = req.getOssType();

        FileLogDO fileLogDO = saasFileLogMapper.selectById(req.getFileId());
        if (fileLogDO == null){
            throw new BizException("1001003001", "文件不存在");
        }

        FileItem fileItem;
        if (FileEnum.ali.getKey().equals(ossType)){
            fileItem = aliClient.dowload(fileLogDO.getFileUrl());
        }else  if (FileEnum.qiniu.getKey().equals(ossType)){
            fileItem = qiNiuClient.dowload(fileLogDO.getFileUrl());
        }else  if (FileEnum.upyun.getKey().equals(ossType)){
            fileItem = upYunClient.dowload(fileLogDO.getFileUrl());
        }else {
            fileItem = localClient.dowload(fileLogDO.getFileUrl());
        }
        fileItem.setFileFormat(fileLogDO.getFileFormat());

        return fileItem;
    }


    @Override
    public int del(List<String> fileIdList) {
        return saasFileLogMapper.deleteBatchIds(fileIdList);
    }

    @Override
    public int del(String objId) {
        return this.del(objId,null,null);
    }

    @Override
    public int del(String objId, String tableName) {
        return this.del(objId,tableName,null);
    }

    @Override
    public int del(String objId, String tableName, String fieldName) {
        return saasFileLogMapper.deleteBy(objId,tableName,fieldName);
    }

    @Override
    public int bind(List<String> fileIdList, String objId) {
        return bind(fileIdList, objId, null, null);
    }

    @Override
    public int bind(List<String> fileIdList, String objId, String tableName) {
        return bind(fileIdList, objId, tableName, null);
    }

    @Override
    public int bind(List<String> fileIdList, String objId, String tableName, String fieldName) {
        if (CollUtilX.isEmpty(fileIdList) || StrUtilX.isEmpty(objId)){
            return 0;
        }
        FileLogDO fileLogDO = new FileLogDO();
        fileLogDO.setObjId(objId);
        fileLogDO.setTableName(tableName);
        fileLogDO.setFieldName(fieldName);
        int i = saasFileLogMapper.updateInId(fileLogDO,fileIdList);
        return i;
    }


    @Override
    public FileLogDO detailDO(String fileId) {
        FileLogDO fileLogDO = saasFileLogMapper.selectById(fileId);
        if (FileEnum.local.getKey().equals(fileLogDO.getOssType())) {
            fileLogDO.setFileUrl(fileLogDO.getDomain() + fileLogDO.getFileUrl());
            fileLogDO.setThumbnailUrl(fileLogDO.getDomain() + fileLogDO.getThumbnailUrl());
        } else {
            fileLogDO.setThumbnailUrl(fileLogDO.getDomain() + fileLogDO.getFileUrl());
            fileLogDO.setFileUrl(fileLogDO.getDomain() + fileLogDO.getFileUrl());
        }
        return fileLogDO;
    }

    @Override
    public List<FileLogDO> listDO(List<String> fileIds) {
        List<FileLogDO> fileLogDOS = saasFileLogMapper.selectBatchIds(fileIds);

        // 拼接域名
        return  joinUrl(fileLogDOS);
    }

    private List<FileLogDO> joinUrl(List<FileLogDO> fileLogDOS) {
        for (FileLogDO fileLogDO : fileLogDOS) {
            if (FileEnum.local.getKey().equals(fileLogDO.getOssType())) {
                fileLogDO.setFileUrl(fileLogDO.getDomain() + fileLogDO.getFileUrl());
                fileLogDO.setThumbnailUrl(fileLogDO.getDomain() + fileLogDO.getThumbnailUrl());
            } else {
                fileLogDO.setThumbnailUrl(fileLogDO.getDomain() + fileLogDO.getFileUrl());
                fileLogDO.setFileUrl(fileLogDO.getDomain() + fileLogDO.getFileUrl());
            }
        }
        return fileLogDOS;
    }

    @Override
    public List<FileLogDO> listDOByObjId(String objId, String tableName, String fieldName) {
        List<FileLogDO> fileLogDOS = saasFileLogMapper.selectList(objId, tableName, fieldName);

        // 拼接域名
        return  joinUrl(fileLogDOS);

    }

    @Override
    public List<FileLogDO> listDOInObjId(List<String> objIds,  String tableName, String fieldName) {
        List<FileLogDO> fileLogDOS = saasFileLogMapper.selectListIn(objIds, tableName, fieldName);
        // 拼接域名
        return  joinUrl(fileLogDOS);
    }


    public void download(HttpServletResponse response, FileItem fileItem) {
        response.setCharacterEncoding("utf-8");
        response.setContentType("application/octet-stream");
        try {
            response.setHeader("Content-disposition", "attachment;filename=" + System.currentTimeMillis() + "." + fileItem.getFileFormat());
            ServletOutputStream outputStream = response.getOutputStream();
            outputStream.write(fileItem.getBytes());
            response.flushBuffer();
        } catch (Exception var14) {
            var14.printStackTrace();
        }
    }

    private FileLogDO build(FileResp resp) {
        FileLogDO fileLogDO = new FileLogDO();
        fileLogDO.setFieldName(resp.getFileName());
        fileLogDO.setFileUrl(resp.getFileUrl());
        fileLogDO.setThumbnailUrl(resp.getThumbnailUrl());
        fileLogDO.setFileFormat(resp.getFileFormat());
        fileLogDO.setFileSize(resp.getFileSize());
        fileLogDO.setFileWidth(resp.getFileWidth());
        fileLogDO.setFileHeight(resp.getFileHeight());
        fileLogDO.setOssType(resp.getOssType());
        fileLogDO.setDomain(resp.getDomain());
        return fileLogDO;
    }

}
