package com.mongoso.mgs.framework.service.permission;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.enums.BaseEnum;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.common.util.CollUtilX;
import com.mongoso.mgs.framework.common.util.StrUtilX;
import com.mongoso.mgs.framework.controller.admin.permission.vo.role.*;
import com.mongoso.mgs.framework.dal.db.permission.AuthRoleDO;
import com.mongoso.mgs.framework.dal.db.permission.AuthRoleMenuDO;
import com.mongoso.mgs.framework.dal.mysql.permission.AppAuthRoleMenuMapper;
import com.mongoso.mgs.framework.dal.mysql.permission.AuthRoleMapper;
import com.mongoso.mgs.framework.dal.mysql.permission.AuthRoleMenuMapper;
import com.mongoso.mgs.framework.dal.mysql.permission.AuthUserRoleMapper;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exception;
import static com.mongoso.mgs.framework.enums.SassErrorCodeConstants.*;


/**
 * 角色表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-21 15:04:16
 */
@Service
public class AuthRoleServiceImpl implements AuthRoleService {

    @Resource
    private AuthRoleMenuMapper authRoleMenuMapper;

    @Resource
    private AppAuthRoleMenuMapper appAuthRoleMenuMapper;

    @Resource
    private AuthRoleMapper authRoleMapper;

    @Resource
    private AuthUserRoleMapper authUserRoleMapper;


    @Override
    @DSTransactional(rollbackFor = Throwable.class)
    public Long authRoleAdd(AuthRoleAditReqVO reqVO){

        // 验重
        duplicheck(reqVO);

        // 类型转换 vo转do
        AuthRoleDO newDO = BeanUtilX.copy(reqVO,  AuthRoleDO::new);

        newDO.setRoleCode(UUID.randomUUID().toString());
        newDO.setEnable(BaseEnum.ENABLE.getKey());

        // 执行sql
        authRoleMapper.insert(newDO);

        // 返回主键
        return newDO.getRoleId();
    }

    private void duplicheck(AuthRoleAditReqVO reqVO) {
        long count1 = authRoleMapper.selectCountByName(reqVO.getRoleName());
        if (count1 > 0) {
            throw exception(ROLE_NAME_DUPLICATE,reqVO.getRoleName());
        }
    }

    @Override
    @DSTransactional(rollbackFor = Throwable.class)
    public Long authRoleEdit(AuthRoleAditReqVO reqVO){
        // 校验存在
        this.roleValidateExists(reqVO.getRoleId());

        AuthRoleDO newDO = BeanUtilX.copy(reqVO,  AuthRoleDO::new);
        authRoleMapper.updateById(newDO);

        return newDO.getRoleId();
    }

    @Override
    @DSTransactional(rollbackFor = Throwable.class)
    public Long authRoleDel(Long id){

        // 校验存在
        this.roleValidateExists(id);

        Long count = authUserRoleMapper.selectCountByRoleId(id);

        if (count != null && count > 1) {
            throw exception(ROLE_DELETE_ERROR);
        }

        // 删除角色
        authRoleMapper.deleteById(id);
//        //同步删除用户角色中间表数据
//        AuthUserRoleDO authUserRoleDO = new AuthUserRoleDO();
//        authUserRoleDO.setRoleId(id);
//        authUserRoleMapper.delete(authUserRoleDO);

        return id;
    }

    @Override
    public AuthRoleRespVO authRoleDetail(Long id){

        AuthRoleDO oldDO = authRoleMapper.selectById(id);
        if (oldDO == null){
            return null;
        }

        AuthRoleRespVO resp = BeanUtilX.copy(oldDO,  AuthRoleRespVO::new);

        // 查询PC端菜单
        List<AuthRoleMenuDO> roleMenuDOS = authRoleMenuMapper.selectList(id);

        List<String> menuIdList = roleMenuDOS.stream().map(AuthRoleMenuDO::getMenuId).collect(Collectors.toList());
        resp.setMenuIdList(menuIdList);

        return resp;
    }

    @Override
    public List<AuthRoleRespVO> authRoleList(AuthRoleQueryReqVO req){
        List<AuthRoleDO> list = authRoleMapper.selectList(req);
        return BeanUtilX.copy(list,  AuthRoleRespVO::new);
    }

    @Override
    public PageResult<AuthRoleRespVO> authRolePage(AuthRolePageReqVO req){
        PageResult<AuthRoleDO> ipage = authRoleMapper.selectPage(req);
        return BeanUtilX.copy(ipage,  AuthRoleRespVO::new);
    }

    @Override
    public Long authRoleDuplicheck(AuthRoleDuplicheckReqVO reqVO) {
        if (reqVO.getRoleId() == null) {
            return authRoleMapper.selectCountByName(reqVO.getRoleName());
        } else {
            AuthRoleDO oldDO = this.roleValidateExists(reqVO.getRoleId());
            if (!reqVO.getRoleName().equals(oldDO.getRoleName())) {
                return authRoleMapper.selectCountByName(reqVO.getRoleName());
            }
        }
        return 0L;
    }

    @Override
    public Integer authRoleMenuConfig(AuthRoleConfigReqVO reqVO) {

        if (CollUtilX.isEmpty(reqVO.getMenuIdList())) {
            return 0;
        }

        // 校验存在
        this.roleValidateExists(reqVO.getRoleId());

//        List<AuthRoleMenuConfigReqVO> menuList = reqVO.getMenuList();

        List<AuthRoleMenuDO> list = new ArrayList<>();
        for (String menuId : reqVO.getMenuIdList()) {
            AuthRoleMenuDO newDO = new AuthRoleMenuDO();
            newDO.setRoleId(reqVO.getRoleId());
            newDO.setMenuId(menuId);
            list.add(newDO);
        }

//        for (AuthRoleMenuConfigReqVO item : menuList) {
//
//            AuthRoleMenuDO newDO = new AuthRoleMenuDO();
//            newDO.setRoleId(reqVO.getRoleId());
//            newDO.setMenuId(item.getMenuId());
//            newDO.setDataScope(item.getDataScope());
//
//            List<String> buttonCodeList = item.getButtonCodeList();
//            String buttonCodes = StrUtilX.listToString(buttonCodeList);
//            newDO.setButtonCodes(buttonCodes);
//            list.add(newDO);
//        }

        // 删除旧的
        authRoleMenuMapper.deleteByRoleId(reqVO.getRoleId());

        authRoleMenuMapper.insertBatch(list);

        return 1;
    }

    @Override
    public AuthRoleMenuConfigRespVO authRoleMenuDetail(AuthRoleMenuPrimaryReqVO req) {
        AuthRoleMenuDO old = authRoleMenuMapper.selectOneBy(req.getRoleId(),req.getMenuId());
        AuthRoleMenuConfigRespVO respVO = new AuthRoleMenuConfigRespVO();
        respVO.setRoleId(req.getRoleId());
        respVO.setMenuId(req.getMenuId());

        if (old != null){
            List<String> buttonCodeList = StrUtilX.stringToList(old.getButtonCodes());
            respVO.setDataScope(old.getDataScope());
            respVO.setButtonCodeList(buttonCodeList);
        }
        return respVO;
    }

    @Override
    public Integer authRoleCopy(AuthRoleCopyReqVO req) {
        // 将当前角色的菜单和按钮复制到目标角色里
        this.roleValidateExists(req.getRoleId());

        List<AuthRoleMenuDO> list = authRoleMenuMapper.selectListByRoleId(req.getRoleId());

        // 删除旧的
        authRoleMenuMapper.deleteByRoleId(req.getTargetRoleId());

        // 添加新的
        if (CollUtilX.isNotEmpty(list)){
            for (AuthRoleMenuDO authRoleMenuDO : list) {
                authRoleMenuDO.setId(null);
                authRoleMenuDO.setCreatedBy(null);
                authRoleMenuDO.setCreatedDt(null);
                authRoleMenuDO.setUpdatedBy(null);
                authRoleMenuDO.setUpdatedDt(null);
                authRoleMenuDO.setRoleId(req.getTargetRoleId());
            }
            authRoleMenuMapper.insertBatch(list);
        }
        return 1;
    }

    private AuthRoleDO roleValidateExists(Long roleId) {
        AuthRoleDO authRoleDO = authRoleMapper.selectById(roleId);
        if (authRoleDO == null) {
            throw exception(ROLE_NOT_EXISTS);
        }
        return authRoleDO;
    }


}