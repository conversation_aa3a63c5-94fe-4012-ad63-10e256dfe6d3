package com.mongoso.mgs.framework.service.permission;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.enums.BaseEnum;
import com.mongoso.mgs.framework.common.enums.NavBarEnum;
import com.mongoso.mgs.framework.common.util.*;
import com.mongoso.mgs.framework.controller.admin.permission.vo.appmenu.AppAuthMenuAditReqVO;
import com.mongoso.mgs.framework.controller.admin.permission.vo.appmenu.AppAuthMenuTreeReqVO;
import com.mongoso.mgs.framework.controller.admin.permission.vo.menu.AuthMenuRespVO;
import com.mongoso.mgs.framework.dal.db.permission.AppAuthMenuDO;
import com.mongoso.mgs.framework.dal.db.permission.AuthRoleDO;
import com.mongoso.mgs.framework.dal.mysql.permission.AppAuthMenuMapper;
import com.mongoso.mgs.framework.util.tree.TreeUtilX;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exception;
import static com.mongoso.mgs.framework.enums.SassErrorCodeConstants.*;


/**
 * 菜单表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-21 15:04:16
 */
@Service
public class AppAuthMenuServiceImpl implements AppAuthMenuService {

    private static int sort = 1;

    private static String key = "app_menu:permission:config";

    public static final String PROJECT_NAME = "platform";

    @Resource
    private AppAuthMenuMapper appAuthMenuMapper;

    @Resource
    private AuthUserService authUserService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;


    /**
     * 菜单的自增id
     * 使用公共的变量来保证自增id的不重复，
     * 会存在并发问题，但是这个功能并发操作频率基本没有，所以不做处理
     */
    private long menuSeq = 10000L;

    @Override
    @DSTransactional(rollbackFor = Throwable.class)
    public String authMenuAdd(AppAuthMenuAditReqVO reqVO) {
        // 校验
        check(reqVO);

        // 获取自增id
        maxSeq();

        // 解析json，封装成菜单对象
        List<AppAuthMenuDO> list2 = iteraJson2(reqVO);

        // 删除旧的菜单
        appAuthMenuMapper.deleteByCategory(reqVO.getCategory());

        // 添加新的菜单
        appAuthMenuMapper.insertBatch(list2);

        // 删除redis的按钮配置
        stringRedisTemplate.delete(key);

        return this.authMenuMind(0,reqVO.getCategory());
    }

    private void maxSeq() {
        Long max = appAuthMenuMapper.max();
        if (max != null) {
            // 有用数据库的
            menuSeq = max;
        } else {
            // 没有用默认的
            menuSeq = 10000L;
        }
    }

    private void check(AppAuthMenuAditReqVO reqVO) {
        LoginUser loginUser = WebFrameworkUtilX.getRequiredLoginUser();

        if (loginUser.getIsAdmin() == BaseEnum.NOT_ADMIN.getKey()) {
            // 只有是超级管理员才能修改
            throw exception(ROOT_OPERATE);

        }

        String menuTreeStr = reqVO.getMenuJson();
        if (StrUtilX.isEmpty(menuTreeStr)) {
            throw exception(NOT_ADD_NULL);
        }

        if (reqVO.getCategory() == null) {
            throw exception(NOT_ADD_NULL2);
        }

    }

    private List<AppAuthMenuDO> iteraJson2(AppAuthMenuAditReqVO req) {
        String menuTreeStr = req.getMenuJson();
        Integer category = req.getCategory();

        List<AppAuthMenuDO> newDOs = new ArrayList<>();
        // 处理1级菜单
        JSONArray jasonArray = JsonUtilX.parseArray(menuTreeStr);

        for (int i = 0; i < jasonArray.size(); i++) {
            sort += 1;
            JSONObject jsonObject = jasonArray.getJSONObject(i);
            String itemName = jsonObject.getStr("itemName");
            if (StrUtilX.isEmpty(itemName)) {
                // 没有名称，不做处理
                continue;
            }

            String itemId = jsonObject.getStr("itemId");// 菜单id
            String remark = jsonObject.getStr("remark");// 菜单path
            String projectName = jsonObject.getStr("projectName"); // 项目名
            String icon = jsonObject.getStr("icon"); // 图标
            String hidden = jsonObject.getStr("hidden"); // 是否隐藏
            String children = jsonObject.getStr("children"); // 子对象

            if (StrUtilX.isEmpty(projectName)) {
                projectName = PROJECT_NAME;// 没有传，默认是平台
            }

            AppAuthMenuDO authMenuDO = new AppAuthMenuDO();

            authMenuDO.setMenuId(itemId);
            authMenuDO.setMenuName(itemName);
            authMenuDO.setPath(remark);
            authMenuDO.setProjectName(projectName);
            authMenuDO.setIcon(icon);
            if (StrUtilX.isEmpty(hidden)){
                authMenuDO.setHidden(0);
            }else {
                authMenuDO.setHidden(Integer.parseInt(hidden));
            }
            authMenuDO.setCategory(category);
            authMenuDO.setSort(sort);

            authMenuDO.setParentItemId("0"); // 1级菜单，父级默认0
            authMenuDO.setParentItemIds("0");

            if (StrUtilX.isEmpty(itemId)) {
                // 新增的菜单
                menuSeq += 1;
                authMenuDO.setMenuId(menuSeq + "");
                authMenuDO.setMenuSeq(menuSeq);
            } else {
                authMenuDO.setMenuSeq(Long.parseLong(itemId));
            }

            if (StrUtilX.isEmpty(remark)) {
                // 没路劲是目录
                authMenuDO.setType(NavBarEnum.DIRECTORY.getKey());
            } else {
                // 有路劲是菜单
                authMenuDO.setType(NavBarEnum.MENU.getKey());
            }

            newDOs.add(authMenuDO);

            List<String> pidList = new ArrayList<>();
            pidList.add(authMenuDO.getMenuId());
            if (StrUtilX.isNotEmpty(children)) {
                digui(children, pidList, newDOs, projectName,category);
            }

        }

        return newDOs;
    }

    private void digui(String menuTreeStr, List<String> pidList, List<AppAuthMenuDO> newDOs, String projectName,Integer category) {

        // 获取所有子集
        JSONArray jasonArray = JsonUtilX.parseArray(menuTreeStr);

        for (int i = 0; i < jasonArray.size(); i++) {
            sort += 1;
            JSONObject jsonObject = jasonArray.getJSONObject(i);
            String itemName = jsonObject.getStr("itemName");
            if (StrUtilX.isEmpty(itemName)) {
                // 没有名称，不做处理
                continue;
            }

            String itemId = jsonObject.getStr("itemId");// 菜单id
            String remark = jsonObject.getStr("remark");// 菜单path
            String icon = jsonObject.getStr("icon"); // 图标
            String hidden = jsonObject.getStr("hidden"); // 是否隐藏
            String children = jsonObject.getStr("children"); // 子对象

            AppAuthMenuDO authMenuDO = new AppAuthMenuDO();
            authMenuDO.setMenuId(itemId);
            authMenuDO.setMenuName(itemName);
            authMenuDO.setPath(remark);
            authMenuDO.setProjectName(projectName);
            authMenuDO.setIcon(icon);
            if (StrUtilX.isEmpty(hidden)){
                authMenuDO.setHidden(0);
            }else {
                authMenuDO.setHidden(Integer.parseInt(hidden));
            }
            authMenuDO.setCategory(category);
            authMenuDO.setSort(sort);

            if (StrUtilX.isEmpty(itemId)) {
                // 新增的菜单
                menuSeq += 1;
                authMenuDO.setMenuId(menuSeq + "");
                authMenuDO.setMenuSeq(menuSeq);
            } else {
                authMenuDO.setMenuSeq(Long.parseLong(itemId));
            }

            String pids = CollUtilX.getPids(pidList);
            String pid = pidList.get(pidList.size() - 1);
            authMenuDO.setParentItemId(pid);
            authMenuDO.setParentItemIds(pids);

            String[] split = itemName.split("~");
            if (split.length == 2) {
                // 按钮
                authMenuDO.setType(NavBarEnum.BUTTON.getKey());
                authMenuDO.setPermissions(split[1]);
            } else {
                if (StrUtilX.isEmpty(remark)) {
                    // 没路劲是目录
                    authMenuDO.setType(NavBarEnum.DIRECTORY.getKey());
                } else {
                    // 有路劲是菜单
                    authMenuDO.setType(NavBarEnum.MENU.getKey());
                }
            }

            newDOs.add(authMenuDO);

            if (StrUtilX.isNotEmpty(children)) {
                List<String> cpidList = new ArrayList<>();
                cpidList.addAll(pidList);
                cpidList.add(authMenuDO.getMenuId());
                digui(children, cpidList, newDOs, projectName,category);
            }
        }
    }

    /**
     * 根据用户id，获取树形菜单
     * <p>
     * rank：
     */
    private List<Map<String, Object>> buildMenuAll(int rank, String projectName) {

        // 获取菜单
        List<AppAuthMenuDO> list = getMenuList(rank, projectName);

        // 正序
        Collections.sort(list);

        // 对象转换
//        List<AuthMenuRespVO> menuResplist = BeanUtilX.copyList(list, AuthMenuRespVO::new);
        List<AuthMenuRespVO> menuResplist = list.stream().map(item -> {
            AuthMenuRespVO convert = BeanUtilX.copy(item, AuthMenuRespVO::new);
            convert.setItemId(convert.getMenuId());
            convert.setItemCode(convert.getMenuId());
            convert.setItemName(convert.getMenuName());
            return convert;
        }).collect(Collectors.toList());

        // 转成树形结构
        List<AuthMenuRespVO> tree = TreeUtilX.listToTree(menuResplist);

        List<Map<String, Object>> maps = menu_digui(tree);

        // 清空内存
        list = null;
        menuResplist = null;
        tree = null;
        return maps;
    }


    private List<AppAuthMenuDO> getMenuList(int rank, String projectName) {

        LoginUser loginUser = WebFrameworkUtilX.getRequiredLoginUser();
        //Integer category = loginUser.getIdentityType();
        Integer category = 0;

        List<AppAuthMenuDO> list = null;
        if (loginUser.getIsAdmin() == BaseEnum.ADMIN.getKey()) {
            // 超级管理员，获取所有菜单1，2级的菜单
            list = appAuthMenuMapper.selectListOrderSort(rank, projectName, category);

        } else {
            // 不是超级管理员, 获取当前用户角色对应的菜单

            //获取用户的角色id
            List<AuthRoleDO> roleList = authUserService.getRoleIdByUserId(loginUser.getUserId());
            if (CollUtilX.isEmpty(roleList)) {
                throw exception(AUTH_LOGIN_BAD_ROLE);
            }
            List<Long> roleIds = roleList.stream().map(AuthRoleDO::getRoleId).collect(Collectors.toList());

            // 根据角色id，获取当前用户角色对应的菜单
            Collection<AppAuthMenuDO> menuDOS = appAuthMenuMapper.getMenuByRoleId(roleIds, category);
            if (CollUtilX.isEmpty(menuDOS)) {
                throw exception(AUTH_LOGIN_BAD_MENU);
            }

            // 获取角色对应的菜单，再根据菜单获取关联的所有1，2级菜单
            list = buildRouter_1_2(menuDOS, projectName, category);
        }

        // 未配置菜单
        if (CollUtilX.isEmpty(list)) {
            throw exception(AUTH_LOGIN_BAD_MENU2);
        }

        return list;
    }


    /**
     * 递归封装菜单
     */
    private List<Map<String, Object>> menu_digui(List<AuthMenuRespVO> menuResps) {
        List<Map<String, Object>> maps = new ArrayList<Map<String, Object>>();
        for (AuthMenuRespVO menuResp : menuResps) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("path", getPath(menuResp.getPath()));
            map.put("menuId", menuResp.getMenuId());
            map.put("menuName", menuResp.getMenuName());
            map.put("hidden", menuResp.getHidden());
            map.put("icon", menuResp.getIcon());
            map.put("projectName", menuResp.getProjectName());

//            getPath(menuResp.getPath());

            List<AuthMenuRespVO> children = menuResp.getChildren();
            if (CollUtilX.isNotEmpty(children) && menuResps.size() != maps.size()) {
                List<Map<String, Object>> maps1 = menu_digui(children);
                map.put("children", maps1);
                maps.add(map);
                //递归
            } else if (CollUtilX.isEmpty(children) && menuResps.size() != maps.size()) {
                maps.add(map);
            } else {
                return maps;
            }
        }
        return maps;
    }

    private String getPath(String path) {
        if (StrUtilX.isEmpty(path)) {
            path = "";
        } else {
            path = "/" + path;
        }
        return path;
    }

    @Override
    public List<Map<String, Object>> authMenuRouterAll(JSONObject jsonString) {
        String key = "menuName";
//        String projectName = MgsApplication.getProjectName();
        String projectName = "";
        if (ObjUtilX.isNotEmpty(jsonString) && StrUtilX.isNotEmpty(jsonString.getStr(key))) {
            projectName = jsonString.getStr(key);
        }
        // 查询当前用户的菜单
        return buildMenuAll(NavBarEnum.MENU.getKey(), projectName);
    }

    @Override
    public List<AuthMenuRespVO> authMenuTree(AppAuthMenuTreeReqVO reqVO) {

        // 这里条件 category
        List<AppAuthMenuDO> list = appAuthMenuMapper.selectListByNotHidden(reqVO.getCategory());

        // 正序
        Collections.sort(list);

        List<AuthMenuRespVO> collect = list.stream().map(item -> {
            AuthMenuRespVO convert = BeanUtilX.copy(item, AuthMenuRespVO::new);
            convert.setItemId(convert.getMenuId());
            convert.setItemCode(convert.getMenuId());
            convert.setItemName(convert.getMenuName());
            return convert;
        }).collect(Collectors.toList());

        //非超级管理员隐藏
        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        if (loginUser.getIsAdmin() != BaseEnum.ADMIN.getKey()) {
            filterMenu(collect);
        }
        List<AuthMenuRespVO> authMenuResps = TreeUtilX.listToTree(collect);
        return authMenuResps;
    }

    /**
     * 非超级管理员过滤菜单
     *
     * @param collect
     */
    private void filterMenu(List<AuthMenuRespVO> collect) {
        String[] menus = {"应用配置", "应用实例", "菜单管理"};
        for (int i = 0; i < menus.length; i++) {
            Iterator<AuthMenuRespVO> iterator = collect.iterator();
            while (iterator.hasNext()) {
                AuthMenuRespVO menuRespVO = iterator.next();
                if (menus[i].equals(menuRespVO.getItemName())) {
                    iterator.remove();
                    break;
                }
            }
        }
    }

    @Override
    public String authMenuMind(Integer hasPermission, Integer category) {
        LoginUser loginUser = WebFrameworkUtilX.getRequiredLoginUser();
        Integer isAdmin = loginUser.getIsAdmin();

//        List<AppAuthMenuDO> list = appAuthMenuMapper.selectList();
        List<AppAuthMenuDO> list = appAuthMenuMapper.selectListByCategory(category);
        // 正序
        Collections.sort(list);

        List<AuthMenuRespVO> collect = new ArrayList<>(list.size());
        for (AppAuthMenuDO item : list) {
            if (isAdmin == BaseEnum.NOT_ADMIN.getKey() && "平台".equals(item.getMenuName())) {
                continue;
            }
            AuthMenuRespVO convert = BeanUtilX.copy(item, AuthMenuRespVO::new);
            convert.setItemId(convert.getMenuId());
            convert.setItemCode(convert.getMenuId());
            convert.setItemName(convert.getMenuName());
            convert.setRemark(convert.getPath());
            collect.add(convert);
        }

        List<AuthMenuRespVO> authMenuResps = TreeUtilX.listToTree(collect);

        String jsonString = JsonUtilX.toJsonString(authMenuResps);
        return jsonString;
    }


    /**
     * 根据不同类型的菜单，构建1，2，3级菜单路由
     */
    private List<AppAuthMenuDO> buildRouter_1_2(Collection<AppAuthMenuDO> menuDOS,
                                                String projectName,
                                                Integer category) {
        List<AppAuthMenuDO> listDO = new ArrayList<>();

        Set<String> pidSet = new HashSet<>();
        for (AppAuthMenuDO entity : menuDOS) {
            String pids = entity.getParentItemIds();
            String[] split = pids.split(",");

            if (entity.getType() == NavBarEnum.DIRECTORY.getKey()) {
                // 如果是目录类型，保存所有的子集
                List<String> strings = appAuthMenuMapper.selectChildrenMenuList(entity.getMenuId(), projectName, category);
                pidSet.addAll(strings);

                // 加上自己
                pidSet.add(entity.getMenuId());

            } else if (entity.getType() == NavBarEnum.MENU.getKey()) {
                // 如果是菜单，加上自己
                pidSet.add(entity.getMenuId());
            }
            // 保存所有的父级
            for (String pid : split) {
                pidSet.add(pid);
            }
        }

        // 遍历找出该用户的层级菜单
        if (CollUtilX.isNotEmpty(pidSet)) {
            listDO = appAuthMenuMapper.selectListInId(pidSet, projectName, category);
        }
        return listDO;

    }

}