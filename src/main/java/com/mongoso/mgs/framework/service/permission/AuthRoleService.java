package com.mongoso.mgs.framework.service.permission;


import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.controller.admin.permission.vo.role.*;

import java.util.List;

/**
 * 角色表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-21 15:04:16
 */
public interface AuthRoleService {

    /**
     * 角色表新增
     */
    Long authRoleAdd(AuthRoleAditReqVO reqVO);

    /**
     * 角色表编辑
     */
    Long authRoleEdit(AuthRoleAditReqVO reqVO);

    /**
     * 角色表删除
     */
    Long authRoleDel(Long id);


    /**
     * 角色表详情
     */
    AuthRoleRespVO authRoleDetail(Long id);

    /**
     * 角色表列表
     */
    List<AuthRoleRespVO> authRoleList(AuthRoleQueryReqVO reqVO);

    /**
     * 角色表分页
     */
    PageResult<AuthRoleRespVO> authRolePage(AuthRolePageReqVO reqVO);

    Long authRoleDuplicheck(AuthRoleDuplicheckReqVO reqVO);

    Integer authRoleMenuConfig(AuthRoleConfigReqVO reqVO);

    AuthRoleMenuConfigRespVO authRoleMenuDetail(AuthRoleMenuPrimaryReqVO req);

    Integer authRoleCopy(AuthRoleCopyReqVO req);

}

