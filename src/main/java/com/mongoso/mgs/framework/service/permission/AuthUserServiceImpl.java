package com.mongoso.mgs.framework.service.permission;


import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.mongoso.mgs.framework.common.constant.PwdConstant;
import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.enums.BaseEnum;
import com.mongoso.mgs.framework.common.enums.UserTypeEnum;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.common.util.CollUtilX;
import com.mongoso.mgs.framework.common.util.StrUtilX;
import com.mongoso.mgs.framework.controller.admin.permission.vo.user.*;
import com.mongoso.mgs.framework.dal.db.permission.AuthRoleDO;
import com.mongoso.mgs.framework.dal.db.permission.AuthUserDO;
import com.mongoso.mgs.framework.dal.db.permission.AuthUserRoleDO;
import com.mongoso.mgs.framework.dal.mysql.permission.AuthRoleMapper;
import com.mongoso.mgs.framework.dal.mysql.permission.AuthUserMapper;
import com.mongoso.mgs.framework.dal.mysql.permission.AuthUserRoleMapper;
import com.mongoso.mgs.framework.enums.RedisKey;
import com.mongoso.mgs.framework.redis.core.RedisTemplateX;
import com.mongoso.mgs.framework.service.auth.AuthService;
import com.mongoso.mgs.framework.util.RsaUtilX;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

import static com.mongoso.mgs.framework.common.exception.enums.GlobalErrorCodeConstants.PARAM_NOT_NULL;
import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exception;
import static com.mongoso.mgs.framework.enums.SassErrorCodeConstants.*;

/**
 * 用户表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-29 20:37:45
 */
@Service
public class AuthUserServiceImpl implements AuthUserService {

    @Value(value = "${login.privateKey}")
    private String privateKey;

    @Resource
    private RedisTemplateX redisTemplateX;

    @Resource
    private AuthUserMapper authUserMapper;

    @Resource
    private AuthRoleMapper authRoleMapper;

    @Resource
    private AuthUserRoleMapper authUserRoleMapper;

    @Resource
    private PasswordEncoder passwordEncoder;

    @Resource
    private AuthService authService;

    @Override
    @DSTransactional(rollbackFor = Throwable.class)
    public Long authUserAdd(AuthUserAditReqVO reqVO) {

        // 验重
        duplicheck(reqVO, null);

        // 类型转换
        AuthUserDO newDO = BeanUtilX.copy(reqVO, new AuthUserDO());

        if (StrUtilX.isNotEmpty(reqVO.getUserPassword())) {
            String encode = passwordEncoder.encode(reqVO.getUserPassword());
            newDO.setUserPassword(encode);
        } else {
            String encode = passwordEncoder.encode(PwdConstant.PWD);
            newDO.setUserPassword(encode);
        }

        newDO.setEnable(BaseEnum.ENABLE.getKey());
        newDO.setIsAdmin(BaseEnum.NOT_ADMIN.getKey());

        authUserMapper.insert(newDO);

        return newDO.getUserId();
    }

    private void duplicheck(AuthUserAditReqVO reqVO, AuthUserDO oldDO) {
        long count = authUserMapper.selectCountByUserAccount(reqVO.getUserAccount());
        if (oldDO == null){
            // 新增验重
            if (count > 0) {
                throw exception(USER_USERNAME_EXISTS,reqVO.getUserAccount());
            }
        }else {
            if (count > 0 && !reqVO.getUserAccount().equals(oldDO.getUserAccount())) {
                throw exception(USER_USERNAME_EXISTS,reqVO.getUserAccount());
            }
        }

        count = authUserMapper.selectCountByUserPhoneNumber(reqVO.getPhoneNumber());
        if (oldDO == null){
            // 新增验重
            if (count > 0) {
                throw exception("**********", "手机号已经存在", reqVO.getPhoneNumber());
            }
        }else {
            if (count > 0 && !reqVO.getPhoneNumber().equals(oldDO.getPhoneNumber())) {
                throw exception("**********", "手机号已经存在",reqVO.getPhoneNumber());
            }
        }
    }

    @Override
    @DSTransactional(rollbackFor = Throwable.class)
    public Long authUserEdit(AuthUserAditReqVO reqVO) {

        // 校验存在
        AuthUserDO oldDO = this.userValidateExists(reqVO.getUserId());

        // 验重
        duplicheck(reqVO, oldDO);

        // 更新
        AuthUserDO newDO = BeanUtilX.copy(reqVO, AuthUserDO::new);

        // 更新密码(密码不等于, 才为没加密)
        if (StrUtilX.isNotEmpty(reqVO.getUserPassword()) && !reqVO.getUserPassword().equals(oldDO.getUserPassword())) {
            String encode = passwordEncoder.encode(reqVO.getUserPassword());
            newDO.setUserPassword(encode);
            // 清除缓存, 退出登录
            //redisTemplateX.clearUserCache(reqVO.getUserId());
            authService.clearLoginUserCache(UserTypeEnum.PC.getValue(),reqVO.getUserId());
        }

        // 更新用户为禁用
        if (reqVO.getEnable() != null && reqVO.getEnable() == BaseEnum.NOT_ENABLE.key && oldDO.getEnable() == BaseEnum.ENABLE.key) {
            // 清除缓存, 退出登录
            //redisTemplateX.clearUserCache(reqVO.getUserId());
            authService.clearLoginUserCache(UserTypeEnum.PC.getValue(),reqVO.getUserId());
        }

        authUserMapper.updateById(newDO);
        return newDO.getUserId();
    }

    private void doInsertBatch(Long userId, List<Long> roleIdList) {
        List<AuthUserRoleDO> collect = roleIdList.stream().map(roleId -> {
            AuthUserRoleDO authUserRoleDO = new AuthUserRoleDO();
            authUserRoleDO.setUserId(userId);
            authUserRoleDO.setRoleId(roleId);
            return authUserRoleDO;
        }).collect(Collectors.toList());

        // 批量添加关联数据
        authUserRoleMapper.insertBatch(collect);
    }

    @Override
    @DSTransactional(rollbackFor = Throwable.class)
    public Long authUserDel(Long id) {
        // 校验存在
        this.userValidateExists(id);

        authUserMapper.deleteById(id);
        return id;
    }

    @Override
    public AuthUserRespVO authUserDetail(Long id) {
        AuthUserDO oldDO = authUserMapper.selectById(id);
        if (oldDO == null) {
            return null;
        }
        AuthUserRespVO resp = BeanUtilX.copy(oldDO, AuthUserRespVO::new);

        // 查询对应的角色id
        List<AuthUserRoleDO> roleDOS = authUserRoleMapper.selectListByUserId(id);

        if (CollUtilX.isNotEmpty(roleDOS)) {
            AuthUserRoleDO authUserRoleDO = roleDOS.get(0);
            resp.setRoleId(authUserRoleDO.getRoleId());
            AuthRoleDO roleDO = authRoleMapper.selectById(authUserRoleDO.getRoleId());
            //因为中间表的数据并未删除,所以加一层判断
            if (roleDO == null) {
                resp.setRoleId(null);
                resp.setRoleName(null);
            } else {
                resp.setRoleName(roleDO.getRoleName());
            }
        }

        return resp;
    }

    /**
     * 用户表列表
     */
    @Override
    public List<AuthUserRespVO> authUserList(AuthUserQueryReqVO reqVO) {
        List<AuthUserDO> list = authUserMapper.selectList(reqVO);
        return BeanUtilX.copyList(list, AuthUserRespVO::new);
    }

    /**
     * 用户表分页
     */
    @Override
    public PageResult<AuthUserRespVO> authUserPage(AuthUserPageReqVO reqVO) {
        Long userId = WebFrameworkUtilX.getLoginUserId();
        //超级管理员不展示
        reqVO.setIsAdmin(BaseEnum.NOT_ADMIN.getKey());
        PageResult<AuthUserDO> pageVo = authUserMapper.selectPage(reqVO);
        //超级管理员不展示
        List<AuthUserRespVO> collect = pageVo.getList().stream()
                .filter(item -> !(item.getIsAdmin() == BaseEnum.ADMIN.getKey()))
                .map((item) -> {
                    AuthUserRespVO resp = new AuthUserRespVO();
                    BeanUtilX.copy(item, resp);
                    // 拷贝对象会把dataRoleId的值考进去，所以先删除

                    // 判断是否为当前登录用户
                    if (item.getUserId().equals(userId)) {
                        resp.setIsLongUser(BaseEnum.YES.key);
                    }

                    // 功能角色
                    List<AuthUserRoleDO> authUserRoleDOS = authUserRoleMapper.selectListByUserId(resp.getUserId());
                    if (CollUtilX.isNotEmpty(authUserRoleDOS)) {
                        // 查询角色名称，一个用户对应一个角色
                        AuthUserRoleDO authUserRoleDO = authUserRoleDOS.get(0);
                        AuthRoleDO roleDO = authRoleMapper.selectById(authUserRoleDO.getRoleId());
                        if (roleDO != null) {
                            resp.setRoleId(roleDO.getRoleId());
                            resp.setRoleName(roleDO.getRoleName());
                        }
                    }
                    return resp;
                }).collect(Collectors.toList());

        return PageResult.init(pageVo, collect);

    }

    @Override
    public Long authUserDuplicheck(AuthUserQueryReqVO reqVO) {
        if (null == reqVO.getUserId()) {
            return authUserMapper.selectCountByName(reqVO.getUserName());
        } else {
            AuthUserDO oldDO = userValidateExists(reqVO.getUserId());
            if (!oldDO.getUserName().equals(reqVO.getUserName())) {
                return authUserMapper.selectCountByName(reqVO.getUserName());
            }
        }
        return 0L;
    }


    private AuthUserDO userValidateExists(Long id) {
        AuthUserDO oldDO = authUserMapper.selectById(id);
        if (oldDO == null) {
            throw exception(USER_NOT_EXISTS);
        }
        return oldDO;
    }


    @Override
    public List<AuthRoleDO> getRoleIdByUserId(Long userId) {
        return authRoleMapper.getRoleIdByUserId(userId);
    }

    @Override
    public boolean isPasswordMatch(String password, String userPassword) {
        return passwordEncoder.matches(password, userPassword);
    }

    @Override
    public AuthUserDO selectOneByUserAccount(String userAccount) {
        return authUserMapper.selectOneByUserAccount(userAccount);
    }

    @Override
    public void authUserRoleConfig(AuthUserConfigReqVO reqVO) {
        // 参数校验
        checkParam(reqVO);
        Long userId = reqVO.getUserId();

        // 修改时先删除再编辑
        authUserRoleMapper.deleteByUserId(userId);

        if (reqVO.getRoleId() != null) {
            AuthUserRoleDO newDO = new AuthUserRoleDO();
            newDO.setUserId(userId);
            newDO.setRoleId(reqVO.getRoleId());
            // 添加新的
            authUserRoleMapper.insert(newDO);
        }
    }

    private void checkParam(AuthUserConfigReqVO reqVO) {

        if (reqVO.getUserId() == null){
            throw exception(PARAM_NOT_NULL,"userId");
        }

//        if (reqVO.getRoleId() == null){
//            throw exception(PARAM_NOT_NULL,"roleId");
//        }
    }


    @Override
    public void authUpdatePwd(AuthUserPwdEditReqVO reqVO) {

        AuthUserDO newDO = new AuthUserDO();
        newDO.setUserId(reqVO.getUserId());

        AuthUserDO oldDO = authUserMapper.selectById(reqVO.getUserId());

        // 加密后的密码
        String password = reqVO.getUserPassword();

        // 使用对称加密,将加密后的密码解析成原密码
        password = RsaUtilX.decrypt(password, privateKey);

//        // 密码匹配
        if (!passwordEncoder.matches(password, oldDO.getUserPassword())) {
            throw exception(USER_PASSWORD_FAILED);
        }

        // 加密后的密码
        String newUserPassword = reqVO.getNewUserPassword();

        // 使用对称加密,将加密后的密码解析成原密码
        newUserPassword = RsaUtilX.decrypt(newUserPassword, privateKey);

        newDO.setUserPassword(passwordEncoder.encode(newUserPassword));
        authUserMapper.updateById(newDO);
        // 删除验证码
        redisTemplateX.deleteKey(RedisKey.getSmsCodeMobile(oldDO.getPhoneNumber()));

        // 退出登录
        authService.logout();
    }

    @Override
    public void authUserForgetPassword(AuthUserForgetPwdReqVO reqVO) {

        // 校验验证码
        if (reqVO.getSmsVerifyCode().length() != 6) {
            throw exception(SMS_CODE_NOT_CORRECT);
        }
        authService.validateSmsCode(reqVO.getPhoneNumber(), reqVO.getSmsVerifyCode());

        AuthUserDO authUserDO = authUserMapper.selectOneByUserAccount(reqVO.getUserAccount());
        if (authUserDO == null) {
            throw exception(USER_OR_MOBILE_ERROR);
        }

        if (!authUserDO.getPhoneNumber().equals(reqVO.getPhoneNumber())) {
            throw exception(USER_OR_MOBILE_ERROR);
        }

        authUserDO.setUserPassword(passwordEncoder.encode(reqVO.getNewUserPassword()));
        authUserMapper.updateById(authUserDO);
        redisTemplateX.deleteKey(RedisKey.getSmsCodeMobile(reqVO.getPhoneNumber()));
    }

    @Override
    public void authUserResetPassword(AuthUserPwdEditReqVO reqVO) {
        AuthUserDO oldDO = userValidateExists(reqVO.getUserId());
        AuthUserDO authUserDO = new AuthUserDO();
        authUserDO.setUserId(reqVO.getUserId());
        String newUserPassword = reqVO.getNewUserPassword();
        newUserPassword = RsaUtilX.decrypt(newUserPassword, privateKey);
        authUserDO.setUserPassword(passwordEncoder.encode(newUserPassword));
        authUserMapper.updateById(authUserDO);
        // 删除验证码
        redisTemplateX.deleteKey(RedisKey.getSmsCodeMobile(oldDO.getPhoneNumber()));
    }

    @Override
    public PageResult<AuthUserRespVO> authUserPicker(AuthUserPageReqVO reqVO) {
        PageResult<AuthUserDO> pageResult = authUserMapper.selectPage(reqVO);
        return BeanUtilX.copy(pageResult, AuthUserRespVO::new);
    }


}