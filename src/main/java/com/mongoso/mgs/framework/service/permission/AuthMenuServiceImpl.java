package com.mongoso.mgs.framework.service.permission;

import cn.hutool.json.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.enums.BaseEnum;
import com.mongoso.mgs.framework.common.enums.NavBarEnum;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.exception.ErrorCode;
import com.mongoso.mgs.framework.common.util.*;
import com.mongoso.mgs.framework.controller.admin.permission.vo.menu.*;
import com.mongoso.mgs.framework.dal.db.permission.AuthMenuButtonDO;
import com.mongoso.mgs.framework.dal.db.permission.AuthMenuDO;
import com.mongoso.mgs.framework.dal.db.permission.AuthRoleDO;
import com.mongoso.mgs.framework.dal.mysql.permission.AuthMenuButtonMapper;
import com.mongoso.mgs.framework.dal.mysql.permission.AuthMenuMapper;
import com.mongoso.mgs.framework.util.tree.TreeDrag;
import com.mongoso.mgs.framework.util.tree.TreeDragBO;
import com.mongoso.mgs.framework.util.tree.TreeUtilX;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exception;
import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exceptionMsg;
import static com.mongoso.mgs.framework.enums.SassErrorCodeConstants.*;

/**
 * 菜单表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-21 15:04:16
 */
@Service
public class AuthMenuServiceImpl implements AuthMenuService {

    private static String key = "system:permission:config";

    @Resource
    private AuthMenuMapper authMenuMapper;

    @Resource
    private AuthMenuButtonMapper authMenuButtonMapper;

    @Resource
    private AuthUserService authUserService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;


    @Override
    @DSTransactional(rollbackFor = Throwable.class)
    public String authMenuAdd(AuthMenuAddReqVO reqVO) {

        if (reqVO.getCategory() == null) {
            throw exception(new ErrorCode("1002000000", "category 不能为空"));
        }

        if (reqVO.getType() == null) {
            throw exception(new ErrorCode("1002000000", "type 不能为空"));
        }

        if (StrUtilX.isEmpty(reqVO.getMenuName())) {
            throw exception(new ErrorCode("1002000000", "menuName 不能为空"));
        }

        // 获取自增id
        Long seq = maxSeq();

        Integer sort = authMenuMapper.sort(reqVO.getCategory());
        if (sort == null) {
            sort = 1;
        }

        String parentItemId = "0";
        String projectName = "setting";
        if (StrUtilX.isNotEmpty(reqVO.getParentItemId())) {
            AuthMenuDO authMenuDO = menuValidateExists(reqVO.getParentItemId());
            if (authMenuDO.getType() != NavBarEnum.DIRECTORY.getKey()) {
                throw exception(MENU_PARENT_NOT_DIR);
            }

            projectName = authMenuDO.getProjectName();
            parentItemId = reqVO.getParentItemId();
        }

        AuthMenuDO newDO = new AuthMenuDO();

        newDO.setCategory(reqVO.getCategory());
        newDO.setMenuName(reqVO.getMenuName());
        newDO.setType(reqVO.getType());

        newDO.setMenuSeq(seq);
        newDO.setMenuId(seq + "");
        newDO.setMenuCode(seq + "");
        newDO.setSort(sort + 1);

        newDO.setProjectName(projectName);
        newDO.setParentItemId(parentItemId);

        newDO.setHidden(0);
        newDO.setIsSystem(1);// 系统菜单
        newDO.setPathToken(0);

        authMenuMapper.insert(newDO);

        // 删除redis的按钮配置
//        stringRedisTemplate.delete(key);

        return newDO.getMenuId();
    }

    private AuthMenuDO menuValidateExists(String menuId) {
        AuthMenuDO oldDO = authMenuMapper.selectById(menuId);
        if (oldDO == null) {
            throw exception(MENU_NOT_EXISTS);
        }
        return oldDO;
    }

    @Override
    public String authMenuEdit(AuthMenuEditReqVO reqVO) {

        if (StrUtilX.isEmpty(reqVO.getMenuId())) {
            throw exception(new ErrorCode("1002000000", "menuId 不能为空"));
        }

        AuthMenuDO oldDO = menuValidateExists(reqVO.getMenuId());

        // 编码验重
        if (StrUtilX.isNotEmpty(reqVO.getMenuCode())){
            if (!reqVO.getMenuCode().equals(oldDO.getMenuCode())){
                // 新值和旧值不一样，需要验重
                Long count = authMenuMapper.selectCountByCode(reqVO.getMenuCode());
                if (count != null && count > 0){
                    throw new BizException("5001", "菜单编码 不能重复");
                }
            }
        }

        AuthMenuDO newDO = new AuthMenuDO();
        newDO.setMenuId(reqVO.getMenuId());

        newDO.setMenuName(reqVO.getMenuName());
        newDO.setIcon(reqVO.getIcon());
        newDO.setMenuParam(reqVO.getMenuParam());

        newDO.setMenuCode(reqVO.getMenuCode());

        // 只能第一次修改
        if (oldDO.getPageType() == null) {
            newDO.setPageType(reqVO.getPageType());
        }

        // 菜单类型修改的部分
        if (oldDO.getType() == NavBarEnum.MENU.getKey()) {
            newDO.setPath(reqVO.getPath());
            newDO.setHidden(reqVO.getHidden());

            newDO.setAppType(reqVO.getAppType());
            newDO.setPathType(reqVO.getPathType());
            newDO.setPathToken(reqVO.getPathToken());
        }

        authMenuMapper.updateById(newDO);

        // 先删除
        authMenuButtonMapper.deleteByMenuId(reqVO.getMenuId());

        // 添加按钮
        if (CollUtilX.isNotEmpty(reqVO.getButtonList())) {
            List<AuthMenuButtonDO> buttonDOList = new ArrayList<>();
            for (int i = 0; i < reqVO.getButtonList().size(); i++) {

                AuthButtonReqVO authButtonReqVO = reqVO.getButtonList().get(i);
                AuthMenuButtonDO buttonDO = new AuthMenuButtonDO();
                buttonDO.setMenuId(reqVO.getMenuId());

                buttonDO.setButtonName(authButtonReqVO.getButtonName());
                buttonDO.setButtonCode(authButtonReqVO.getButtonCode());

                buttonDO.setPath(authButtonReqVO.getPath());
                buttonDO.setButtonParam(authButtonReqVO.getButtonParam());
                buttonDO.setSort(i + 1);

                buttonDO.setCategory(oldDO.getCategory());

                buttonDOList.add(buttonDO);
            }

            // 在新增
            authMenuButtonMapper.insertBatch(buttonDOList);

        }
        return reqVO.getMenuId();
    }

    @Override
    public String authMenuDel(AuthMenuPrimaryReqVO reqVO) {
        int i = authMenuMapper.deleteByMenuId(reqVO.getMenuId());
        return reqVO.getMenuId();
    }

    @Override
    public AuthMenuDetailRespVO authMenuDetail(String menuId) {
        if (StrUtilX.isEmpty(menuId)) {
            throw exception(new ErrorCode("1002000000", "menuId 不能为空"));
        }
        AuthMenuDO authMenuDO = menuValidateExists(menuId);

        AuthMenuDetailRespVO respVO = BeanUtilX.copy(authMenuDO, AuthMenuDetailRespVO::new);

        List<AuthMenuButtonDO> list = authMenuButtonMapper.selectByMenuId(menuId);
        List<AuthMenuButtonRespVO> buttonRespVOS = BeanUtilX.copy(list, AuthMenuButtonRespVO::new);
        respVO.setButtonList(buttonRespVOS);
        return respVO;
    }

    private Long maxSeq() {
        Long menuSeq = 10001L;
        Long max = authMenuMapper.max();
        if (max != null) {
            // 有用数据库的
            menuSeq = max + 1;
        }
        return menuSeq;
    }


    /**
     * 根据用户id，获取树形菜单
     * <p>
     * rank：
     */
    private List<Map<String, Object>> buildMenu(int rank, String projectName) {

        // 获取菜单
        List<AuthMenuDO> list = getMenuList(rank, projectName);

        // 对象转换
        List<AuthMenuRespVO> collect = list.stream().map(item -> {
            AuthMenuRespVO convert = BeanUtilX.copy(item, AuthMenuRespVO::new);
            convert.setItemId(convert.getMenuId());
            convert.setItemCode(convert.getMenuId());
            convert.setItemName(convert.getMenuName());
            return convert;
        }).collect(Collectors.toList());

        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();

        //非超级管理员隐藏
        if (loginUser.getIsAdmin() != BaseEnum.ADMIN.getKey()) {
            filterMenu(collect);
        }

        // 转成树形结构
        List<AuthMenuRespVO> tree = TreeUtilX.listToTree(collect);

        // 去除最顶层
        tree = rmTop(tree);

        List<Map<String, Object>> maps = menu_digui(tree);

        // 清空内存
        list = null;
        collect = null;
        tree = null;
        return maps;
    }

    private List<AuthMenuRespVO> rmTop(List<AuthMenuRespVO> tree) {
        if (CollUtilX.isEmpty(tree)) {
            return null;
        }

        AuthMenuRespVO authMenuRespVO = tree.get(0);
        return authMenuRespVO.getChildren();
    }

    private List<AuthMenuDO> getMenuList(int rank, String projectName) {

        LoginUser loginUser = WebFrameworkUtilX.getRequiredLoginUser();

        //Integer category = loginUser.getIdentityType();
        Integer category = 0;// saas代码，默认是查0的菜单

        List<AuthMenuDO> list = authMenuMapper.selectListOrderSort(rank, projectName, category);;
        if (loginUser.getIsAdmin() != BaseEnum.ADMIN.getKey()) {
            // 不是超级管理员, 获取当前用户角色对应的菜单

            //获取用户的角色id
            List<AuthRoleDO> roleList = authUserService.getRoleIdByUserId(loginUser.getUserId());
            if (CollUtilX.isEmpty(roleList)) {
                throw exception(AUTH_LOGIN_BAD_ROLE);
            }
            List<Long> roleIds = roleList.stream().map(AuthRoleDO::getRoleId).collect(Collectors.toList());

            // 根据角色id，获取当前用户角色对应的菜单
            Collection<AuthMenuDO> menuDOS = authMenuMapper.getMenuByRoleId(roleIds, category);
            if (CollUtilX.isEmpty(menuDOS)) {
                throw exception(AUTH_LOGIN_BAD_MENU);
            }

            // 拼接菜单的所有父级
            list = buildRouter_1_2(list,menuDOS);
        }

        // 未配置菜单
        if (CollUtilX.isEmpty(list)) {
            throw exception(AUTH_LOGIN_BAD_MENU2);
        }

        return list;
    }

    /**
     * 递归封装菜单
     */
    private List<Map<String, Object>> menu_digui(List<AuthMenuRespVO> menuResps) {
        List<Map<String, Object>> maps = new ArrayList<Map<String, Object>>();
        for (AuthMenuRespVO menuResp : menuResps) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("path", getPath(menuResp.getPath()));
            map.put("menuId", menuResp.getMenuId());
            map.put("menuName", menuResp.getMenuName());
            map.put("pid", menuResp.getParentItemId());
            map.put("hidden", menuResp.getHidden());
            map.put("icon", menuResp.getIcon());
            map.put("projectName", menuResp.getProjectName());

//            getPath(menuResp.getPath());

            List<AuthMenuRespVO> children = menuResp.getChildren();
            if (CollUtilX.isNotEmpty(children) && menuResps.size() != maps.size()) {
                List<Map<String, Object>> maps1 = menu_digui(children);
                map.put("children", maps1);
                maps.add(map);
                //递归
            } else if (CollUtilX.isEmpty(children) && menuResps.size() != maps.size()) {
                maps.add(map);
            } else {
                return maps;
            }
        }
        return maps;
    }

    private String getPath(String path) {
        if (StrUtilX.isEmpty(path)) {
            path = "";
        } else {
            path = "/" + path;
        }
        return path;
    }

    @Override
    public List<AuthMenuRespVO> authMenuTree(AuthMenuTreeReqVO reqVO) {

        if (reqVO.getCategory() == null) {
            throw exception(new ErrorCode("1002000000", "category 不能为空"));
        }

        // 这里条件 category
        List<AuthMenuDO> list = authMenuMapper.selectListByNotHidden(reqVO.getCategory());

        List<AuthMenuRespVO> collect = list.stream().map(item -> {
            AuthMenuRespVO convert = BeanUtilX.copy(item, AuthMenuRespVO::new);
            convert.setItemId(convert.getMenuId());
            convert.setItemCode(convert.getMenuId());
            convert.setItemName(convert.getMenuName());
            return convert;
        }).collect(Collectors.toList());

        List<AuthMenuRespVO> authMenuResps = TreeUtilX.listToTree(collect);
        return authMenuResps;
    }

    /**
     * 非超级管理员过滤菜单
     *
     * @param collect
     */
    private void filterMenu(List<AuthMenuRespVO> collect) {
        String[] menus = {"应用配置", "应用实例", "菜单管理"};
        for (int i = 0; i < menus.length; i++) {
            Iterator<AuthMenuRespVO> iterator = collect.iterator();
            while (iterator.hasNext()) {
                AuthMenuRespVO menuRespVO = iterator.next();
                if (menus[i].equals(menuRespVO.getItemName())) {
                    iterator.remove();
                    break;
                }
            }
        }
    }

    @Override
    public Collection<AuthMenuDO> selectListInRoleId(List<Long> collect, Integer category) {
        return authMenuMapper.getMenuByRoleId(collect, category);
    }

    @Override
    public List<Map<String, Object>> authMenuRouter(@RequestBody JSONObject jsonString) {
        String key = "menuName";
        String projectName = "";
        if (ObjUtilX.isNotEmpty(jsonString) && StrUtilX.isNotEmpty(jsonString.getStr(key))) {
            projectName = jsonString.getStr(key);
        }
        // 查询当前用户的菜单
        return buildMenu(NavBarEnum.MENU.getKey(), projectName);

    }


    @Override
    public List<Map<String, Object>> authMenuRouterAll(JSONObject jsonString) {
        String key = "menuName";
//        String projectName = MgsApplication.getProjectName();
        String projectName = "";
        if (ObjUtilX.isNotEmpty(jsonString) && StrUtilX.isNotEmpty(jsonString.getStr(key))) {
            projectName = jsonString.getStr(key);
        }
        // 查询当前用户的菜单
        return buildMenuAll(NavBarEnum.MENU.getKey(), projectName);
    }

    /**
     *   上方
     *     1.获取目标对象当前层级的所有对象list
     *     2.获取目标对象的索引
     *     3.当前对象父级id=目标对象的父级id
     *     4.当前对象插入list中目标对象的左侧，index
     *     5.重新调整排序
     *   下方
     *     1.获取目标对象当前层级的所有对象list
     *     2.获取目标对象的索引
     *     3.当前对象父级id=目标对象的父级id
     *     4.当前对象插入list中目标对象的右侧，index+1
     *     5.重新调整排序
     *   内部
     *     1.获取目标对象下级的所有对象list
     *     2.当前对象父级id=目标对象id
     *     3.当前对象插入list尾部
     *     4.重新调整排序
     */
    @Override
    public List<String> authMenuDrag(TreeDrag reqVO) {
        if (reqVO.getDragType() == null) {
            throw exception(new ErrorCode("1002000000", "dragType 不能为空"));
        }
        if (StrUtilX.isEmpty(reqVO.getItemId())) {
            throw exception(new ErrorCode("1002000000", "itemId 不能为空"));
        }
        if (StrUtilX.isEmpty(reqVO.getDestItemId())) {
            throw exception(new ErrorCode("1002000000", "destItemId 不能为空"));
        }

        String itemId = reqVO.getItemId();
        String destItemId = reqVO.getDestItemId();
        Integer dragType = reqVO.getDragType();

        // 当前对象
        AuthMenuDO currentDO = menuValidateExists(itemId);

        // 目标对象
        AuthMenuDO targetDO = menuValidateExists(destItemId);

        // 校验
        // 目标对象必须是目录，目标对象不能是自己
        if (itemId.equals(destItemId)){
            throw exception(MENU_DRAG_ERROR1);
        }

        if (dragType == 2) { // 里面
            // 当方向类型是里面，目标对象必须是目录
            if (targetDO.getType() != NavBarEnum.DIRECTORY.getKey()){
                throw exception(MENU_DRAG_ERROR2);
            }
        }


        // 获取当前菜单类型的所有菜单
        List<AuthMenuDO> list = authMenuMapper.selectListByNotHidden(currentDO.getCategory());

        List<AuthMenuRespVO> collect = list.stream().map(item -> {
            AuthMenuRespVO convert = BeanUtilX.copy(item, AuthMenuRespVO::new);
            convert.setItemId(convert.getMenuId());
            convert.setItemCode(convert.getMenuId());
            convert.setItemName(convert.getMenuName());
            return convert;
        }).collect(Collectors.toList());

        // 转成树形
        List<AuthMenuRespVO> tree = TreeUtilX.listToTree(collect);

        // 当前对象的所有子集
        List<AuthMenuRespVO> child = TreeUtilX.getChildren(tree, currentDO.getMenuId());
        for (AuthMenuRespVO item : child) {
            // 目标对象不能是当前对象的子对象
            if (destItemId.equals(item.getItemId())){
                throw exception(MENU_DRAG_ERROR3);
            }
        }

        // 目标对象当前层级的所有对象（包含目标对象）
        List<AuthMenuRespVO> targetCurrentLevelList = TreeUtilX.getListByPid(collect, targetDO.getParentItemId());

        // 目标对象下级的所有对象
        List<AuthMenuRespVO> targetNextLevelList = TreeUtilX.getListByPid(collect, targetDO.getMenuId());

        TreeDragBO<AuthMenuDO> treeDragBO = buildTreeDragBO(currentDO.getMenuId(), targetDO.getMenuId(), targetCurrentLevelList, targetNextLevelList);

        List<AuthMenuDO> updateList = null;

        AuthMenuDO newDO = new AuthMenuDO();
        newDO.setMenuId(currentDO.getMenuId());
        if (dragType == 0) { // 上方

            // 1.获取目标对象当前层级的所有对象和索引
            List<AuthMenuDO> allItemList = treeDragBO.getAllItemList();
            Integer index = treeDragBO.getIndex();

            // 2.父级id=目标对象的父级id
            newDO.setParentItemId(targetDO.getParentItemId());

            // 3.当前对象添加到目标对象的左侧
            allItemList.add(index, newDO);

            // 4.重新调整sort
            updateList = sort1(allItemList);

        } else if (dragType == 1) { // 下方

            // 1.获取目标当前层级的所有对象
            List<AuthMenuDO> allItemList = treeDragBO.getAllItemList();
            Integer index = treeDragBO.getIndex();

            // 2.父级id=目标对象的父级id
            newDO.setParentItemId(targetDO.getParentItemId());

            // 3.当前对象添加到目标对象的前面，索引
            allItemList.add(index + 1, newDO);

            // 4.重新调整sort
            updateList = sort1(allItemList);

        } else { // 内部

            // 1.获取目标下级的所有对象
            List<AuthMenuDO> nextLevelItemList = treeDragBO.getTargetNextLevelList();

            // 2.当前对象添加到目标对象的最尾部，父级id改为目标对象
            newDO.setParentItemId(targetDO.getMenuId());
            nextLevelItemList.add(newDO);

            // 4.重新调整sort
            updateList = sort1(nextLevelItemList);
        }

        // 批量修改
        if (CollUtilX.isNotEmpty(updateList)){
            authMenuMapper.updateBatch(updateList);
        }

        List<String> collect1 = updateList.stream().map(AuthMenuDO::getMenuId).collect(Collectors.toList());

        return collect1;
    }

    @Override
    public Integer authButtonInit(AuthMenuTreeReqVO reqVO) {
        return 0;
    }


    //@Resource
    //TenantMenuApi tenantMenuApi;
    //
    //@Override
    //public Integer authButtonInit(AuthMenuTreeReqVO reqVO) {
    //
    //    ResultX<List<AuthButtonRespDTO>> listResultX = tenantMenuApi.authButtonList();
    //    List<AuthButtonRespDTO> data = listResultX.getData();
    //
    //    // 查询所有事页面的菜单id
    //    List<AuthMenuDO> authMenuDOS = authMenuMapper.selectList();
    //
    //    List<String> list = Arrays.asList("aa1", "aa2", "aa3", "aa4", "aa5");
    //    List<AuthMenuButtonDO> addList = new ArrayList<>();
    //    for (AuthMenuDO authMenuDO : authMenuDOS) {
    //        System.out.println(authMenuDO.getMenuId()+"="+authMenuDO.getMenuName());
    //        if (authMenuDO.getType() != 2){
    //            continue;
    //        }
    //
    //        for (int i = 0; i < data.size(); i++) {
    //            AuthButtonRespDTO item = data.get(i);
    //            // 跳过特殊的几个
    //            if (list.contains(item.getButtonCode())){
    //                continue;
    //            }
    //
    //            AuthMenuButtonDO addDO = new AuthMenuButtonDO();
    //            addDO.setMenuId(authMenuDO.getMenuId());
    //            addDO.setButtonCode(item.getButtonCode());
    //            addDO.setButtonName(item.getButtonName());
    //            addDO.setSort(i+1);
    //            addDO.setCategory(authMenuDO.getCategory());
    //            addList.add(addDO);
    //        }
    //    }
    //
    //    // 先删除
    //    authMenuButtonMapper.delete(null);
    //
    //    boolean b = authMenuButtonMapper.insertBatch(addList);
    //
    //    return addList.size();
    //}

    private List<AuthMenuDO> sort1(List<AuthMenuDO> allItemList) {
        List<AuthMenuDO> updateList = new ArrayList<>();
        for (int i = 0; i < allItemList.size(); i++) {
            AuthMenuDO authMenuDO = allItemList.get(i);
            authMenuDO.setSort(i + 1);
            updateList.add(authMenuDO);
        }
        return updateList;
    }

    private TreeDragBO<AuthMenuDO> buildTreeDragBO(String currentId,String targetId, List<AuthMenuRespVO> targetCurrentLevelList, List<AuthMenuRespVO> targetNextLevelList) {
        TreeDragBO<AuthMenuDO> treeDragBO = new TreeDragBO<AuthMenuDO>();

        List<AuthMenuDO> allItemList = new ArrayList<>();
//        List<AuthMenuDO> leftItemList = new ArrayList<>();
//        List<AuthMenuDO> rightItemList = new ArrayList<>();
        List<AuthMenuDO> nextLevelItemList = new ArrayList<>();
        boolean flag = false;
        int index = 0;

        // 当前层级list排除当前对象
        Iterator<AuthMenuRespVO> iterator1 = targetCurrentLevelList.iterator();
        while (iterator1.hasNext()){
            AuthMenuRespVO next = iterator1.next();
            if (next.getItemId().equals(currentId)) {
                iterator1.remove();
            }
        }

        for (int i = 0; i < targetCurrentLevelList.size(); i++) {
            AuthMenuRespVO item = targetCurrentLevelList.get(i);

            if (item.getItemId().equals(targetId)) {
                index = i;
            }

            AuthMenuDO authMenuDO = new AuthMenuDO();
            authMenuDO.setMenuId(item.getMenuId());

            // 目标层级的所有对象
            allItemList.add(authMenuDO);
        }

        // 下级list排除当前对象
        Iterator<AuthMenuRespVO> iterator2 = targetNextLevelList.iterator();
        while (iterator2.hasNext()){
            AuthMenuRespVO next = iterator2.next();
            if (next.getItemId().equals(currentId)) {
                iterator2.remove();
            }
        }
        // 目标下级的所有对象
        for (int i = 0; i < targetNextLevelList.size(); i++) {
            AuthMenuRespVO item = targetNextLevelList.get(i);
            AuthMenuDO authMenuDO = new AuthMenuDO();
            authMenuDO.setMenuId(item.getMenuId());
            nextLevelItemList.add(authMenuDO);
        }

        treeDragBO.setIndex(index);
        treeDragBO.setAllItemList(allItemList);
        treeDragBO.setTargetNextLevelList(nextLevelItemList);
        return treeDragBO;
    }


    private List<Map<String, Object>> buildMenuAll(int rank, String projectName) {

        // 获取菜单
        List<AuthMenuDO> list = getMenuList(rank, projectName);

        // 对象转换
        List<AuthMenuRespVO> collect = list.stream().map(item -> {
            AuthMenuRespVO convert = BeanUtilX.copy(item, AuthMenuRespVO::new);
            convert.setItemId(convert.getMenuId());
            convert.setItemCode(convert.getMenuId());
            convert.setItemName(convert.getMenuName());
            return convert;
        }).collect(Collectors.toList());

        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        //非超级管理员隐藏
        if (loginUser.getIsAdmin() != BaseEnum.ADMIN.getKey()) {
            filterMenu(collect);
        }

        // 转成树形结构
        List<AuthMenuRespVO> tree = TreeUtilX.listToTree(collect);

        List<Map<String, Object>> maps = menu_digui(tree);

        // 清空内存
        list = null;
        collect = null;
        tree = null;
        return maps;
    }

    @Override
    public List<String> permissionConfigList() {
        String s = stringRedisTemplate.opsForValue().get(key);
        if (StrUtilX.isNotEmpty(s)) {
            return JsonUtilX.parseArray(s, String.class);
        }

        // TODO 改成按钮表
//        List<String> configList = authMenuMapper.selectListByButton(NavBarEnum.BUTTON.getKey());
        List<String> list = new ArrayList<>();
//        if (CollUtilX.isNotEmpty(configList)) {
//            for (String s1 : configList) {
//                if (StrUtilX.isNotEmpty(s1)) {
//                    list.add(s1);
//                }
//            }
//        }

        String jsonString = JsonUtilX.toJsonString(list);
        stringRedisTemplate.opsForValue().set(key, jsonString);
        return list;
    }

    @Override
    public int insert(String appName, String projectName) {
        // 查询有没有创建项目菜单，有的话直接返回
        long l = authMenuMapper.selectCountByName(projectName);
        if (l > 0) {
            return 0;
        }

        AuthMenuDO newDO = new AuthMenuDO();
        long menuSeq = 10000L;
        Long max = authMenuMapper.max();
        if (max != null) {
            menuSeq = max + 1;
        }
        newDO.setMenuId(menuSeq + "");
        newDO.setMenuSeq(menuSeq);
        newDO.setMenuName(appName);
        newDO.setProjectName(projectName);
        newDO.setPath(projectName);
        newDO.setSort(99999);
        newDO.setHidden(0);
        newDO.setType(NavBarEnum.DIRECTORY.getKey());
        newDO.setParentItemId("0");
        return authMenuMapper.insert(newDO);
    }

    /**
     * 根据不同类型的菜单，构建1，2级菜单路由
     */
    private List<AuthMenuDO> buildRouter_1_2(
                                    List<AuthMenuDO> list,
                                    Collection<AuthMenuDO> menuDOS) {


        List<AuthMenuRespVO> menuResplist = list.stream().map(item -> {
            AuthMenuRespVO convert = BeanUtilX.copy(item, AuthMenuRespVO::new);
            convert.setItemId(convert.getMenuId());
            convert.setItemCode(convert.getMenuId());
            convert.setItemName(convert.getMenuName());
            return convert;
        }).collect(Collectors.toList());

        // 转成树形结构
        List<AuthMenuRespVO> tree = TreeUtilX.listToTree(menuResplist);

        List<AuthMenuDO> listDO = new ArrayList<>();

        Set<String> pidSet = new HashSet<>();
        for (AuthMenuDO item : menuDOS) {
            if ("0".equals(item.getParentItemId())){
                // 父级，找所有的子集
                List<String> childrenIdList = TreeUtilX.getChildrenIdList(tree, item.getMenuId());
                // 加上所有子集
                pidSet.addAll(childrenIdList);
            }else {
                // 父级，子集都找
                List<String> parentIdList = TreeUtilX.getParentIdList(tree, item.getMenuId());

                List<String> childrenIdList = TreeUtilX.getChildrenIdList(tree, item.getMenuId());

                // 加上所有父级
                pidSet.addAll(parentIdList);

                // 加上所有子集
                pidSet.addAll(childrenIdList);
            }
            // 加上自己
            pidSet.add(item.getMenuId());
        }

        for (AuthMenuDO authMenuDO : list) {
            if (pidSet.contains(authMenuDO.getMenuId())){
                listDO.add(authMenuDO);
            }
        }
        return listDO;

    }

}