package com.mongoso.mgs.framework.service.permission;

import cn.hutool.json.JSONObject;
import com.mongoso.mgs.framework.controller.admin.permission.vo.appmenu.AppAuthMenuAditReqVO;
import com.mongoso.mgs.framework.controller.admin.permission.vo.appmenu.AppAuthMenuTreeReqVO;
import com.mongoso.mgs.framework.controller.admin.permission.vo.menu.AuthMenuRespVO;

import java.util.List;
import java.util.Map;

/**
 * 菜单表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-21 15:04:16
 */
public interface AppAuthMenuService {

    /**
     * 菜单表新增
     */
    String authMenuAdd(AppAuthMenuAditReqVO req);

    /**
     * 查询思维导图
     * @param hasPermission
     * @return
     */
    String authMenuMind(Integer hasPermission, Integer category);

    List<Map<String, Object>> authMenuRouterAll(JSONObject jsonString);

    List<AuthMenuRespVO> authMenuTree(AppAuthMenuTreeReqVO reqVO);

}

