package com.mongoso.mgs.framework.service.auth;

import com.mongoso.mgs.framework.common.domain.LoginResp;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.controller.admin.auth.vo.AuthPropertiesRespVO;
import com.mongoso.mgs.framework.controller.admin.auth.vo.AuthSmsSendReqVO;
import com.mongoso.mgs.framework.controller.admin.auth.vo.AuthUserReqVO;
import com.mongoso.mgs.framework.dal.db.permission.AuthUserDO;

import java.util.List;

/**
 * auth service
 */
public interface AuthService {

    /**
     * 登录
     */
    LoginResp login(AuthUserReqVO authUser);

    /**
     * 退出
     */
    void logout();

    void clearLoginUserCache(Integer userType, Long userId);
    /**
     * 获取用户info
     */
    String getInfo();

    LoginUser checkToken(String token);

    /**
     * 给用户发送短信验证码
     *
     * @param reqVO 发送信息
     */
    void sendSmsCode(AuthSmsSendReqVO reqVO);

    /**
     * 验证短信验证码
     * @param phoneNumber
     * @param code
     */
    void validateSmsCode(String phoneNumber, String code);

    /**
     * 获取图形验证码
     */
    String getVerifyCode(String verifyCodeKey);

    /**
     * 生成图形验证码
     */
    void genVerifyCode(String verifyCodeKey);

    /**
     * 查询系统属性列表
     */
    List<AuthPropertiesRespVO> propertiesList();

    /**
     * 系统属性列表返回值
     */
    boolean propertiesListReturn(String propCode);

    /**
     * 图形码校验
     */
    void verifyCodeCheck(String verifyCodeKey,String verifyCode);

    /**
     * 密码错误次数锁定账号
     */
    void passErrorsLock(AuthUserDO oldDO);


}
