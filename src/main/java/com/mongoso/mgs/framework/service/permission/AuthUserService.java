package com.mongoso.mgs.framework.service.permission;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.controller.admin.permission.vo.user.*;
import com.mongoso.mgs.framework.dal.db.permission.AuthRoleDO;
import com.mongoso.mgs.framework.dal.db.permission.AuthUserDO;

import java.util.List;

/**
 * 用户表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-29 20:37:45
 */
public interface AuthUserService {

    /**
     * 用户表新增
     */
    Long authUserAdd(AuthUserAditReqVO reqVO);

    /**
     * 用户表编辑
     */
    Long authUserEdit(AuthUserAditReqVO reqVO);

    /**
     * 用户表删除
     */
    Long authUserDel(Long id);

    /**
     * 用户表详情
     */
    AuthUserRespVO authUserDetail(Long id);

    /**
     * 用户表列表
     */
    List<AuthUserRespVO> authUserList(AuthUserQueryReqVO reqVO);

    /**
     * 用户表分页
     */
    PageResult<AuthUserRespVO> authUserPage(AuthUserPageReqVO reqVO);


    /**
     * 用户校验
     */
    Long authUserDuplicheck(AuthUserQueryReqVO reqVO);

    /**
     * 根据用户id查角色
     */
    List<AuthRoleDO> getRoleIdByUserId(Long userId);

    /**
     * 校验密码
     */
    boolean isPasswordMatch(String password, String userPassword);


    AuthUserDO selectOneByUserAccount(String userAccount);

    void authUserRoleConfig(AuthUserConfigReqVO reqVO);

    /**
     * 修改密码
     * @param reqVO
     */
    void authUpdatePwd(AuthUserPwdEditReqVO reqVO);

    /**
     * 忘记密码
     * @param reqVO
     */
    void authUserForgetPassword(AuthUserForgetPwdReqVO reqVO);

    /**
     * 重置密码
     * @param reqVO
     */
    void authUserResetPassword(AuthUserPwdEditReqVO reqVO);


    PageResult<AuthUserRespVO> authUserPicker(AuthUserPageReqVO reqVO);

}

