package com.mongoso.mgs.framework.service.permission.bo;

import com.mongoso.mgs.framework.dal.db.file.FileLogDO;
import lombok.Data;

import java.io.Serializable;

/**
 * 用户表
 * 返回参数
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-21 15:04:16
 */
@Data
public class AuthUserRespBO implements Serializable {

    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 用户名
     */
    private String userName;
    /**
     * 用户账号
     */
    private String userAccount;
    /**
     * 手机号
     */
    private String phoneNumber;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 备注
     */
    private String remark;
    /**
     * 是否启用
     */
    private Integer enable;

    private Long roleId;

    private String roleName;
    private String roleNames;

    /**
     * 是否为管理员
     */
    private Integer isAdmin;

    /**
     * 员工档案ID
     */
    private Long employeeArchivesId;

    /**
     * 员工编号
     */
    private String employeeNumber;

    /**
     * 是否为登录用户
     */
    private Integer isLongUser = 0;

    /**
     * 数据角色ID
     */
    private Long dataRoleId;
    private String dataRoleName;
    private Long tenantId;

    private String positionName;
    private String empOrgId;
    private String orgPath;
    private FileLogDO userImage;

}