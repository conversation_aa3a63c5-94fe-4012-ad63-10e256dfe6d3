package com.mongoso.mgs.framework.service.auth;

import com.mongoso.mgs.framework.apilog.config.LogProperties;
import com.mongoso.mgs.framework.common.constant.RedisKeyConstant;
import com.mongoso.mgs.framework.common.domain.LoginResp;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.enums.BaseEnum;
import com.mongoso.mgs.framework.common.enums.UserTypeEnum;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.*;
import com.mongoso.mgs.framework.controller.admin.auth.vo.AuthPropertiesRespVO;
import com.mongoso.mgs.framework.controller.admin.auth.vo.AuthSmsSendReqVO;
import com.mongoso.mgs.framework.controller.admin.auth.vo.AuthUserReqVO;
import com.mongoso.mgs.framework.dal.db.permission.AuthMenuDO;
import com.mongoso.mgs.framework.dal.db.permission.AuthRoleDO;
import com.mongoso.mgs.framework.dal.db.permission.AuthUserDO;
import com.mongoso.mgs.framework.dal.db.properties.AuthPropertiesDO;
import com.mongoso.mgs.framework.dal.mysql.permission.AuthUserMapper;
import com.mongoso.mgs.framework.dal.mysql.properties.AuthPropertiesMapper;
import com.mongoso.mgs.framework.enums.RedisKey;
import com.mongoso.mgs.framework.enums.logger.LoginLogTypeEnum;
import com.mongoso.mgs.framework.enums.logger.LoginResultEnum;
import com.mongoso.mgs.framework.redis.core.RedisTemplateX;
import com.mongoso.mgs.framework.security.config.SecurityProperties;

import com.mongoso.mgs.framework.service.permission.AuthMenuService;
import com.mongoso.mgs.framework.service.permission.AuthUserService;
import com.mongoso.mgs.framework.util.RsaUtilX;
import com.mongoso.mgs.framework.web.core.util.WebFrameworkUtilX;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MultiValuedMap;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.util.LinkedMultiValueMap;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.mongoso.mgs.framework.common.exception.enums.GlobalErrorCodeConstants.UNAUTHORIZED;
import static com.mongoso.mgs.framework.common.exception.util.BizExceptionUtilX.exception;


@Slf4j
@Service("authService")
public class AuthServiceImpl implements AuthService {

    @Value(value = "${login.passErrorsCount}")
    private Integer passErrorsCount;

    @Value(value = "${login.passErrorsTime}")
    private Integer passErrorsTime;

    @Value(value = "${login.privateKey}")
    private String privateKey;

    @Value(value = "${mgs.data-permission.enable:false}")
    private Boolean dataPermissionEnable;

    @Resource
    private AuthUserMapper authUserMapper;

    @Resource
    private AuthUserService authUserService;

    @Resource
    private AuthMenuService menuService;

    @Resource
    private SecurityProperties securityProperties;

    @Resource
    private LogProperties logProperties;

    @Resource
    private HttpServletRequest request;

    @Resource
    private HttpServletResponse response;

    @Resource
    private RedisTemplateX redisTemplateX;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private AuthPropertiesMapper authPropertiesMapper;



    @Override
    public LoginResp login(AuthUserReqVO authUser) {

        // 校验
        AuthUserDO oldDO = checkUser(authUser);

        // 封装登陆用户
        LoginUser loginUser = buildLoginUser(oldDO);

        // 添加到上下文中，日志会用到
        WebFrameworkUtilX.setLoginUser(request, loginUser);

        // 生成token，保存到redis
        LoginResp loginResp = buildLoginResp(loginUser);

        // 添加登陆日志
        createLoginLog(loginUser, LoginLogTypeEnum.LOGIN_USERNAME, LoginResultEnum.SUCCESS);

        if (authUser.getLoginVerifyType() == 1) {
            // 登录后验证码失效
            redisTemplateX.deleteKey(RedisKey.getSmsCodeMobile(authUser.getPhoneNumber()));
        }

        return loginResp;
    }

    private LoginResp buildLoginResp(LoginUser loginUser) {
        ValueOperations<String, String> redisStr = stringRedisTemplate.opsForValue();
        // 生成令牌
        String user = JsonUtilX.toJsonString(loginUser);
        String userKey = RedisKeyConstant.getTokenUser(loginUser.getUserId());

        String token = "";
        String redisToken = redisStr.get(userKey);
        if (StrUtilX.isNotEmpty(redisToken)){
            // 有用户token就用旧的
            token = redisToken;
        }else {
            // 没有就用新的token
            token = RedisKeyConstant.getToken(UUID.randomUUID().toString());
        }

        // 保存在线用户
        onlineUser(loginUser.getUserId(), token);

        // 保存token
        if (securityProperties.getTokenTime() == -1) {
            // -1，永久有效
            redisStr.set(userKey, token);
            redisStr.set(token, user);
        } else {
            // 不是永久有效
            redisStr.set(token, user, securityProperties.getTokenTime(), TimeUnit.HOURS);
            redisStr.set(userKey, token, securityProperties.getTokenTime(), TimeUnit.HOURS);
        }

        LoginResp loginResp = new LoginResp();
        loginResp.setToken(token);
        loginResp.setUser(loginUser);
        return loginResp;
    }

    private void onlineUser(Long userId, String token) {
        stringRedisTemplate.opsForHash().put(RedisKeyConstant.TOKEN_USER_ONLINE, userId.toString(), token);
    }

    private LoginUser buildLoginUser(AuthUserDO oldDO) {
        Integer category = 0;// 可以写死，平台的菜单

        Set<String> permissions = new HashSet<>();
        StringBuilder roleNames = new StringBuilder();
        if (oldDO.getIsAdmin() == BaseEnum.ADMIN.getKey()){
            roleNames.append("超级管理员");
            permissions.add("admin");
        }else {
            // 不是超级管理员
            // 查询角色
            List<AuthRoleDO> roleDOList = authUserService.getRoleIdByUserId(oldDO.getUserId());
            if (CollUtilX.isEmpty(roleDOList)) {
                //throw exception(AUTH_LOGIN_BAD_ROLE);
                throw new BizException("5001", "没有角色权限");
            }
            List<Long> roleIds = roleDOList.stream().map(AuthRoleDO::getRoleId).collect(Collectors.toList());

            // 根据角色查询对应的菜单
            Collection<AuthMenuDO> menuDOS = menuService.selectListInRoleId(roleIds, category);
            if (CollUtilX.isEmpty(menuDOS)) {
                //throw exception(AUTH_LOGIN_BAD_MENU);
                throw new BizException("5001", "没有菜单权限");
            }

            // todo 需要后端做数据权限，需要在这里查询登录用户的所有按钮

            // 拼接角色名称
            for (AuthRoleDO role : roleDOList) {
                if (StrUtilX.isNotEmpty(role.getRoleCode())) {
                    // 添加角色标识
                    permissions.add(role.getRoleCode());
                }
                if (roleNames.length() == 0) {
                    roleNames.append(role.getRoleName());
                } else {
                    roleNames.append("/").append(role.getRoleName());
                }
            }

        }

        // 创建用户基础信息
        LoginUser loginUser = new LoginUser();
        loginUser.setTenantId(null);
        //loginUser.setIdentityType(category);
        loginUser.setIsAdmin(oldDO.getIsAdmin());
        loginUser.setUserType(UserTypeEnum.PC.getValue());
        loginUser.setUserId(oldDO.getUserId());
        loginUser.setUserAccount(oldDO.getUserAccount());
        loginUser.setUserName(oldDO.getUserName());
        loginUser.setFullUserName(oldDO.getUserAccount().concat("/").concat(oldDO.getUserName()));
        loginUser.setRoleNames(roleNames.toString());
        loginUser.setPermissionList(permissions);
        loginUser.setDataPermissionEnable(dataPermissionEnable);

        //查所有按钮配置
//        List<String> buttons = menuService.permissionListNocache();
//        loginUser.setButtons(buttons);

        return loginUser;
    }

    /**
     * 校验
     * @param authUser
     * @return
     */
    private AuthUserDO checkUser(AuthUserReqVO authUser) {
        // 校验账号和密码
        String userAccount = StrUtilX.isNotEmpty(authUser.getUserAccount()) ? authUser.getUserAccount() : authUser.getPhoneNumber();
        AuthUserDO oldDO = authUserMapper.selectOneByAccountOrPhone(userAccount);
        if (oldDO == null) {
            //throw exception(AUTH_LOGIN_BAD_CREDENTIALS);
            throw new BizException("5001", "登录失败，账号密码不正确");
        }

        if (oldDO.getEnable() == BaseEnum.NOT_ENABLE.getKey()) {
            //throw exception(AUTH_LOGIN_USER_DISABLED);
            throw new BizException("5001", "登录失败，账号被禁用");
        }

        if (oldDO.getLoginStatus() == 1) {
            long timeNow = System.currentTimeMillis();
            long timeError = oldDO.getLastLoginErrorTime().toInstant(ZoneOffset.ofHours(8)).toEpochMilli();
            long time = (timeNow - timeError) / (1000 * 60);
            if (time >= passErrorsTime) {
                oldDO.setLoginStatus(0);
                oldDO.setLastLoginErrorTime(LocalDateTime.now());
                oldDO.setLoginErrorCount(0);
                authUserMapper.updateById(oldDO);
            }else {
                //throw exception(AUTH_LOGIN_BAD_CREDENTIALS_LOCK,passErrorsTime - time);
                throw new BizException("5001", "当前用户登陆已锁定，请"+(passErrorsTime - time)+"分钟后重试");
            }
        }

        if(authUser.getLoginVerifyType() == 1) {//验证码登录
            if(StrUtilX.isEmpty(authUser.getSmsVerifyCode())){
                throw new BizException("1001000000", "验证码不能为空");
            }
            // 校验验证码
            if(authUser.getSmsVerifyCode().length() != 6){
                //throw exception(SMS_CODE_NOT_CORRECT);
                throw new BizException("1001000000", "验证码不正确");
            }
            this.validateSmsCode(oldDO.getPhoneNumber(), authUser.getSmsVerifyCode());

        }else{//密码登录
            if(StrUtilX.isEmpty(authUser.getUserPassword())){
                throw new BizException("1001000000", "密码不能为空");
            }

            // 加密后的密码
            String password = authUser.getUserPassword();

            // 使用对称加密,将加密后的密码解析成原密码
            password = RsaUtilX.decrypt(password, privateKey);


            if (!authUserService.isPasswordMatch(password, oldDO.getUserPassword())) {
                passErrorsLock(oldDO);
            }
        }

        verifyCodeCheck(authUser.getVerifyCodeKey(),authUser.getVerifyCode());

        oldDO.setLoginStatus(0);
        oldDO.setLastLoginErrorTime(LocalDateTime.now());
        oldDO.setLoginErrorCount(0);
        authUserMapper.updateById(oldDO);
        return oldDO;
    }


    private void createLoginLog(LoginUser loginUser,
                                LoginLogTypeEnum logTypeEnum, LoginResultEnum loginResult) {

        // 不记录登录日志
        if (!logProperties.getLoginLog()) {
            return;
        }

        // 插入登录日志
//        LoginLogCreateReqDTO reqDTO = new LoginLogCreateReqDTO();
//        reqDTO.setLogType(logTypeEnum.getType());
//        // 暂时没有链路追踪
////         reqDTO.setTraceId(TracerUtils.getTraceId());
//        reqDTO.setUserId(loginUser.getUserId());
//        reqDTO.setUserType(loginUser.getUserType());
//        reqDTO.setUserName(loginUser.getUserName());
//        reqDTO.setUserAgent(ServletUtilX.getUserAgent());
//        reqDTO.setUserIp(ServletUtilX.getClientIP());
//        reqDTO.setResult(loginResult.getResult());
//        loginLogService.createLoginLog(reqDTO);
    }


    @Override
    public void logout() {
        LoginUser loginUser = WebFrameworkUtilX.getRequiredLoginUser();
        // 清除用户缓存
        clearLoginUserCache(UserTypeEnum.PC.getValue(), loginUser.getUserId());

    }

    public void clearLoginUserCache(Integer userType, Long userId) {
        // 清空缓存
        if (userType == UserTypeEnum.APP.getValue()){
            // app端清除缓存
            String userKey = RedisKeyConstant.getAppTokenUser(userId);
            String token = stringRedisTemplate.opsForValue().get(userKey);
            // 删除用户的token
            stringRedisTemplate.delete(token);
            // 删除用户的id
            stringRedisTemplate.delete(userKey);
            // 删除在线用户的id
            stringRedisTemplate.opsForHash().delete(RedisKeyConstant.APP_TOKEN_USER_ONLINE,userId);

        }else {
            // pc端清除缓存
            String userKey = RedisKeyConstant.getTokenUser(userId);
            String token = stringRedisTemplate.opsForValue().get(userKey);
            // 删除用户的token
            stringRedisTemplate.delete(token);
            // 删除用户的id
            stringRedisTemplate.delete(userKey);
            // 删除在线用户的id
            stringRedisTemplate.opsForHash().delete(RedisKeyConstant.TOKEN_USER_ONLINE,userId.toString());
        }

        WebFrameworkUtilX.clear();
    }

    @Override
    public String getInfo() {
        String token = request.getHeader(securityProperties.getTokenHeader());
        return stringRedisTemplate.opsForValue().get(token);
    }

    @Override
    public LoginUser checkToken(String token) {

//        String requestURI = request.getRequestURI();

        // 先从上下文里获取
        LoginUser loginUser = WebFrameworkUtilX.getLoginUser();
        if(loginUser != null){
            return loginUser;
        }

        // 再从redis获取
        String loginUserJson = stringRedisTemplate.opsForValue().get(token);
        if(StrUtilX.isEmpty(loginUserJson)){
            throw exception(UNAUTHORIZED);
        }
        return JsonUtilX.parseObject(loginUserJson, LoginUser.class);
    }


    @Override
    public void sendSmsCode(AuthSmsSendReqVO reqVO) {
        //生成验证码：随机生成6位数
        verifyCodeCheck(reqVO.getVerifyCodeKey(), reqVO.getVerifyCode());

        // 生产随机数
        String code = String.valueOf((int) ((Math.random() * 9 + 1) * 100000));
//        SmsUtilX.sendSmsCode(reqVO.getPhoneNumber(), code);

        String redisCodeKey = RedisKey.getSmsCodeMobile(reqVO.getPhoneNumber());

        stringRedisTemplate.opsForValue().set(redisCodeKey, code, 10, TimeUnit.MINUTES);

    }

    @Override
    public void validateSmsCode(String phoneNumber, String code) {
        String smsCode = stringRedisTemplate.opsForValue().get(RedisKey.getSmsCodeMobile(phoneNumber));
        // 若验证码不存在，抛出异常
        if (StrUtilX.isEmpty(smsCode)) {
            //throw exception(SMS_CODE_NOT_FOUND);
            throw new BizException("5001", "验证码不存在");
        }
        if (!smsCode.equals(code)) {
            //throw exception(SMS_CODE_NOT_CORRECT);
            throw new BizException("5001", "验证码不正确");
        }
    }

    /*密码错误次数锁定账号*/
    public void passErrorsLock(AuthUserDO oldDO) {
        if (oldDO.getLoginErrorCount() == null || oldDO.getLoginErrorCount() == 0) {
            oldDO.setLoginStatus(0);
            oldDO.setLastLoginErrorTime(LocalDateTime.now());
            oldDO.setLoginErrorCount(1);
            authUserMapper.updateById(oldDO);
            //throw exception(AUTH_LOGIN_BAD_CREDENTIALS_COUNT,passErrorsCount-1);
            throw new BizException("5001", "登录失败，账号密码不正确，可尝试登陆次数为："+(passErrorsCount-1)+"次");
        }else {
            long timeNow = System.currentTimeMillis();
            long timeError = oldDO.getLastLoginErrorTime().toInstant(ZoneOffset.ofHours(8)).toEpochMilli();
            long time = (timeNow - timeError) / (1000 * 60);
            if (time >= passErrorsTime) {
                oldDO.setLoginStatus(0);
                oldDO.setLastLoginErrorTime(LocalDateTime.now());
                oldDO.setLoginErrorCount(1);
                authUserMapper.updateById(oldDO);
                //throw exception(AUTH_LOGIN_BAD_CREDENTIALS_COUNT,passErrorsCount-1);
                throw new BizException("5001", "登录失败，账号密码不正确，可尝试登陆次数为："+(passErrorsCount-1)+"次");
            }else {
                if (oldDO.getLoginErrorCount() >= passErrorsCount) {
                    oldDO.setLoginStatus(1);
                    authUserMapper.updateById(oldDO);
                    //throw exception(AUTH_LOGIN_BAD_CREDENTIALS_LOCK,passErrorsTime - time);
                    throw new BizException("5001", "登录失败，当前用户登陆已锁定，可尝试登陆次数为："+(passErrorsCount-1)+"次");
                }else {
                    oldDO.setLoginErrorCount(oldDO.getLoginErrorCount() + 1);
                    oldDO.setLastLoginErrorTime(LocalDateTime.now());
                    oldDO.setLoginStatus(0);
                    authUserMapper.updateById(oldDO);
                    //throw exception(AUTH_LOGIN_BAD_CREDENTIALS_COUNT,(3 - (oldDO.getLoginErrorCount())));
                    throw new BizException("5001", "登录失败，账号密码不正确，可尝试登陆次数为："+(3 - (oldDO.getLoginErrorCount()))+"次");
                }
            }
        }
    }


    public String getVerifyCode(String verifyCodeKey) {
        String verifyCode = VerifyCodeUtilX.generateVerifyCode(4);
        /*验证码存储到redis*/
        redisTemplateX.set(verifyCodeKey, verifyCode,5, TimeUnit.MINUTES);
        return verifyCode;
    }

    public void genVerifyCode(String verifyCodeKey) {
        try {
            String verifyCode = VerifyCodeUtilX.generateVerifyCode(4);
            /*验证码存储到redis*/
            redisTemplateX.set(verifyCodeKey, verifyCode,5, TimeUnit.MINUTES);
            //生成图片
            int imageWidth = 250, imageHeight = 70;
            VerifyCodeUtilX.outputImage(imageWidth, imageHeight, response.getOutputStream(), verifyCode);
            log.info("生成图形验证码:" + verifyCode);
        } catch (IOException e) {
            log.error("生成图形验证码异常", e);
        }
    }

    /**
     * 查询系统属性列表
     */
    @Override
    public List<AuthPropertiesRespVO> propertiesList() {
        //先查询redis
//        String propertiesListString = redisTemplateX.get(RedisKeyConstant.PROPERTIES_LIST);
        List<AuthPropertiesDO> list = new ArrayList<>();
//        if (StrUtilX.isEmpty(propertiesListString)) {
        //redis没有再查询数据库
        list = authPropertiesMapper.selectList();
//        redisTemplateX.set(RedisKeyConstant.PROPERTIES_LIST, JSON.toJSONString(list));
//        }else {
//            //redis有，则查询redis
//            list = JSONArray.parseArray(propertiesListString, AuthPropertiesDO.class);
//        }
        AuthPropertiesDO auth = new AuthPropertiesDO();
        auth.setPropCode(RedisKey.VERIFY_CODE_KEY);
        String verifyCode = VerifyCodeUtilX.generateVerifyCode(6);
        auth.setPropValue("code_key:" + verifyCode + String.valueOf(System.currentTimeMillis()));
        list.add(auth);

        return BeanUtilX.copyList(list, AuthPropertiesRespVO::new);
    }

    @Override
    public boolean propertiesListReturn(String propCode) {
        List<AuthPropertiesRespVO> list = propertiesList();
        for (AuthPropertiesRespVO item : list) {
            // 如果图形验证码状态为开启
            if (propCode.equals(item.getPropCode()) && "1".equals(item.getPropValue())) {
                return true;
            }
        }
        return false;
    }

    /*验证码校验*/
    @Override
    public void verifyCodeCheck(String verifyCodeKey,String verifyCode) {
        if (propertiesListReturn(WebFrameworkUtilX.GRAPHIC_VERIFY_CODE_ENABLE)) {
            String redisVerifyCode = redisTemplateX.get(verifyCodeKey);
            if (StrUtilX.isEmpty(verifyCode)) {
                //throw exception(AUTH_LOGIN_VERIFY_CODE_IS_EMPTY);
                throw new BizException("5001", "图形验证码不能为空");
            }
            if (!verifyCode.equalsIgnoreCase(redisVerifyCode)) {
                //throw exception(AUTH_LOGIN_VERIFY_CODE);
                throw new BizException("5001", "图形验证码不正确");
            }
        }
    }


}
