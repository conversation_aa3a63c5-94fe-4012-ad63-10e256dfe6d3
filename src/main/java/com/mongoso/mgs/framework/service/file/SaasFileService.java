package com.mongoso.mgs.framework.service.file;

import com.mongoso.mgs.framework.dal.db.file.FileLogDO;
import com.mongoso.mgs.framework.file.core.domain.FileItem;
import com.mongoso.mgs.framework.file.core.domain.FileReq;
import com.mongoso.mgs.framework.file.core.domain.FileResp;
import org.springframework.lang.Nullable;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/12 14:36
 */
public interface SaasFileService {

    FileResp upload(MultipartFile file, FileReq req) throws IOException;

    FileResp upload(FileReq req,String fileName,byte[] bytes);

    void download(FileReq req);
    FileItem downloadFileItem(FileReq req);

    Object del(FileReq req);

    int del(List<String> fileIdList);

    int del(String objId);

    int del(String objId,@Nullable String tableName);

    int del(String objId,@Nullable String tableName,@Nullable String fieldName);



    int bind(List<String> fileIdList,String objId);
    int bind(List<String> fileIdList,String objId,String tableName);
    int bind(List<String> fileIdList,String objId,@Nullable String tableName,@Nullable String fieldName);


    FileLogDO detailDO(String fileId);

    List<FileLogDO> listDO(List<String> fileIds);

    List<FileLogDO> listDOByObjId(String objId, @Nullable String tableName, @Nullable String fieldName);

    List<FileLogDO> listDOInObjId(List<String> objIds, @Nullable String tableName, @Nullable String fieldName);

}
