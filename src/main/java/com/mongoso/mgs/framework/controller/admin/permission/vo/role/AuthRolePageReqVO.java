package com.mongoso.mgs.framework.controller.admin.permission.vo.role;

import com.mongoso.mgs.framework.common.domain.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 角色 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AuthRolePageReqVO extends PageParam {

    private String roleName;

    private String roleCode;

    private String remark;

    private Integer enable;

}
