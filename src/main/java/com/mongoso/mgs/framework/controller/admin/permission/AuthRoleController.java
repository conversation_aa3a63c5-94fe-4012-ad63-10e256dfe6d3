package com.mongoso.mgs.framework.controller.admin.permission;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.controller.admin.permission.vo.role.*;
import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.framework.service.permission.AuthRoleService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

import static com.mongoso.mgs.framework.common.domain.ResultX.success;

/**
 * 角色表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-21 15:04:16
 */
@Validated
@RestController
@RequestMapping("/system")
public class AuthRoleController {

    @Resource
    private AuthRoleService authRoleService;

    @OperateLog("角色新增或编辑")
    @PostMapping("/authRoleAdit")
    @PreAuthorize("@ss.hasAnyPermissions('authRole:adit')")
    public ResultX<Long> authRoleAdit(@Valid @RequestBody AuthRoleAditReqVO reqVO){
        return success(reqVO.getRoleId() == null
                ? authRoleService.authRoleAdd(reqVO)
                : authRoleService.authRoleEdit(reqVO));
    }

    @OperateLog("角色删除")
    @PostMapping("/authRoleDel")
    @PreAuthorize("@ss.hasAnyPermissions('authRole:del')")
    public ResultX<Long> authRoleDel(@Valid @RequestBody AuthRolePrimaryReqVO req){
        return success(authRoleService.authRoleDel(req.getRoleId()));
    }

    @OperateLog("角色详情")
    @PostMapping("/authRoleDetail")
    @PreAuthorize("@ss.hasAnyPermissions('authRole:query')")
    public ResultX<AuthRoleRespVO> authRoleDetail(@Valid @RequestBody AuthRolePrimaryReqVO req){
        return success(authRoleService.authRoleDetail(req.getRoleId()));
    }

    @OperateLog("角色列表")
    @PostMapping("/authRoleList")
    @PreAuthorize("@ss.hasAnyPermissions('authRole:query')")
    public ResultX<List<AuthRoleRespVO>> authRoleList(@Valid @RequestBody AuthRoleQueryReqVO req){
        List<AuthRoleRespVO> list = authRoleService.authRoleList(req);
        return success(list);
    }

    @OperateLog("角色分页")
    @PostMapping("/authRolePage")
    @PreAuthorize("@ss.hasAnyPermissions('authRole:query')")
    public ResultX<PageResult<AuthRoleRespVO>> authRolePage(@Valid @RequestBody AuthRolePageReqVO req){
        return success(authRoleService.authRolePage(req));
    }

    @OperateLog("角色验重")
    @PostMapping("/authRoleDuplicheck")
    @PreAuthorize("@ss.hasPermission('system:authRole:query')")
    public ResultX<Long> authRoleDuplicheck(@Valid @RequestBody AuthRoleDuplicheckReqVO reqVO) {
        return success(authRoleService.authRoleDuplicheck(reqVO));
    }
    @OperateLog("角色配置菜单")
    @PostMapping("/authRoleMenuConfig")
    @PreAuthorize("@ss.hasPermission('authRole:adit')")
    public ResultX<Integer> authRoleMenuConfig(@Valid @RequestBody AuthRoleConfigReqVO reqVO) {
        return success(authRoleService.authRoleMenuConfig(reqVO));
    }

    @OperateLog("角色菜单详情")
    @PostMapping("/authRoleMenuDetail")
    @PreAuthorize("@ss.hasAnyPermissions('authRole:query')")
    public ResultX<AuthRoleMenuConfigRespVO> authRoleMenuDetail(@Valid @RequestBody AuthRoleMenuPrimaryReqVO req){
        return success(authRoleService.authRoleMenuDetail(req));
    }

    @OperateLog("角色复制")
    @PostMapping("/authRoleCopy")
    @PreAuthorize("@ss.hasAnyPermissions('authRole:query')")
    public ResultX<Integer> authRoleCopy(@Valid @RequestBody AuthRoleCopyReqVO req){
        return success(authRoleService.authRoleCopy(req));
    }
}
