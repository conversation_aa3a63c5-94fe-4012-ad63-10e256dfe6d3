package com.mongoso.mgs.framework.controller.admin.permission.vo.menu;

import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * 菜单 AditReqVO
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
public class AuthMenuEditReqVO {

    private String menuId;
    private String menuCode;
    private String menuName;
    private String path;
    private Integer hidden;
    private String icon;
    private Integer pageType;
    private Integer pathType;
    private Integer pathToken;
    private Integer appType;
    private String menuParam;


    private List<AuthButtonReqVO> buttonList;

}
