package com.mongoso.mgs.framework.controller.admin.permission;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.controller.admin.permission.vo.user.*;
import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.framework.service.permission.AuthUserService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

import static com.mongoso.mgs.framework.common.domain.ResultX.success;

/**
 * 用户表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-29 20:37:45
 */
@Validated
@RestController
@RequestMapping("/system")
public class AuthUserController {

    @Resource
    private AuthUserService authUserService;

    @OperateLog("用户新增或编辑")
    @PostMapping("/authUserAdit")
    @PreAuthorize("@ss.hasAnyPermissions('authUser:adit')")
    public ResultX<Long> authUserAdit(@Valid @RequestBody AuthUserAditReqVO reqVO){
        return success(reqVO.getUserId() == null
                ? authUserService.authUserAdd(reqVO)
                : authUserService.authUserEdit(reqVO));
    }

    @OperateLog("用户删除")
    @PostMapping("/authUserDel")
    @PreAuthorize("@ss.hasAnyPermissions('authUser:delete')")
    public ResultX<Long> authUserDel(@Valid @RequestBody AuthUserPrimaryReqVO reqVO){
        return success(authUserService.authUserDel(reqVO.getUserId()));
    }

    @OperateLog("用户详情")
    @PostMapping("/authUserDetail")
    @PreAuthorize("@ss.hasAnyPermissions('authUser:query')")
    public ResultX<AuthUserRespVO> authUserDetail(@Valid @RequestBody AuthUserPrimaryReqVO reqVO){
        return success(authUserService.authUserDetail(reqVO.getUserId()));
    }

    @OperateLog("用户列表")
    @PostMapping("/authUserList")
    @PreAuthorize("@ss.hasAnyPermissions('authUser:query')")
    public ResultX<List<AuthUserRespVO>> authUserList(@RequestBody AuthUserQueryReqVO reqVO){
        return success(authUserService.authUserList(reqVO));
    }

    @OperateLog("用户分页")
    @PostMapping("/authUserPage")
    @PreAuthorize("@ss.hasAnyPermissions('authUser:query')")
    public ResultX<PageResult<AuthUserRespVO>> authUserPage(@Valid @RequestBody AuthUserPageReqVO reqVO){
        return success(authUserService.authUserPage(reqVO));
    }

    @OperateLog("用户下拉框")
    @PostMapping("/authUserPicker")
    @PreAuthorize("@ss.hasAnyPermissions('authUser:query')")
    public ResultX<PageResult<AuthUserRespVO>> authUserPicker(@Valid @RequestBody AuthUserPageReqVO reqVO){
        return success(authUserService.authUserPicker(reqVO));
    }

    @OperateLog("用户验重")
    @PostMapping("/authUserDuplicheck")
    @PreAuthorize("@ss.hasPermission('authUser:query')")
    public ResultX<Long> authUserDuplicheck(@Valid @RequestBody AuthUserQueryReqVO reqVO) {
        return success(authUserService.authUserDuplicheck(reqVO));
    }


    @OperateLog("用户配置角色")
    @PostMapping("/authUserRoleConfig")
    @PreAuthorize("@ss.hasPermission('authRole:adit')")
    public ResultX<Boolean> authUserRoleConfig(@Valid @RequestBody AuthUserConfigReqVO reqVO) {
        authUserService.authUserRoleConfig(reqVO);
        return success(true);
    }


    @OperateLog("用户修改密码")
    @PostMapping("/authUpdatePwd")
    @PreAuthorize("@ss.hasPermission('authUser:aditPwd')")
    public ResultX<Boolean> authUpdatePwd(@Valid @RequestBody AuthUserPwdEditReqVO reqVO) {
        authUserService.authUpdatePwd(reqVO);
        return success(true);
    }

    @OperateLog("用户忘记密码")
    @PostMapping("/authUserForgetPwd")
    //@PreAuthorize("@ss.hasPermission('authUser:adit')")
    public ResultX<Boolean> authUserForgetPassword(@Valid @RequestBody AuthUserForgetPwdReqVO reqVO) {
        authUserService.authUserForgetPassword(reqVO);
        return success(true);
    }

    @OperateLog("用户重设密码")
    @PostMapping("/authUserResetPassword")
    @PreAuthorize("@ss.hasPermission('authUser:restPsw')")
    public ResultX<Boolean> authUserResetPassword(@Valid @RequestBody AuthUserPwdEditReqVO reqVO) {
        authUserService.authUserResetPassword(reqVO);
        return success(true);
    }

}
