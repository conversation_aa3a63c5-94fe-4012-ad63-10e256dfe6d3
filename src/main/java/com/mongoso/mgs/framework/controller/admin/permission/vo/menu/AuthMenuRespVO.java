package com.mongoso.mgs.framework.controller.admin.permission.vo.menu;

import com.mongoso.mgs.framework.util.tree.TreeItem;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 菜单表
 * 返回参数
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-21 15:04:16
 */
@Data
public class AuthMenuRespVO extends TreeItem<AuthMenuRespVO> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 菜单id
     */
    private String menuId;
    /**
     * 菜单名称
     */
    private String menuName;
    /**
     * 后端权限
     */
    private String permissions;
    /**
     * 前端按钮
     */
    private String buttonName;
    /**
     * 前端路由
     */
    private String path;
    /**
     * 是否隐藏
     */
    private Integer hidden;
    /**
     * 类型
     */
    private Integer type;
    /**
     * 排序
     */
    private Integer sort;
    /**
     * 图标
     */
    private String icon;
    /**
     * 备注
     */
    private String remark;
    /**
     * 更新人
     */
    private String updatedBy;
    /**
     * 项目名
     */
    private String projectName;
    /**
     * 更新时间
     */
    private LocalDateTime updatedDt;

//    private List<AuthMenuRespVO> children;
}