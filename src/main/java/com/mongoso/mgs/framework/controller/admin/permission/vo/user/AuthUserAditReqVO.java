package com.mongoso.mgs.framework.controller.admin.permission.vo.user;

import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 用户表
 * 新增和编辑接口请求参数
 */
@Data
public class AuthUserAditReqVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 用户名
     */
    @NotNull(message = "用户名称不能为空")
    private String userName;
    /**
     * 用户账号
     */
    @NotNull(message = "账号不能为空")
    private String userAccount;
    /**
     * 手机号
     */
    @NotNull(message = "手机号不能为空")
    private String phoneNumber;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 备注
     */
    private String remark;
    private String userPassword;
    /**
     * 是否启用
     */
    private Integer enable;


}