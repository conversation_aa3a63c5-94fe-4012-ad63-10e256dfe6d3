package com.mongoso.mgs.framework.controller.admin.permission.vo.user;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户表
 * 返回参数
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-21 15:04:16
 */
@Data
public class AuthUserRespVO implements Serializable {


    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 用户名
     */
    private String userName;
    /**
     * 用户账号
     */
    private String userAccount;
    /**
     * 手机号
     */
    private String phoneNumber;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 备注
     */
    private String remark;
    /**
     * 是否启用
     */
    private Integer enable;


    private Long roleId;
    private String roleName;

    private List<Long> roleIdList;
    private String roleNames;

    private Integer isLongUser;

    /**
     * 更新人
     */
    private String updatedBy;
    /**
     * 更新时间
     */
    private LocalDateTime updatedDt;
}