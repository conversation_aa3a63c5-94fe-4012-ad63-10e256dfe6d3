package com.mongoso.mgs.framework.controller.admin.permission.vo.role;

import lombok.Data;
import lombok.ToString;

import jakarta.validation.constraints.NotNull;

/**
 * 角色 AditReqVO
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
public class AuthRoleAditReqVO {

    private Long roleId;

    @NotNull(message = "角色名称不能为空")
    private String roleName;

    private String roleCode;

    private String remark;

    private Integer enable;

}
