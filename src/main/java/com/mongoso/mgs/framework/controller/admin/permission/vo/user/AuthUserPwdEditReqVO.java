package com.mongoso.mgs.framework.controller.admin.permission.vo.user;

import lombok.Data;
import lombok.ToString;

import jakarta.validation.constraints.NotNull;

/**
 * 用户 AditReqVO
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
public class AuthUserPwdEditReqVO {

    @NotNull(message = "用户id不能为空")
    private Long userId;

    @NotNull(message = "旧密码不能为空")
    private String userPassword;

    @NotNull(message = "新密码不能为空")
    private String newUserPassword;

}
