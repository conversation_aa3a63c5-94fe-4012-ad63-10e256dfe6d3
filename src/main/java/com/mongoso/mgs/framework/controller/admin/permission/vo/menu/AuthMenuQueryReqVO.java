package com.mongoso.mgs.framework.controller.admin.permission.vo.menu;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * 菜单表
 * 请求参数
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-21 15:04:16
 */
@Data
public class AuthMenuQueryReqVO implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 菜单id
     */
    private String menuId;
    private Set<String> pidSet;
    /**
     * 菜单名称
     */
    private String menuName;
    /**
     * 父级ID
     */
    private String parentItemId;
    private List<Integer> typeList;

    // 菜单分类，0：平台，1：企业菜单，2：供应商菜单，
    private Integer category;
}