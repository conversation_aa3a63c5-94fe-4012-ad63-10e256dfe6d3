package com.mongoso.mgs.framework.controller.admin.auth;


import com.mongoso.mgs.framework.common.domain.LoginResp;
import com.mongoso.mgs.framework.common.domain.LoginUser;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.util.JsonUtilX;
import com.mongoso.mgs.framework.controller.admin.auth.vo.AuthPropertiesRespVO;
import com.mongoso.mgs.framework.controller.admin.auth.vo.AuthSmsSendReqVO;
import com.mongoso.mgs.framework.controller.admin.auth.vo.AuthUserReqVO;
import com.mongoso.mgs.framework.controller.admin.auth.vo.AuthVerifyCodeVo;
import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.framework.service.auth.AuthService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.validation.Valid;
import java.util.List;

import static com.mongoso.mgs.framework.common.domain.ResultX.success;


/**
 * @Auther: wangzhong
 * @Date: 2020/4/27 17:13
 * @Description: 认证接口
 */
@Validated
@RestController
@RequestMapping("/system")
public class AuthController {

    @Resource
    private AuthService authService;


    @PermitAll
    @PostMapping("/login")
    public ResultX<LoginResp> login(@Valid @RequestBody AuthUserReqVO authUser) {
        return success(authService.login(authUser));
    }

    /**
     * 用户退出
     */
    @PostMapping("/logout")
    public ResultX<Object> logout() {
        authService.logout();
        return success(1);
    }

    /**
     * info
     */
    @PostMapping("/info")
    public ResultX<LoginUser> getInfo() {
        String loginUserJson = authService.getInfo();
        return success(JsonUtilX.parseObject(loginUserJson, LoginUser.class));
    }

//    @PostMapping("/sendSmsCode")
//    @OperateLog("发送手机验证码")
//    public ResultX<Boolean> sendSmsCode(@RequestBody @Valid AuthSmsSendReqVO reqVO) {
//        authService.sendSmsCode(reqVO);
//        return success(true);
//    }

    @PostMapping("/genVerifyCode")
    @OperateLog("生成图形验证码")
    public void genVerifyCode(@RequestBody AuthVerifyCodeVo params) {
        authService.genVerifyCode(params.getVerifyCodeKey());
    }

    @OperateLog("查询系统属性列表")
    @PostMapping("/propertiesList")
    public ResultX<List<AuthPropertiesRespVO>> propertiesList(){
        return success(authService.propertiesList());
    }


}