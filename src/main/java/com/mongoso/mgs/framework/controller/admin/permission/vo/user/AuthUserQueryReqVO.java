package com.mongoso.mgs.framework.controller.admin.permission.vo.user;

import lombok.Data;

import java.io.Serializable;

/**
 * 用户表
 * 查询接口的请求参数
 */
@Data
public class AuthUserQueryReqVO implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 用户名
     */
    private String userName;
    /**
     * 手机号
     */
    private String phoneNumber;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 是否启用
     */
    private Integer enable;
}