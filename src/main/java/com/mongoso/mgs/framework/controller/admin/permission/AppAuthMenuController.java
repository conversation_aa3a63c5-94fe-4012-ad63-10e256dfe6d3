package com.mongoso.mgs.framework.controller.admin.permission;

import cn.hutool.json.JSONObject;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.controller.admin.permission.vo.appmenu.AppAuthMenuAditReqVO;
import com.mongoso.mgs.framework.controller.admin.permission.vo.appmenu.AppAuthMenuTreeReqVO;
import com.mongoso.mgs.framework.controller.admin.permission.vo.menu.AuthMenuRespVO;
import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.framework.service.permission.AppAuthMenuService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

import static com.mongoso.mgs.framework.common.domain.ResultX.success;

/**
 * 菜单表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-21 15:04:16
 */
@Validated
@RestController
@RequestMapping("/system")
public class AppAuthMenuController {

    @Resource
    private AppAuthMenuService appAuthMenuService;

    @OperateLog("菜单新增")
    @PostMapping("/appAuthMenuAdit")
    @PreAuthorize("@ss.hasAnyPermissions('authMenu:adit')")
    public ResultX<String> appauthMenuAdit(@Valid  @RequestBody AppAuthMenuAditReqVO req){
        return success(appAuthMenuService.authMenuAdd(req));

    }

//    @OperateLog("菜单Mind")
//    @PostMapping("/appAuthMenuMind")
//    @PreAuthorize("@ss.hasPermission('authMenu:query')")
//    public ResultX<String> authMenuMind(@Valid @RequestBody AppAuthMenuTreeReqVO reqVO) {
//        return success(appAuthMenuService.authMenuMind(reqVO.getHasPermission(),reqVO.getCategory()));
//    }

    @OperateLog("菜单RouterAll")
    @PostMapping("/appAuthMenuRouterAll")
    @PreAuthorize("@ss.hasPermission('authMenu:query')")
    public ResultX<List<Map<String, Object>>> authMenuRouterAll(@RequestBody JSONObject jsonString) {
        return success(appAuthMenuService.authMenuRouterAll(jsonString));
    }

    @OperateLog("菜单Tree")
    @PostMapping("/appAuthMenuTree")
    @PreAuthorize("@ss.hasPermission('authMenu:query')")
    public ResultX<List<AuthMenuRespVO>> authMenuTree( @RequestBody AppAuthMenuTreeReqVO reqVO) {
        return success(appAuthMenuService.authMenuTree(reqVO));
    }


}
