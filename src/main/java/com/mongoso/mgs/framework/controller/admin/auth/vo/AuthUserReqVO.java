package com.mongoso.mgs.framework.controller.admin.auth.vo;

import lombok.Data;

import jakarta.validation.constraints.NotNull;

/**
 * 登录接口参数
 */
@Data
public class AuthUserReqVO {

    @NotNull(message = "账号不能为空")
    private String userAccount;

    /*手机号*/
    private String phoneNumber;

    //@NotNull(message = "密码不能为空")
    private String userPassword;

    /*图形验证码唯一key*/
    private String verifyCodeKey;

    /*图形验证码*/
    private String verifyCode;

    /* 登陆验证方式 0：密码登录，1：验证码登录*/
    private Integer loginVerifyType = 0;

    /* 手机验证码 */
    private String smsVerifyCode;

    /* 租户ID，对应企业，0：平台 */
    private Long tenantId;
}
