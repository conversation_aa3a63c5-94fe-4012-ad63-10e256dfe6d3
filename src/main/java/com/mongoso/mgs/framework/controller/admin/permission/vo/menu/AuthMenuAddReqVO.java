package com.mongoso.mgs.framework.controller.admin.permission.vo.menu;

import lombok.Data;
import lombok.ToString;

import jakarta.validation.constraints.NotNull;

/**
 * 菜单 AditReqVO
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
public class AuthMenuAddReqVO {

    @NotNull(message = "category 不能为空")
    private Integer category;

    @NotNull(message = "type 不能为空")
    private Integer type;

//    @NotNull(message = "parentItemId 不能为空")
    private String parentItemId;

    @NotNull(message = "menuName 不能为空")
    private String menuName;

}
