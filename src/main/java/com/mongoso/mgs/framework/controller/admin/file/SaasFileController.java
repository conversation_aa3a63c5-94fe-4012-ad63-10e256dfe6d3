package com.mongoso.mgs.framework.controller.admin.file;

import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.ObjUtilX;
import com.mongoso.mgs.framework.controller.admin.file.vo.FileBindVO;
import com.mongoso.mgs.framework.dal.db.file.FileLogDO;
import com.mongoso.mgs.framework.file.core.domain.FileEnum;
import com.mongoso.mgs.framework.file.core.domain.FileReq;
import com.mongoso.mgs.framework.file.core.domain.FileResp;
import com.mongoso.mgs.framework.service.file.SaasFileService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import java.io.IOException;
import java.util.List;

import static com.mongoso.mgs.framework.common.domain.ResultX.success;

/**
 *
 * saas文件上传
 *
 */
@RestController
@RequestMapping("/system/file")
public class SaasFileController {

    @Resource
    private SaasFileService saasFileService;


    /**
     * 上传
     */
    @PostMapping("/upload")
    public ResultX<FileResp> upload(MultipartFile file, FileReq req) throws IOException {
        // 默认用阿里的文件服务器
        req.setOssType(FileEnum.ali.getKey());
        //if (req.getOverWrite() == 1) {
        //    // 是否覆盖，覆盖就删除以前的文件
        //    saasFileService.del(req.getObjId(), req.getTableName(), req.getFieldName());
        //}
        if(ObjUtilX.isEmpty(file.getOriginalFilename())){
            throw new BizException("5001", "请选择文件");
        }
        FileResp fileResp = saasFileService.upload(file, req);
        fileResp.setThumbnailUrl(fileResp.getFileUrl());
        return success(fileResp);
    }

    /**
     * 下载
     */
    @PostMapping("/download")
    public void download(@RequestBody FileReq req){
        saasFileService.download(req);
    }

    /**
     * 删除
     */
    @PostMapping("/del")
    public ResultX<Object> del(@RequestBody FileReq req){
        return success(saasFileService.del(req));
    }

    /**
     * 根据文件id
     * 文件详情
     */
    @PostMapping("/detail")
    public ResultX<FileLogDO> detail(@RequestBody FileReq req){
        return success(saasFileService.detailDO(req.getFileId()));
    }

    /**
     * 根据对象id
     * 文件详情
     */
    @PostMapping("/detail2")
    public ResultX<List<FileLogDO>> detail2(@RequestBody FileReq req){
        return success(saasFileService.listDOByObjId(req.getObjId(),req.getTableName(),req.getFieldName()));

    }

    /**
     * 文件绑定对象
     */
    @PostMapping("/bind")
    public ResultX<Integer> bind(@RequestBody FileBindVO req){
        return success(saasFileService.bind(req.getFileIdList(),req.getObjId(),req.getTableName(),req.getFieldName()));
    }

}
