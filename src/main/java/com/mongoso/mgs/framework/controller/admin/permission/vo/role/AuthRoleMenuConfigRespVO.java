package com.mongoso.mgs.framework.controller.admin.permission.vo.role;

import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * 角色 AditReqVO
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
public class AuthRoleMenuConfigRespVO {

    private Long roleId;// 角色id
    private String menuId;// 菜单id
    private List<String> buttonCodeList;// 按钮编码
    private Integer dataScope;// 数据范围

}
