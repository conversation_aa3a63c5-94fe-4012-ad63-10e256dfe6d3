package com.mongoso.mgs.framework.controller.admin.permission.vo.menu;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 菜单表
 * 返回参数
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-21 15:04:16
 */
@Data
public class AuthMenuDetailRespVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String menuId;
    private String menuCode;
    private String menuName;
    private String path;
    private Integer type;
    private String icon;
    private Integer pageType;
    private Integer pathType;
    private Integer pathToken;
    private Integer appType;
    private String menuParam;
    private Integer isSystem;

    private List<AuthMenuButtonRespVO> buttonList;

}