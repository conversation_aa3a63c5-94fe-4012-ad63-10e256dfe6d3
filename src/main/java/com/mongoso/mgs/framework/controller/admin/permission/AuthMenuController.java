package com.mongoso.mgs.framework.controller.admin.permission;

import cn.hutool.json.JSONObject;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.controller.admin.permission.vo.menu.*;
import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.framework.service.permission.AuthMenuService;
import com.mongoso.mgs.framework.util.tree.TreeDrag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

import static com.mongoso.mgs.framework.common.domain.ResultX.success;

/**
 * 菜单表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-21 15:04:16
 */
@Validated
@RestController
@RequestMapping("/system")
public class AuthMenuController {

    @Resource
    private AuthMenuService authMenuService;

    //@Resource
    //private TenantMenuApi tenantMenuApi;

    @OperateLog("菜单新增")
    @PostMapping("/authMenuAdd")
    @PreAuthorize("@ss.hasAnyPermissions('authMenu:adit')")
    public ResultX<String> authMenuAdd(@Valid  @RequestBody AuthMenuAddReqVO reqVO){
        return success(authMenuService.authMenuAdd(reqVO));
    }

    @OperateLog("菜单编辑")
    @PostMapping("/authMenuEdit")
    @PreAuthorize("@ss.hasAnyPermissions('authMenu:adit')")
    public ResultX<String> authMenuEdit(@Valid  @RequestBody AuthMenuEditReqVO reqVO){
        return success(authMenuService.authMenuEdit(reqVO));
    }

    @OperateLog("菜单删除")
    @PostMapping("/authMenuDel")
    @PreAuthorize("@ss.hasAnyPermissions('authMenu:adit')")
    public ResultX<String> authMenuDel(@Valid  @RequestBody AuthMenuPrimaryReqVO reqVO){
        return success(authMenuService.authMenuDel(reqVO));
    }

    @OperateLog("菜单详情")
    @PostMapping("/authMenuDetail")
    @PreAuthorize("@ss.hasPermission('authMenu:query')")
    public ResultX<AuthMenuDetailRespVO> authMenuDetail(@RequestBody AuthMenuPrimaryReqVO reqVO) {
        return success(authMenuService.authMenuDetail(reqVO.getMenuId()));
    }

    @OperateLog("菜单Tree")
    @PostMapping("/authMenuTree")
    @PreAuthorize("@ss.hasPermission('authMenu:query')")
    public ResultX<List<AuthMenuRespVO>> authMenuTree(@RequestBody AuthMenuTreeReqVO reqVO) {
        return success(authMenuService.authMenuTree(reqVO));
    }

    @OperateLog("菜单Router")
    @PostMapping("/authMenuRouter")
    @PreAuthorize("@ss.hasPermission('authMenu:query')")
    public ResultX<List<Map<String, Object>>> authMenuRouter(@RequestBody JSONObject jsonString) {
        return success(authMenuService.authMenuRouter(jsonString));
    }

    @OperateLog("菜单RouterAll")
    @PostMapping("/authMenuRouterAll")
    @PreAuthorize("@ss.hasPermission('authMenu:query')")
    public ResultX<List<Map<String, Object>>> authMenuRouterAll(@RequestBody JSONObject jsonString) {
        return success(authMenuService.authMenuRouterAll(jsonString));
    }

    @OperateLog("菜单拖动")
    @PostMapping("authMenuDrag")
    @PreAuthorize("@ss.hasPermission('authMenu:adit')")
    public ResultX<List<String>> authMenuDrag(@RequestBody TreeDrag reqVO){
        return success(authMenuService.authMenuDrag(reqVO));
    }

    //@OperateLog("按钮枚举")
    //@PostMapping("/authButtonList")
    //@PreAuthorize("@ss.hasPermission('authMenu:query')")
    //public ResultX<List<AuthButtonRespDTO>> authButtonList(@RequestBody AuthMenuTenantPrimaryReqDTO reqVO) {
    //    ResultX<List<AuthButtonRespDTO>> listResultX = tenantMenuApi.authButtonList();
    //    return ResultX.success(listResultX.getData());
    //}

    @OperateLog("初始化按钮")
    @PostMapping("/authButtonInit")
    @PreAuthorize("@ss.hasPermission('authMenu:query')")
    public ResultX<Integer> authButtonInit( @RequestBody AuthMenuTreeReqVO reqVO) {
        return success(authMenuService.authButtonInit(reqVO));
    }

}
