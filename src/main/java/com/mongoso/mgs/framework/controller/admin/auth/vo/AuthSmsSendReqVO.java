package com.mongoso.mgs.framework.controller.admin.auth.vo;

import lombok.Data;

import jakarta.validation.constraints.NotEmpty;

/**
 * 融资端 - 发送手机验证码 Request VO
 *
 * <AUTHOR>
 * @since 2023/7/27 20:15
 */
@Data
public class AuthSmsSendReqVO {

    /**
     *
     */
    @NotEmpty(message = "手机号不能为空")
    private String phoneNumber;

    /*图形验证码*/
    private String verifyCode;

    /*图形验证码唯一key*/
    private String verifyCodeKey;

    /**
     * 验证码
     */
    private String code;

}
