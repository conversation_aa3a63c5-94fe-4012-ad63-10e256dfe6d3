package com.mongoso.mgs.framework.controller.admin.permission.vo.user;

import lombok.Data;
import lombok.ToString;

import jakarta.validation.constraints.NotEmpty;

/**
 * 用户 AditReqVO
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
public class AuthUserForgetPwdReqVO {

    @NotEmpty(message = "账号不能为空")
    private String userAccount;

    @NotEmpty(message = "手机号码不能为空")
    private String phoneNumber;

    /** 短信验证码 */
    @NotEmpty(message = "短信验证码不能为空")
    private String smsVerifyCode;

    /**
     * 用户密码（新密码）
     */
    @NotEmpty(message = "新密码不能为空")
    private String newUserPassword;
}
