package com.mongoso.mgs.framework.controller.admin.permission.vo.user;

import com.mongoso.mgs.framework.common.domain.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 用户 PageReqVO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AuthUserPageReqVO extends PageParam {

    private String userName;

    private String userAccount;

    private String phoneNumber;

    private String email;

    private String remark;

    private Integer enable;

    private Integer isAdmin;
}
