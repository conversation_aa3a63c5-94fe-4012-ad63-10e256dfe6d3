package com.mongoso.mgs.framework.enums;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/2/10
 * @description
 */
@Data
public class RedisKey {
    public final static String APP_STATUS_KEY = "app_status"; // 应用状态key
    public final static String SMS_CODE_MOBILE = "sms_code_mobile:"; // 短信验证码前缀
    public final static String VERIFY_CODE_KEY = "verify_code_key"; // 系统属性列表

    public static String getAppStatusHashKey(String projectName, Integer AppId) {
        // 项目名+id
        return projectName + "-" + AppId;
    }

    public static String getSmsCodeMobile(String mobile) {
        return SMS_CODE_MOBILE + mobile;
    }
}
