package com.mongoso.mgs.framework.enums;


import com.mongoso.mgs.framework.common.exception.ErrorCode;

/**
 * System 错误码枚举类
 * <p>
 * system 系统，使用 1-001-000-000 段
 */
public interface SassErrorCodeConstants {

    // ========== AUTH 模块 1002000000 ==========
    ErrorCode AUTH_LOGIN_BAD_TENANT = new ErrorCode("1002000000", "登录失败，找不到该账号绑定的企业信息");
    ErrorCode AUTH_LOGIN_BAD_CREDENTIALS = new ErrorCode("1002000000", "登录失败，账号密码不正确");
    ErrorCode AUTH_LOGIN_USER_DISABLED = new ErrorCode("1002000001", "登录失败，账号被禁用");
    ErrorCode AUTH_LOGIN_CAPTCHA_CODE_ERROR = new ErrorCode("1002000004", "验证码不正确，原因：{}");
    ErrorCode AUTH_THIRD_LOGIN_NOT_BIND = new ErrorCode("1002000005", "未绑定账号，需要进行绑定");
    ErrorCode AUTH_TOKEN_EXPIRED = new ErrorCode("1002000006", "Token 已经过期");
    ErrorCode AUTH_MOBILE_NOT_EXISTS = new ErrorCode("1002000007", "手机号不存在");
    ErrorCode AUTH_LOGIN_BAD_ROLE = new ErrorCode("1002000008", "账号未分配角色");
    ErrorCode AUTH_LOGIN_BAD_MENU = new ErrorCode("1002000008", "角色未分配菜单");
    ErrorCode AUTH_LOGIN_BAD_MENU2 = new ErrorCode("1002000010", "未分配置菜单");
    ErrorCode AUTH_LOGIN_VERIFY_CODE = new ErrorCode("1002000013", "图形验证码不正确");
    ErrorCode AUTH_LOGIN_BAD_CREDENTIALS_COUNT = new ErrorCode("1002000014", "登录失败，账号密码不正确，可尝试登陆次数为：{}次" );
    ErrorCode AUTH_LOGIN_BAD_CREDENTIALS_LOCK = new ErrorCode("1002000015", "当前用户登陆已锁定，请{}分钟后重试" );
    ErrorCode AUTH_LOGIN_VERIFY_CODE_IS_EMPTY = new ErrorCode("1002000016", "图形验证码不能为空");



    // ========== 菜单模块 1002001000 ==========
    ErrorCode ROOT_OPERATE = new ErrorCode("1002002006", "该功能只有超级管理员才能操作！");
    ErrorCode NOT_ADD_NULL = new ErrorCode("1002002007", "空的菜单不能保存");
    ErrorCode NOT_ADD_NULL2 = new ErrorCode("1002002007", "category参数必传");


    ErrorCode MENU_NAME_DUPLICATE = new ErrorCode("1002001000", "已经存在该名字的菜单");
    ErrorCode MENU_PARENT_NOT_EXISTS = new ErrorCode("1002001001", "父菜单不存在");
    ErrorCode MENU_PARENT_ERROR = new ErrorCode("1002001002", "不能设置自己为父菜单");
    ErrorCode MENU_NOT_EXISTS = new ErrorCode("1002001003", "菜单不存在");
    ErrorCode MENU_EXISTS_CHILDREN = new ErrorCode("1002001004", "存在子菜单，无法删除");
    ErrorCode MENU_PARENT_NOT_DIR_OR_MENU = new ErrorCode("1002001005", "父菜单的类型必须是目录或者菜单");
    ErrorCode MENU_PARENT_NOT_DIR = new ErrorCode("1002001005", "父类型必须是目录");
    ErrorCode MENU_DRAG_ERROR1 = new ErrorCode("1002001005", "目标对象不能是自己");
    ErrorCode MENU_DRAG_ERROR2 = new ErrorCode("1002001005", "目标对象必须是目录类型");
    ErrorCode MENU_DRAG_ERROR3 = new ErrorCode("1002001005", "目标对象不能移到当前对象的子集中");

    // ========== 角色模块 1002002000 ==========
    ErrorCode ROLE_NOT_EXISTS = new ErrorCode("1002002000", "角色不存在");
    ErrorCode ROLE_NAME_DUPLICATE = new ErrorCode("1002002001", "已经存在名为【{}】的角色");
    ErrorCode ROLE_CODE_DUPLICATE = new ErrorCode("1002002002", "已经存在编码为【{}】的角色");
    ErrorCode ROLE_CAN_NOT_UPDATE_SYSTEM_TYPE_ROLE = new ErrorCode("1002002003", "不能操作类型为系统内置的角色");
    ErrorCode ROLE_IS_DISABLE = new ErrorCode("1002002004", "名字为【{}】的角色已被禁用");
    ErrorCode ROLE_ADMIN_CODE_ERROR = new ErrorCode("1002002005", "编码【{}】不能使用");
    ErrorCode ROLE_DELETE_ERROR = new ErrorCode("1002002005", "该角色已被使用，不能删除");


    // ========== 用户模块 1002003000 ==========
    ErrorCode USER_USERNAME_EXISTS = new ErrorCode("1002003000", "用户账号已经存在");
    ErrorCode USER_MOBILE_EXISTS = new ErrorCode("1002003001", "手机号已经存在");
    ErrorCode USER_EMAIL_EXISTS = new ErrorCode("1002003002", "邮箱已经存在");
    ErrorCode USER_NOT_EXISTS = new ErrorCode("1002003003", "用户不存在");
    ErrorCode USER_IMPORT_LIST_IS_EMPTY = new ErrorCode("1002003004", "导入用户数据不能为空！");
    ErrorCode USER_PASSWORD_FAILED = new ErrorCode("1002003005", "用户密码校验失败");
    ErrorCode USER_IS_DISABLE = new ErrorCode("1002003006", "名字为【{}】的用户已被禁用");
    ErrorCode USER_COUNT_MAX = new ErrorCode("1002003008", "创建用户失败，原因：超过租户最大租户配额({})！");
    ErrorCode USER_OR_MOBILE_ERROR = new ErrorCode("1002003009", "账号或手机号错误！");


    // ========= 文件相关 1001003000=================
    ErrorCode FILE_PATH_EXISTS = new ErrorCode("1001003000", "文件路径已存在");
    ErrorCode FILE_NOT_EXISTS = new ErrorCode("1001003001", "文件不存在");
    ErrorCode FILE_IS_EMPTY = new ErrorCode("1001003002", "文件为空");



    // ========== 短信验证码 1002014000 ==========
    ErrorCode SMS_CODE_NOT_FOUND = new ErrorCode("1002014000", "验证码不存在");
    ErrorCode SMS_CODE_EXPIRED = new ErrorCode("1002014001", "验证码已过期");
    ErrorCode SMS_CODE_USED = new ErrorCode("1002014002", "验证码已使用");
    ErrorCode SMS_CODE_NOT_CORRECT = new ErrorCode("1002014003", "验证码不正确");
    ErrorCode SMS_CODE_EXCEED_SEND_MAXIMUM_QUANTITY_PER_DAY = new ErrorCode("1002014004", "超过每日短信发送数量");
    ErrorCode SMS_CODE_SEND_TOO_FAST = new ErrorCode("1002014005", "短信发送过于频率");
    ErrorCode SMS_CODE_IS_EXISTS = new ErrorCode("1002014006", "手机号已被使用");
    ErrorCode SMS_CODE_IS_UNUSED = new ErrorCode("1002014007", "验证码未被使用");

    // ========== 租户信息 1002015000 ==========
    ErrorCode TENANT_NOT_EXISTS = new ErrorCode("1002015000", "租户不存在");
    ErrorCode TENANT_DISABLE = new ErrorCode("1002015001", "名字为【{}】的租户已被禁用");
    ErrorCode TENANT_EXPIRE = new ErrorCode("1002015002", "名字为【{}】的租户已过期");
    ErrorCode TENANT_CAN_NOT_UPDATE_SYSTEM = new ErrorCode("1002015003", "系统租户不能进行修改、删除等操作！");
    ErrorCode TENANT_NAME_DISABLE = new ErrorCode("1002015001", "名字为【{}】的租户已重复");
    ErrorCode TENANT_USER_DISABLE = new ErrorCode("1002015001", "用户id为【{}】的租户已重复");
    ErrorCode TENANT_DOMAIN_DISABLE = new ErrorCode("1002015001", "域名为【{}】的租户已重复");

    // ========== 租户版本 1002016000 ==========
    ErrorCode TENANT_VERSION_NOT_EXISTS = new ErrorCode("1002016000", "租户版本不存在");
    ErrorCode TENANT_VERSION_USED = new ErrorCode("1002016001", "租户正在使用该版本，请给租户重新设置版本后再尝试删除");
    ErrorCode TENANT_VERSION_DISABLE = new ErrorCode("1002016002", "名字为【{}】的租户版本已被禁用");

    // ========== 错误码模块 1002017000 ==========
    ErrorCode ERROR_CODE_NOT_EXISTS = new ErrorCode("1002017000", "错误码不存在");
    ErrorCode ERROR_CODE_DUPLICATE = new ErrorCode("1002017001", "已经存在编码为【{}】的错误码");

    // ========== 社交用户 1002018000 ==========
    ErrorCode SOCIAL_USER_AUTH_FAILURE = new ErrorCode("1002018000", "社交授权失败，原因是：{}");
    ErrorCode SOCIAL_USER_UNBIND_NOT_SELF = new ErrorCode("1002018001", "社交解绑失败，非当前用户绑定");
    ErrorCode SOCIAL_USER_NOT_FOUND = new ErrorCode("1002018002", "社交授权失败，找不到对应的用户");

    // ========== 系统敏感词 1002019000 =========
    ErrorCode SENSITIVE_WORD_NOT_EXISTS = new ErrorCode("1002019000", "系统敏感词在所有标签中都不存在");
    ErrorCode SENSITIVE_WORD_EXISTS = new ErrorCode("1002019001", "系统敏感词已在标签中存在");

    // ========== OAuth2 客户端 1002020000 =========
    ErrorCode OAUTH2_CLIENT_NOT_EXISTS = new ErrorCode("1002020000", "OAuth2 客户端不存在");
    ErrorCode OAUTH2_CLIENT_EXISTS = new ErrorCode("1002020001", "OAuth2 客户端编号已存在");
    ErrorCode OAUTH2_CLIENT_DISABLE = new ErrorCode("1002020002", "OAuth2 客户端已禁用");
    ErrorCode OAUTH2_CLIENT_AUTHORIZED_GRANT_TYPE_NOT_EXISTS = new ErrorCode("1002020003", "不支持该授权类型");
    ErrorCode OAUTH2_CLIENT_SCOPE_OVER = new ErrorCode("1002020004", "授权范围过大");
    ErrorCode OAUTH2_CLIENT_REDIRECT_URI_NOT_MATCH = new ErrorCode("1002020005", "无效 redirect_uri: {}");
    ErrorCode OAUTH2_CLIENT_CLIENT_SECRET_ERROR = new ErrorCode("1002020006", "无效 client_secret: {}");

    // ========== OAuth2 授权 1002021000 =========
    ErrorCode OAUTH2_GRANT_CLIENT_ID_MISMATCH = new ErrorCode("1002021000", "client_id 不匹配");
    ErrorCode OAUTH2_GRANT_REDIRECT_URI_MISMATCH = new ErrorCode("1002021001", "redirect_uri 不匹配");
    ErrorCode OAUTH2_GRANT_STATE_MISMATCH = new ErrorCode("**********", "state 不匹配");
    ErrorCode OAUTH2_GRANT_CODE_NOT_EXISTS = new ErrorCode("**********", "code 不存在");

    // ========== OAuth2 授权 ********** =========
    ErrorCode OAUTH2_CODE_NOT_EXISTS = new ErrorCode("**********", "code 不存在");
    ErrorCode OAUTH2_CODE_EXPIRE = new ErrorCode("**********", "code 已过期");

    // ========== 邮箱账号 ********** ==========
    ErrorCode MAIL_ACCOUNT_NOT_EXISTS = new ErrorCode("**********", "邮箱账号不存在");
    ErrorCode MAIL_ACCOUNT_RELATE_TEMPLATE_EXISTS = new ErrorCode("**********", "无法删除，该邮箱账号还有邮件模板");

    // ========== 邮件模版 ********** ==========
    ErrorCode MAIL_TEMPLATE_NOT_EXISTS = new ErrorCode("**********", "邮件模版不存在");
    ErrorCode MAIL_TEMPLATE_CODE_EXISTS = new ErrorCode("**********", "邮件模版 code({}) 已存在");

    // ========== 邮件发送 ********** ==========
    ErrorCode MAIL_SEND_TEMPLATE_PARAM_MISS = new ErrorCode("**********", "模板参数({})缺失");
    ErrorCode MAIL_SEND_MAIL_NOT_EXISTS = new ErrorCode("**********", "邮箱不存在");

    // ========== 站内信模版 ********** ==========
    ErrorCode NOTIFY_TEMPLATE_NOT_EXISTS = new ErrorCode("**********", "站内信模版不存在");
    ErrorCode NOTIFY_TEMPLATE_CODE_DUPLICATE = new ErrorCode("**********", "已经存在编码为【{}】的站内信模板");

    // ========== 站内信模版 ********** ==========

    // ========== 站内信发送 ********** ==========
    ErrorCode NOTIFY_SEND_TEMPLATE_PARAM_MISS = new ErrorCode("**********", "模板参数({})缺失");


    ErrorCode AUTH_USER_NOT_EXISTS = new ErrorCode("100200023", "用户不存在");

    // ========== 用户 100200023 ==========

    ErrorCode APP_ITEM_NOT_EXISTS = new ErrorCode("1002003003", "应用不存在");
    ErrorCode APP_CONNECTION_FAILED = new ErrorCode("1002003003", "应用连接失败，域名配置错误或服务没启动");
    ErrorCode APP_PATH_FAILED = new ErrorCode("1002003003", "应用连接成功，接口配置错误");
    ErrorCode APP_STATUS_ERROR = new ErrorCode("1002003003", "已禁用的应用不能开关机");
    ErrorCode APP_STATUS_ERROR2 = new ErrorCode("1002003003", "没有上传zip包的应用不能开关机");
    ErrorCode APP_VERSION_ERROR2 = new ErrorCode("1002003003", "没有安装版本，不能启动");
    ErrorCode APP_SHELL_ERROR = new ErrorCode("1002003003", "执行shell命令，错误原因【{}】");
    ErrorCode APP_START_ERROR = new ErrorCode("1002003003", "已开机");
    ErrorCode APP_STOP_ERROR = new ErrorCode("1002003003", "已关机");
    ErrorCode APP_ZIP_ERROR = new ErrorCode("1002003003", "上传的zip文件命名和包名不一致，如：{}");
    ErrorCode APP_ZIP_ERROR2 = new ErrorCode("1002003003", "上传的zip文件命名规则不对：如：{}");
    ErrorCode APP_ZIP_ERROR3 = new ErrorCode("1002003003", "版本号重复，请更改jar包和zip包的版本号在上传，重复版本号：{}");
    ErrorCode APP_ZIP_NOT_EXISTS = new ErrorCode("1002003003", "应用版本不存在");
    ErrorCode PROJECTNAME_NOT_EXISTS = new ErrorCode("1002003001", "应用名称不存在");
    ErrorCode APP_ZIP_NOT3_EXISTS = new ErrorCode("1002003003", "当前版本不能重复安装！");
    ErrorCode APP_ZIP_NOT4_EXISTS = new ErrorCode("1002003003", "已运行的应用不能安装！");
    ErrorCode APP_ZIP_NOT5_EXISTS = new ErrorCode("1002003003", "当前版本的应用不能卸载！");
    ErrorCode APP_CONFIG_NOT_EXISTS = new ErrorCode("1002003003", "用户不存在");
    ErrorCode APP_BAO_NOT_EXISTS = new ErrorCode("1002003003", "用户不存在");

    // ========== 数据角色 ==========
    ErrorCode DATA_ROLE_NOT_EXISTS = new ErrorCode("1003003001", "数据角色不存在");


    // ========== 企业PC端菜单 TODO 补充编号 ==========
    ErrorCode AUTH_MENU_TENANT_NOT_EXISTS = new ErrorCode("TODO 补充编号", "企业PC端菜单不存在");



    ErrorCode DICT_NOT_EXISTS = new ErrorCode("100204000", "字典不存在");
    ErrorCode DICT_EXISTS = new ErrorCode("100204001", "字典名称存在");
}
