package com.mongoso.mgs.common;//package com.mongoso.mgs.common;
//
//import com.alibaba.fastjson.JSONObject;
//import com.baomidou.mybatisplus.extension.handlers.AbstractJsonTypeHandler;
//import org.apache.ibatis.type.Alias;
//
//@Alias("jsonObjectTypeHandler") // 可以使用 @Alias 注解为这个 TypeHandler 定义别名
//public class JsonObjectTypeHandler extends AbstractJsonTypeHandler<JSONObject> {
//
//
//    @Override
//    protected JSONObject parse(String json) {
//        return JSONObject.parseObject(json);
//    }
//
//    @Override
//    protected String toJson(JSONObject obj) {
//        return JSONObject.toJSONString(obj);
//    }
//}