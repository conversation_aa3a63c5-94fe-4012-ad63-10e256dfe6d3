package com.mongoso.mgs.common.security.config;

import com.mongoso.mgs.framework.common.constant.feign.RpcConstant;
import com.mongoso.mgs.framework.security.config.AuthorizeRequestsCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configurers.AuthorizeHttpRequestsConfigurer;
import org.springframework.security.config.annotation.web.configurers.ExpressionUrlAuthorizationConfigurer;

/**
 *  Security过滤器自定义规则配置
 *
 */
@Configuration(proxyBeanMethods = false, value = "systemSecurityConfiguration")
public class SecurityFilterRule {

    @Bean("systemAuthorizeRequestsCustomizer")
    public AuthorizeRequestsCustomizer authorizeRequestsCustomizer() {
        return new AuthorizeRequestsCustomizer() {

            @Override
            public void customize(AuthorizeHttpRequestsConfigurer<HttpSecurity>.AuthorizationManagerRequestMatcherRegistry registry) {

                // 配置自定义过滤器规则
                // 内部服务调用,全部放行 /rpc-api
                registry.requestMatchers(RpcConstant.RPC_API_PREFIX + "/**").permitAll();
            }

        };
    }

}
