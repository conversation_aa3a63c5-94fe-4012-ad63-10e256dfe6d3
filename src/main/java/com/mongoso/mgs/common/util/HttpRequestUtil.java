package com.mongoso.mgs.common.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import okhttp3.*;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;

/**
 * http工具类
 * common包里面超时时间默认是8秒，重新写一个
 * daijinbiao
 * 2024-11-12
 */
public class HttpRequestUtil {
    // 发送 GET 请求
    public static String sendGet(String url) throws IOException {
        HttpURLConnection connection = null;
        BufferedReader reader = null;
        try {
            URL requestUrl = new URL(url);
            connection = (HttpURLConnection) requestUrl.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(5000); // 设置连接超时时间
            connection.setReadTimeout(5000);    // 设置读取超时时间

            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) { // 请求成功
                reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                StringBuilder response = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                return response.toString();
            } else {
                throw new IOException("GET请求失败：HTTP错误代码 " + responseCode);
            }
        } finally {
            if (reader != null) {
                reader.close();
            }
            if (connection != null) {
                connection.disconnect();
            }
        }
    }

    // 发送 POST 请求
    public static String sendPost(String url, String jsonInputString, Map<String,String> headers, Integer conTimeOut, Integer readTimeOut) throws IOException {
        HttpURLConnection connection = null;
        conTimeOut = conTimeOut == null? 10000:conTimeOut;
        readTimeOut = readTimeOut == null? 10000:readTimeOut;
        OutputStream os = null;
        BufferedReader reader = null;
        try {
            URL requestUrl = new URL(url);
            connection = (HttpURLConnection) requestUrl.openConnection();
            connection.setRequestMethod("POST");
            connection.setConnectTimeout(conTimeOut); // 设置连接超时时间
            connection.setReadTimeout(readTimeOut);    // 设置读取超时时间
            connection.setDoOutput(true);
            connection.setRequestProperty("Content-Type", "application/json; utf-8");
            connection.setRequestProperty("Accept", "application/json");

            // 设置请求头
            if (headers != null && !headers.isEmpty()) {
                for (Map.Entry<String, String> entry : headers.entrySet()) {
                    String key = entry.getKey();
                    String value = entry.getValue();
                    connection.setRequestProperty(key, value);
                }
            }
            // 发送请求体
            os = connection.getOutputStream();
            byte[] input = jsonInputString.getBytes("utf-8");
            os.write(input, 0, input.length);

            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) { // 请求成功
                reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), "utf-8"));
                StringBuilder response = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                return response.toString();
            } else {
                throw new IOException("POST请求失败：HTTP错误代码 " + responseCode);
            }
        } finally {
            if (os != null) {
                os.close();
            }
            if (reader != null) {
                reader.close();
            }
            if (connection != null) {
                connection.disconnect();
            }
        }
    }

    //private final static OkHttpClient client = new OkHttpClient();
    //private final static MediaType JSON_MEDIA_TYPE = MediaType.parse("application/json; charset=utf-8");
    //
    //public static JSONObject sendPostRequest(String url, String jsonParams, Map<String, String> headers) throws IOException {
    //    // 创建请求体
    //    RequestBody requestBody = RequestBody.create(JSON_MEDIA_TYPE, jsonParams);
    //
    //    // 构建默认头和自定义头的组合
    //    Headers requestHeaders = buildHeaders(headers);
    //
    //    // 创建请求对象
    //    Request request = new Request.Builder()
    //            .url(url)
    //            .post(requestBody)
    //            .headers(requestHeaders)
    //            .build();
    //
    //    try (Response response = client.newCall(request).execute()) {
    //        if (!response.isSuccessful()) {
    //            throw new IOException("Unexpected response code: " + response.code());
    //        }
    //
    //        // 获取响应内容
    //        String responseBody = response.body().string();
    //        return JSONObject.parseObject(responseBody);
    //    }
    //}

    private static Headers buildHeaders(Map<String, String> customHeaders) {
        Headers.Builder headersBuilder = new Headers.Builder();
        // 设置默认的 Content-Type 为 application/json
        headersBuilder.add("Content-Type", "application/json");

        // 添加自定义头（如果有）
        if (customHeaders != null && !customHeaders.isEmpty()) {
            for (Map.Entry<String, String> entry : customHeaders.entrySet()) {
                headersBuilder.set(entry.getKey(), entry.getValue());
            }
        }

        return headersBuilder.build();
    }

    public static void main(String[] args) {
        testAI();
    }


    private static void testAI() {
        String url = "https://xiaosong.mongoso.vip/api/v1/chat/completions";
        String param = "{\"chatId\":\"67ac74d5423b4217bdb90de4\",\"stream\":false,\"messages\":[{\"role\":\"user\",\"content\":\"[{\\\"itemName\\\":\\\"演示架构图\\\",\\\"children\\\":[{\\\"itemName\\\":\\\"列表信息\\\",\\\"remark\\\":\\\"\\\",\\\"editing\\\":false,\\\"expanded\\\":true,\\\"selected\\\":false,\\\"children\\\":[{\\\"itemName\\\":\\\"姓名\\\",\\\"remark\\\":\\\"\\\",\\\"editing\\\":false,\\\"expanded\\\":false,\\\"selected\\\":false,\\\"children\\\":[],\\\"_itemId\\\":20,\\\"color\\\":\\\"dark\\\",\\\"sn\\\":\\\"0.0.0\\\",\\\"level\\\":3,\\\"index\\\":0,\\\"multipleLine\\\":false,\\\"dragging\\\":false,\\\"touched\\\":false},{\\\"itemName\\\":\\\"年龄\\\",\\\"remark\\\":\\\"\\\",\\\"editing\\\":false,\\\"expanded\\\":false,\\\"selected\\\":false,\\\"children\\\":[],\\\"_itemId\\\":22,\\\"color\\\":\\\"dark\\\",\\\"sn\\\":\\\"0.0.1\\\",\\\"level\\\":3,\\\"index\\\":1,\\\"multipleLine\\\":false,\\\"dragging\\\":false},{\\\"itemName\\\":\\\"性别\\\",\\\"remark\\\":\\\"\\\",\\\"editing\\\":false,\\\"expanded\\\":false,\\\"selected\\\":false,\\\"children\\\":[],\\\"_itemId\\\":23,\\\"color\\\":\\\"dark\\\",\\\"sn\\\":\\\"0.0.2\\\",\\\"level\\\":3,\\\"index\\\":2,\\\"multipleLine\\\":false,\\\"dragging\\\":false},{\\\"itemName\\\":\\\"生日\\\",\\\"remark\\\":\\\"\\\",\\\"editing\\\":false,\\\"expanded\\\":false,\\\"selected\\\":false,\\\"children\\\":[],\\\"_itemId\\\":24,\\\"color\\\":\\\"dark\\\",\\\"sn\\\":\\\"0.0.3\\\",\\\"level\\\":3,\\\"index\\\":3,\\\"multipleLine\\\":false,\\\"dragging\\\":false},{\\\"itemName\\\":\\\"住址\\\",\\\"remark\\\":\\\"\\\",\\\"editing\\\":false,\\\"expanded\\\":false,\\\"selected\\\":false,\\\"children\\\":[],\\\"_itemId\\\":25,\\\"color\\\":\\\"dark\\\",\\\"sn\\\":\\\"0.0.4\\\",\\\"level\\\":3,\\\"index\\\":4,\\\"multipleLine\\\":false,\\\"dragging\\\":false},{\\\"itemName\\\":\\\"\\\",\\\"remark\\\":\\\"\\\",\\\"editing\\\":true,\\\"expanded\\\":false,\\\"selected\\\":true,\\\"children\\\":[],\\\"_itemId\\\":26,\\\"color\\\":\\\"dark\\\",\\\"sn\\\":\\\"0.0.5\\\",\\\"level\\\":3,\\\"index\\\":5}],\\\"_itemId\\\":null,\\\"color\\\":\\\"dark\\\",\\\"sn\\\":\\\"0.0\\\",\\\"level\\\":2,\\\"index\\\":0,\\\"multipleLine\\\":false,\\\"partSelected\\\":false,\\\"dragging\\\":false,\\\"touched\\\":false},{\\\"itemName\\\":\\\"新增信息\\\",\\\"remark\\\":\\\"\\\",\\\"editing\\\":false,\\\"expanded\\\":false,\\\"selected\\\":false,\\\"children\\\":[],\\\"_itemId\\\":19,\\\"color\\\":\\\"dark\\\",\\\"sn\\\":\\\"0.1\\\",\\\"level\\\":2,\\\"index\\\":1,\\\"multipleLine\\\":false,\\\"touched\\\":false,\\\"dragging\\\":false}],\\\"mindType\\\":\\\"arch\\\",\\\"expanded\\\":true,\\\"selected\\\":false,\\\"partSelected\\\":false,\\\"sn\\\":\\\"0\\\",\\\"level\\\":1,\\\"dragging\\\":false,\\\"editing\\\":false,\\\"touched\\\":false,\\\"startIndex\\\":26,\\\"index\\\":0,\\\"copied\\\":false,\\\"multipleLine\\\":false}]将上方返回结果集中的 content 属性的内容转为下一个接口的入参，格式示例如下：{\\\"tableName\\\":\\\"teststs\\\",\\\"tableCode\\\":\\\"u_asdfasfsa\\\",\\\"remark\\\":\\\"\\\",\\\"projectId\\\":\\\"1889599398700933121\\\",\\\"fields\\\":[{\\\"sort\\\":0,\\\"propType\\\":1,\\\"isNullable\\\":0,\\\"fieldType\\\":\\\"VARCHAR\\\",\\\"leng\\\":50,\\\"rowIndex\\\":9,\\\"fieldName\\\":\\\"姓名\\\",\\\"fieldCode\\\":\\\"name\\\",\\\"defaultVal\\\":\\\"\\\",\\\"remark\\\":\\\"\\\"}]}1.英文翻译不要直接翻译成拼音，专业一点，且适当做缩写2.将 content 内的属性添加到 fields 属性中，tableName 为第一个 itemName 的文本，tableCode 为翻译为英文并且添加 u_的前缀；3.fields 中的 fieldCode 也进行翻译英文，但是不加 u_前缀，在提供的数据基础上，fields 的第一个数据额外生成一个主键数据，规范如下：fieldName 为: itemName+\\\"主键\\\"，fieldCode 为tableCode+\\\"_pk\\\"，增加一个属性 isPrimaryKey:1，且主键的 isNullable 为1\"}],\"detail\":false}\n";
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Bearer mongoso-h6IJVQ6puZq46oIw5G3GXrSsEYyeAfEbfCwmspAShekfZJK82KrD25");
        headers.put("Content-Type", "application/json");

        JSONObject requestJson = new JSONObject();
        // 填充基本字段
        requestJson.put("chatId", "67ac74d5423b4217bdb90de4");
        requestJson.put("stream", false);
        requestJson.put("detail", false);

        // 创建 messages 数组
        JSONArray messagesArray = new JSONArray();
        JSONObject messageJson = new JSONObject();
        messageJson.put("content", "我想去三亚旅游，帮我推荐一些三亚酒店吧");
        messageJson.put("role", "user");

        // 将消息加入数组
        messagesArray.add(messageJson);
        requestJson.put("messages", messagesArray);
        try {
            String s = sendPost(url, requestJson.toJSONString(), headers, 50000, 50000);
            System.out.println(s);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static void testQuery() {
        String url = "http://localhost:8018/admin-api/component/queryProjectComp";
        String param = "{\n" +
                "    \"cmptList\": \"{test}\"\n" +
                "}";
        Map<String, String> headers = new HashMap<>();
        headers.put("token", "test1");

        try {
            String s = sendPost(url, param, headers, 5000, 5000);
            System.out.println(s);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
