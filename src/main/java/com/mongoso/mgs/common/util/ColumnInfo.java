package com.mongoso.mgs.common.util;

import lombok.Data;

import java.io.Serializable;

@Data
public class ColumnInfo implements Serializable {
    private String name;   // 列名
    private String type;   // 数据类型
    private Integer size;      // 列大小
    private Integer fieldPrecision;      // 精度
    private Integer isAutoIncrease;      // 是否自增
    private Integer sort = 0;      // 排序
    private String defaultValue;      // 默认值
    private String remark;      // 描述
    private Boolean nullable; // 是否可为空
    private Boolean primaryKey; // 是否主键


    // Getters 和 Setters
//        public String getName() { return name; }
//        public void setName(String name) { this.name = name; }
//        public String getType() { return type; }
//        public void setType(String type) { this.type = type; }
//        public int getSize() { return size; }
//        public void setSize(int size) { this.size = size; }
//        public boolean isNullable() { return nullable; }
//        public void setNullable(boolean nullable) { this.nullable = nullable; }
}
