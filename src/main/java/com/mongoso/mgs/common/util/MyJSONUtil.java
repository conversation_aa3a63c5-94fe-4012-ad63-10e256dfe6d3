package com.mongoso.mgs.common.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.mongoso.mgs.module.model.dal.db.modelfield.ModelFieldDO;
import com.mongoso.mgs.module.model.dal.db.modelfieldconf.ModelFieldConfDO;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 用来处理数据建模中JSON字段配置的属性翻译
 * daijinbiao
 * 2024-11-7 17:26
 */
public class MyJSONUtil {

    /**
     * 修改 JSON 数组中的 propertyCode
     * @param jsonArray
     * @param propertyCodeMap
     */
    public static void modifyJsonArray(JSONArray jsonArray, Map<String, String> propertyCodeMap) {
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);

            // 获取 propertyName
            String propertyName = jsonObject.getString("propertyName");

            // 查找对应的 propertyCode
            if (propertyCodeMap.containsKey(propertyName)) {
                jsonObject.put("propertyName", propertyCodeMap.get(propertyName));
            }

            // 检查是否有 children 属性，并递归调用
            if (jsonObject.containsKey("children")) {
                JSONArray childrenArray = jsonObject.getJSONArray("children");
                modifyJsonArray(childrenArray, propertyCodeMap); // 递归调用
            }
        }
    }

    /**
     * 修改 List<JSONObject> 中的 propertyCode
     * @param jsonList
     * @param propertyCodeMap
     */
    public static void modifyJsonList(List<JSONObject> jsonList, Map<String, String> propertyCodeMap) {
        for (JSONObject jsonObject : jsonList) {
            // 获取 propertyName
            String propertyName = jsonObject.getString("propertyName");

            // 查找对应的 propertyCode
            if (propertyCodeMap.containsKey(propertyName)) {
                jsonObject.put("propertyName", propertyCodeMap.get(propertyName));
            }

            // 检查是否有 children 属性，并递归调用
            if (jsonObject.containsKey("children")) {
                JSONArray childrenArray = jsonObject.getJSONArray("children");
                modifyJsonArray(childrenArray, propertyCodeMap); // 递归调用
            }
        }
    }

    /**
     * 修改 List<JSONObject> 中的 propertyCode
     * @param jsonList
     * @param fieldConfs
     */
//    public static void modifyJsonList(List<JSONObject> jsonList, List<ModelFieldConf> fieldConfs) {
//        // 构建 propertyName 映射关系
//        Map<String, String> propertyCodeMap = new HashMap<>();
//        for (ModelFieldConf conf : fieldConfs) {
//            propertyCodeMap.put(conf.getFieldName(), conf.getFieldCode());
//        }
//
//        // 遍历 JSON 对象列表并修改 propertyName
//        for (JSONObject jsonObject : jsonList) {
//            // 获取 propertyName
//            String propertyName = jsonObject.getString("propertyName");
//
//            // 查找对应的 propertyCode
//            if (propertyCodeMap.containsKey(propertyName)) {
//                jsonObject.put("propertyName", propertyCodeMap.get(propertyName));
//            }
//
//            // 检查是否有 children 属性，并递归调用
//            if (jsonObject.containsKey("children")) {
//                List<JSONObject> childrenList = jsonObject.getObject("children", List.class);
//                modifyJsonList(childrenList, fieldConfs); // 递归调用
//            }
//        }
//    }


    /**
     * 翻译JSON结构
     * @param modelFields
     * @param configMap
     * @return
     */
    public JSONObject transJsonStructure(List<ModelFieldDO> modelFields, Map<String, ModelFieldConfDO> configMap) {
        // 创建根JSON对象
        List<JSONObject> jsonArray = new ArrayList<>();

        for (ModelFieldDO field : modelFields) {
            // 构建 JSON 对象
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("fieldCode", field.getFieldCode());
            jsonObject.put("fieldType", field.getFieldType());

            // 翻译字段名称和备注
            if (configMap.containsKey(field.getFieldName())) {
                ModelFieldConfDO fieldConf = configMap.get(field.getFieldName());
                jsonObject.put("fieldName", fieldConf.getFieldName());
                jsonObject.put("remark", fieldConf.getRemark());
            } else {
                // 如果没有找到翻译，可以保留原始字段名称或设定为默认值
                jsonObject.put("fieldName", field.getFieldName());
                jsonObject.put("remark", "未翻译");
            }

            // 处理子字段
            if (field.getJsonFields() != null) {
                jsonObject.put("jsonFields", buildJsonFields(field.getJsonFields(), configMap));
            }

            jsonArray.add(jsonObject);
        }

        // 可以考虑返回一个根对象包含整个数组
        JSONObject root = new JSONObject();
        root.put("fields", jsonArray);
        return root;
    }

    /**
     * josnb格式字段下的json封装
     * @param childFields
     * @param configMap
     * @return
     */
    private List<JSONObject> buildJsonFields(List<JSONObject> childFields, Map<String, ModelFieldConfDO> configMap) {
        List<JSONObject> jsonArray = new ArrayList<>();

        for (JSONObject childField : childFields) {
            JSONObject jsonField = new JSONObject();
            String childFieldName = childField.getString("fieldName");

            // 填充基本信息并翻译
            jsonField.put("fieldCode", childField.getString("fieldCode"));
            if (configMap.containsKey(childFieldName)) {
                ModelFieldConfDO fieldConf = configMap.get(childFieldName);
                jsonField.put("fieldName", fieldConf.getFieldName());
                jsonField.put("remark", fieldConf.getRemark());
            } else {
                jsonField.put("fieldName", childFieldName);
//                jsonField.put("remark", "未翻译");
            }

            // 如果存在子节点，可以递归处理
            if (childField.containsKey("children")) {
                // 将 JSONArray 转换为 List<JSONObject>
                List<JSONObject> children = convertToList(childField.getJSONArray("children"));
                jsonField.put("children", buildJsonFields(children, configMap));
            }

            jsonArray.add(jsonField);
        }

        return jsonArray;
    }

    private List<JSONObject> convertToList(JSONArray jsonArray) {
        List<JSONObject> list = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            list.add(jsonArray.getJSONObject(i));
        }
        return list;
    }
}
