package com.mongoso.mgs.common.util;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * 编码&单号统一生成工具
 *
 * <AUTHOR>
 */
public class CodeGenUtil {

    /**
     * 枚举类型，用于维护编码场景
     */
    public enum CodeType {
        // 报表编号: RT + 年月日 + 4位序列号
        REPORT_CODE("RT", 4, true,"报表编码"),

        ;

        //  前缀
        private final String prefix;
        //  序列号长度
        private final int sequenceLength;
        //  是否包含日期
        private final boolean includeDate;
        //  描述
        private final String description;

        CodeType(String prefix, int sequenceLength, String description) {
            this(prefix, sequenceLength, false, description);
        }

        CodeType(String prefix, int sequenceLength, boolean includeDate, String description) {
            this.prefix = prefix;
            this.sequenceLength = sequenceLength;
            this.includeDate = includeDate;
            this.description = description;
        }

        public String getPrefix() {
            return prefix;
        }

        public int getSequenceLength() {
            return sequenceLength;
        }

        public boolean isIncludeDate() {
            return includeDate;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 生成编码
     *
     * @param type           编码类型
     * @param sequenceNumber 序列号
     * @return 生成的编码
     */
    public static String generateCode(CodeType type, Long sequenceNumber) {
        StringBuilder code = new StringBuilder();
        code.append(type.getPrefix());

        if (type.isIncludeDate()) {
            // yyyyMMdd
            String date = LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE);
            code.append(date);
        }

        String sequenceStr = String.format("%0" + type.getSequenceLength() + "d", sequenceNumber);
        code.append(sequenceStr);

        return code.toString();
    }

    public static String generateCode(CodeType type) {
        StringBuilder code = new StringBuilder();
        code.append(type.getPrefix());

        if (type.isIncludeDate()) {
            // yyyyMMdd
            String date = LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE);
            code.append(date);
        }

        String sequenceStr = String.format("%0" + type.getSequenceLength() + "d", 18L);
        code.append(sequenceStr);

        return code.toString();
    }

    public static void main(String[] args) {
        System.out.println(generateCode(CodeType.REPORT_CODE));
    }

}
