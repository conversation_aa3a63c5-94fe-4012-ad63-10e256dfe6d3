package com.mongoso.mgs.common.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/6/19
 * @description
 */
public class OpenApiConverter {
    private final ObjectMapper objectMapper = new ObjectMapper();

    public String convertOpenApiToApiJson(Map<String, Object> openApiJson) throws JsonProcessingException {
        if (openApiJson == null || !openApiJson.containsKey("paths")) return null;

        ArrayNode result = objectMapper.createArrayNode();
        ObjectNode apiStructure = objectMapper.createObjectNode();

        apiStructure.put("mindType", "api");
        apiStructure.put("itemName", "API");
        ArrayNode children = apiStructure.withArray("children");

        // Creating the "接口地址" entry
        ObjectNode addressEntry = createAddressEntry();

        // If there are any paths, add the first one as a child.
        Map<String, Object> paths = (Map<String, Object>) openApiJson.get("paths");
        if (!paths.isEmpty()) {
            Map.Entry<String, Object> firstPathEntry = paths.entrySet().iterator().next();
            String firstPath = firstPathEntry.getKey();
            ObjectNode urlEntry = createUrlEntry(firstPath, firstPathEntry.getValue());

            addressEntry.withArray("children").add(urlEntry);
        }

        children.add(addressEntry);
        children.add(createRequestBodyEntry());
        children.add(createResponseEntry());

        result.add(apiStructure);

        // Convert ArrayNode to JSON string
        return objectMapper.writeValueAsString(result);
    }

    private ObjectNode createAddressEntry() {
        ObjectNode addressEntry = objectMapper.createObjectNode();
        addressEntry.put("englishName", "");
        addressEntry.put("touched", false);
        addressEntry.put("level", 2);
        addressEntry.put("dataType", "Object");
        addressEntry.put("index", 0);
        addressEntry.put("editing", false);
        addressEntry.put("itemName", "接口地址");
        addressEntry.put("expanded", true);
        addressEntry.put("readonly", true);
        return addressEntry;
    }

    private ObjectNode createUrlEntry(String path, Object methods) {
        ObjectNode urlEntry = objectMapper.createObjectNode();
        urlEntry.put("englishName", "");
        urlEntry.put("touched", false);
        urlEntry.put("color", "dark");
        urlEntry.put("level", 3);
        urlEntry.put("dataType", "Object");
        urlEntry.put("index", 0);
        urlEntry.put("editing", false);
        urlEntry.put("itemName", path);
        urlEntry.put("expanded", false);
        urlEntry.put("readonly", false);
        urlEntry.put("sn", "0.0.0");

        // Process methods for the URL
        for (Map.Entry<String, Object> methodEntry : ((Map<String, Object>) methods).entrySet()) {
            String method = methodEntry.getKey();
            urlEntry.put("remark", method); // Add request method as remark
            break; // Only take the first method
        }

        return urlEntry;
    }

    private ObjectNode createRequestBodyEntry() {
        ObjectNode requestBodyEntry = objectMapper.createObjectNode();
        requestBodyEntry.put("englishName", "params");
        requestBodyEntry.put("touched", false);
        requestBodyEntry.put("level", 2);
        requestBodyEntry.put("dataType", "Object");
        requestBodyEntry.put("index", 0);
        requestBodyEntry.put("editing", false);
        requestBodyEntry.put("itemName", "请求参数");
        requestBodyEntry.put("expanded", true);
        requestBodyEntry.put("readonly", true);
        return requestBodyEntry;
    }

    private ObjectNode createResponseEntry() {
        ObjectNode responseEntry = objectMapper.createObjectNode();
        responseEntry.put("englishName", "response");
        responseEntry.put("touched", false);
        responseEntry.put("level", 2);
        responseEntry.put("dataType", "Object");
        responseEntry.put("index", 1);
        responseEntry.put("editing", false);
        responseEntry.put("itemName", "响应参数");
        responseEntry.put("expanded", true);
        responseEntry.put("readonly", true);
        return responseEntry;
    }

    // Example usage
    public static void main(String[] args) {
        OpenApiConverter converter = new OpenApiConverter();
        // Example OpenAPI JSON structure
        Map<String, Object> openApiJson = Map.of(
                "paths", Map.of(
                        "/api/v1/resource", Map.of(
                                "get", Map.of(
                                        "description", "Fetch resource",
                                        "parameters", new Object[]{}
                                ),
                                "post", Map.of(
                                        "description", "Create resource",
                                        "requestBody", Map.of(
                                                "content", Map.of(
                                                        "application/json", Map.of(
                                                                "schema", Map.of(
                                                                        "type", "object",
                                                                        "properties", Map.of(
                                                                                "name", Map.of("type", "string"),
                                                                                "age", Map.of("type", "integer")
                                                                        )
                                                                )
                                                        )
                                                )
                                        )
                                )
                        )
                )
        );

        try {
            String apiJson = converter.convertOpenApiToApiJson(openApiJson);
            System.out.println(apiJson);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
    }
}
