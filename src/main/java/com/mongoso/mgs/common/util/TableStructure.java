package com.mongoso.mgs.common.util;

import com.mongoso.mgs.module.model.controller.admin.modeltableindex.vo.ModelTableIndexRespVO;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class TableStructure implements Serializable {
    /** 模型表id */
    private Long tableId;

    /** 模型表中文名 */
    private String tableName;

    /** 模型表实名 */
    private String tableCode;

    private List<ColumnInfo> columns = new ArrayList<>();
    List<ModelTableIndexRespVO> indexs = new ArrayList<>();

    public void addColumn(ColumnInfo column) {
        columns.add(column);
    }
    public void addIndex(ModelTableIndexRespVO index) {
        indexs.add(index);
    }

}

