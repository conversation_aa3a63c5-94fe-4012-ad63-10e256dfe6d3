package com.mongoso.mgs.common.util;

import cn.hutool.db.Db;
import cn.hutool.db.Entity;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.ObjUtilX;
import com.mongoso.mgs.framework.common.util.StrUtilX;
import com.mongoso.mgs.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mongoso.mgs.module.codegen.service.db.DataSourceConfigService;
import com.mongoso.mgs.module.codegen.service.db.DataSourceManager;
import com.mongoso.mgs.module.enums.DBOPTypeEnum;
import com.mongoso.mgs.module.enums.DBTypeEnum;
import com.mongoso.mgs.module.enums.ForbidKeyWordEnum;
import com.mongoso.mgs.module.enums.SysPreEnum;
import com.mongoso.mgs.module.model.controller.admin.modeltableindex.vo.ModelTableIndexRespVO;
import com.mongoso.mgs.module.model.dal.db.modeltable.ModelTableDO;
import com.mongoso.mgs.module.model.dal.db.sqllog.SqlLogDO;
import com.mongoso.mgs.module.model.dal.mysql.sqllog.SqlLogMapper;
import com.mongoso.mgs.module.table.dal.db.tablerelationconf.TableRelationConfDO;
import com.mongoso.mgs.module.table.dal.mysql.tablerelationconf.TableRelationConfMapper;
import jakarta.annotation.Resource;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.*;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * 数据库操作
 * daijinbiao
 * 2024-9-14 16:59:09
 */
@Log4j2
@Component
public class DatabaseUtil {

    @Autowired
    private DataSource dataSource;
    @Autowired
    private DataSourceManager dataSourceManager;

    //@Autowired
    //private DatabaseUtil databaseUtil;

    @Resource
    private SqlLogMapper sqlLogMapper;
    @Resource
    private TableRelationConfMapper relationConfMapper;
    @Resource
    private DataSourceConfigService dataSourceConfigService;

    @Value("${model.schema}")
    private String schemaName;

    // 防止篡改系统表
    public static String[] forbidKeyWord= {"DELETE", "DROP ", "TRUNCATE", "ALTER"};
//    static String[] sysTable= {"sys_model_table", "sys_model_table_index", "sys_model_field"};


    /**
     * 动态获取连接
     * @param configId
     * @return
     */
    public Connection getCon(Long configId) {
        try {
            if(ObjUtilX.isEmpty(configId) || configId == 0L){
                try {
                    return dataSource.getConnection();
                }catch (Exception e) {
                    e.printStackTrace();
                    throw new BizException("5001","获取默认数据库连接失败");
                }
            }
            return dataSourceConfigService.getConnectionFromConfig(configId);
        }catch (BizException e) {
            throw new BizException("5001", e.getMessage());
        }catch (Exception e) {
            throw new BizException("5001", "连接数据库失败:" + e.getMessage());
        }

    }

    @Deprecated
    public String getDbType() {
        Connection con = null;
        try {
            con = dataSource.getConnection();
            return con.getMetaData().getDatabaseProductName();
        }catch (Exception e) {
            e.printStackTrace();
            throw new BizException("5001","获取数据库类型失败");
        }finally {
            try {
                con.close();
            } catch (SQLException e) {
                throw new RuntimeException(e);
            }
        }
    }

    /**
     * 获取数据库类型，这里有大小写
     * @param configId
     * @return
     */
    public String getDbTypeDynamic(Long configId) {
        Connection con = null;
        try {
            con = this.getCon(configId);
            System.out.println("连接信息: " + con);
            return con.getMetaData().getDatabaseProductName();
        }catch (BizException e) {
            throw new BizException("5001", "数据库连接失败："+e.getMessage());
        }catch (Exception e) {
            e.printStackTrace();
            throw new BizException("5001","获取数据库类型失败:"+e.getMessage());
        }finally {
            try {
                if(null != con) {
                    con.close();
                }
            } catch (Exception e) {
                throw new BizException("5001", "连接关闭失败："+e.getMessage());
            }
        }
    }

    public static TableStructure getTableStructure(Connection connection, ModelTableDO table) throws SQLException {
        String tableName = table.getTableCode();
        DatabaseMetaData metaData = connection.getMetaData();

        ResultSet columns = metaData.getColumns(connection.getCatalog(), connection.getSchema(), tableName, null);
        // 获取表的信息，这里 third 参数是表名
        ResultSet tables = metaData.getTables(connection.getCatalog(), connection.getSchema(), tableName, null);
        String remarks = "";
        if (tables.next()) {
            // 获取表的类型
            String tableType = tables.getString("TABLE_TYPE");

            // 获取表的描述（如果存在）
            remarks = tables.getString("REMARKS"); // 表的描述
            table.setTableName(remarks);
            table.setRemark(remarks);
        } else {
            throw new BizException("5001", tableName+"表未找到");
        }

        TableStructure tableStructure = new TableStructure();
        tableStructure.setTableId(table.getTableId());
        tableStructure.setTableCode(table.getTableCode());
        tableStructure.setTableName(remarks);
        while (columns.next()) {
            String columnName = columns.getString("COLUMN_NAME");
//            if(columnName.equals("id")){
//                continue;
//            }
            ColumnInfo column = new ColumnInfo();
            column.setName(columnName);
            column.setType(columns.getString("TYPE_NAME")); // 数据类型名称
            column.setSize(columns.getInt("COLUMN_SIZE")); // 列大小
            column.setNullable(columns.getInt("NULLABLE") == DatabaseMetaData.columnNullable); // 是否可空

            column.setFieldPrecision(columns.getInt("DECIMAL_DIGITS"));

            // 默认值
            column.setDefaultValue(columns.getString("COLUMN_DEF"));

            // 字段描述
            column.setRemark(columns.getString("REMARKS")); // 获取字段描述

            column.setPrimaryKey(false);
            // 判断是否为主键
            ResultSet pkResultSet = metaData.getPrimaryKeys(null, null, tableName);
            while (pkResultSet.next()) {
                String pkColumnName = pkResultSet.getString("COLUMN_NAME");
                if (pkColumnName.equals(column.getName())) {
                    column.setPrimaryKey(true);
                    break;
                }
            }
            tableStructure.addColumn(column);
        }
        //获取索引信息 这个搞不了类型
//        ResultSet indexResultSet = metaData.getIndexInfo(null, null, tableName, false, false);
//        while (indexResultSet.next()) {
//            String indexName = indexResultSet.getString("INDEX_NAME");
//            String columnName = indexResultSet.getString("COLUMN_NAME");
//            if(columnName.equals("id")){
//                continue;
//            }
//            String indexType = indexResultSet.getString("TYPE");
//            boolean nonUnique = indexResultSet.getBoolean("NON_UNIQUE");
//            // 确定索引类型
//            String indexCategory;
//            if ("FULLTEXT".equalsIgnoreCase(indexType)) {
//                indexCategory = "FULLTEXT";
//            } else if (nonUnique) {
//                indexCategory = "NORMAL"; // 普通索引
//            } else {
//                indexCategory = "UNIQUE";  // 唯一索引
//            }
//
//            short cardinality = indexResultSet.getShort("CARDINALITY");
//            String ascOrDesc = indexResultSet.getString("ASC_OR_DESC");  // 是否升序或降序（MySQL 5.7+ 支持）
//            String filtered = indexResultSet.getString("FILTER_CONDITION"); // 过滤条件（可选）
//            String PAGES = indexResultSet.getString("PAGES"); // 过滤条件（可选）
//
//            System.out.println("Index Name: " + indexName);
//            System.out.println("Column Name: " + columnName);
//            System.out.println("Non-Unique: " + nonUnique);
//            System.out.println("Cardinality: " + cardinality);
//            System.out.println("Index Type: " + indexCategory);
//            System.out.println("Asc or Desc: " + ascOrDesc);
//            System.out.println("Filter Condition: " + filtered);
//            System.out.println("PAGES: " + PAGES);
//            System.out.println("---------------------------");
//
//            ModelTableIndexRespVO index = new ModelTableIndexRespVO();
//        }
        Statement stmt = connection.createStatement();
        stmt.execute("SHOW INDEX FROM " + tableName);
        ResultSet indexResultSet = stmt.getResultSet();
        while (indexResultSet.next()) {
            String indexName = indexResultSet.getString("Key_name");
            String columnName = indexResultSet.getString("Column_name");
            if(columnName.equals("id")){
                continue;
            }
            String indexType = indexResultSet.getString("Index_type");
            boolean nonUnique = indexResultSet.getBoolean("Non_unique");
            String ascOrDesc = indexResultSet.getString("Collation");  // 是否升序或降序（MySQL 5.7+ 支持）
            // 确定索引类型
            String indexCategory;
            if ("FULLTEXT".equalsIgnoreCase(indexType)) {
                indexCategory = "FULLTEXT";
            }else {
                if (nonUnique) {
                    indexCategory = "NORMAL"; // 普通索引
                } else {
                    indexCategory = "UNIQUE";  // 唯一索引
                }
                if ("A".equalsIgnoreCase(ascOrDesc)) {
                    ascOrDesc = "ASC";
                } else if ("D".equalsIgnoreCase(ascOrDesc)) {
                    ascOrDesc = "DESC";
                } else {
                    ascOrDesc = "";
                }
            }

            short cardinality = indexResultSet.getShort("Cardinality");
            Integer seq = indexResultSet.getInt("Seq_in_index");
            String comment = indexResultSet.getString("Index_comment");

            ModelTableIndexRespVO index = new ModelTableIndexRespVO();
            index.setFields(new ArrayList<>());
            index.setIdxId(0L);//新增时填充
            index.setIdxName(indexName);
            index.setTableId(table.getTableId());
            index.setTableCode(tableName);
            index.setNonUnique(nonUnique?1:0);
            index.setIdxSeq(seq);

            index.setFieldId(0L);//新增时填充
            index.setFieldCode(columnName);
            index.setSortType(ascOrDesc);
            index.setIdxType(indexCategory);
            index.setIdxWay("FULLTEXT".equalsIgnoreCase(indexType)?"":"BTREE");
            index.setRemark(comment);
            tableStructure.addIndex(index);
        }
        //关闭资源
        stmt.close();
        indexResultSet.close();
        return tableStructure;
    }

    public static TableStructure getPGTableStructure(Connection connection, ModelTableDO table) throws SQLException {
        String tableName = table.getTableCode();
        DatabaseMetaData metaData = connection.getMetaData();

        // 获取列信息
        ResultSet columns = metaData.getColumns(null, null, tableName, null);
        TableStructure tableStructure = new TableStructure();
        tableStructure.setTableId(table.getTableId());
        tableStructure.setTableCode(table.getTableCode());
        tableStructure.setTableName(table.getTableName());

        while (columns.next()) {
            String columnName = columns.getString("COLUMN_NAME");
            ColumnInfo column = new ColumnInfo();
            column.setName(columnName);
            column.setType(columns.getString("TYPE_NAME")); // 数据类型名称
            column.setSize(columns.getInt("COLUMN_SIZE")); // 列大小
            column.setNullable(columns.getInt("NULLABLE") == DatabaseMetaData.columnNullable); // 是否可空
            column.setFieldPrecision(columns.getInt("DECIMAL_DIGITS"));
            column.setDefaultValue(columns.getString("COLUMN_DEF")); // 默认值
            column.setRemark(columns.getString("REMARKS")); // 字段描述
            column.setPrimaryKey(false);

            // 判断是否为主键
            try (ResultSet pkResultSet = metaData.getPrimaryKeys(null, null, tableName)) {
                while (pkResultSet.next()) {
                    String pkColumnName = pkResultSet.getString("COLUMN_NAME");
                    if (pkColumnName.equals(column.getName())) {
                        column.setPrimaryKey(true);
                        break;
                    }
                }
            }

            tableStructure.addColumn(column);
        }

        // 获取索引信息
        String indexQuery = "SELECT i.relname as index_name, a.attname as column_name, " +
                "i.indisunique as is_unique, i.indisprimary as is_primary, " +
                "i.indkey as indkey, am.amname as index_type, " +
                "pg_catalog.pg_get_indexdef(i.oid) as index_definition " +
                "FROM pg_catalog.pg_index i " +
                "JOIN pg_catalog.pg_class c ON c.oid = i.indrelid " +
                "JOIN pg_catalog.pg_attribute a ON a.attnum = ANY(i.indkey) AND a.attrelid = c.oid " +
                "JOIN pg_catalog.pg_am am ON am.oid = i.indam " +
                "WHERE c.relname = ?";

        try (PreparedStatement pstmt = connection.prepareStatement(indexQuery)) {
            pstmt.setString(1, tableName);
            ResultSet indexResultSet = pstmt.executeQuery();

            while (indexResultSet.next()) {
                String indexName = indexResultSet.getString("index_name");
                String columnName = indexResultSet.getString("column_name");
                boolean nonUnique = !indexResultSet.getBoolean("is_unique"); // true if it's not unique
                String indexType = indexResultSet.getString("index_type");

                ModelTableIndexRespVO index = new ModelTableIndexRespVO();
                index.setFields(new ArrayList<>());
                index.setIdxId(0L); // 新增时填充
                index.setIdxName(indexName);
                index.setTableId(table.getTableId());
                index.setTableCode(tableName);
                index.setNonUnique(nonUnique ? 1 : 0);
                index.setFieldId(0L); // 新增时填充
                index.setFieldCode(columnName);
                index.setIdxType(nonUnique ? "NORMAL" : "UNIQUE"); // 索引类型
                index.setIdxWay(indexType);
                index.setRemark(indexResultSet.getString("index_definition")); // 获取索引定义作为备注

                tableStructure.addIndex(index);
            }
        }

        return tableStructure;
    }

    public void exeSQL(ModelTableDO tableDO, String sql, DBOPTypeEnum opType){
        SqlLogDO sqlLog = new SqlLogDO(tableDO.getProjectId(), tableDO.getTableId(), tableDO.getTableCode(), sql, opType.getType(), 1, opType.getDesc());
        try (Connection connection = this.getCon(tableDO.getDataSourceConfigId());
             Statement stmt = connection.createStatement()) {

            //DatabaseMetaData metaData = connection.getMetaData();
            // 获取数据库信息
            //String databaseProductName = metaData.getDatabaseProductName();
            //String databaseProductVersion = metaData.getDatabaseProductVersion();
            //
            //System.out.println("数据库类型: " + databaseProductName);
            //System.out.println("数据库版本: " + databaseProductVersion);
            //
            //if (databaseProductName.toLowerCase().contains(DBTypeEnum.MYSQL.getValue())) {
            //    System.out.println("当前连接的是 MySQL 数据库");
            //} else if (databaseProductName.toLowerCase().contains(DBTypeEnum.PG.getValue())) {
            //    System.out.println("当前连接的是 PostgreSQL 数据库");
            //} else {
            //    System.out.println("未知数据库类型");
            //}

            stmt.execute(sql);

            log.info("执行SQL完毕：" + sql);
        } catch (SQLException e) {
            sqlLog.setSucStatus(0);
            sqlLog.setErrMgs(e.getMessage());
            // 捕获 SQLException 并提供特定错误信息
            log.error("执行SQL失败：" + sql);
            throw new BizException("5002", "数据库操作失败: " + e.getMessage());
        } catch (Exception e) {
            sqlLog.setSucStatus(0);
            sqlLog.setErrMgs(e.getMessage());
            log.error("执行SQL失败：" + sql);
            e.printStackTrace();
            throw new BizException("5001", "执行SQL失败");
        } finally {
            try {
                sqlLogMapper.insert(sqlLog);
            } catch (Exception e) {
                log.error("插入日志失败:" + e.getMessage());
            }
        }
    }

    /**
     * 验证SQL
     * @param tempSQL
     * @param dataSourceConfigId
     */
    public void validateSQL(String tempSQL, Long dataSourceConfigId) {
        // 防止修改系统表
        if (dataSourceConfigId == 0L && containsIllegalSQL(tempSQL)) {
            throw new BizException("5001", "存在非法SQL！ -> " + tempSQL);
        }
    }

    /**
     * 检查是否包含非法操作，防止修改系统表，防止严重的DDL操作
     * @param tempSQL
     * @return
     */
    private boolean containsIllegalSQL(String tempSQL) {
        // 检查是否以禁止的DDL关键字开头
        boolean startsWithForbidKeyWord = Arrays.stream(ForbidKeyWordEnum.values())
                .anyMatch(keywordEnum -> keywordEnum.startWith(tempSQL));

        // 检查是否包含系统前缀的表操作
        boolean containsSysPre = Arrays.stream(SysPreEnum.values())
                .anyMatch(preEnum -> tempSQL.trim().startsWith(preEnum.getPrefix()));

        return startsWithForbidKeyWord || containsSysPre;
    }

    /**
     * 执行SQL查询，返回最后一条SQL的结果
     * @param dataSourceConfigId
     * @param sql
     * @return
     * @throws Exception
     */
    public List<Map<String, Object>> exeQueryLastSQL(Long dataSourceConfigId, String sql) throws Exception {
        List<Map<String, Object>> results = new ArrayList<>();
        //Connection connection = databaseUtil.getCon(dataSourceConfigId);
        Connection connection = getCon(dataSourceConfigId);
        System.out.println("连接信息：" + connection);
        String lastSQL = "";
        try {
            //注意：如果是true则表示自动提交第一个语句，第二个语句直接无视
            connection.setAutoCommit(false);
            // 之前的SQL执行
            String[] sqls = sql.replaceAll("\n", " ").split(";");
            Statement preStmt = connection.createStatement();
            for (int i = 0; i < sqls.length - 1; i++) {
                String tempSQL = sqls[i].trim().toString();
                if(StrUtilX.isEmpty(tempSQL)){
                    continue;
                }
                //防止非法操作
                validateSQL(tempSQL, dataSourceConfigId);

                // 校验 from 后面的表是否是 sys_ 开头的
                int fromIndex = tempSQL.toUpperCase().indexOf("FROM ");
                if (fromIndex != -1) {
                    // 提取 FROM 后面的部分
                    String afterFrom = tempSQL.substring(fromIndex + 5).trim(); // 5 是 "FROM ".length()
                    String[] parts = afterFrom.split("\\s+"); // 分割空格

                    // 获取表名（可能有多个表用逗号分隔）
                    for (String part : parts) {
                        // 检查是否为表名
                        if (!part.equalsIgnoreCase("JOIN") && !part.contains(",")) { // 忽略 JOIN 和逗号分隔
                            if (dataSourceConfigId == 0L && part.startsWith(SysPreEnum.SYS.getPrefix())) {
                                throw new BizException("5001", "存在非法SQL，不能操作系统表 -> " + tempSQL);
                            }
                            break; // 找到第一个有效的表名就可以退出循环
                        }
                    }
                }
                preStmt.addBatch(tempSQL);
            }
            // 批量执行除了最后一条的SQL。
            preStmt.executeBatch();
            preStmt.close();

            // 用hutool批量
//            int[] ints = Db.use(dataSource).executeBatch(sqls);

            // 执行最后一条查询
            lastSQL = sqls[sqls.length - 1];
            Statement stmt = connection.createStatement();
            String UPCASESQL = lastSQL.trim().toUpperCase();
            // 防止非法操作
            validateSQL(lastSQL, dataSourceConfigId);
            if(UPCASESQL.startsWith("SELECT")) {
                if(!UPCASESQL.contains("LIMIT")){
                    lastSQL = lastSQL + " LIMIT 1000";
                }
                log.info("最终SQL："+lastSQL);
                ResultSet resultSet = stmt.executeQuery(lastSQL);
                // 获取列名
                ResultSetMetaData metaData = resultSet.getMetaData();
                int columnCount = metaData.getColumnCount();
//                List<String> colName = new ArrayList<>();
                while (resultSet.next()) {
                    Map<String, Object> row = new LinkedHashMap<>();
                    for (int i = 1; i <= columnCount; i++) {
                        Object value = resultSet.getObject(i);
                        String columnName = metaData.getColumnName(i);
//                        columnName = rstMap.getOrDefault(columnName, columnName);
                        if(columnName.equals("?column?")){
//                            columnName = "column" + i;
                            columnName = value.toString();
                        }
                        row.put(columnName, value);
//                        colName.add(columnName);
                    }
                    results.add(row);
                }
                stmt.close();
                resultSet.close();
                // 打印结果
//                if (results.size() > 0) {
//                    results.get(0).forEach((key, value) -> {
//                        System.out.print(key + "    ");
//                    });
//                    System.out.println();
//                    for (Map<String, Object> row : results) {
////                System.out.println(row);
//
//                        row.forEach((key, value) -> {
//                            System.out.print(value + "    ");
//                        });
//                        System.out.println();
//                    }
//                }
            }else{//如果最后一条语句不是select就直接执行，不需要返回map
                stmt.execute(lastSQL);
                stmt.close();
            }
            //提交sql语句执行
            connection.commit();
            connection.close();
            log.info("执行SQL完毕：" + sql);
            return results;
        } catch (BizException e) {
            connection.rollback();
            e.printStackTrace();
            log.error("执行SQL失败：" + sql);
            log.error("最终查询失败：" + lastSQL);
            throw e;
        }catch (Exception e) {
            e.printStackTrace();
            connection.rollback();
            log.error("执行SQL失败：" + sql);
            log.error("最终查询失败：" + lastSQL);
            throw new BizException("5001", "执行SQL失败");
        } finally {
            try {
                connection.close();
            }catch (Exception e) {
                e.printStackTrace();
                throw new BizException("5001", "关闭连接失败");
            }
        }
    }

    /**
     * 判断字符串是否包含一些关键字
     * @param str
     * @param keywords
     * @return
     */
    public boolean containsAny(String str, String[] keywords) {
        for (String keyword : keywords) {
            if (str.trim().toUpperCase().contains(keyword)) {
                return true;
            }
        }
        return false;
    }

    public boolean containsAny(String str, String keywords) {
        if (str.trim().toUpperCase().contains(keywords)) {
            return true;
        }
        return false;
    }

    /**
     * 判断字符串是否是一些关键字开头
     * @param str
     * @param keywords
     * @return
     */
    public boolean startWith(String str, String[] keywords) {
        for (String keyword : keywords) {
            if (str.trim().toUpperCase().startsWith(keyword)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取数据库名称
     * @param confId
     * @param havaQuot
     * @return
     */
    public String getDBNameAllDynamic(Long confId, Boolean havaQuot) {
        try {
            String dbType = this.getDbTypeDynamic(confId);
            if (dbType.toLowerCase().contains(DBTypeEnum.MYSQL.getValue())) {
                System.out.println("当前连接的是 MySQL 数据库");
                return getMysqlName(confId, havaQuot);
            } else if (dbType.toLowerCase().contains(DBTypeEnum.PG.getValue())) {
                System.out.println("当前连接的是 PostgreSQL 数据库");
                return getPGDBName(confId, havaQuot);
            } else {
                throw new BizException("5001","暂不支持此数据库：" + dbType);
            }
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            throw e;
        }

    }

    public String getDBType(Long confId) {
        try {
            String dbType = this.getDbTypeDynamic(confId);
            if (dbType.toLowerCase().contains(DBTypeEnum.MYSQL.getValue())) {
                System.out.println("当前连接的是 MySQL 数据库");
                return DBTypeEnum.MYSQL.getValue();
            } else if (dbType.toLowerCase().contains(DBTypeEnum.PG.getValue())) {
                System.out.println("当前连接的是 PostgreSQL 数据库");
                return DBTypeEnum.OTHER.getValue();
            } else {
                return "暂不支持";
            }
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            throw e;
        }

    }

    //@Deprecated
    public String getMysqlName(Long confId, Boolean havaQuot) {
        try (Connection connection = this.getCon(confId)) {
            // 获取数据库元数据
            String catalog = connection.getCatalog(); // 使用 getCatalog() 获取当前数据库名
            if(havaQuot) {
                return "`" + catalog + "`";
            }else{
                return catalog;
            }
        } catch (Exception e) {
            log.error("获取数据库名称失败：");
            e.printStackTrace();
            throw new BizException("5001", "获取数据库名称失败");
        }
    }


    // 改为从配置文件读取配置schema
//    @Deprecated
    public String getPGDBName(Long confId, Boolean havaQuot) {
        try (Connection connection = this.getCon(confId)) {
            // 获取当前连接的 schema 名称
//            schemaName = ObjUtilX.isEmpty(schemaName) ? connection.getSchema() : schemaName;
            schemaName = connection.getSchema();

            if (havaQuot) {
                return "\"" + schemaName + "\""; // 使用双引号包裹 schema 名称
            } else {
                return schemaName; // 返回原始 schema 名称
            }
        } catch (Exception e) {
            log.error("获取 schema 名称失败：");
            e.printStackTrace();
            throw new BizException("5001", "获取 schema 名称失败");
        }
    }

    public String getTableDDL(String tableCode) {
        try (Connection connection = dataSource.getConnection()) {
            // 获取数据库元数据
            DatabaseMetaData databaseMetaData = connection.getMetaData();
            // 获取当前连接的 schema 名称
            String schemaName = connection.getSchema();
            // 获取表的建表DDL
            ResultSet resultSet = databaseMetaData.getTablePrivileges(null, schemaName, tableCode);
            if (resultSet.next()) {
                String ddl = resultSet.getString("SQL");
                System.out.println(ddl);
                return ddl;
            }
        } catch (Exception e) {
            log.error("获取建表DDL失败：");
            e.printStackTrace();
            throw new BizException("5001", "获取建表DDL失败");
        }
        return "";
    }

    //获取所有 @开头的变量
    public static Set<String> extractVariables(String sql) {
        Set<String> variableSet = new HashSet<>();
        // 正则表达式匹配 @开头的变量名，或者(@开头的
        Pattern pattern = Pattern.compile("@(\\w+)|\\(@(\\w+)(?=[\\s,.;)])");
        Matcher matcher = pattern.matcher(sql);

        while (matcher.find()) {
            variableSet.add(matcher.group().replace("(", ""));
        }
        return variableSet;
    }

    public static void main(String[] args) {
        //String sql = "SELECT T1.DocEntry,T1.ProCode,T3.ProName,T3.Item,T3.Model,T3.Unit,T5.Name UnitName, T1.Qty,T1.FinQty,T1.ScrQty,\n" +
        //        "\n" +
        //        "T2.ProCode CProCode,T2.Qty CQty,T4.ProName CProName,T4.Item CItem,T4.Model CModel,T4.Unit CUnit,T6.Name CUnitName FROM U_WorkOrder T1\n" +
        //        "\n" +
        //        "LEFT JOIN U_WorkOrderAss T2 ON T1.DocEntry = T2.DocEntry\n" +
        //        "\n" +
        //        "LEFT JOIN U_ProductInfo T3 ON T1.ProCode = T3.DocEntry\n" +
        //        "\n" +
        //        "LEFT JOIN U_ProductInfo T4 ON T2.ProCode = T4.DocEntry\n" +
        //        "\n" +
        //        "LEFT JOIN S_Account T5 ON T3.Unit = T5.AccountID AND T5.TypeID = 'KM0002'\n" +
        //        "\n" +
        //        "LEFT JOIN S_Account T6 ON T4.Unit = T6.AccountID AND T6.TypeID = 'KM0002'\n" +
        //        "\n" +
        //        "WHERE (T1.DocEntry = @DocEntry OR @DocEntry IS NULL)\n" +
        //        "\n" +
        //        "AND (T1.DocDate >= @StartDate OR @StartDate IS NULL)\n" +
        //        "\n" +
        //        "AND (@Table.DocDate <= @EndDate OR @EndDate IS NULL)";
        //Set<String> strings = extractVariables(sql);
        //System.out.println(strings);

        String sql2 = """ 
                select * from lowcode.sys_model_table;
                select * from lowcode.sys_model_field;
                """;
    }


    /***************************** 搞hutool的工具类试试 ********************************/

    //public List<Map<String, List<Entity>>> executeBatchQueries(String sql, Long configId) {
    //    List<Map<String, List<Entity>>> results = new ArrayList<>();
    //    String[] sqls = sql.split(";"); // 按照分号分割 SQL 字符串
    //    DataSource dataSource = dataSourceManager.getDataSource(configId);
    //    try {
    //        String prefixSchema = dataSource.getConnection().getSchema() + ".";
    //        Map<String, Integer> tableMap = new HashMap<>();
    //        for (String tempSQL : sqls) {
    //            String trimmedSQL = tempSQL.trim();
    //            if (!trimmedSQL.isEmpty()) {
    //                // 提取表名并执行查询
    //                String tableName = extractTableName(trimmedSQL);
    //                tableName = tableName.replace(prefixSchema, "");
    //                Integer count = 0;
    //                if (tableMap.containsKey(tableName)) {
    //                    count = tableMap.get(tableName) + 1;
    //                }
    //                tableMap.put(tableName, count);
    //                if(count > 0){
    //                    tableName = tableName + "_" + count;
    //                }
    //                List<Entity> result = Db.use(dataSource).query(trimmedSQL); // 执行查询
    //
    //                // 将结果放入 Map 中，key 为表名
    //                Map<String, List<Entity>> resultMap = new HashMap<>();
    //                resultMap.put(tableName, result);
    //
    //                // 添加到最终结果列表
    //                results.add(resultMap);
    //            }
    //        }
    //    } catch (SQLException e) {
    //        e.printStackTrace(); // 处理异常
    //    }
    //
    //    return results; // 返回所有结果
    //}
    //
    //// 提取表名的辅助方法
    //private String extractTableName(String sql) {
    //    // 具体的表名提取逻辑根据 SQL 的结构来定
    //    // 假设 SQL 语句为 "SELECT * FROM table_name WHERE ..."
    //    // 使用正则表达式或者简单的字符串操作来提取表名
    //    String[] parts = sql.split("\\s+");
    //    for (int i = 0; i < parts.length; i++) {
    //        if ("FROM".equalsIgnoreCase(parts[i]) && i + 1 < parts.length) {
    //            return parts[i + 1]; // 返回紧跟在 FROM 后面的部分作为表名
    //        }
    //    }
    //    return null; // 没有找到表名
    //}

    // 方法：执行批量查询并返回层级关系
    public List<Map<String, Object>> executeBatchQueries(Long queryId, String sql, Long configId) {
        List<Map<String, Object>> results = new ArrayList<>();
        String[] sqls = sql.split(";"); // 按照分号分割 SQL 字符串
        DataSource dataSource = dataSourceManager.getDataSource(configId);

        try {
            String prefixSchema = dataSource.getConnection().getSchema() + ".";
            Map<String, List<Entity>> allResults = new HashMap<>();

            for (String tempSQL : sqls) {
                String trimmedSQL = tempSQL.trim();
                if (!trimmedSQL.isEmpty()) {
                    // 提取表名并执行查询
                    String tableName = extractTableName(trimmedSQL);
                    tableName = tableName.replace(prefixSchema, "");

                    List<Entity> result = Db.use(dataSource).query(trimmedSQL); // 执行查询

                    // 将结果放入 Map 中，key 为表名
                    allResults.put(tableName, result);
                }
            }

            // 构建层级关系
            List<TableRelationConfDO> rootTables = relationConfMapper.getRootTable(queryId); // 获取所有根表
            if (rootTables.isEmpty()) {
                // 如果没有找到根表，直接返回 allResults 的数据
                return convertAllResultsToList(allResults);
            }

            // 循环处理每个根表
            for (TableRelationConfDO rootTable : rootTables) {
                String rootTableName = rootTable.getParentTable(); // 获取根表名称
                //List<Map<String, Object>> hierarchy = buildHierarchy(rootTableName, allResults);
                List<Object> hierarchy = testBuildHierarchy(rootTableName, allResults);
                Map<String, Object> rootMap = new HashMap<>();
                //rootMap.put(rootTableName, hierarchy);
                rootMap.put("itemName", rootTableName);
                rootMap.put("childrens", hierarchy);
                results.add(rootMap); // 将构建的层次结构添加到结果中
            }

        } catch (SQLException e) {
            e.printStackTrace(); // 处理异常
        }

        return results; // 返回所有结果
    }

    // 辅助方法：将 allResults 转换为 List<Map<String, Object>>
    private List<Map<String, Object>> convertAllResultsToList(Map<String, List<Entity>> allResults) {
        List<Map<String, Object>> wrappedResults = new ArrayList<>();

        for (Map.Entry<String, List<Entity>> entry : allResults.entrySet()) {
            String tableName = entry.getKey(); // 获取表名
            List<Entity> entities = entry.getValue();
            Map<String, Object> entityMap = new HashMap<>();
            entityMap.put(tableName, entities);
            wrappedResults.add(entityMap);
        }

        return wrappedResults;
    }

    // 提取表名的辅助方法
    private String extractTableName(String sql) {
        // 具体的表名提取逻辑根据 SQL 的结构来定
        String[] parts = sql.split("\\s+");
        for (int i = 0; i < parts.length; i++) {
            if ("FROM".equalsIgnoreCase(parts[i]) && i + 1 < parts.length) {
                return parts[i + 1]; // 返回紧跟在 FROM 后面的部分作为表名
            }
        }
        return null; // 没有找到表名
    }

    private List<Object> testBuildHierarchy(String tableName, Map<String, List<Entity>> allResults) {
        List<Object> finalResults = new ArrayList<>();

        // 获取当前表的所有实体
        List<Entity> parentEntities = allResults.get(tableName);
        if (parentEntities == null) {
            return finalResults; // 如果没有实体，直接返回空列表
        }

        // 查找当前表的子集
        List<TableRelationConfDO> childRelations = relationConfMapper.selectList(
                LambdaQueryWrapperX.<TableRelationConfDO>lambdaQueryX()
                        .eq(TableRelationConfDO::getParentTable, tableName)
        );

        // 遍历每个父对象
        for (Entity parent : parentEntities) {
            Map<String, Object> parentMap = new HashMap<>();
            parent.getFieldNames().forEach(fieldName -> {
                parentMap.put(fieldName, parent.get(fieldName));
            });

            // 遍历每种关系，递归查找子集
            for (TableRelationConfDO relation : childRelations) {
                String childTable = relation.getChildTable();
                List<Entity> childEntities = allResults.get(childTable);

                if (childEntities != null) {
                    List<Object> childMaps = new ArrayList<>();

                    // 递归处理子对象的层级
                    List<Object> grandChildren = testBuildHierarchy(childTable, allResults);
                    if (!grandChildren.isEmpty()) {
                        childMaps.addAll(grandChildren); // 添加子集
                    }

                    // 将所有子对象添加到父对象的子节点中
                    if (!childMaps.isEmpty()) {
                        //parentMap.put(childTable, childMaps);
                        parentMap.put("itemName", childTable);
                        parentMap.put("childrens", childMaps);
                    }
                }
            }

            // 包裹当前父对象，键为表名
            //Map<String, Object> wrappedParentResult = new HashMap<>();
            //wrappedParentResult.put(tableName, parentMap);

            // 添加封装的父对象到最终结果
            finalResults.add(parentMap);
        }
        return finalResults;
    }

    // 递归构建层级关系
    private List<Map<String, Object>> buildHierarchy(String tableName, Map<String, List<Entity>> allResults) {
        List<Map<String, Object>> finalResults = new ArrayList<>();

        // 获取当前表的所有实体
        List<Entity> parentEntities = allResults.get(tableName);
        if (parentEntities == null) {
            return finalResults; // 如果没有实体，直接返回空列表
        }

        // 查找当前表的子集
        List<TableRelationConfDO> childRelations = relationConfMapper.selectList(
                LambdaQueryWrapperX.<TableRelationConfDO>lambdaQueryX()
                        .eq(TableRelationConfDO::getParentTable, tableName)
        );

        // 遍历每个父对象
        for (Entity parent : parentEntities) {
            Map<String, Object> parentMap = new HashMap<>();
            parent.getFieldNames().forEach(fieldName -> {
                parentMap.put(fieldName, parent.get(fieldName));
            });

            // 遍历每种关系，递归查找子集
            for (TableRelationConfDO relation : childRelations) {
                String childTable = relation.getChildTable();
                List<Entity> childEntities = allResults.get(childTable);

                if (childEntities != null) {
                    List<Map<String, Object>> childMaps = new ArrayList<>();

                    // 递归处理子对象的层级
                    List<Map<String, Object>> grandChildren = buildHierarchy(childTable, allResults);
                    if (!grandChildren.isEmpty()) {
                        childMaps.addAll(grandChildren); // 添加子集
                    }

                    // 遍历子实体，检查是否属于当前父对象
                    //for (Entity child : childEntities) {
                    //    String childParentId = child.getStr(relation.getChildCol());
                    //    String parentId = parent.getStr(relation.getParentCol());
                    //
                    //    // 只添加属于当前父对象的子对象
                    //    if (childParentId.equals(parentId)) {
                    //        Map<String, Object> childMap = new HashMap<>();
                    //        child.getFieldNames().forEach(fieldName -> {
                    //            childMap.put(fieldName, child.get(fieldName));
                    //        });
                    //
                    //        // 递归处理子对象的层级
                    //        grandChildren = buildHierarchy(childTable, allResults);
                    //        if (!grandChildren.isEmpty()) {
                    //            childMap.put(childTable, grandChildren); // 添加子集
                    //        }
                    //
                    //        childMaps.add(childMap); // 收集子对象
                    //    }
                    //}

                    // 将所有子对象添加到父对象的子节点中
                    if (!childMaps.isEmpty()) {
                        parentMap.put(childTable, childMaps);
                    }
                }
            }

            // 包裹当前父对象，键为表名
            Map<String, Object> wrappedParentResult = new HashMap<>();
            wrappedParentResult.put(tableName, parentMap);

            // 添加封装的父对象到最终结果
            finalResults.add(wrappedParentResult);
        }
        return finalResults;
    }


    /******************************************************************************/
    public void insert() throws SQLException {
        // 创建数据库连接
        Db db = Db.use(dataSource);

        // 插入数据
        Entity entity = Entity.create("users")
                .set("name", "Alice")
                .set("age", 30);
        int insertSuccess = db.insert(entity);
        System.out.println("插入成功：" + insertSuccess);

        // 查询数据
        List<Entity> users = db.query("SELECT * FROM users");
        for (Entity userEntity : users) {
            System.out.println(userEntity.getStr("name") + " - Age: " + userEntity.getInt("age"));
        }

        // 更新数据
        Entity up = Entity.create("users");
        int updatedRows = db.update(up.set("age", 31), up.set("name", "Alice"));
        System.out.println("更新了 " + updatedRows + " 行数据。");

        // 删除数据
        int deletedRows = db.del(up.set("name", "Alice"));
        System.out.println("删除了 " + deletedRows + " 行数据。");


        // 使用 execute() 执行 INSERT
        String insertSql = "INSERT INTO users (name, age) VALUES (?, ?)";
        int rowsInserted = db.execute(insertSql, "John Doe", 25);
        System.out.println("插入了 " + rowsInserted + " 行数据。");


        // 准备批量插入数据
        List<String> sqlList = new ArrayList<>();
        sqlList.add("INSERT INTO users (name, age) VALUES ('Alice', 30)");
        sqlList.add("INSERT INTO users (name, age) VALUES ('Bob', 25)");
        sqlList.add("INSERT INTO users (name, age) VALUES ('Charlie', 28)");

        // 使用 executeBatch() 执行批量插入
        int[] results = db.executeBatch(sqlList);
        System.out.println("批量插入行数：" + results.length);

        // 可选：遍历结果，查看每条 SQL 的影响行数
        for (int result : results) {
            System.out.println("插入影响的行数：" + result);
        }

        // 事务
        db.tx(tempDB -> {
            Entity user1 = Entity.create("users")
                    .set("name", "Bob")
                    .set("age", 25);
            Entity user2 = Entity.create("users")
                    .set("name", "Charlie")
                    .set("age", 28);

            // 插入多个用户
            tempDB.insert(user1);
            tempDB.insert(user2);

            // 如果发生异常，将会回滚
        });
    }
}
