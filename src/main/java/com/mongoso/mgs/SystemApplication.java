package com.mongoso.mgs;


import com.alibaba.druid.spring.boot3.autoconfigure.DruidDataSourceAutoConfigure;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.scheduling.annotation.EnableScheduling;

@Slf4j
@SpringBootApplication(exclude = DruidDataSourceAutoConfigure.class)
@EnableScheduling // 启用定时任务
public class SystemApplication {

	public static void main(String[] args) {
		ConfigurableApplicationContext run = SpringApplication.run(SystemApplication.class, args);
		String port = run.getEnvironment().getProperty("server.port");
		log.info("\n----------------------------------------------------------\n\t" +
						"项目启动成功！\n\t" +
						"代码生成器: \t{}{}{} \n\t" +

						"----------------------------------------------------------",
				"http://localhost:",
				port,
				"/admin-ui");
	}

}
