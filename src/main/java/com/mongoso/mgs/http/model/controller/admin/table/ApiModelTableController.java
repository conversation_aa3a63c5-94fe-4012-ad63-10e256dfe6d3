package com.mongoso.mgs.http.model.controller.admin.table;

import com.mongoso.mgs.framework.common.domain.PageResult;
import com.mongoso.mgs.framework.common.domain.ResultX;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.BeanUtilX;
import com.mongoso.mgs.framework.common.util.ObjUtilX;
import com.mongoso.mgs.framework.operatelog.core.annotations.OperateLog;
import com.mongoso.mgs.http.check.NeedPrivateKeyValidation;
import com.mongoso.mgs.module.model.controller.admin.modeltable.vo.ModelTablePageReqVO;
import com.mongoso.mgs.module.model.controller.admin.modeltable.vo.ModelTablePrimaryReqVO;
import com.mongoso.mgs.module.model.controller.admin.modeltable.vo.ModelTableQueryReqVO;
import com.mongoso.mgs.module.model.controller.admin.modeltable.vo.ModelTableRespVO;
import com.mongoso.mgs.module.model.dal.db.modeltable.ModelTableDO;
import com.mongoso.mgs.module.model.service.modeltable.ModelTableService;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.mongoso.mgs.framework.common.domain.ResultX.success;

/**
 * 图形建模主 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/openapi")
@Validated
@NeedPrivateKeyValidation
public class ApiModelTableController {

    @Resource
    private ModelTableService tableService;

    @OperateLog("图形建模-表格视图对外API")
    @PostMapping("/modelTableDetail")
    @PermitAll
    public ResultX<ModelTableRespVO> modelTableInfo(@Valid @RequestBody ModelTablePrimaryReqVO reqVO) {
        ModelTableRespVO oldDO = tableService.modelTableDetail(reqVO.getTableId());
        return success(oldDO);
    }
    @OperateLog("图形建模-表格视图对外API")
    @PostMapping("/modelTableDetailByCode")
    @PermitAll
    public ResultX<ModelTableRespVO> modelTableDetailByCode(@Valid @RequestBody ModelTableQueryReqVO reqVO) {
        ModelTableRespVO oldDO = tableService.modelTableDetailByCode(reqVO);
        return success(oldDO);
    }

    @OperateLog("图形建模主列表")
    @PostMapping("/modelTableList")
    @PermitAll
    public ResultX<List<ModelTableRespVO>> modelTableList(@Valid @RequestBody ModelTableQueryReqVO reqVO) {
        List<ModelTableDO> list = tableService.modelTableList(reqVO);
        return success(BeanUtilX.copyList(list, ModelTableRespVO::new));
    }

    @OperateLog("图形建模主分页")
    @PostMapping("/modelTablePage")
    @PermitAll
    public ResultX<PageResult<ModelTableRespVO>> modelTablePage(@Valid @RequestBody ModelTablePageReqVO reqVO) {
        return success(tableService.modelTablePage(reqVO));
    }

    @OperateLog("表Tree")
    @PostMapping("/modelTableTree")
    @PermitAll
    public ResultX<List<ModelTableRespVO>> modelTableTree(@Valid @RequestBody ModelTableQueryReqVO reqVO) {
        if(ObjUtilX.isEmpty(reqVO.getProjectCode())){
            throw new BizException("5001", "项目编码不能为空");
        }
        // 根据编码查项目
        ModelTableRespVO modelTableRespVO = tableService.modelTableDetailByCode(reqVO.getProjectCode(), -1);
        reqVO.setProjectId(modelTableRespVO.getTableId());
        return success(tableService.modelTableTree(reqVO));
    }
}
