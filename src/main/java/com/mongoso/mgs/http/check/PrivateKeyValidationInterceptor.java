package com.mongoso.mgs.http.check;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mongoso.mgs.framework.common.exception.BizException;
import com.mongoso.mgs.framework.common.util.ObjUtilX;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import java.io.BufferedReader;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/4/16
 * @description
 */
@ControllerAdvice
public class PrivateKeyValidationInterceptor implements HandlerInterceptor {

    @Value(value = "${openapi.privateKey}")
    private String privateKey;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (handler instanceof HandlerMethod) {
            HandlerMethod handlerMethod = (HandlerMethod) handler;
            Class<?> beanType = handlerMethod.getBeanType();

            if (beanType.isAnnotationPresent(NeedPrivateKeyValidation.class)) {
                // 从请求 body 中读取 privateKey
                String privateKey = getPrivateKeyFromRequestBody(request);

                if (ObjUtilX.isEmpty(privateKey)) {
                    throw new BizException("5001", "请输入秘钥！");
                }
                if (!privateKey.equals(this.privateKey)) {
                    throw new BizException("5001", "秘钥错误，无权访问！");
                }
            }
        }
        return true;
    }

    private String getPrivateKeyFromRequestBody(HttpServletRequest request) throws Exception {
        // 使用 ObjectMapper 来读取请求体中的 JSON 数据
        ObjectMapper objectMapper = new ObjectMapper();

        // 将请求体读入字节数组
        StringBuilder sb = new StringBuilder();
        String line;
        try (BufferedReader reader = request.getReader()) {
            while ((line = reader.readLine()) != null) {
                sb.append(line);
            }
        }

        // 解析 JSON 数据
        Map<String, Object> jsonMap = objectMapper.readValue(sb.toString(), Map.class);
        return (String) jsonMap.get("privateKey"); // 获取 privateKey
    }
}
