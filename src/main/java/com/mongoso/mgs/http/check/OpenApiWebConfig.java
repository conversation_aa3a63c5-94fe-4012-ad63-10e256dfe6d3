package com.mongoso.mgs.http.check;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 * @date 2025/4/16
 * @description
 */
@Configuration
public class OpenApiWebConfig implements WebMvcConfigurer {

    @Autowired
    private PrivateKeyValidationInterceptor privateKeyValidationInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(privateKeyValidationInterceptor)
                .addPathPatterns("/admin-api/openapi/**"); // 只拦截 /openapi 路径下的请求
    }
}
