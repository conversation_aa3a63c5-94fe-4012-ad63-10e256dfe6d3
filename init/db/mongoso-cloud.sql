/*
 Navicat Premium Data Transfer

 Source Server         : *************
 Source Server Type    : MySQL
 Source Server Version : 50739 (5.7.39)
 Source Host           : *************:3306
 Source Schema         : mongoso-vue

 Target Server Type    : MySQL
 Target Server Version : 50739 (5.7.39)
 File Encoding         : 65001

 Date: 22/07/2023 14:13:35
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for dynamic_config
-- ----------------------------
DROP TABLE IF EXISTS `dynamic_config`;
CREATE TABLE `dynamic_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `data_id` varchar(255) DEFAULT NULL COMMENT '数据id，目前没有用到',
  `content` longtext COMMENT '配置文件的内容',
  `created_dt` datetime DEFAULT NULL,
  `updated_dt` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COMMENT='动态配置表';

-- ----------------------------
-- Records of dynamic_config
-- ----------------------------
BEGIN;
INSERT INTO `dynamic_config` (`id`, `data_id`, `content`, `created_dt`, `updated_dt`) VALUES (2, 'mysql.yml', 'spring:\n  datasource:\n    driver-class-name: \"com.mysql.cj.jdbc.Driver\"\n    jdbc-url: \"jdbc:mysql://*************/mongoso-vue?characterEncoding=utf-8&serverTimezone=Asia/Shanghai&useSSL=false\"\n    username: \"root\"\n    password: 123456\n  servlet:\n    multipart:\n      max-request-size: \"15MB\"\n      max-file-size: \"10MB\"\n  profiles:\n    active: \"local\"\n  jackson:\n    date-format: \"yyyy-MM-dd HH:mm:ss\"\n    time-zone: \"GMT+8\"\n  output:\n    ansi:\n      enabled: \"ALWAYS\"\nlogging:\n  level:\n    root: \"info\"\nserver:\n  port: 10001\n', '2023-03-27 17:25:24', '2023-03-27 17:35:36');
COMMIT;

-- ----------------------------
-- Table structure for s_auth_menu
-- ----------------------------
DROP TABLE IF EXISTS `s_auth_menu`;
CREATE TABLE `s_auth_menu` (
  `menu_id` varchar(30) COLLATE utf8_bin NOT NULL COMMENT '菜单id',
  `menu_seq` bigint(20) DEFAULT NULL COMMENT '菜单自增id',
  `menu_name` varchar(50) COLLATE utf8_bin DEFAULT NULL COMMENT '菜单名称',
  `parent_item_id` varchar(30) COLLATE utf8_bin DEFAULT NULL COMMENT '父级id',
  `parent_item_ids` varchar(50) COLLATE utf8_bin DEFAULT NULL COMMENT '父级ids',
  `permissions` varchar(50) COLLATE utf8_bin DEFAULT NULL COMMENT '后端权限',
  `button_name` varchar(30) COLLATE utf8_bin DEFAULT NULL COMMENT '前端按钮',
  `path` varchar(50) COLLATE utf8_bin DEFAULT NULL COMMENT '前端路由',
  `hidden` tinyint(2) unsigned DEFAULT NULL COMMENT '是否隐藏',
  `type` tinyint(2) unsigned DEFAULT NULL COMMENT '类型',
  `sort` int(10) unsigned DEFAULT NULL COMMENT '排序',
  `icon` varchar(50) COLLATE utf8_bin DEFAULT NULL COMMENT '图标',
  `created_by` varchar(64) COLLATE utf8_bin DEFAULT NULL COMMENT '创建人',
  `created_dt` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_by` varchar(64) COLLATE utf8_bin DEFAULT NULL COMMENT '更新人',
  `updated_dt` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) DEFAULT NULL,
  PRIMARY KEY (`menu_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin ROW_FORMAT=COMPACT COMMENT='菜单表';

-- ----------------------------
-- Records of s_auth_menu
-- ----------------------------
BEGIN;
INSERT INTO `s_auth_menu` (`menu_id`, `menu_seq`, `menu_name`, `parent_item_id`, `parent_item_ids`, `permissions`, `button_name`, `path`, `hidden`, `type`, `sort`, `icon`, `created_by`, `created_dt`, `updated_by`, `updated_dt`, `deleted`) VALUES ('10001', 10001, '系统管理', '0', '0', NULL, NULL, '', NULL, 1, 38, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `s_auth_menu` (`menu_id`, `menu_seq`, `menu_name`, `parent_item_id`, `parent_item_ids`, `permissions`, `button_name`, `path`, `hidden`, `type`, `sort`, `icon`, `created_by`, `created_dt`, `updated_by`, `updated_dt`, `deleted`) VALUES ('10002', 10002, '用户管理', '10001', '10001', NULL, NULL, 'account', NULL, 2, 39, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `s_auth_menu` (`menu_id`, `menu_seq`, `menu_name`, `parent_item_id`, `parent_item_ids`, `permissions`, `button_name`, `path`, `hidden`, `type`, `sort`, `icon`, `created_by`, `created_dt`, `updated_by`, `updated_dt`, `deleted`) VALUES ('10003', 10003, '角色管理', '10001', '10001', NULL, NULL, 'role', NULL, 2, 44, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `s_auth_menu` (`menu_id`, `menu_seq`, `menu_name`, `parent_item_id`, `parent_item_ids`, `permissions`, `button_name`, `path`, `hidden`, `type`, `sort`, `icon`, `created_by`, `created_dt`, `updated_by`, `updated_dt`, `deleted`) VALUES ('10004', 10004, '菜单管理', '10001', '10001', NULL, NULL, 'menu', NULL, 2, 49, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `s_auth_menu` (`menu_id`, `menu_seq`, `menu_name`, `parent_item_id`, `parent_item_ids`, `permissions`, `button_name`, `path`, `hidden`, `type`, `sort`, `icon`, `created_by`, `created_dt`, `updated_by`, `updated_dt`, `deleted`) VALUES ('10005', 10005, '用户新增-user:add', '10002', '10001,10002', 'user:add', NULL, '', NULL, 3, 40, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `s_auth_menu` (`menu_id`, `menu_seq`, `menu_name`, `parent_item_id`, `parent_item_ids`, `permissions`, `button_name`, `path`, `hidden`, `type`, `sort`, `icon`, `created_by`, `created_dt`, `updated_by`, `updated_dt`, `deleted`) VALUES ('10006', 10006, '用户删除-user:del', '10002', '10001,10002', 'user:del', NULL, '', NULL, 3, 41, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `s_auth_menu` (`menu_id`, `menu_seq`, `menu_name`, `parent_item_id`, `parent_item_ids`, `permissions`, `button_name`, `path`, `hidden`, `type`, `sort`, `icon`, `created_by`, `created_dt`, `updated_by`, `updated_dt`, `deleted`) VALUES ('10007', 10007, '用户编辑-user:edit', '10002', '10001,10002', 'user:edit', NULL, '', NULL, 3, 42, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `s_auth_menu` (`menu_id`, `menu_seq`, `menu_name`, `parent_item_id`, `parent_item_ids`, `permissions`, `button_name`, `path`, `hidden`, `type`, `sort`, `icon`, `created_by`, `created_dt`, `updated_by`, `updated_dt`, `deleted`) VALUES ('10008', 10008, '用户查询-user:query', '10002', '10001,10002', 'user:query', NULL, '', NULL, 3, 43, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `s_auth_menu` (`menu_id`, `menu_seq`, `menu_name`, `parent_item_id`, `parent_item_ids`, `permissions`, `button_name`, `path`, `hidden`, `type`, `sort`, `icon`, `created_by`, `created_dt`, `updated_by`, `updated_dt`, `deleted`) VALUES ('10009', 10009, '角色新增-role:add', '10003', '10001,10003', 'role:add', NULL, '', NULL, 3, 45, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `s_auth_menu` (`menu_id`, `menu_seq`, `menu_name`, `parent_item_id`, `parent_item_ids`, `permissions`, `button_name`, `path`, `hidden`, `type`, `sort`, `icon`, `created_by`, `created_dt`, `updated_by`, `updated_dt`, `deleted`) VALUES ('10010', 10010, '角色删除-role:del', '10003', '10001,10003', 'role:del', NULL, '', NULL, 3, 46, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `s_auth_menu` (`menu_id`, `menu_seq`, `menu_name`, `parent_item_id`, `parent_item_ids`, `permissions`, `button_name`, `path`, `hidden`, `type`, `sort`, `icon`, `created_by`, `created_dt`, `updated_by`, `updated_dt`, `deleted`) VALUES ('10011', 10011, '角色编辑-role:edit', '10003', '10001,10003', 'role:edit', NULL, '', NULL, 3, 47, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `s_auth_menu` (`menu_id`, `menu_seq`, `menu_name`, `parent_item_id`, `parent_item_ids`, `permissions`, `button_name`, `path`, `hidden`, `type`, `sort`, `icon`, `created_by`, `created_dt`, `updated_by`, `updated_dt`, `deleted`) VALUES ('10012', 10012, '角色查询-role:query', '10003', '10001,10003', 'role:query', NULL, '', NULL, 3, 48, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `s_auth_menu` (`menu_id`, `menu_seq`, `menu_name`, `parent_item_id`, `parent_item_ids`, `permissions`, `button_name`, `path`, `hidden`, `type`, `sort`, `icon`, `created_by`, `created_dt`, `updated_by`, `updated_dt`, `deleted`) VALUES ('10013', 10013, '菜单新增-menu:add', '10004', '10001,10004', 'menu:add', NULL, '', NULL, 3, 50, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `s_auth_menu` (`menu_id`, `menu_seq`, `menu_name`, `parent_item_id`, `parent_item_ids`, `permissions`, `button_name`, `path`, `hidden`, `type`, `sort`, `icon`, `created_by`, `created_dt`, `updated_by`, `updated_dt`, `deleted`) VALUES ('10014', 10014, '菜单删除-menu:del', '10004', '10001,10004', 'menu:del', NULL, '', NULL, 3, 51, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `s_auth_menu` (`menu_id`, `menu_seq`, `menu_name`, `parent_item_id`, `parent_item_ids`, `permissions`, `button_name`, `path`, `hidden`, `type`, `sort`, `icon`, `created_by`, `created_dt`, `updated_by`, `updated_dt`, `deleted`) VALUES ('10015', 10015, '菜单编辑-menu:edit', '10004', '10001,10004', 'menu:edit', NULL, '', NULL, 3, 52, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `s_auth_menu` (`menu_id`, `menu_seq`, `menu_name`, `parent_item_id`, `parent_item_ids`, `permissions`, `button_name`, `path`, `hidden`, `type`, `sort`, `icon`, `created_by`, `created_dt`, `updated_by`, `updated_dt`, `deleted`) VALUES ('10016', 10016, '菜单查询-menu:query', '10004', '10001,10004', 'menu:query', NULL, '', NULL, 3, 53, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `s_auth_menu` (`menu_id`, `menu_seq`, `menu_name`, `parent_item_id`, `parent_item_ids`, `permissions`, `button_name`, `path`, `hidden`, `type`, `sort`, `icon`, `created_by`, `created_dt`, `updated_by`, `updated_dt`, `deleted`) VALUES ('10017', 10017, '系统监控', '0', '0', NULL, NULL, '', NULL, 1, 54, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `s_auth_menu` (`menu_id`, `menu_seq`, `menu_name`, `parent_item_id`, `parent_item_ids`, `permissions`, `button_name`, `path`, `hidden`, `type`, `sort`, `icon`, `created_by`, `created_dt`, `updated_by`, `updated_dt`, `deleted`) VALUES ('10018', 10018, '访问日志', '10017', '10017', NULL, NULL, 'apiLog', NULL, 2, 55, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `s_auth_menu` (`menu_id`, `menu_seq`, `menu_name`, `parent_item_id`, `parent_item_ids`, `permissions`, `button_name`, `path`, `hidden`, `type`, `sort`, `icon`, `created_by`, `created_dt`, `updated_by`, `updated_dt`, `deleted`) VALUES ('10019', 10019, '错误日志', '10017', '10017', NULL, NULL, 'errorLog', NULL, 2, 56, NULL, NULL, NULL, NULL, NULL, NULL);
COMMIT;

-- ----------------------------
-- Table structure for s_auth_role
-- ----------------------------
DROP TABLE IF EXISTS `s_auth_role`;
CREATE TABLE `s_auth_role` (
  `role_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '角色id',
  `role_name` varchar(50) COLLATE utf8_bin NOT NULL COMMENT '角色名称',
  `role_code` varchar(50) COLLATE utf8_bin DEFAULT NULL COMMENT '角色编码',
  `remark` varchar(255) COLLATE utf8_bin DEFAULT NULL COMMENT '备注',
  `enable` tinyint(2) unsigned NOT NULL COMMENT '是否启用',
  `created_by` varchar(64) COLLATE utf8_bin DEFAULT NULL COMMENT '创建人',
  `created_dt` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_by` varchar(64) COLLATE utf8_bin DEFAULT NULL COMMENT '更新人',
  `updated_dt` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) DEFAULT NULL COMMENT '是否删除',
  `tenant_id` bigint(20) DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`role_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8 COLLATE=utf8_bin ROW_FORMAT=COMPACT COMMENT='角色表';

-- ----------------------------
-- Records of s_auth_role
-- ----------------------------
BEGIN;
INSERT INTO `s_auth_role` (`role_id`, `role_name`, `role_code`, `remark`, `enable`, `created_by`, `created_dt`, `updated_by`, `updated_dt`, `deleted`, `tenant_id`) VALUES (1, '管理员', 'admin', '租户1', 1, '', '2022-02-22 06:52:25', 'wang1', '2022-05-27 14:58:57', 0, 1);
INSERT INTO `s_auth_role` (`role_id`, `role_name`, `role_code`, `remark`, `enable`, `created_by`, `created_dt`, `updated_by`, `updated_dt`, `deleted`, `tenant_id`) VALUES (2, '人事', 'hr', 'dsa', 1, 'sda', '2022-04-08 17:17:12', NULL, NULL, 0, 1);
INSERT INTO `s_auth_role` (`role_id`, `role_name`, `role_code`, `remark`, `enable`, `created_by`, `created_dt`, `updated_by`, `updated_dt`, `deleted`, `tenant_id`) VALUES (3, '财务', 'cw', '租户2', 1, 'aq', '2023-04-28 21:07:39', 'aq', '2023-04-28 21:07:46', 0, 2);
INSERT INTO `s_auth_role` (`role_id`, `role_name`, `role_code`, `remark`, `enable`, `created_by`, `created_dt`, `updated_by`, `updated_dt`, `deleted`, `tenant_id`) VALUES (4, '1', '10d8de62-0f2e-4da9-9504-74e84a98f5dd', NULL, 1, 'wang1/王忠', '2023-05-06 21:18:41', 'wang1/王忠', '2023-05-06 21:18:41', 1, 1);
INSERT INTO `s_auth_role` (`role_id`, `role_name`, `role_code`, `remark`, `enable`, `created_by`, `created_dt`, `updated_by`, `updated_dt`, `deleted`, `tenant_id`) VALUES (5, '111', '0ff17e66-940e-4488-8ea3-afb91c4b66a8', NULL, 1, 'wang1/王忠', '2023-05-06 21:18:43', 'wang1/王忠', '2023-05-06 21:18:43', 1, 1);
COMMIT;

-- ----------------------------
-- Table structure for s_auth_role_menu
-- ----------------------------
DROP TABLE IF EXISTS `s_auth_role_menu`;
CREATE TABLE `s_auth_role_menu` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `role_id` bigint(20) unsigned NOT NULL COMMENT '角色id',
  `menu_id` bigint(20) unsigned NOT NULL COMMENT '菜单id',
  `created_by` varchar(64) COLLATE utf8_bin DEFAULT NULL COMMENT '创建人',
  `created_dt` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_by` varchar(64) COLLATE utf8_bin DEFAULT NULL COMMENT '更新人',
  `updated_dt` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) DEFAULT NULL COMMENT '是否删除',
  `tenant_id` bigint(20) DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=24 DEFAULT CHARSET=utf8 COLLATE=utf8_bin ROW_FORMAT=COMPACT COMMENT='角色菜单表';

-- ----------------------------
-- Records of s_auth_role_menu
-- ----------------------------
BEGIN;
INSERT INTO `s_auth_role_menu` (`id`, `role_id`, `menu_id`, `created_by`, `created_dt`, `updated_by`, `updated_dt`, `deleted`, `tenant_id`) VALUES (23, 2, 10002, 'wang1/王忠', '2023-07-22 14:11:32', 'wang1/王忠', '2023-07-22 14:11:32', 0, NULL);
COMMIT;

-- ----------------------------
-- Table structure for s_auth_user
-- ----------------------------
DROP TABLE IF EXISTS `s_auth_user`;
CREATE TABLE `s_auth_user` (
  `user_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '用户id',
  `user_name` varchar(50) COLLATE utf8_bin NOT NULL COMMENT '用户名',
  `user_account` varchar(125) COLLATE utf8_bin NOT NULL COMMENT '用户账号',
  `user_password` varchar(255) COLLATE utf8_bin NOT NULL COMMENT '用户密码',
  `phone_number` varchar(64) COLLATE utf8_bin NOT NULL COMMENT '手机号',
  `email` varchar(64) COLLATE utf8_bin DEFAULT NULL COMMENT '邮箱',
  `remark` varchar(255) COLLATE utf8_bin DEFAULT NULL COMMENT '备注',
  `enable` tinyint(2) unsigned NOT NULL COMMENT '是否启用',
  `created_by` varchar(64) COLLATE utf8_bin DEFAULT NULL COMMENT '创建人',
  `created_dt` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_by` varchar(64) COLLATE utf8_bin DEFAULT NULL COMMENT '更新人',
  `updated_dt` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NOT NULL COMMENT '是否删除',
  `tenant_id` bigint(20) DEFAULT NULL COMMENT '租户id',
  `is_admin` tinyint(4) DEFAULT NULL COMMENT '是否是超级管理员',
  PRIMARY KEY (`user_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8 COLLATE=utf8_bin ROW_FORMAT=COMPACT COMMENT='用户表';

-- ----------------------------
-- Records of s_auth_user
-- ----------------------------
BEGIN;
INSERT INTO `s_auth_user` (`user_id`, `user_name`, `user_account`, `user_password`, `phone_number`, `email`, `remark`, `enable`, `created_by`, `created_dt`, `updated_by`, `updated_dt`, `deleted`, `tenant_id`, `is_admin`) VALUES (1, '王忠', 'wang1', '$2a$10$rCZ2JSNuH.ayXXJr3tjAYurzQwLBfEBgUTS0nw73yAlFrl03oy1hq', '***********', '<EMAIL>', 'beizhu1', 1, '', '2022-02-22 06:36:37', '', '2022-02-22 07:33:35', 0, 1, 1);
INSERT INTO `s_auth_user` (`user_id`, `user_name`, `user_account`, `user_password`, `phone_number`, `email`, `remark`, `enable`, `created_by`, `created_dt`, `updated_by`, `updated_dt`, `deleted`, `tenant_id`, `is_admin`) VALUES (2, 'wangzhong2', 'wang2', '$2a$10$rCZ2JSNuH.ayXXJr3tjAYurzQwLBfEBgUTS0nw73yAlFrl03oy1hq', '*********', '3asd', 'dsad', 1, 'das', '2022-04-08 17:16:15', 'wang1(wangzhong1)', '2022-09-01 11:11:36', 0, 1, 0);
INSERT INTO `s_auth_user` (`user_id`, `user_name`, `user_account`, `user_password`, `phone_number`, `email`, `remark`, `enable`, `created_by`, `created_dt`, `updated_by`, `updated_dt`, `deleted`, `tenant_id`, `is_admin`) VALUES (3, 'wangzhong3', 'wang3', '$2a$10$rCZ2JSNuH.ayXXJr3tjAYurzQwLBfEBgUTS0nw73yAlFrl03oy1hq', '***********', '<EMAIL>', 'beizhu', 1, 'wang1/王忠', '2023-04-13 10:15:05', 'wang1/王忠', '2023-04-13 10:15:05', 0, 1, 0);
INSERT INTO `s_auth_user` (`user_id`, `user_name`, `user_account`, `user_password`, `phone_number`, `email`, `remark`, `enable`, `created_by`, `created_dt`, `updated_by`, `updated_dt`, `deleted`, `tenant_id`, `is_admin`) VALUES (4, 'wangzhong4', 'wang4', '$2a$10$rCZ2JSNuH.ayXXJr3tjAYurzQwLBfEBgUTS0nw73yAlFrl03oy1hq', '***********', '<EMAIL>', 'beizhu', 1, 'wang1/王忠', '2023-04-13 11:14:27', 'wang1/王忠', '2023-04-13 11:14:27', 0, 2, 0);
INSERT INTO `s_auth_user` (`user_id`, `user_name`, `user_account`, `user_password`, `phone_number`, `email`, `remark`, `enable`, `created_by`, `created_dt`, `updated_by`, `updated_dt`, `deleted`, `tenant_id`, `is_admin`) VALUES (5, 'wangzhong5', 'wang5', '$2a$10$rCZ2JSNuH.ayXXJr3tjAYurzQwLBfEBgUTS0nw73yAlFrl03oy1hq', '***********', '<EMAIL>', 'beizhu', 1, 'wang3/wangzhong', '2023-04-28 21:35:17', 'wang3/wangzhong', '2023-04-28 21:35:17', 0, 2, 0);
INSERT INTO `s_auth_user` (`user_id`, `user_name`, `user_account`, `user_password`, `phone_number`, `email`, `remark`, `enable`, `created_by`, `created_dt`, `updated_by`, `updated_dt`, `deleted`, `tenant_id`, `is_admin`) VALUES (6, '11', '11', '$2a$10$hz7F6jPRuK3ba.J/4uburuHpv22mVY6rjXHzqd0EraiiHccoWcM2W', '11', '11', '11', 1, 'wang1/王忠', '2023-05-06 21:26:30', 'wang1/王忠', '2023-05-06 21:26:30', 1, 1, 0);
COMMIT;

-- ----------------------------
-- Table structure for s_auth_user_role
-- ----------------------------
DROP TABLE IF EXISTS `s_auth_user_role`;
CREATE TABLE `s_auth_user_role` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户id',
  `role_id` bigint(20) unsigned NOT NULL COMMENT '角色id',
  `created_by` varchar(64) COLLATE utf8_bin DEFAULT NULL COMMENT '创建人',
  `created_dt` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_by` varchar(64) COLLATE utf8_bin DEFAULT NULL COMMENT '更新人',
  `updated_dt` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) DEFAULT NULL COMMENT '是否删除',
  `tenant_id` bigint(20) DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=utf8 COLLATE=utf8_bin ROW_FORMAT=COMPACT COMMENT='用户角色表';

-- ----------------------------
-- Records of s_auth_user_role
-- ----------------------------
BEGIN;
INSERT INTO `s_auth_user_role` (`id`, `user_id`, `role_id`, `created_by`, `created_dt`, `updated_by`, `updated_dt`, `deleted`, `tenant_id`) VALUES (17, 2, 2, 'wang1/王忠', '2023-07-22 14:11:03', 'wang1/王忠', '2023-07-22 14:11:03', 0, NULL);
COMMIT;

-- ----------------------------
-- Table structure for s_file_log
-- ----------------------------
DROP TABLE IF EXISTS `s_file_log`;
CREATE TABLE `s_file_log` (
  `file_id` varchar(30) COLLATE utf8_bin NOT NULL COMMENT '文件id',
  `file_name` varchar(50) COLLATE utf8_bin DEFAULT NULL COMMENT '文件名称',
  `file_url` varchar(255) COLLATE utf8_bin DEFAULT NULL COMMENT '文件url',
  `thumbnail_url` varchar(255) COLLATE utf8_bin DEFAULT NULL COMMENT '缩略图url',
  `file_format` varchar(50) COLLATE utf8_bin DEFAULT NULL COMMENT '文件格式',
  `file_size` bigint(30) DEFAULT NULL COMMENT '文件大小',
  `file_width` int(10) DEFAULT NULL COMMENT '图片宽度',
  `file_height` int(10) DEFAULT NULL COMMENT '图片高度',
  `obj_id` varchar(30) COLLATE utf8_bin DEFAULT NULL COMMENT '对象id',
  `table_name` varchar(30) COLLATE utf8_bin DEFAULT NULL COMMENT '表名',
  `field_name` varchar(30) COLLATE utf8_bin DEFAULT NULL COMMENT '字段名称',
  `oss_type` varchar(50) COLLATE utf8_bin DEFAULT NULL COMMENT '第三方类型',
  `created_by` varchar(50) COLLATE utf8_bin DEFAULT NULL COMMENT '创建人',
  `created_dt` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_by` varchar(50) COLLATE utf8_bin DEFAULT NULL COMMENT '更新人',
  `updated_dt` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted` int(4) DEFAULT NULL COMMENT '是否删除',
  PRIMARY KEY (`file_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin ROW_FORMAT=COMPACT COMMENT='菜单表';

-- ----------------------------
-- Records of s_file_log
-- ----------------------------

-- ----------------------------
-- Table structure for s_log_api_access_log
-- ----------------------------
DROP TABLE IF EXISTS `s_log_api_access_log`;
CREATE TABLE `s_log_api_access_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志主键',
  `trace_id` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '链路追踪编号',
  `user_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '用户编号',
  `user_type` tinyint(2) NOT NULL DEFAULT '0' COMMENT '用户类型',
  `application_name` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '应用名',
  `request_method` varchar(16) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '请求方法名',
  `request_url` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '请求地址',
  `request_params` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '请求参数',
  `user_ip` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户 IP',
  `user_agent` varchar(512) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '浏览器 UA',
  `begin_time` datetime NOT NULL COMMENT '开始请求时间',
  `end_time` datetime NOT NULL COMMENT '结束请求时间',
  `duration` int(11) NOT NULL COMMENT '执行时长',
  `result_code` int(11) NOT NULL DEFAULT '0' COMMENT '结果码',
  `result_msg` varchar(512) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '结果提示',
  `created_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `created_dt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `updated_dt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(2) NOT NULL DEFAULT '0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=989 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='API 访问日志表';

-- ----------------------------
-- Records of s_log_api_access_log
-- ----------------------------


-- ----------------------------
-- Table structure for s_log_api_error_log
-- ----------------------------
DROP TABLE IF EXISTS `s_log_api_error_log`;
CREATE TABLE `s_log_api_error_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `trace_id` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '链路追踪编号\n     *\n     * 一般来说，通过链路追踪编号，可以将访问日志，错误日志，链路追踪日志，logger 打印日志等，结合在一起，从而进行排错。',
  `user_id` bigint(20) DEFAULT '0' COMMENT '用户编号',
  `user_type` tinyint(2) DEFAULT '0' COMMENT '用户类型',
  `application_name` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '应用名\n     *\n     * 目前读取 spring.application.name',
  `request_method` varchar(16) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '请求方法名',
  `request_url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '请求地址',
  `request_params` text COLLATE utf8mb4_unicode_ci COMMENT '请求参数',
  `user_ip` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户 IP',
  `user_agent` varchar(512) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '浏览器 UA',
  `exception_dt` datetime DEFAULT NULL COMMENT '异常发生时间',
  `exception_name` varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '异常名\n     *\n     * {@link Throwable#getClass()} 的类全名',
  `exception_message` text COLLATE utf8mb4_unicode_ci COMMENT '异常导致的消息\n     *\n     * {@link cn.iocoder.common.framework.util.ExceptionUtil#getMessage(Throwable)}',
  `exception_root_cause_message` text COLLATE utf8mb4_unicode_ci COMMENT '异常导致的根消息\n     *\n     * {@link cn.iocoder.common.framework.util.ExceptionUtil#getRootCauseMessage(Throwable)}',
  `exception_stack_trace` text COLLATE utf8mb4_unicode_ci COMMENT '异常的栈轨迹\n     *\n     * {@link cn.iocoder.common.framework.util.ExceptionUtil#getServiceException(Exception)}',
  `exception_class_name` varchar(512) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '异常发生的类全名\n     *\n     * {@link StackTraceElement#getClassName()}',
  `exception_file_name` varchar(512) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '异常发生的类文件\n     *\n     * {@link StackTraceElement#getFileName()}',
  `exception_method_name` varchar(512) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '异常发生的方法名\n     *\n     * {@link StackTraceElement#getMethodName()}',
  `exception_line_number` int(11) DEFAULT NULL COMMENT '异常发生的方法所在行\n     *\n     * {@link StackTraceElement#getLineNumber()}',
  `process_status` tinyint(2) DEFAULT NULL COMMENT '处理状态',
  `process_dt` datetime DEFAULT NULL COMMENT '处理时间',
  `process_user_id` int(11) DEFAULT '0' COMMENT '处理用户编号',
  `created_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `created_dt` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `updated_dt` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(2) DEFAULT '0' COMMENT '是否删除',
  `tenant_id` bigint(20) DEFAULT '0' COMMENT '租户id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=48 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='系统异常日志';

-- ----------------------------
-- Records of s_log_api_error_log
-- ----------------------------


-- ----------------------------
-- Table structure for s_log_login_log
-- ----------------------------
DROP TABLE IF EXISTS `s_log_login_log`;
CREATE TABLE `s_log_login_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志id',
  `log_type` varchar(20) DEFAULT NULL COMMENT '日志类型',
  `trace_id` varchar(50) DEFAULT NULL COMMENT '链路追踪编号',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户id',
  `user_type` tinyint(2) DEFAULT NULL COMMENT '用户类型',
  `user_name` varchar(255) DEFAULT NULL COMMENT '用户名',
  `result` varchar(2000) DEFAULT NULL COMMENT '返回',
  `user_ip` varchar(2000) DEFAULT NULL COMMENT '用户ip',
  `user_agent` varchar(255) DEFAULT NULL COMMENT '浏览器',
  `created_by` varchar(255) DEFAULT NULL COMMENT '操作人',
  `created_dt` datetime DEFAULT NULL COMMENT '操作时间',
  `updated_by` varchar(255) DEFAULT NULL COMMENT '更新人',
  `updated_dt` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) DEFAULT NULL COMMENT '是否删除',
  `tenant_id` bigint(20) DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=103 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='系统访问记录';

-- ----------------------------
-- Records of s_log_login_log
-- ----------------------------

-- ----------------------------
-- Table structure for s_log_operate_log
-- ----------------------------
DROP TABLE IF EXISTS `s_log_operate_log`;
CREATE TABLE `s_log_operate_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志主键',
  `trace_id` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '链路追踪编号',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户编号',
  `user_type` tinyint(2) DEFAULT '0' COMMENT '用户类型',
  `module` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '模块标题',
  `name` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作名',
  `type` tinyint(2) DEFAULT '0' COMMENT '操作分类',
  `content` text COLLATE utf8mb4_unicode_ci COMMENT '操作内容',
  `exts` varchar(512) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '拓展字段',
  `request_method` varchar(16) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '请求方法名',
  `request_url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '请求地址',
  `user_ip` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户 IP',
  `user_agent` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '浏览器 UA',
  `java_method` varchar(512) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT 'Java 方法名',
  `java_method_args` text COLLATE utf8mb4_unicode_ci COMMENT 'Java 方法的参数',
  `start_dt` datetime DEFAULT NULL COMMENT '操作时间',
  `duration` int(11) DEFAULT NULL COMMENT '执行时长',
  `result_code` int(11) DEFAULT '0' COMMENT '结果码',
  `result_msg` varchar(512) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '结果提示',
  `result_data` text COLLATE utf8mb4_unicode_ci COMMENT '结果数据',
  `created_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `created_dt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `updated_dt` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(2) DEFAULT '0' COMMENT '是否删除',
  `tenant_id` bigint(20) DEFAULT '0' COMMENT '租户id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=625 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='操作日志记录';

-- ----------------------------
-- Records of s_log_operate_log
-- ----------------------------

-- ----------------------------
-- Table structure for s_tenant
-- ----------------------------
DROP TABLE IF EXISTS `s_tenant`;
CREATE TABLE `s_tenant` (
  `tenant_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '租户编号',
  `tenant_name` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '租户名',
  `contact_user_id` bigint(20) DEFAULT NULL COMMENT '联系人的用户编号',
  `contact_name` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '联系人',
  `contact_mobile` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '联系手机',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '租户状态（0正常 1停用）',
  `domain` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '绑定域名',
  `version_id` bigint(20) NOT NULL COMMENT '版本id',
  `expire_time` datetime NOT NULL COMMENT '过期时间',
  `account_count` int(11) NOT NULL COMMENT '账号数量',
  `created_by` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '创建人',
  `created_dt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新人',
  `updated_dt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(2) NOT NULL DEFAULT '0' COMMENT '是否删除',
  PRIMARY KEY (`tenant_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='租户表';

-- ----------------------------
-- Records of s_tenant
-- ----------------------------
BEGIN;
INSERT INTO `s_tenant` (`tenant_id`, `tenant_name`, `contact_user_id`, `contact_name`, `contact_mobile`, `status`, `domain`, `version_id`, `expire_time`, `account_count`, `created_by`, `created_dt`, `updated_by`, `updated_dt`, `deleted`) VALUES (1, '租户-1', 1, 'wang1', '***********', 1, 'www.mongoso.com', 1, '2025-04-28 21:13:22', 10, 'wang1/王忠', '2023-04-28 14:19:20', 'wang1/王忠', '2023-05-06 12:36:26', 0);
INSERT INTO `s_tenant` (`tenant_id`, `tenant_name`, `contact_user_id`, `contact_name`, `contact_mobile`, `status`, `domain`, `version_id`, `expire_time`, `account_count`, `created_by`, `created_dt`, `updated_by`, `updated_dt`, `deleted`) VALUES (2, '租户-2', 3, 'wang3', '***********', 1, 'zu3.mongoso.com', 1, '2025-04-28 21:13:22', 10, 'wang1/王忠', '2023-04-28 13:13:25', 'wang1/王忠', '2023-05-06 12:36:30', 0);
COMMIT;

-- ----------------------------
-- Table structure for s_tenant_version
-- ----------------------------
DROP TABLE IF EXISTS `s_tenant_version`;
CREATE TABLE `s_tenant_version` (
  `version_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '版本id',
  `version_code` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '版本编码',
  `version_name` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '版本名称',
  `version_status` tinyint(2) NOT NULL DEFAULT '0' COMMENT '版本状态（0正常 1停用）',
  `remark` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '备注',
  `menu_ids` varchar(2048) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '关联的菜单编号',
  `created_by` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '创建人',
  `created_dt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新人',
  `updated_dt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(2) NOT NULL DEFAULT '0' COMMENT '是否删除',
  PRIMARY KEY (`version_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='租户版本表';

-- ----------------------------
-- Records of s_tenant_version
-- ----------------------------
BEGIN;
INSERT INTO `s_tenant_version` (`version_id`, `version_code`, `version_name`, `version_status`, `remark`, `menu_ids`, `created_by`, `created_dt`, `updated_by`, `updated_dt`, `deleted`) VALUES (1, 'v1.0', '基础版', 0, 'beizhu', '[\"10000\",\"10001\"]', 'wang1/王忠', '2023-04-28 14:18:36', 'wang1/王忠', '2023-05-04 03:29:03', 0);
INSERT INTO `s_tenant_version` (`version_id`, `version_code`, `version_name`, `version_status`, `remark`, `menu_ids`, `created_by`, `created_dt`, `updated_by`, `updated_dt`, `deleted`) VALUES (2, 'v2.0', '基础版2', 0, 'beizhu', '[\"10000\",\"10001\"]', 'wang1/王忠', '2023-05-04 11:20:55', 'wang1/王忠', '2023-05-04 11:20:55', 0);
INSERT INTO `s_tenant_version` (`version_id`, `version_code`, `version_name`, `version_status`, `remark`, `menu_ids`, `created_by`, `created_dt`, `updated_by`, `updated_dt`, `deleted`) VALUES (3, 'v3.0', '基础版3', 0, 'beizhu', '[\"10000\",\"10001\"]', 'wang1/王忠', '2023-05-04 11:34:01', 'wang1/王忠', '2023-05-04 11:34:01', 0);
INSERT INTO `s_tenant_version` (`version_id`, `version_code`, `version_name`, `version_status`, `remark`, `menu_ids`, `created_by`, `created_dt`, `updated_by`, `updated_dt`, `deleted`) VALUES (4, 'v3.0', '基础版3', 0, 'beizhu', '[\"10000\",\"10001\"]', 'wang1/王忠', '2023-05-04 11:38:37', 'wang1/王忠', '2023-05-04 11:38:37', 0);
INSERT INTO `s_tenant_version` (`version_id`, `version_code`, `version_name`, `version_status`, `remark`, `menu_ids`, `created_by`, `created_dt`, `updated_by`, `updated_dt`, `deleted`) VALUES (5, 'v4.0', '基础版4', 0, 'beizhu', '[\"10000\",\"10001\"]', 'wang1/王忠', '2023-05-04 14:39:55', 'wang1/王忠', '2023-05-04 14:39:55', 0);
COMMIT;

SET FOREIGN_KEY_CHECKS = 1;
