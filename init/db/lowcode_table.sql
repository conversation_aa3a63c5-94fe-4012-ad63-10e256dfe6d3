/*
 Navicat Premium Data Transfer

 Source Server         : jgzy-dev
 Source Server Type    : PostgreSQL
 Source Server Version : 140013 (140013)
 Source Host           : **************:54321
 Source Catalog        : mgs-saas
 Source Schema         : lowcode

 Target Server Type    : PostgreSQL
 Target Server Version : 140013 (140013)
 File Encoding         : 65001

 Date: 20/06/2025 17:43:24
*/


-- ----------------------------
-- Type structure for gtrgm
-- ----------------------------
DROP TYPE IF EXISTS "lowcode"."gtrgm";
CREATE TYPE "lowcode"."gtrgm";
ALTER TYPE "lowcode"."gtrgm" OWNER TO "postgres";

-- ----------------------------
-- Sequence structure for a_codegen_data_source_config_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "lowcode"."a_codegen_data_source_config_seq";
CREATE SEQUENCE "lowcode"."a_codegen_data_source_config_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for s_properties_prop_pk_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "lowcode"."s_properties_prop_pk_id_seq";
CREATE SEQUENCE "lowcode"."s_properties_prop_pk_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for sys_model_function_history_data_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "lowcode"."sys_model_function_history_data_id_seq";
CREATE SEQUENCE "lowcode"."sys_model_function_history_data_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for sys_model_function_version_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "lowcode"."sys_model_function_version_id_seq";
CREATE SEQUENCE "lowcode"."sys_model_function_version_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Table structure for a_codegen_column
-- ----------------------------
DROP TABLE IF EXISTS "lowcode"."a_codegen_column";
CREATE TABLE "lowcode"."a_codegen_column" (
  "id" varchar(256) COLLATE "pg_catalog"."default" NOT NULL,
  "table_id" varchar(256) COLLATE "pg_catalog"."default" NOT NULL,
  "column_name" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
  "data_type" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "column_comment" varchar(500) COLLATE "pg_catalog"."default" NOT NULL,
  "nullable" bool NOT NULL,
  "primary_key" bool NOT NULL,
  "auto_increment" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "ordinal_position" int4 NOT NULL,
  "java_type" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "java_field" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "java_field_upper" varchar(64) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "example" varchar(64) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "adit_operation" bool NOT NULL,
  "page_operation" bool NOT NULL,
  "list_operation_condition" varchar(32) COLLATE "pg_catalog"."default" NOT NULL DEFAULT '='::character varying,
  "list_operation_result" bool NOT NULL,
  "creator" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "create_time" timestamp(6) NOT NULL,
  "updater" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "update_time" timestamp(6) NOT NULL,
  "deleted" int4 NOT NULL DEFAULT 0
)
;
COMMENT ON COLUMN "lowcode"."a_codegen_column"."id" IS '编号';
COMMENT ON COLUMN "lowcode"."a_codegen_column"."table_id" IS '表编号';
COMMENT ON COLUMN "lowcode"."a_codegen_column"."column_name" IS '字段名';
COMMENT ON COLUMN "lowcode"."a_codegen_column"."data_type" IS '字段类型';
COMMENT ON COLUMN "lowcode"."a_codegen_column"."column_comment" IS '字段描述';
COMMENT ON COLUMN "lowcode"."a_codegen_column"."nullable" IS '是否允许为空';
COMMENT ON COLUMN "lowcode"."a_codegen_column"."primary_key" IS '是否主键';
COMMENT ON COLUMN "lowcode"."a_codegen_column"."auto_increment" IS '是否自增';
COMMENT ON COLUMN "lowcode"."a_codegen_column"."ordinal_position" IS '排序';
COMMENT ON COLUMN "lowcode"."a_codegen_column"."java_type" IS 'Java 属性类型';
COMMENT ON COLUMN "lowcode"."a_codegen_column"."java_field" IS 'Java 属性名';
COMMENT ON COLUMN "lowcode"."a_codegen_column"."java_field_upper" IS 'Java 大写属性名';
COMMENT ON COLUMN "lowcode"."a_codegen_column"."example" IS '数据示例';
COMMENT ON COLUMN "lowcode"."a_codegen_column"."adit_operation" IS '是否为 Adit 创建操作的字段';
COMMENT ON COLUMN "lowcode"."a_codegen_column"."page_operation" IS '是否为 Page 查询操作的字段';
COMMENT ON COLUMN "lowcode"."a_codegen_column"."list_operation_condition" IS 'List 查询操作的条件类型';
COMMENT ON COLUMN "lowcode"."a_codegen_column"."list_operation_result" IS '是否为 List 查询操作的返回字段';
COMMENT ON COLUMN "lowcode"."a_codegen_column"."creator" IS '创建者';
COMMENT ON COLUMN "lowcode"."a_codegen_column"."create_time" IS '创建时间';
COMMENT ON COLUMN "lowcode"."a_codegen_column"."updater" IS '更新者';
COMMENT ON COLUMN "lowcode"."a_codegen_column"."update_time" IS '更新时间';
COMMENT ON COLUMN "lowcode"."a_codegen_column"."deleted" IS '是否删除';
COMMENT ON TABLE "lowcode"."a_codegen_column" IS '代码生成表字段定义';

-- ----------------------------
-- Table structure for a_codegen_data_source_config
-- ----------------------------
DROP TABLE IF EXISTS "lowcode"."a_codegen_data_source_config";
CREATE TABLE "lowcode"."a_codegen_data_source_config" (
  "id" int8 NOT NULL,
  "name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "url" varchar(1024) COLLATE "pg_catalog"."default" NOT NULL,
  "username" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "password" varchar(255) COLLATE "pg_catalog"."default" NOT NULL DEFAULT ''::character varying,
  "creator" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "create_time" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updater" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "update_time" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "deleted" int4 NOT NULL DEFAULT 0,
  "db_type" varchar(255) COLLATE "pg_catalog"."default" DEFAULT ''::character varying
)
;
COMMENT ON COLUMN "lowcode"."a_codegen_data_source_config"."id" IS '主键编号';
COMMENT ON COLUMN "lowcode"."a_codegen_data_source_config"."name" IS '参数名称';
COMMENT ON COLUMN "lowcode"."a_codegen_data_source_config"."url" IS '数据源连接';
COMMENT ON COLUMN "lowcode"."a_codegen_data_source_config"."username" IS '用户名';
COMMENT ON COLUMN "lowcode"."a_codegen_data_source_config"."password" IS '密码';
COMMENT ON COLUMN "lowcode"."a_codegen_data_source_config"."creator" IS '创建者';
COMMENT ON COLUMN "lowcode"."a_codegen_data_source_config"."create_time" IS '创建时间';
COMMENT ON COLUMN "lowcode"."a_codegen_data_source_config"."updater" IS '更新者';
COMMENT ON COLUMN "lowcode"."a_codegen_data_source_config"."update_time" IS '更新时间';
COMMENT ON COLUMN "lowcode"."a_codegen_data_source_config"."deleted" IS '是否删除';
COMMENT ON COLUMN "lowcode"."a_codegen_data_source_config"."db_type" IS '数据库类型';
COMMENT ON TABLE "lowcode"."a_codegen_data_source_config" IS '数据源配置表';

-- ----------------------------
-- Table structure for a_codegen_table
-- ----------------------------
DROP TABLE IF EXISTS "lowcode"."a_codegen_table";
CREATE TABLE "lowcode"."a_codegen_table" (
  "id" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "data_source_config_id" int8 NOT NULL,
  "scene" int4 NOT NULL DEFAULT 1,
  "table_name" varchar(200) COLLATE "pg_catalog"."default" NOT NULL DEFAULT ''::character varying,
  "table_comment" varchar(500) COLLATE "pg_catalog"."default" NOT NULL DEFAULT ''::character varying,
  "remark" varchar(500) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "module_name" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "business_name" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "class_name" varchar(100) COLLATE "pg_catalog"."default" NOT NULL DEFAULT ''::character varying,
  "class_comment" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "author" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "template_type" int4 NOT NULL DEFAULT 1,
  "creator" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "create_time" timestamp(6) NOT NULL,
  "updater" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "update_time" timestamp(6) NOT NULL,
  "deleted" int4 NOT NULL DEFAULT 0,
  "project_id" int8 NOT NULL DEFAULT 0
)
;
COMMENT ON COLUMN "lowcode"."a_codegen_table"."id" IS '编号';
COMMENT ON COLUMN "lowcode"."a_codegen_table"."data_source_config_id" IS '数据源配置的编号';
COMMENT ON COLUMN "lowcode"."a_codegen_table"."scene" IS '生成场景';
COMMENT ON COLUMN "lowcode"."a_codegen_table"."table_name" IS '表名称';
COMMENT ON COLUMN "lowcode"."a_codegen_table"."table_comment" IS '表描述';
COMMENT ON COLUMN "lowcode"."a_codegen_table"."remark" IS '备注';
COMMENT ON COLUMN "lowcode"."a_codegen_table"."module_name" IS '模块名';
COMMENT ON COLUMN "lowcode"."a_codegen_table"."business_name" IS '业务名';
COMMENT ON COLUMN "lowcode"."a_codegen_table"."class_name" IS '类名称';
COMMENT ON COLUMN "lowcode"."a_codegen_table"."class_comment" IS '类描述';
COMMENT ON COLUMN "lowcode"."a_codegen_table"."author" IS '作者';
COMMENT ON COLUMN "lowcode"."a_codegen_table"."template_type" IS '模板类型';
COMMENT ON COLUMN "lowcode"."a_codegen_table"."creator" IS '创建者';
COMMENT ON COLUMN "lowcode"."a_codegen_table"."create_time" IS '创建时间';
COMMENT ON COLUMN "lowcode"."a_codegen_table"."updater" IS '更新者';
COMMENT ON COLUMN "lowcode"."a_codegen_table"."update_time" IS '更新时间';
COMMENT ON COLUMN "lowcode"."a_codegen_table"."deleted" IS '是否删除';
COMMENT ON COLUMN "lowcode"."a_codegen_table"."project_id" IS '项目id';
COMMENT ON TABLE "lowcode"."a_codegen_table" IS '代码生成表定义';

-- ----------------------------
-- Table structure for a_influx_device_running
-- ----------------------------
DROP TABLE IF EXISTS "lowcode"."a_influx_device_running";
CREATE TABLE "lowcode"."a_influx_device_running" (
  "id" int8 NOT NULL,
  "device_id" varchar(20) COLLATE "pg_catalog"."default",
  "device_name" varchar(255) COLLATE "pg_catalog"."default",
  "start_dt" timestamp(6),
  "end_dt" timestamp(6),
  "running" int2
)
;
COMMENT ON COLUMN "lowcode"."a_influx_device_running"."id" IS 'id';
COMMENT ON COLUMN "lowcode"."a_influx_device_running"."device_id" IS '设备id';
COMMENT ON COLUMN "lowcode"."a_influx_device_running"."device_name" IS '设备名称';
COMMENT ON COLUMN "lowcode"."a_influx_device_running"."start_dt" IS '开始时间';
COMMENT ON COLUMN "lowcode"."a_influx_device_running"."end_dt" IS '结束时间';
COMMENT ON COLUMN "lowcode"."a_influx_device_running"."running" IS '状态';
COMMENT ON TABLE "lowcode"."a_influx_device_running" IS '测试统计报表';

-- ----------------------------
-- Table structure for a_influx_device_warn
-- ----------------------------
DROP TABLE IF EXISTS "lowcode"."a_influx_device_warn";
CREATE TABLE "lowcode"."a_influx_device_warn" (
  "id" int8 NOT NULL,
  "device_id" varchar(20) COLLATE "pg_catalog"."default",
  "device_name" varchar(255) COLLATE "pg_catalog"."default",
  "start_dt" timestamp(6),
  "end_dt" timestamp(6),
  "warn" int2
)
;
COMMENT ON COLUMN "lowcode"."a_influx_device_warn"."id" IS 'id';
COMMENT ON COLUMN "lowcode"."a_influx_device_warn"."device_id" IS '设备id';
COMMENT ON COLUMN "lowcode"."a_influx_device_warn"."device_name" IS '设备名称';
COMMENT ON COLUMN "lowcode"."a_influx_device_warn"."start_dt" IS '开始时间';
COMMENT ON COLUMN "lowcode"."a_influx_device_warn"."end_dt" IS '结束时间';
COMMENT ON COLUMN "lowcode"."a_influx_device_warn"."warn" IS '状态';
COMMENT ON TABLE "lowcode"."a_influx_device_warn" IS '测试统计报表';

-- ----------------------------
-- Table structure for s_auth_menu
-- ----------------------------
DROP TABLE IF EXISTS "lowcode"."s_auth_menu";
CREATE TABLE "lowcode"."s_auth_menu" (
  "menu_id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "menu_seq" int8,
  "menu_name" varchar(255) COLLATE "pg_catalog"."default",
  "parent_item_id" varchar(255) COLLATE "pg_catalog"."default",
  "parent_item_ids" varchar(255) COLLATE "pg_catalog"."default",
  "permissions" varchar(255) COLLATE "pg_catalog"."default",
  "path" varchar(255) COLLATE "pg_catalog"."default",
  "component" varchar(255) COLLATE "pg_catalog"."default",
  "component_name" varchar(255) COLLATE "pg_catalog"."default",
  "button_name" varchar(255) COLLATE "pg_catalog"."default",
  "hidden" int2,
  "type" int2,
  "sort" int4,
  "icon" varchar(255) COLLATE "pg_catalog"."default",
  "deleted" int2,
  "created_by" varchar COLLATE "pg_catalog"."default",
  "created_dt" timestamp(6),
  "updated_by" varchar COLLATE "pg_catalog"."default",
  "updated_dt" timestamp(6),
  "project_name" varchar(255) COLLATE "pg_catalog"."default",
  "category" int2 DEFAULT 1,
  "page_type" int2,
  "path_type" int2,
  "app_type" int2,
  "menu_code" varchar(255) COLLATE "pg_catalog"."default",
  "menu_param" varchar(255) COLLATE "pg_catalog"."default",
  "is_system" int2,
  "path_token" int2
)
;
COMMENT ON COLUMN "lowcode"."s_auth_menu"."menu_id" IS '菜单id';
COMMENT ON COLUMN "lowcode"."s_auth_menu"."menu_seq" IS '菜单seq';
COMMENT ON COLUMN "lowcode"."s_auth_menu"."menu_name" IS '菜单名称';
COMMENT ON COLUMN "lowcode"."s_auth_menu"."parent_item_id" IS '父级id';
COMMENT ON COLUMN "lowcode"."s_auth_menu"."parent_item_ids" IS '父级ids';
COMMENT ON COLUMN "lowcode"."s_auth_menu"."permissions" IS '权限';
COMMENT ON COLUMN "lowcode"."s_auth_menu"."path" IS '路劲';
COMMENT ON COLUMN "lowcode"."s_auth_menu"."component" IS '组件';
COMMENT ON COLUMN "lowcode"."s_auth_menu"."component_name" IS '组件名称';
COMMENT ON COLUMN "lowcode"."s_auth_menu"."button_name" IS '按钮名称';
COMMENT ON COLUMN "lowcode"."s_auth_menu"."hidden" IS '隐藏';
COMMENT ON COLUMN "lowcode"."s_auth_menu"."type" IS '类型，1：目录，2：菜单，3：按钮';
COMMENT ON COLUMN "lowcode"."s_auth_menu"."sort" IS '排序';
COMMENT ON COLUMN "lowcode"."s_auth_menu"."icon" IS '图标';
COMMENT ON COLUMN "lowcode"."s_auth_menu"."deleted" IS '删除状态';
COMMENT ON COLUMN "lowcode"."s_auth_menu"."created_by" IS '创建人';
COMMENT ON COLUMN "lowcode"."s_auth_menu"."created_dt" IS '创建时间';
COMMENT ON COLUMN "lowcode"."s_auth_menu"."updated_by" IS '更新人';
COMMENT ON COLUMN "lowcode"."s_auth_menu"."updated_dt" IS '更新时间';
COMMENT ON COLUMN "lowcode"."s_auth_menu"."project_name" IS '项目名';
COMMENT ON COLUMN "lowcode"."s_auth_menu"."category" IS '菜单大类，0：平台，1：企业菜单，2：供应商菜单，';
COMMENT ON COLUMN "lowcode"."s_auth_menu"."page_type" IS '页面类型，0：单据，1：自定义，2：查询';
COMMENT ON COLUMN "lowcode"."s_auth_menu"."path_type" IS '跳转类型';
COMMENT ON COLUMN "lowcode"."s_auth_menu"."app_type" IS '应用类型';
COMMENT ON COLUMN "lowcode"."s_auth_menu"."menu_code" IS '菜单编码';
COMMENT ON COLUMN "lowcode"."s_auth_menu"."menu_param" IS '菜单参数';
COMMENT ON COLUMN "lowcode"."s_auth_menu"."is_system" IS '是否是系统菜单';
COMMENT ON COLUMN "lowcode"."s_auth_menu"."path_token" IS '跳转类型';
COMMENT ON TABLE "lowcode"."s_auth_menu" IS '菜单表';

-- ----------------------------
-- Table structure for s_auth_role
-- ----------------------------
DROP TABLE IF EXISTS "lowcode"."s_auth_role";
CREATE TABLE "lowcode"."s_auth_role" (
  "role_id" int8 NOT NULL,
  "role_code" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "role_name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "remark" varchar COLLATE "pg_catalog"."default",
  "enable" int2 NOT NULL,
  "deleted" int2 NOT NULL,
  "created_by" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "created_dt" timestamp(6) NOT NULL,
  "updated_by" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "updated_dt" timestamp(6) NOT NULL,
  "tenant_id" int8
)
;
COMMENT ON COLUMN "lowcode"."s_auth_role"."role_id" IS '角色id';
COMMENT ON COLUMN "lowcode"."s_auth_role"."role_code" IS '角色编码';
COMMENT ON COLUMN "lowcode"."s_auth_role"."role_name" IS '角色名称';
COMMENT ON COLUMN "lowcode"."s_auth_role"."remark" IS '备注';
COMMENT ON COLUMN "lowcode"."s_auth_role"."enable" IS '是否启用';
COMMENT ON COLUMN "lowcode"."s_auth_role"."deleted" IS '删除状态';
COMMENT ON COLUMN "lowcode"."s_auth_role"."created_by" IS '创建人';
COMMENT ON COLUMN "lowcode"."s_auth_role"."created_dt" IS '创建时间';
COMMENT ON COLUMN "lowcode"."s_auth_role"."updated_by" IS '更新人';
COMMENT ON COLUMN "lowcode"."s_auth_role"."updated_dt" IS '更新时间';
COMMENT ON COLUMN "lowcode"."s_auth_role"."tenant_id" IS '租户id';
COMMENT ON TABLE "lowcode"."s_auth_role" IS '角色表';

-- ----------------------------
-- Table structure for s_auth_user
-- ----------------------------
DROP TABLE IF EXISTS "lowcode"."s_auth_user";
CREATE TABLE "lowcode"."s_auth_user" (
  "user_id" int8 NOT NULL,
  "user_name" varchar(255) COLLATE "pg_catalog"."default",
  "user_account" varchar(255) COLLATE "pg_catalog"."default",
  "user_password" varchar(255) COLLATE "pg_catalog"."default",
  "phone_number" varchar(255) COLLATE "pg_catalog"."default",
  "email" varchar(255) COLLATE "pg_catalog"."default",
  "remark" varchar(255) COLLATE "pg_catalog"."default",
  "is_admin" int2,
  "enable" int2 DEFAULT 0,
  "deleted" int2,
  "created_by" varchar(255) COLLATE "pg_catalog"."default",
  "created_dt" timestamp(6),
  "updated_by" varchar(255) COLLATE "pg_catalog"."default",
  "updated_dt" timestamp(6),
  "tenant_id" int8,
  "login_status" int2,
  "last_login_error_time" timestamp(6),
  "login_error_count" int2,
  "data_role_id" int8,
  "data_role_name" varchar(255) COLLATE "pg_catalog"."default" DEFAULT ''::character varying
)
;

-- ----------------------------
-- Table structure for s_auth_user_passport
-- ----------------------------
DROP TABLE IF EXISTS "lowcode"."s_auth_user_passport";
CREATE TABLE "lowcode"."s_auth_user_passport" (
  "id" int8 NOT NULL,
  "passport_number" varchar(50) COLLATE "pg_catalog"."default",
  "last_name" varchar(255) COLLATE "pg_catalog"."default",
  "first_name" varchar(255) COLLATE "pg_catalog"."default",
  "sex" varchar(50) COLLATE "pg_catalog"."default",
  "nationality" int4,
  "birthday" varchar(50) COLLATE "pg_catalog"."default",
  "issuing_country" int4,
  "issue_day" date,
  "expiration_day" date
)
;
COMMENT ON COLUMN "lowcode"."s_auth_user_passport"."id" IS '主键 主键';
COMMENT ON COLUMN "lowcode"."s_auth_user_passport"."passport_number" IS '护照号码 护照号码';
COMMENT ON COLUMN "lowcode"."s_auth_user_passport"."last_name" IS '姓 姓';
COMMENT ON COLUMN "lowcode"."s_auth_user_passport"."first_name" IS '名 名';
COMMENT ON COLUMN "lowcode"."s_auth_user_passport"."sex" IS '性别 性别 0-女 1-男 2-其他';
COMMENT ON COLUMN "lowcode"."s_auth_user_passport"."nationality" IS '国籍 国籍';
COMMENT ON COLUMN "lowcode"."s_auth_user_passport"."birthday" IS '出生日期 出生日期';
COMMENT ON COLUMN "lowcode"."s_auth_user_passport"."issuing_country" IS '护照签发国家 护照签发国家';
COMMENT ON COLUMN "lowcode"."s_auth_user_passport"."issue_day" IS '签发日期 签发日期';
COMMENT ON COLUMN "lowcode"."s_auth_user_passport"."expiration_day" IS '到期日期 到期日期';
COMMENT ON TABLE "lowcode"."s_auth_user_passport" IS '员工护照表';

-- ----------------------------
-- Table structure for s_auth_user_role
-- ----------------------------
DROP TABLE IF EXISTS "lowcode"."s_auth_user_role";
CREATE TABLE "lowcode"."s_auth_user_role" (
  "id" int8 NOT NULL,
  "user_id" int8,
  "role_id" int8,
  "deleted" int2,
  "created_by" varchar COLLATE "pg_catalog"."default",
  "created_dt" timestamp(6),
  "updated_by" varchar COLLATE "pg_catalog"."default",
  "updated_dt" timestamp(6),
  "tenant_id" int8
)
;
COMMENT ON COLUMN "lowcode"."s_auth_user_role"."id" IS 'id';
COMMENT ON COLUMN "lowcode"."s_auth_user_role"."user_id" IS '用户id';
COMMENT ON COLUMN "lowcode"."s_auth_user_role"."role_id" IS '角色id';
COMMENT ON COLUMN "lowcode"."s_auth_user_role"."deleted" IS '删除状态';
COMMENT ON COLUMN "lowcode"."s_auth_user_role"."created_by" IS '创建人';
COMMENT ON COLUMN "lowcode"."s_auth_user_role"."created_dt" IS '创建时间';
COMMENT ON COLUMN "lowcode"."s_auth_user_role"."updated_by" IS '更新人';
COMMENT ON COLUMN "lowcode"."s_auth_user_role"."updated_dt" IS '更新时间';
COMMENT ON COLUMN "lowcode"."s_auth_user_role"."tenant_id" IS '租户id';
COMMENT ON TABLE "lowcode"."s_auth_user_role" IS '用户角色表';

-- ----------------------------
-- Table structure for s_file_log
-- ----------------------------
DROP TABLE IF EXISTS "lowcode"."s_file_log";
CREATE TABLE "lowcode"."s_file_log" (
  "file_id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "file_name" varchar(255) COLLATE "pg_catalog"."default",
  "file_url" varchar(255) COLLATE "pg_catalog"."default",
  "thumbnail_url" varchar(255) COLLATE "pg_catalog"."default",
  "file_format" varchar(255) COLLATE "pg_catalog"."default",
  "file_size" int8,
  "file_width" int4,
  "file_height" int4,
  "obj_id" varchar(255) COLLATE "pg_catalog"."default",
  "table_name" varchar(255) COLLATE "pg_catalog"."default",
  "field_name" varchar(255) COLLATE "pg_catalog"."default",
  "oss_type" varchar(255) COLLATE "pg_catalog"."default",
  "created_by" varchar COLLATE "pg_catalog"."default",
  "created_dt" timestamp(6),
  "updated_by" varchar COLLATE "pg_catalog"."default",
  "updated_dt" timestamp(6),
  "domain" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "lowcode"."s_file_log"."file_id" IS '文件id';
COMMENT ON COLUMN "lowcode"."s_file_log"."file_name" IS '文件名称';
COMMENT ON COLUMN "lowcode"."s_file_log"."file_url" IS '文件url';
COMMENT ON COLUMN "lowcode"."s_file_log"."thumbnail_url" IS '缩略图url';
COMMENT ON COLUMN "lowcode"."s_file_log"."file_format" IS '文件格式';
COMMENT ON COLUMN "lowcode"."s_file_log"."file_size" IS '文件大小';
COMMENT ON COLUMN "lowcode"."s_file_log"."file_width" IS '文件宽度';
COMMENT ON COLUMN "lowcode"."s_file_log"."file_height" IS '文件高度';
COMMENT ON COLUMN "lowcode"."s_file_log"."obj_id" IS '对象id';
COMMENT ON COLUMN "lowcode"."s_file_log"."table_name" IS '表名';
COMMENT ON COLUMN "lowcode"."s_file_log"."field_name" IS '字段名';
COMMENT ON COLUMN "lowcode"."s_file_log"."oss_type" IS 'oss类型';
COMMENT ON COLUMN "lowcode"."s_file_log"."created_by" IS '创建人';
COMMENT ON COLUMN "lowcode"."s_file_log"."created_dt" IS '创建时间';
COMMENT ON COLUMN "lowcode"."s_file_log"."updated_by" IS '更新人';
COMMENT ON COLUMN "lowcode"."s_file_log"."updated_dt" IS '更新时间';
COMMENT ON COLUMN "lowcode"."s_file_log"."domain" IS '域名';
COMMENT ON TABLE "lowcode"."s_file_log" IS '文件日志';

-- ----------------------------
-- Table structure for s_properties
-- ----------------------------
DROP TABLE IF EXISTS "lowcode"."s_properties";
CREATE TABLE "lowcode"."s_properties" (
  "prop_pk_id" int8 NOT NULL DEFAULT nextval('"lowcode".s_properties_prop_pk_id_seq'::regclass),
  "prop_code" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "prop_value" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "prop_desc" varchar(200) COLLATE "pg_catalog"."default" NOT NULL
)
;
COMMENT ON COLUMN "lowcode"."s_properties"."prop_pk_id" IS '系统属性 ID';
COMMENT ON COLUMN "lowcode"."s_properties"."prop_code" IS '系统属性编码';
COMMENT ON COLUMN "lowcode"."s_properties"."prop_value" IS '系统属性值';
COMMENT ON COLUMN "lowcode"."s_properties"."prop_desc" IS '系统属性描述';

-- ----------------------------
-- Table structure for sys_api_case_info
-- ----------------------------
DROP TABLE IF EXISTS "lowcode"."sys_api_case_info";
CREATE TABLE "lowcode"."sys_api_case_info" (
  "id" int8 NOT NULL,
  "item_id" int8 NOT NULL,
  "case_name" varchar(50) COLLATE "pg_catalog"."default",
  "api_url" varchar(255) COLLATE "pg_catalog"."default",
  "env_url" varchar(300) COLLATE "pg_catalog"."default",
  "env_name" varchar(30) COLLATE "pg_catalog"."default",
  "request_type" int2 DEFAULT 1,
  "request" text COLLATE "pg_catalog"."default",
  "response" text COLLATE "pg_catalog"."default",
  "is_default" bool DEFAULT false,
  "created_by" varchar(50) COLLATE "pg_catalog"."default",
  "created_dt" timestamp(6),
  "updated_by" varchar(50) COLLATE "pg_catalog"."default",
  "updated_dt" timestamp(6)
)
;
COMMENT ON COLUMN "lowcode"."sys_api_case_info"."id" IS '主键id';
COMMENT ON COLUMN "lowcode"."sys_api_case_info"."item_id" IS '接口ID';
COMMENT ON COLUMN "lowcode"."sys_api_case_info"."case_name" IS '用例名称';
COMMENT ON COLUMN "lowcode"."sys_api_case_info"."api_url" IS '接口URL';
COMMENT ON COLUMN "lowcode"."sys_api_case_info"."env_url" IS '环境URL';
COMMENT ON COLUMN "lowcode"."sys_api_case_info"."env_name" IS '环境名称';
COMMENT ON COLUMN "lowcode"."sys_api_case_info"."request_type" IS '请求类型(0-get 1-post)';
COMMENT ON COLUMN "lowcode"."sys_api_case_info"."request" IS '请求信息';
COMMENT ON COLUMN "lowcode"."sys_api_case_info"."response" IS '响应信息';
COMMENT ON COLUMN "lowcode"."sys_api_case_info"."is_default" IS '是否默认';
COMMENT ON COLUMN "lowcode"."sys_api_case_info"."created_by" IS '创建人';
COMMENT ON COLUMN "lowcode"."sys_api_case_info"."created_dt" IS '创建时间';
COMMENT ON COLUMN "lowcode"."sys_api_case_info"."updated_by" IS '更新人';
COMMENT ON COLUMN "lowcode"."sys_api_case_info"."updated_dt" IS '更新时间';
COMMENT ON TABLE "lowcode"."sys_api_case_info" IS '用例管理表';

-- ----------------------------
-- Table structure for sys_item
-- ----------------------------
DROP TABLE IF EXISTS "lowcode"."sys_item";
CREATE TABLE "lowcode"."sys_item" (
  "item_id" int8 NOT NULL,
  "item_name" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "parent_item_id" int8 DEFAULT 0,
  "project_id" int8,
  "item_type" int2 DEFAULT 1,
  "item_seq" int4,
  "user_id" int8,
  "active" int2 DEFAULT 1,
  "is_default" int2 DEFAULT 0,
  "creator" varchar(64) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "created_dt" timestamp(6),
  "updator" varchar(64) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "updated_dt" timestamp(6),
  "task_set_type" int2 DEFAULT 0,
  "query_type" int2 DEFAULT 0,
  "has_dashboard" int2 DEFAULT 0,
  "category" int2 DEFAULT 1,
  "content" text COLLATE "pg_catalog"."default",
  "description" text COLLATE "pg_catalog"."default",
  "api_url" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "lowcode"."sys_item"."item_id" IS '主键';
COMMENT ON COLUMN "lowcode"."sys_item"."item_name" IS '文件夹名称';
COMMENT ON COLUMN "lowcode"."sys_item"."parent_item_id" IS '父文件夹Id';
COMMENT ON COLUMN "lowcode"."sys_item"."project_id" IS '项目ID';
COMMENT ON COLUMN "lowcode"."sys_item"."item_type" IS '文件夹（0文件夹 1项目文件夹 2项目内文件夹 3文件集 5:思维导图 6:数据导图）';
COMMENT ON COLUMN "lowcode"."sys_item"."item_seq" IS '文件排序号';
COMMENT ON COLUMN "lowcode"."sys_item"."user_id" IS '用户Id';
COMMENT ON COLUMN "lowcode"."sys_item"."active" IS '有效性（0无效 1有效 2回收站）';
COMMENT ON COLUMN "lowcode"."sys_item"."is_default" IS '是否默认（0否 1是）';
COMMENT ON COLUMN "lowcode"."sys_item"."task_set_type" IS '任务集类型（0工作 1问题）';
COMMENT ON COLUMN "lowcode"."sys_item"."query_type" IS '应用类型 0-MAX 1-测试任务 2-用例库';
COMMENT ON COLUMN "lowcode"."sys_item"."has_dashboard" IS '是否有看板';
COMMENT ON COLUMN "lowcode"."sys_item"."category" IS '大类别，0：架构，1：接口文档';
COMMENT ON COLUMN "lowcode"."sys_item"."content" IS '内容';
COMMENT ON COLUMN "lowcode"."sys_item"."description" IS '描述';
COMMENT ON COLUMN "lowcode"."sys_item"."api_url" IS '接口文档的URL';

-- ----------------------------
-- Table structure for sys_item_test
-- ----------------------------
DROP TABLE IF EXISTS "lowcode"."sys_item_test";
CREATE TABLE "lowcode"."sys_item_test" (
  "item_id" int8 NOT NULL,
  "item_name" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "parent_item_id" int8 DEFAULT 0,
  "project_id" int8,
  "item_type" int2 DEFAULT 1,
  "item_seq" int4,
  "user_id" int8,
  "active" int2 DEFAULT 1,
  "is_default" int2 DEFAULT 0,
  "creator" varchar(64) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "created_dt" timestamp(6),
  "updator" varchar(64) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "updated_dt" timestamp(6),
  "task_set_type" int2 DEFAULT 0,
  "query_type" int2 DEFAULT 0,
  "has_dashboard" int2 DEFAULT 0,
  "category" int2 DEFAULT 1,
  "content" text COLLATE "pg_catalog"."default",
  "description" text COLLATE "pg_catalog"."default",
  "api_url" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "lowcode"."sys_item_test"."item_id" IS '主键';
COMMENT ON COLUMN "lowcode"."sys_item_test"."item_name" IS '文件夹名称';
COMMENT ON COLUMN "lowcode"."sys_item_test"."parent_item_id" IS '父文件夹Id';
COMMENT ON COLUMN "lowcode"."sys_item_test"."project_id" IS '项目ID';
COMMENT ON COLUMN "lowcode"."sys_item_test"."item_type" IS '文件夹（0文件夹 1项目文件夹 2项目内文件夹 3文件集 5:思维导图 6:数据导图）';
COMMENT ON COLUMN "lowcode"."sys_item_test"."item_seq" IS '文件排序号';
COMMENT ON COLUMN "lowcode"."sys_item_test"."user_id" IS '用户Id';
COMMENT ON COLUMN "lowcode"."sys_item_test"."active" IS '有效性（0无效 1有效 2回收站）';
COMMENT ON COLUMN "lowcode"."sys_item_test"."is_default" IS '是否默认（0否 1是）';
COMMENT ON COLUMN "lowcode"."sys_item_test"."task_set_type" IS '任务集类型（0工作 1问题）';
COMMENT ON COLUMN "lowcode"."sys_item_test"."query_type" IS '应用类型 0-MAX 1-测试任务 2-用例库';
COMMENT ON COLUMN "lowcode"."sys_item_test"."has_dashboard" IS '是否有看板';
COMMENT ON COLUMN "lowcode"."sys_item_test"."category" IS '大类别，0：架构，1：接口文档';
COMMENT ON COLUMN "lowcode"."sys_item_test"."content" IS '内容';
COMMENT ON COLUMN "lowcode"."sys_item_test"."description" IS '描述';
COMMENT ON COLUMN "lowcode"."sys_item_test"."api_url" IS '接口文档的URL';

-- ----------------------------
-- Table structure for sys_model_field
-- ----------------------------
DROP TABLE IF EXISTS "lowcode"."sys_model_field";
CREATE TABLE "lowcode"."sys_model_field" (
  "field_id" int8 NOT NULL,
  "field_name" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "field_code" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "field_type" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
  "leng" int4 DEFAULT 0,
  "field_precision" int4 DEFAULT 0,
  "table_id" int8 NOT NULL,
  "is_nullable" int4 NOT NULL DEFAULT 0,
  "is_primary_key" int4 NOT NULL DEFAULT 0,
  "default_val" varchar(200) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "sort" int4 NOT NULL DEFAULT 0,
  "prop_type" int4 NOT NULL DEFAULT 1,
  "remark" varchar(255) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "created_by" varchar(32) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "created_dt" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_by" varchar(32) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "updated_dt" timestamp(6) NOT NULL,
  "field_type_name" varchar(50) COLLATE "pg_catalog"."default",
  "json_fields" jsonb,
  "is_auto_increase" int4 NOT NULL DEFAULT 0,
  "json_type" int4 NOT NULL DEFAULT 0
)
;
COMMENT ON COLUMN "lowcode"."sys_model_field"."field_id" IS '模型字段表id';
COMMENT ON COLUMN "lowcode"."sys_model_field"."field_name" IS '模型字段表中文名';
COMMENT ON COLUMN "lowcode"."sys_model_field"."field_code" IS '模型字段表实名';
COMMENT ON COLUMN "lowcode"."sys_model_field"."field_type" IS '字段类型';
COMMENT ON COLUMN "lowcode"."sys_model_field"."leng" IS '长度';
COMMENT ON COLUMN "lowcode"."sys_model_field"."field_precision" IS '精度';
COMMENT ON COLUMN "lowcode"."sys_model_field"."table_id" IS '模型表id';
COMMENT ON COLUMN "lowcode"."sys_model_field"."is_nullable" IS '是否为空';
COMMENT ON COLUMN "lowcode"."sys_model_field"."is_primary_key" IS '是否为主键';
COMMENT ON COLUMN "lowcode"."sys_model_field"."default_val" IS '默认值';
COMMENT ON COLUMN "lowcode"."sys_model_field"."sort" IS '排序';
COMMENT ON COLUMN "lowcode"."sys_model_field"."prop_type" IS '属性类型，0：系统，1：用户';
COMMENT ON COLUMN "lowcode"."sys_model_field"."remark" IS '备注描述';
COMMENT ON COLUMN "lowcode"."sys_model_field"."created_by" IS '创建人';
COMMENT ON COLUMN "lowcode"."sys_model_field"."created_dt" IS '创建时间';
COMMENT ON COLUMN "lowcode"."sys_model_field"."updated_by" IS '更新人';
COMMENT ON COLUMN "lowcode"."sys_model_field"."updated_dt" IS '更新时间';
COMMENT ON COLUMN "lowcode"."sys_model_field"."field_type_name" IS '字段类型名称';
COMMENT ON COLUMN "lowcode"."sys_model_field"."json_fields" IS 'json字段配置';
COMMENT ON COLUMN "lowcode"."sys_model_field"."is_auto_increase" IS '是否自增';
COMMENT ON COLUMN "lowcode"."sys_model_field"."json_type" IS 'JSON类型，0：子表，1：主表';
COMMENT ON TABLE "lowcode"."sys_model_field" IS '图形建模字段表';

-- ----------------------------
-- Table structure for sys_model_field_conf
-- ----------------------------
DROP TABLE IF EXISTS "lowcode"."sys_model_field_conf";
CREATE TABLE "lowcode"."sys_model_field_conf" (
  "id" int8 NOT NULL,
  "field_name" varchar(100) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "field_code" varchar(64) COLLATE "pg_catalog"."default",
  "field_type" varchar(200) COLLATE "pg_catalog"."default",
  "leng" int4 DEFAULT 0,
  "field_precision" int4 DEFAULT 0,
  "is_nullable" int4 NOT NULL DEFAULT 0,
  "is_primary_key" int4 NOT NULL DEFAULT 0,
  "default_val" varchar(200) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "sort" int4 NOT NULL DEFAULT 0,
  "prop_type" int4 NOT NULL DEFAULT 1,
  "remark" varchar(255) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "created_by" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "created_dt" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_by" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "updated_dt" timestamp(6) NOT NULL,
  "field_type_name" varchar(50) COLLATE "pg_catalog"."default",
  "json_fields" jsonb,
  "item_name" varchar(255) COLLATE "pg_catalog"."default",
  "english_name" varchar(255) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "field_length" int4 DEFAULT 0,
  "project_id" int8 DEFAULT 999,
  "data_type" varchar(200) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "lowcode"."sys_model_field_conf"."id" IS '字段翻译配置id';
COMMENT ON COLUMN "lowcode"."sys_model_field_conf"."field_name" IS '模型字段表中文名';
COMMENT ON COLUMN "lowcode"."sys_model_field_conf"."field_code" IS '模型字段表实名';
COMMENT ON COLUMN "lowcode"."sys_model_field_conf"."field_type" IS '字段类型（数据库）';
COMMENT ON COLUMN "lowcode"."sys_model_field_conf"."leng" IS '长度';
COMMENT ON COLUMN "lowcode"."sys_model_field_conf"."field_precision" IS '精度';
COMMENT ON COLUMN "lowcode"."sys_model_field_conf"."is_nullable" IS '是否必填';
COMMENT ON COLUMN "lowcode"."sys_model_field_conf"."is_primary_key" IS '是否为主键';
COMMENT ON COLUMN "lowcode"."sys_model_field_conf"."default_val" IS '默认值';
COMMENT ON COLUMN "lowcode"."sys_model_field_conf"."sort" IS '排序';
COMMENT ON COLUMN "lowcode"."sys_model_field_conf"."prop_type" IS '属性类型，0：系统，1：用户';
COMMENT ON COLUMN "lowcode"."sys_model_field_conf"."remark" IS '备注描述';
COMMENT ON COLUMN "lowcode"."sys_model_field_conf"."created_by" IS '创建人';
COMMENT ON COLUMN "lowcode"."sys_model_field_conf"."created_dt" IS '创建时间';
COMMENT ON COLUMN "lowcode"."sys_model_field_conf"."updated_by" IS '更新人';
COMMENT ON COLUMN "lowcode"."sys_model_field_conf"."updated_dt" IS '更新时间';
COMMENT ON COLUMN "lowcode"."sys_model_field_conf"."field_type_name" IS '字段类型名称';
COMMENT ON COLUMN "lowcode"."sys_model_field_conf"."json_fields" IS 'json字段配置';
COMMENT ON COLUMN "lowcode"."sys_model_field_conf"."item_name" IS '模型字段表中文名';
COMMENT ON COLUMN "lowcode"."sys_model_field_conf"."english_name" IS '模型字段表实名';
COMMENT ON COLUMN "lowcode"."sys_model_field_conf"."field_length" IS '长度';
COMMENT ON COLUMN "lowcode"."sys_model_field_conf"."project_id" IS '项目ID';
COMMENT ON COLUMN "lowcode"."sys_model_field_conf"."data_type" IS '文档类型（接口）';
COMMENT ON TABLE "lowcode"."sys_model_field_conf" IS '图形建模字段翻译配置表';

-- ----------------------------
-- Table structure for sys_model_field_json
-- ----------------------------
DROP TABLE IF EXISTS "lowcode"."sys_model_field_json";
CREATE TABLE "lowcode"."sys_model_field_json" (
  "id" int8 NOT NULL,
  "table_id" int8 NOT NULL,
  "field_id" int8 NOT NULL,
  "field_code" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "json_code" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "json_name" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "json_type" varchar(32) COLLATE "pg_catalog"."default",
  "remark" varchar COLLATE "pg_catalog"."default",
  "created_by" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "created_dt" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_by" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "updated_dt" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP
)
;
COMMENT ON COLUMN "lowcode"."sys_model_field_json"."id" IS '主键ID  ';
COMMENT ON COLUMN "lowcode"."sys_model_field_json"."table_id" IS '模型表id';
COMMENT ON COLUMN "lowcode"."sys_model_field_json"."field_id" IS '字段id';
COMMENT ON COLUMN "lowcode"."sys_model_field_json"."field_code" IS '字段编码';
COMMENT ON COLUMN "lowcode"."sys_model_field_json"."json_code" IS 'json字段编码';
COMMENT ON COLUMN "lowcode"."sys_model_field_json"."json_name" IS 'json字段名称';
COMMENT ON COLUMN "lowcode"."sys_model_field_json"."json_type" IS 'json字段类型';
COMMENT ON COLUMN "lowcode"."sys_model_field_json"."remark" IS '备注  ';
COMMENT ON COLUMN "lowcode"."sys_model_field_json"."created_by" IS '创建人  ';
COMMENT ON COLUMN "lowcode"."sys_model_field_json"."created_dt" IS '创建时间  ';
COMMENT ON COLUMN "lowcode"."sys_model_field_json"."updated_by" IS '更新人  ';
COMMENT ON COLUMN "lowcode"."sys_model_field_json"."updated_dt" IS '更新时间  ';
COMMENT ON TABLE "lowcode"."sys_model_field_json" IS '模型字段扩展表';

-- ----------------------------
-- Table structure for sys_model_function
-- ----------------------------
DROP TABLE IF EXISTS "lowcode"."sys_model_function";
CREATE TABLE "lowcode"."sys_model_function" (
  "fun_id" int8 NOT NULL,
  "fun_name" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "fun_code" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "fun_body" text COLLATE "pg_catalog"."default",
  "run_env" varchar(64) COLLATE "pg_catalog"."default",
  "timeout" int4 DEFAULT '-1'::integer,
  "remark" varchar COLLATE "pg_catalog"."default",
  "version_no" int4 NOT NULL DEFAULT 1,
  "is_publish" int2 NOT NULL DEFAULT 0,
  "is_lock" int2 NOT NULL DEFAULT 0,
  "dir_type" int2 NOT NULL DEFAULT 1,
  "parent_id" int8 NOT NULL DEFAULT 0,
  "prop_type" int4 NOT NULL DEFAULT 1,
  "updated_dt" timestamp(6) NOT NULL,
  "created_by" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "created_dt" timestamp(6) NOT NULL,
  "updated_by" varchar COLLATE "pg_catalog"."default" NOT NULL
)
;
COMMENT ON COLUMN "lowcode"."sys_model_function"."fun_id" IS '函数id  ';
COMMENT ON COLUMN "lowcode"."sys_model_function"."fun_name" IS '函数名称';
COMMENT ON COLUMN "lowcode"."sys_model_function"."fun_code" IS '函数编号';
COMMENT ON COLUMN "lowcode"."sys_model_function"."fun_body" IS '函数主体';
COMMENT ON COLUMN "lowcode"."sys_model_function"."run_env" IS '运行环境  ';
COMMENT ON COLUMN "lowcode"."sys_model_function"."timeout" IS '超时时间';
COMMENT ON COLUMN "lowcode"."sys_model_function"."remark" IS '备注  ';
COMMENT ON COLUMN "lowcode"."sys_model_function"."version_no" IS '版本号';
COMMENT ON COLUMN "lowcode"."sys_model_function"."is_publish" IS '是否发布';
COMMENT ON COLUMN "lowcode"."sys_model_function"."is_lock" IS '是否锁定';
COMMENT ON COLUMN "lowcode"."sys_model_function"."dir_type" IS '目录类型';
COMMENT ON COLUMN "lowcode"."sys_model_function"."parent_id" IS '父节点';
COMMENT ON COLUMN "lowcode"."sys_model_function"."prop_type" IS '属性类型，0：系统，1：用户';
COMMENT ON COLUMN "lowcode"."sys_model_function"."updated_dt" IS '更新时间  ';
COMMENT ON COLUMN "lowcode"."sys_model_function"."created_by" IS '创建人  ';
COMMENT ON COLUMN "lowcode"."sys_model_function"."created_dt" IS '创建时间  ';
COMMENT ON COLUMN "lowcode"."sys_model_function"."updated_by" IS '更新人  ';
COMMENT ON TABLE "lowcode"."sys_model_function" IS '自定义函数表';

-- ----------------------------
-- Table structure for sys_model_function_history
-- ----------------------------
DROP TABLE IF EXISTS "lowcode"."sys_model_function_history";
CREATE TABLE "lowcode"."sys_model_function_history" (
  "fun_id" int8 NOT NULL,
  "fun_name" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "fun_code" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "fun_body" text COLLATE "pg_catalog"."default",
  "run_env" varchar(64) COLLATE "pg_catalog"."default",
  "timeout" int4 DEFAULT '-1'::integer,
  "remark" varchar COLLATE "pg_catalog"."default",
  "version_no" int4 NOT NULL DEFAULT 1,
  "is_publish" int2 NOT NULL DEFAULT 0,
  "is_lock" int2 NOT NULL DEFAULT 0,
  "dir_type" int2 NOT NULL DEFAULT 1,
  "parent_id" int8 NOT NULL DEFAULT 0,
  "prop_type" int4 NOT NULL DEFAULT 1,
  "updated_dt" timestamp(6) NOT NULL,
  "created_by" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "created_dt" timestamp(6) NOT NULL,
  "updated_by" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "data_id" int8 NOT NULL DEFAULT nextval('"lowcode".sys_model_function_history_data_id_seq'::regclass)
)
;
COMMENT ON COLUMN "lowcode"."sys_model_function_history"."fun_id" IS '函数id  ';
COMMENT ON COLUMN "lowcode"."sys_model_function_history"."fun_name" IS '函数名称';
COMMENT ON COLUMN "lowcode"."sys_model_function_history"."fun_code" IS '函数编号';
COMMENT ON COLUMN "lowcode"."sys_model_function_history"."fun_body" IS '函数主体';
COMMENT ON COLUMN "lowcode"."sys_model_function_history"."run_env" IS '运行环境  ';
COMMENT ON COLUMN "lowcode"."sys_model_function_history"."timeout" IS '超时时间';
COMMENT ON COLUMN "lowcode"."sys_model_function_history"."remark" IS '备注  ';
COMMENT ON COLUMN "lowcode"."sys_model_function_history"."version_no" IS '版本号';
COMMENT ON COLUMN "lowcode"."sys_model_function_history"."is_publish" IS '是否发布';
COMMENT ON COLUMN "lowcode"."sys_model_function_history"."is_lock" IS '是否锁定';
COMMENT ON COLUMN "lowcode"."sys_model_function_history"."dir_type" IS '目录类型';
COMMENT ON COLUMN "lowcode"."sys_model_function_history"."parent_id" IS '父节点';
COMMENT ON COLUMN "lowcode"."sys_model_function_history"."prop_type" IS '属性类型，0：系统，1：用户';
COMMENT ON COLUMN "lowcode"."sys_model_function_history"."updated_dt" IS '更新时间  ';
COMMENT ON COLUMN "lowcode"."sys_model_function_history"."created_by" IS '创建人  ';
COMMENT ON COLUMN "lowcode"."sys_model_function_history"."created_dt" IS '创建时间  ';
COMMENT ON COLUMN "lowcode"."sys_model_function_history"."updated_by" IS '更新人  ';
COMMENT ON COLUMN "lowcode"."sys_model_function_history"."data_id" IS '数据主键';
COMMENT ON TABLE "lowcode"."sys_model_function_history" IS '自定义函数历史表';

-- ----------------------------
-- Table structure for sys_model_function_version
-- ----------------------------
DROP TABLE IF EXISTS "lowcode"."sys_model_function_version";
CREATE TABLE "lowcode"."sys_model_function_version" (
  "id" int8 NOT NULL DEFAULT nextval('"lowcode".sys_model_function_version_id_seq'::regclass),
  "fun_id" int8 NOT NULL,
  "version_no" int4 NOT NULL,
  "created_by" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "created_dt" timestamp(6) NOT NULL,
  "updated_by" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "updated_dt" timestamp(6) NOT NULL
)
;
COMMENT ON COLUMN "lowcode"."sys_model_function_version"."id" IS '主键';
COMMENT ON COLUMN "lowcode"."sys_model_function_version"."fun_id" IS '函数id  ';
COMMENT ON COLUMN "lowcode"."sys_model_function_version"."version_no" IS '版本号';
COMMENT ON COLUMN "lowcode"."sys_model_function_version"."created_by" IS '创建人  ';
COMMENT ON COLUMN "lowcode"."sys_model_function_version"."created_dt" IS '创建时间  ';
COMMENT ON COLUMN "lowcode"."sys_model_function_version"."updated_by" IS '更新人  ';
COMMENT ON COLUMN "lowcode"."sys_model_function_version"."updated_dt" IS '更新时间  ';
COMMENT ON TABLE "lowcode"."sys_model_function_version" IS '自定义报表版本';

-- ----------------------------
-- Table structure for sys_model_query
-- ----------------------------
DROP TABLE IF EXISTS "lowcode"."sys_model_query";
CREATE TABLE "lowcode"."sys_model_query" (
  "query_id" int8 NOT NULL,
  "query_code" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "query_name" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "query_statment" text COLLATE "pg_catalog"."default",
  "remark" varchar(200) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "created_by" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "created_dt" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_by" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "updated_dt" timestamp(6) NOT NULL,
  "dir_type" int4 NOT NULL DEFAULT 1,
  "parent_id" int8 DEFAULT 0,
  "data_source_config_id" int4,
  "type_conf" jsonb
)
;
COMMENT ON COLUMN "lowcode"."sys_model_query"."query_id" IS '主键ID';
COMMENT ON COLUMN "lowcode"."sys_model_query"."query_code" IS '查询编码';
COMMENT ON COLUMN "lowcode"."sys_model_query"."query_name" IS '查询中文名';
COMMENT ON COLUMN "lowcode"."sys_model_query"."query_statment" IS '查询语句';
COMMENT ON COLUMN "lowcode"."sys_model_query"."remark" IS '备注';
COMMENT ON COLUMN "lowcode"."sys_model_query"."created_by" IS '创建人';
COMMENT ON COLUMN "lowcode"."sys_model_query"."created_dt" IS '创建时间';
COMMENT ON COLUMN "lowcode"."sys_model_query"."updated_by" IS '更新人';
COMMENT ON COLUMN "lowcode"."sys_model_query"."updated_dt" IS '更新时间';
COMMENT ON COLUMN "lowcode"."sys_model_query"."dir_type" IS '类型';
COMMENT ON COLUMN "lowcode"."sys_model_query"."parent_id" IS '父节点';
COMMENT ON COLUMN "lowcode"."sys_model_query"."data_source_config_id" IS '数据源配置的编号';
COMMENT ON COLUMN "lowcode"."sys_model_query"."type_conf" IS '类型配置';
COMMENT ON TABLE "lowcode"."sys_model_query" IS '自定义查询表';

-- ----------------------------
-- Table structure for sys_model_query_param
-- ----------------------------
DROP TABLE IF EXISTS "lowcode"."sys_model_query_param";
CREATE TABLE "lowcode"."sys_model_query_param" (
  "id" int8 NOT NULL,
  "query_id" int8 NOT NULL,
  "param_code" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "param_name" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "param_type" int4 NOT NULL,
  "data_type" varchar(64) COLLATE "pg_catalog"."default",
  "leng" int4,
  "field_precision" int4,
  "data_table" varchar(64) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "data_fields" varchar(500) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "data_field_str" text COLLATE "pg_catalog"."default",
  "remark" varchar(200) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "created_by" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "created_dt" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_by" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "updated_dt" timestamp(6) NOT NULL,
  "table_id" int8
)
;
COMMENT ON COLUMN "lowcode"."sys_model_query_param"."id" IS '主键ID';
COMMENT ON COLUMN "lowcode"."sys_model_query_param"."query_id" IS '查询id';
COMMENT ON COLUMN "lowcode"."sys_model_query_param"."param_code" IS '参数英文名';
COMMENT ON COLUMN "lowcode"."sys_model_query_param"."param_name" IS '参数中文名';
COMMENT ON COLUMN "lowcode"."sys_model_query_param"."param_type" IS '参数模式';
COMMENT ON COLUMN "lowcode"."sys_model_query_param"."data_type" IS '数据类型';
COMMENT ON COLUMN "lowcode"."sys_model_query_param"."leng" IS '长度';
COMMENT ON COLUMN "lowcode"."sys_model_query_param"."field_precision" IS '精度';
COMMENT ON COLUMN "lowcode"."sys_model_query_param"."data_table" IS '数据源';
COMMENT ON COLUMN "lowcode"."sys_model_query_param"."data_fields" IS '数据列';
COMMENT ON COLUMN "lowcode"."sys_model_query_param"."data_field_str" IS '数据列完整属性';
COMMENT ON COLUMN "lowcode"."sys_model_query_param"."remark" IS '备注';
COMMENT ON COLUMN "lowcode"."sys_model_query_param"."created_by" IS '创建人';
COMMENT ON COLUMN "lowcode"."sys_model_query_param"."created_dt" IS '创建时间';
COMMENT ON COLUMN "lowcode"."sys_model_query_param"."updated_by" IS '更新人';
COMMENT ON COLUMN "lowcode"."sys_model_query_param"."updated_dt" IS '更新时间';
COMMENT ON COLUMN "lowcode"."sys_model_query_param"."table_id" IS '模型表id';
COMMENT ON TABLE "lowcode"."sys_model_query_param" IS '自定义查询参数表';

-- ----------------------------
-- Table structure for sys_model_query_result
-- ----------------------------
DROP TABLE IF EXISTS "lowcode"."sys_model_query_result";
CREATE TABLE "lowcode"."sys_model_query_result" (
  "id" int8 NOT NULL,
  "query_id" int8 NOT NULL,
  "result_field" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "result_field_name" varchar(64) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "data_type" varchar(64) COLLATE "pg_catalog"."default",
  "leng" int4,
  "field_precision" int4,
  "remark" varchar(200) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "is_show" int4 NOT NULL DEFAULT 1,
  "created_by" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "created_dt" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_by" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "updated_dt" timestamp(6) NOT NULL,
  "sort" int4 NOT NULL DEFAULT 0
)
;
COMMENT ON COLUMN "lowcode"."sys_model_query_result"."id" IS '主键ID';
COMMENT ON COLUMN "lowcode"."sys_model_query_result"."query_id" IS '查询id';
COMMENT ON COLUMN "lowcode"."sys_model_query_result"."result_field" IS '结果字段';
COMMENT ON COLUMN "lowcode"."sys_model_query_result"."result_field_name" IS '结果字段别名';
COMMENT ON COLUMN "lowcode"."sys_model_query_result"."data_type" IS '数据类型';
COMMENT ON COLUMN "lowcode"."sys_model_query_result"."leng" IS '长度';
COMMENT ON COLUMN "lowcode"."sys_model_query_result"."field_precision" IS '精度';
COMMENT ON COLUMN "lowcode"."sys_model_query_result"."remark" IS '备注';
COMMENT ON COLUMN "lowcode"."sys_model_query_result"."is_show" IS '显示状态';
COMMENT ON COLUMN "lowcode"."sys_model_query_result"."created_by" IS '创建人';
COMMENT ON COLUMN "lowcode"."sys_model_query_result"."created_dt" IS '创建时间';
COMMENT ON COLUMN "lowcode"."sys_model_query_result"."updated_by" IS '更新人';
COMMENT ON COLUMN "lowcode"."sys_model_query_result"."updated_dt" IS '更新时间';
COMMENT ON COLUMN "lowcode"."sys_model_query_result"."sort" IS '排序';
COMMENT ON TABLE "lowcode"."sys_model_query_result" IS '自定义查询结果表';

-- ----------------------------
-- Table structure for sys_model_report
-- ----------------------------
DROP TABLE IF EXISTS "lowcode"."sys_model_report";
CREATE TABLE "lowcode"."sys_model_report" (
  "report_id" int8 NOT NULL,
  "report_name" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "report_code" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "query_id" int8,
  "remark" varchar COLLATE "pg_catalog"."default",
  "version_no" int4 NOT NULL DEFAULT 1,
  "is_publish" int2 NOT NULL DEFAULT 0,
  "dir_type" int4 NOT NULL DEFAULT 1,
  "parent_id" int8 DEFAULT 0,
  "created_by" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "created_dt" timestamp(6) NOT NULL,
  "updated_by" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "updated_dt" timestamp(6) NOT NULL
)
;
COMMENT ON COLUMN "lowcode"."sys_model_report"."report_id" IS '报表id  ';
COMMENT ON COLUMN "lowcode"."sys_model_report"."report_name" IS '报表名称';
COMMENT ON COLUMN "lowcode"."sys_model_report"."report_code" IS '报表编号';
COMMENT ON COLUMN "lowcode"."sys_model_report"."query_id" IS '查询id  ';
COMMENT ON COLUMN "lowcode"."sys_model_report"."remark" IS '备注  ';
COMMENT ON COLUMN "lowcode"."sys_model_report"."version_no" IS '版本号';
COMMENT ON COLUMN "lowcode"."sys_model_report"."is_publish" IS '是否发布';
COMMENT ON COLUMN "lowcode"."sys_model_report"."dir_type" IS '类型,0:目录，1:报表';
COMMENT ON COLUMN "lowcode"."sys_model_report"."parent_id" IS '父节点';
COMMENT ON COLUMN "lowcode"."sys_model_report"."created_by" IS '创建人  ';
COMMENT ON COLUMN "lowcode"."sys_model_report"."created_dt" IS '创建时间  ';
COMMENT ON COLUMN "lowcode"."sys_model_report"."updated_by" IS '更新人  ';
COMMENT ON COLUMN "lowcode"."sys_model_report"."updated_dt" IS '更新时间  ';
COMMENT ON TABLE "lowcode"."sys_model_report" IS '自定义报表';

-- ----------------------------
-- Table structure for sys_model_report_config
-- ----------------------------
DROP TABLE IF EXISTS "lowcode"."sys_model_report_config";
CREATE TABLE "lowcode"."sys_model_report_config" (
  "id" int8 NOT NULL,
  "report_id" int8 NOT NULL,
  "data_table" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "field_code" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "field_name" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "control_type" int4,
  "field_type" varchar(50) COLLATE "pg_catalog"."default",
  "leng" int4,
  "field_precision" int4,
  "reg_expression" varchar(32) COLLATE "pg_catalog"."default",
  "data_mask" varchar(32) COLLATE "pg_catalog"."default",
  "is_edit" int2 NOT NULL DEFAULT 0,
  "is_show" int2 NOT NULL DEFAULT 0,
  "empty_show" varchar(32) COLLATE "pg_catalog"."default",
  "is_required" int2 NOT NULL DEFAULT 0,
  "default_val" varchar COLLATE "pg_catalog"."default",
  "remark" varchar COLLATE "pg_catalog"."default",
  "version_no" int4 NOT NULL DEFAULT 1,
  "category" int2 DEFAULT 0,
  "created_by" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "created_dt" timestamp(6) NOT NULL,
  "updated_by" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "updated_dt" timestamp(6) NOT NULL
)
;
COMMENT ON COLUMN "lowcode"."sys_model_report_config"."id" IS '条件id';
COMMENT ON COLUMN "lowcode"."sys_model_report_config"."report_id" IS '报表id  ';
COMMENT ON COLUMN "lowcode"."sys_model_report_config"."data_table" IS '数据源  ';
COMMENT ON COLUMN "lowcode"."sys_model_report_config"."field_code" IS '字段编码';
COMMENT ON COLUMN "lowcode"."sys_model_report_config"."field_name" IS '字段名称';
COMMENT ON COLUMN "lowcode"."sys_model_report_config"."control_type" IS '控件类型';
COMMENT ON COLUMN "lowcode"."sys_model_report_config"."field_type" IS '字段类型  ';
COMMENT ON COLUMN "lowcode"."sys_model_report_config"."leng" IS '长度';
COMMENT ON COLUMN "lowcode"."sys_model_report_config"."field_precision" IS '字段精度';
COMMENT ON COLUMN "lowcode"."sys_model_report_config"."reg_expression" IS '正则表达式';
COMMENT ON COLUMN "lowcode"."sys_model_report_config"."data_mask" IS '数据掩码';
COMMENT ON COLUMN "lowcode"."sys_model_report_config"."is_edit" IS '可编辑';
COMMENT ON COLUMN "lowcode"."sys_model_report_config"."is_show" IS '是否显示';
COMMENT ON COLUMN "lowcode"."sys_model_report_config"."empty_show" IS '空值显示';
COMMENT ON COLUMN "lowcode"."sys_model_report_config"."is_required" IS '是否必填';
COMMENT ON COLUMN "lowcode"."sys_model_report_config"."default_val" IS '默认值';
COMMENT ON COLUMN "lowcode"."sys_model_report_config"."remark" IS '备注  ';
COMMENT ON COLUMN "lowcode"."sys_model_report_config"."version_no" IS '版本号';
COMMENT ON COLUMN "lowcode"."sys_model_report_config"."category" IS '0:查询条件，1:展示数据';
COMMENT ON COLUMN "lowcode"."sys_model_report_config"."created_by" IS '创建人  ';
COMMENT ON COLUMN "lowcode"."sys_model_report_config"."created_dt" IS '创建时间  ';
COMMENT ON COLUMN "lowcode"."sys_model_report_config"."updated_by" IS '更新人  ';
COMMENT ON COLUMN "lowcode"."sys_model_report_config"."updated_dt" IS '更新时间  ';
COMMENT ON TABLE "lowcode"."sys_model_report_config" IS '自定义报表配置表';

-- ----------------------------
-- Table structure for sys_model_report_version
-- ----------------------------
DROP TABLE IF EXISTS "lowcode"."sys_model_report_version";
CREATE TABLE "lowcode"."sys_model_report_version" (
  "id" int8 NOT NULL,
  "report_id" int8 NOT NULL,
  "version_no" int4 NOT NULL,
  "created_by" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "created_dt" timestamp(6) NOT NULL,
  "updated_by" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "updated_dt" timestamp(6) NOT NULL
)
;
COMMENT ON COLUMN "lowcode"."sys_model_report_version"."id" IS '主键ID  ';
COMMENT ON COLUMN "lowcode"."sys_model_report_version"."report_id" IS '报表id  ';
COMMENT ON COLUMN "lowcode"."sys_model_report_version"."version_no" IS '版本号';
COMMENT ON COLUMN "lowcode"."sys_model_report_version"."created_by" IS '创建人  ';
COMMENT ON COLUMN "lowcode"."sys_model_report_version"."created_dt" IS '创建时间  ';
COMMENT ON COLUMN "lowcode"."sys_model_report_version"."updated_by" IS '更新人  ';
COMMENT ON COLUMN "lowcode"."sys_model_report_version"."updated_dt" IS '更新时间  ';
COMMENT ON TABLE "lowcode"."sys_model_report_version" IS '自定义报表版本';

-- ----------------------------
-- Table structure for sys_model_table
-- ----------------------------
DROP TABLE IF EXISTS "lowcode"."sys_model_table";
CREATE TABLE "lowcode"."sys_model_table" (
  "table_id" int8 NOT NULL,
  "table_name" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "table_code" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "remark" varchar(255) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "upgrade_flag" int4 NOT NULL DEFAULT 0,
  "is_gen" int4 NOT NULL DEFAULT 0,
  "dir_type" int4 NOT NULL DEFAULT 1,
  "parent_id" int8 DEFAULT 0,
  "created_by" varchar(32) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "created_dt" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_by" varchar(32) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "updated_dt" timestamp(6) NOT NULL,
  "prop_type" int4 NOT NULL DEFAULT 1,
  "data_source_config_id" int4 DEFAULT 3,
  "project_id" int8 DEFAULT 0,
  "version" varchar(255) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "icon" varchar(500) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "is_lock" int4 NOT NULL DEFAULT 0,
  "seq" int4 NOT NULL DEFAULT 0
)
;
COMMENT ON COLUMN "lowcode"."sys_model_table"."table_id" IS '模型表id';
COMMENT ON COLUMN "lowcode"."sys_model_table"."table_name" IS '模型表中文名';
COMMENT ON COLUMN "lowcode"."sys_model_table"."table_code" IS '模型表实名';
COMMENT ON COLUMN "lowcode"."sys_model_table"."remark" IS '备注描述';
COMMENT ON COLUMN "lowcode"."sys_model_table"."upgrade_flag" IS '升级标记';
COMMENT ON COLUMN "lowcode"."sys_model_table"."is_gen" IS '是否生成过';
COMMENT ON COLUMN "lowcode"."sys_model_table"."dir_type" IS '类型';
COMMENT ON COLUMN "lowcode"."sys_model_table"."parent_id" IS '父节点';
COMMENT ON COLUMN "lowcode"."sys_model_table"."created_by" IS '创建人';
COMMENT ON COLUMN "lowcode"."sys_model_table"."created_dt" IS '创建时间';
COMMENT ON COLUMN "lowcode"."sys_model_table"."updated_by" IS '更新人';
COMMENT ON COLUMN "lowcode"."sys_model_table"."updated_dt" IS '更新时间';
COMMENT ON COLUMN "lowcode"."sys_model_table"."prop_type" IS '属性类型，0：系统，1：用户';
COMMENT ON COLUMN "lowcode"."sys_model_table"."data_source_config_id" IS '数据源配置的编号';
COMMENT ON COLUMN "lowcode"."sys_model_table"."project_id" IS '项目id';
COMMENT ON COLUMN "lowcode"."sys_model_table"."version" IS '版本';
COMMENT ON COLUMN "lowcode"."sys_model_table"."icon" IS '图标URL';
COMMENT ON COLUMN "lowcode"."sys_model_table"."is_lock" IS '是否锁定';
COMMENT ON COLUMN "lowcode"."sys_model_table"."seq" IS '排序';
COMMENT ON TABLE "lowcode"."sys_model_table" IS '图形建模主表';

-- ----------------------------
-- Table structure for sys_model_table_index
-- ----------------------------
DROP TABLE IF EXISTS "lowcode"."sys_model_table_index";
CREATE TABLE "lowcode"."sys_model_table_index" (
  "id" int8 NOT NULL,
  "idx_id" int8 NOT NULL,
  "idx_name" varchar(64) COLLATE "pg_catalog"."default" NOT NULL DEFAULT '1'::character varying,
  "table_id" int8 NOT NULL,
  "table_code" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "non_unique" int4 NOT NULL DEFAULT 1,
  "idx_seq" int4 NOT NULL DEFAULT 1,
  "field_id" int8 NOT NULL,
  "field_code" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "sort_type" varchar(10) COLLATE "pg_catalog"."default" NOT NULL DEFAULT ''::character varying,
  "idx_type" varchar(10) COLLATE "pg_catalog"."default" NOT NULL DEFAULT ''::character varying,
  "idx_way" varchar(10) COLLATE "pg_catalog"."default" NOT NULL DEFAULT ''::character varying,
  "remark" varchar(255) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "created_by" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "created_dt" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_by" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "updated_dt" timestamp(6) NOT NULL
)
;
COMMENT ON COLUMN "lowcode"."sys_model_table_index"."id" IS 'id';
COMMENT ON COLUMN "lowcode"."sys_model_table_index"."idx_id" IS '索引id';
COMMENT ON COLUMN "lowcode"."sys_model_table_index"."idx_name" IS '索引名称';
COMMENT ON COLUMN "lowcode"."sys_model_table_index"."table_id" IS '模型表id';
COMMENT ON COLUMN "lowcode"."sys_model_table_index"."table_code" IS '模型表实名';
COMMENT ON COLUMN "lowcode"."sys_model_table_index"."non_unique" IS '是否生成过';
COMMENT ON COLUMN "lowcode"."sys_model_table_index"."idx_seq" IS '索引序号';
COMMENT ON COLUMN "lowcode"."sys_model_table_index"."field_id" IS '模型字段表id';
COMMENT ON COLUMN "lowcode"."sys_model_table_index"."field_code" IS '字段编码';
COMMENT ON COLUMN "lowcode"."sys_model_table_index"."sort_type" IS '排序类型(asc,desc)';
COMMENT ON COLUMN "lowcode"."sys_model_table_index"."idx_type" IS '索引类型(NORMAL, UNIQUE, FULLTEXT)';
COMMENT ON COLUMN "lowcode"."sys_model_table_index"."idx_way" IS '索引方法(betree,hash)';
COMMENT ON COLUMN "lowcode"."sys_model_table_index"."remark" IS '备注描述';
COMMENT ON COLUMN "lowcode"."sys_model_table_index"."created_by" IS '创建人';
COMMENT ON COLUMN "lowcode"."sys_model_table_index"."created_dt" IS '创建时间';
COMMENT ON COLUMN "lowcode"."sys_model_table_index"."updated_by" IS '更新人';
COMMENT ON COLUMN "lowcode"."sys_model_table_index"."updated_dt" IS '更新时间';
COMMENT ON TABLE "lowcode"."sys_model_table_index" IS '图形建模主表索引表';

-- ----------------------------
-- Table structure for sys_page_config
-- ----------------------------
DROP TABLE IF EXISTS "lowcode"."sys_page_config";
CREATE TABLE "lowcode"."sys_page_config" (
  "id" int8 NOT NULL,
  "parent_id" int8 DEFAULT 0,
  "name" varchar(200) COLLATE "pg_catalog"."default",
  "content" text COLLATE "pg_catalog"."default",
  "seq" int4,
  "deleted" int2 NOT NULL,
  "created_by" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "created_dt" timestamp(6) NOT NULL,
  "updated_by" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "updated_dt" timestamp(6) NOT NULL,
  "router_path" varchar(255) COLLATE "pg_catalog"."default",
  "is_show" int2 NOT NULL DEFAULT 1,
  "project_id" int8 DEFAULT 0,
  "item_type" int4 DEFAULT 0
)
;
COMMENT ON COLUMN "lowcode"."sys_page_config"."id" IS 'id';
COMMENT ON COLUMN "lowcode"."sys_page_config"."parent_id" IS '父id';
COMMENT ON COLUMN "lowcode"."sys_page_config"."name" IS '名称';
COMMENT ON COLUMN "lowcode"."sys_page_config"."content" IS '内容';
COMMENT ON COLUMN "lowcode"."sys_page_config"."seq" IS '排序号';
COMMENT ON COLUMN "lowcode"."sys_page_config"."deleted" IS '删除状态 ["否", "是"]';
COMMENT ON COLUMN "lowcode"."sys_page_config"."created_by" IS '创建人';
COMMENT ON COLUMN "lowcode"."sys_page_config"."created_dt" IS '创建时间';
COMMENT ON COLUMN "lowcode"."sys_page_config"."updated_by" IS '更新人';
COMMENT ON COLUMN "lowcode"."sys_page_config"."updated_dt" IS '更新时间';
COMMENT ON COLUMN "lowcode"."sys_page_config"."router_path" IS '路径标识';
COMMENT ON COLUMN "lowcode"."sys_page_config"."is_show" IS '是否显示在菜单里 ["否", "是"]';
COMMENT ON COLUMN "lowcode"."sys_page_config"."project_id" IS '项目id';
COMMENT ON COLUMN "lowcode"."sys_page_config"."item_type" IS '选项类型';
COMMENT ON TABLE "lowcode"."sys_page_config" IS '页面配置表';

-- ----------------------------
-- Table structure for sys_project_api_env
-- ----------------------------
DROP TABLE IF EXISTS "lowcode"."sys_project_api_env";
CREATE TABLE "lowcode"."sys_project_api_env" (
  "id" int8 NOT NULL,
  "project_id" int8 NOT NULL,
  "env_name" varchar(20) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "env_url" varchar(200) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "is_default" bool NOT NULL
)
;
COMMENT ON COLUMN "lowcode"."sys_project_api_env"."id" IS '环境ID';
COMMENT ON COLUMN "lowcode"."sys_project_api_env"."project_id" IS '项目Id';
COMMENT ON COLUMN "lowcode"."sys_project_api_env"."env_name" IS '环境名称';
COMMENT ON COLUMN "lowcode"."sys_project_api_env"."env_url" IS '环境地址';
COMMENT ON COLUMN "lowcode"."sys_project_api_env"."is_default" IS '是否默认(0-否 1-是)';
COMMENT ON TABLE "lowcode"."sys_project_api_env" IS '环境配置';

-- ----------------------------
-- Table structure for sys_project_api_params
-- ----------------------------
DROP TABLE IF EXISTS "lowcode"."sys_project_api_params";
CREATE TABLE "lowcode"."sys_project_api_params" (
  "id" int8 NOT NULL,
  "project_id" int8 NOT NULL,
  "header_params" jsonb,
  "common_params" jsonb
)
;
COMMENT ON COLUMN "lowcode"."sys_project_api_params"."id" IS '主键Id';
COMMENT ON COLUMN "lowcode"."sys_project_api_params"."project_id" IS '项目Id';
COMMENT ON COLUMN "lowcode"."sys_project_api_params"."header_params" IS 'heder参数';
COMMENT ON COLUMN "lowcode"."sys_project_api_params"."common_params" IS '普通参数';
COMMENT ON TABLE "lowcode"."sys_project_api_params" IS '公共参数';

-- ----------------------------
-- Table structure for sys_project_comp
-- ----------------------------
DROP TABLE IF EXISTS "lowcode"."sys_project_comp";
CREATE TABLE "lowcode"."sys_project_comp" (
  "comp_id" int8 NOT NULL,
  "cmpt_list" text COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "lowcode"."sys_project_comp"."comp_id" IS '组件ID';
COMMENT ON COLUMN "lowcode"."sys_project_comp"."cmpt_list" IS '组件列表';

-- ----------------------------
-- Table structure for sys_project_field_type
-- ----------------------------
DROP TABLE IF EXISTS "lowcode"."sys_project_field_type";
CREATE TABLE "lowcode"."sys_project_field_type" (
  "id" int8 NOT NULL,
  "project_id" int8 NOT NULL,
  "field_type_list" jsonb,
  "db_type" varchar(60) COLLATE "pg_catalog"."default",
  "created_by" varchar(32) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "created_dt" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_by" varchar(32) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "updated_dt" timestamp(6) NOT NULL
)
;
COMMENT ON COLUMN "lowcode"."sys_project_field_type"."id" IS '主键id';
COMMENT ON COLUMN "lowcode"."sys_project_field_type"."project_id" IS '项目id';
COMMENT ON COLUMN "lowcode"."sys_project_field_type"."field_type_list" IS '字段类型列表';
COMMENT ON COLUMN "lowcode"."sys_project_field_type"."db_type" IS '数据库类型';
COMMENT ON COLUMN "lowcode"."sys_project_field_type"."created_by" IS '创建人';
COMMENT ON COLUMN "lowcode"."sys_project_field_type"."created_dt" IS '创建时间';
COMMENT ON COLUMN "lowcode"."sys_project_field_type"."updated_by" IS '更新人';
COMMENT ON COLUMN "lowcode"."sys_project_field_type"."updated_dt" IS '更新时间';
COMMENT ON TABLE "lowcode"."sys_project_field_type" IS '项目图形建模字段类型表';

-- ----------------------------
-- Table structure for sys_project_login_auth
-- ----------------------------
DROP TABLE IF EXISTS "lowcode"."sys_project_login_auth";
CREATE TABLE "lowcode"."sys_project_login_auth" (
  "id" int8 NOT NULL,
  "project_id" int8 NOT NULL,
  "item_id" int8,
  "login_api_code" int8,
  "login_faild_code" varchar(100) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "login_auth_key" varchar(100) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "login_auth_val" varchar(200) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "login_faild_key" varchar(100) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "created_by" varchar(20) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "created_dt" timestamp(6),
  "updated_by" varchar(20) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "updated_dt" timestamp(6)
)
;
COMMENT ON COLUMN "lowcode"."sys_project_login_auth"."id" IS '接口登录信息ID';
COMMENT ON COLUMN "lowcode"."sys_project_login_auth"."project_id" IS '项目ID';
COMMENT ON COLUMN "lowcode"."sys_project_login_auth"."item_id" IS '登录接口';
COMMENT ON COLUMN "lowcode"."sys_project_login_auth"."login_api_code" IS '登录接口任务ID';
COMMENT ON COLUMN "lowcode"."sys_project_login_auth"."login_faild_code" IS '登录时效编码';
COMMENT ON COLUMN "lowcode"."sys_project_login_auth"."login_auth_key" IS '登录认证字段名';
COMMENT ON COLUMN "lowcode"."sys_project_login_auth"."login_auth_val" IS '登录认证字段值';
COMMENT ON COLUMN "lowcode"."sys_project_login_auth"."login_faild_key" IS '登录时效字段';
COMMENT ON COLUMN "lowcode"."sys_project_login_auth"."created_by" IS '创建人';
COMMENT ON COLUMN "lowcode"."sys_project_login_auth"."created_dt" IS '创建时间';
COMMENT ON COLUMN "lowcode"."sys_project_login_auth"."updated_by" IS '更新人';
COMMENT ON COLUMN "lowcode"."sys_project_login_auth"."updated_dt" IS '更新时间';
COMMENT ON TABLE "lowcode"."sys_project_login_auth" IS '授权配置';

-- ----------------------------
-- Table structure for sys_project_model_field
-- ----------------------------
DROP TABLE IF EXISTS "lowcode"."sys_project_model_field";
CREATE TABLE "lowcode"."sys_project_model_field" (
  "id" int8 NOT NULL,
  "field_name" varchar(100) COLLATE "pg_catalog"."default" NOT NULL DEFAULT ''::character varying,
  "field_code" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "field_type" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
  "leng" int4 DEFAULT 0,
  "field_precision" int4 DEFAULT 0,
  "project_id" int8 NOT NULL,
  "is_nullable" int4 NOT NULL DEFAULT 0,
  "is_primary_key" int4 NOT NULL DEFAULT 0,
  "is_auto_increase" int4 NOT NULL DEFAULT 0,
  "default_val" varchar(200) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "sort" int4 NOT NULL DEFAULT 0,
  "prop_type" int4 NOT NULL DEFAULT 1,
  "field_type_name" varchar(50) COLLATE "pg_catalog"."default",
  "json_fields" jsonb,
  "remark" varchar(255) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "created_by" varchar(32) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "created_dt" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_by" varchar(32) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "updated_dt" timestamp(6) NOT NULL,
  "json_type" int4 NOT NULL DEFAULT 0
)
;
COMMENT ON COLUMN "lowcode"."sys_project_model_field"."id" IS '主键id';
COMMENT ON COLUMN "lowcode"."sys_project_model_field"."field_name" IS '模型字段表中文名';
COMMENT ON COLUMN "lowcode"."sys_project_model_field"."field_code" IS '模型字段表实名';
COMMENT ON COLUMN "lowcode"."sys_project_model_field"."field_type" IS '字段类型';
COMMENT ON COLUMN "lowcode"."sys_project_model_field"."leng" IS '长度';
COMMENT ON COLUMN "lowcode"."sys_project_model_field"."field_precision" IS '精度';
COMMENT ON COLUMN "lowcode"."sys_project_model_field"."project_id" IS '项目id';
COMMENT ON COLUMN "lowcode"."sys_project_model_field"."is_nullable" IS '是否为空';
COMMENT ON COLUMN "lowcode"."sys_project_model_field"."is_primary_key" IS '是否为主键';
COMMENT ON COLUMN "lowcode"."sys_project_model_field"."is_auto_increase" IS '是否自增';
COMMENT ON COLUMN "lowcode"."sys_project_model_field"."default_val" IS '默认值';
COMMENT ON COLUMN "lowcode"."sys_project_model_field"."sort" IS '排序';
COMMENT ON COLUMN "lowcode"."sys_project_model_field"."prop_type" IS '属性类型，0：系统，1：用户';
COMMENT ON COLUMN "lowcode"."sys_project_model_field"."field_type_name" IS '字段类型名称';
COMMENT ON COLUMN "lowcode"."sys_project_model_field"."json_fields" IS 'json字段配置';
COMMENT ON COLUMN "lowcode"."sys_project_model_field"."remark" IS '备注描述';
COMMENT ON COLUMN "lowcode"."sys_project_model_field"."created_by" IS '创建人';
COMMENT ON COLUMN "lowcode"."sys_project_model_field"."created_dt" IS '创建时间';
COMMENT ON COLUMN "lowcode"."sys_project_model_field"."updated_by" IS '更新人';
COMMENT ON COLUMN "lowcode"."sys_project_model_field"."updated_dt" IS '更新时间';
COMMENT ON COLUMN "lowcode"."sys_project_model_field"."json_type" IS 'JSON类型，0：子表，1：主表';
COMMENT ON TABLE "lowcode"."sys_project_model_field" IS '项目图形建模初始化字段表';

-- ----------------------------
-- Table structure for sys_sql_log
-- ----------------------------
DROP TABLE IF EXISTS "lowcode"."sys_sql_log";
CREATE TABLE "lowcode"."sys_sql_log" (
  "id" int8 NOT NULL,
  "project_id" int8 NOT NULL,
  "table_code" varchar(50) COLLATE "pg_catalog"."default",
  "sql" text COLLATE "pg_catalog"."default" NOT NULL,
  "op_type" int2,
  "suc_status" int2 DEFAULT 1,
  "err_mgs" text COLLATE "pg_catalog"."default",
  "created_by" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "created_dt" timestamp(6) NOT NULL,
  "updated_by" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "updated_dt" timestamp(6) NOT NULL,
  "table_id" int8 NOT NULL DEFAULT 0,
  "deleted" int2 DEFAULT 0
)
;
COMMENT ON COLUMN "lowcode"."sys_sql_log"."id" IS '主键';
COMMENT ON COLUMN "lowcode"."sys_sql_log"."project_id" IS '项目id';
COMMENT ON COLUMN "lowcode"."sys_sql_log"."table_code" IS '表名';
COMMENT ON COLUMN "lowcode"."sys_sql_log"."sql" IS '脚本';
COMMENT ON COLUMN "lowcode"."sys_sql_log"."op_type" IS '操作类别 [建表，新增字段，修改字段，删除字段，修改表，删除表，清空数据]';
COMMENT ON COLUMN "lowcode"."sys_sql_log"."suc_status" IS '执行状态 [失败，成功]';
COMMENT ON COLUMN "lowcode"."sys_sql_log"."err_mgs" IS '异常信息';
COMMENT ON COLUMN "lowcode"."sys_sql_log"."created_by" IS '创建人';
COMMENT ON COLUMN "lowcode"."sys_sql_log"."created_dt" IS '创建时间';
COMMENT ON COLUMN "lowcode"."sys_sql_log"."updated_by" IS '更新人';
COMMENT ON COLUMN "lowcode"."sys_sql_log"."updated_dt" IS '更新时间';
COMMENT ON COLUMN "lowcode"."sys_sql_log"."table_id" IS '表id';
COMMENT ON COLUMN "lowcode"."sys_sql_log"."deleted" IS '删除状态';
COMMENT ON TABLE "lowcode"."sys_sql_log" IS '脚本日志表';

-- ----------------------------
-- Table structure for sys_table_relation_conf
-- ----------------------------
DROP TABLE IF EXISTS "lowcode"."sys_table_relation_conf";
CREATE TABLE "lowcode"."sys_table_relation_conf" (
  "id" int8 NOT NULL,
  "parent_table" varchar(200) COLLATE "pg_catalog"."default",
  "child_table" varchar(200) COLLATE "pg_catalog"."default",
  "parent_col" varchar(200) COLLATE "pg_catalog"."default",
  "child_col" varchar(200) COLLATE "pg_catalog"."default",
  "query_id" int8 NOT NULL DEFAULT 0
)
;
COMMENT ON COLUMN "lowcode"."sys_table_relation_conf"."id" IS '主键';
COMMENT ON COLUMN "lowcode"."sys_table_relation_conf"."parent_table" IS '父表';
COMMENT ON COLUMN "lowcode"."sys_table_relation_conf"."child_table" IS '子表';
COMMENT ON COLUMN "lowcode"."sys_table_relation_conf"."parent_col" IS '父表字段';
COMMENT ON COLUMN "lowcode"."sys_table_relation_conf"."child_col" IS '子表字段';
COMMENT ON COLUMN "lowcode"."sys_table_relation_conf"."query_id" IS '自定义查询id  ';
COMMENT ON TABLE "lowcode"."sys_table_relation_conf" IS '表关系配置';

-- ----------------------------
-- Table structure for t_item
-- ----------------------------
DROP TABLE IF EXISTS "lowcode"."t_item";
CREATE TABLE "lowcode"."t_item" (
  "item_id" int4 NOT NULL,
  "item_name" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "parent_item_id" int4,
  "project_id" int4,
  "item_type" int2,
  "item_seq" int4,
  "user_id" int4 NOT NULL,
  "active" int2,
  "is_default" int2,
  "creator" varchar(64) COLLATE "pg_catalog"."default",
  "created_dt" timestamp(6),
  "updator" varchar(64) COLLATE "pg_catalog"."default",
  "updated_dt" timestamp(6),
  "task_set_type" varchar(1) COLLATE "pg_catalog"."default",
  "query_type" int2,
  "has_dashboard" int2
)
;
COMMENT ON COLUMN "lowcode"."t_item"."item_id" IS '主键';
COMMENT ON COLUMN "lowcode"."t_item"."item_name" IS '文件夹名称';
COMMENT ON COLUMN "lowcode"."t_item"."parent_item_id" IS '父文件夹Id';
COMMENT ON COLUMN "lowcode"."t_item"."project_id" IS '项目ID';
COMMENT ON COLUMN "lowcode"."t_item"."item_type" IS '文件夹（0文件夹 1项目文件夹 2项目内文件夹 3文件集 5:思维导图 6:数据导图）';
COMMENT ON COLUMN "lowcode"."t_item"."item_seq" IS '文件排序号';
COMMENT ON COLUMN "lowcode"."t_item"."user_id" IS '用户Id';
COMMENT ON COLUMN "lowcode"."t_item"."active" IS '有效性（0无效 1有效 2回收站）';
COMMENT ON COLUMN "lowcode"."t_item"."is_default" IS '是否默认（0否 1是）';
COMMENT ON COLUMN "lowcode"."t_item"."creator" IS '创建人';
COMMENT ON COLUMN "lowcode"."t_item"."created_dt" IS '创建时间';
COMMENT ON COLUMN "lowcode"."t_item"."updator" IS '操作人';
COMMENT ON COLUMN "lowcode"."t_item"."updated_dt" IS '操作时间';
COMMENT ON COLUMN "lowcode"."t_item"."task_set_type" IS '任务集类型（0工作 1问题）';
COMMENT ON COLUMN "lowcode"."t_item"."query_type" IS '应用类型 0-MAX 1-测试任务 2-用例库';
COMMENT ON COLUMN "lowcode"."t_item"."has_dashboard" IS '是否有看板';
COMMENT ON TABLE "lowcode"."t_item" IS '文件夹表';

-- ----------------------------
-- Table structure for t_task_mind_detail
-- ----------------------------
DROP TABLE IF EXISTS "lowcode"."t_task_mind_detail";
CREATE TABLE "lowcode"."t_task_mind_detail" (
  "task_id" int4 NOT NULL,
  "mind_type" int2,
  "api_type" int2,
  "api_url" varchar(500) COLLATE "pg_catalog"."default",
  "task_mind_content" text COLLATE "pg_catalog"."default",
  "create_user" varchar(20) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "update_user" varchar(20) COLLATE "pg_catalog"."default",
  "update_time" timestamp(6)
)
;
COMMENT ON COLUMN "lowcode"."t_task_mind_detail"."task_id" IS '任务ID';
COMMENT ON COLUMN "lowcode"."t_task_mind_detail"."mind_type" IS '导图类型 0-Mind 1-Table 2-API';
COMMENT ON COLUMN "lowcode"."t_task_mind_detail"."api_type" IS '接口类型 0-新增或者编辑 1-查询 2-删除';
COMMENT ON COLUMN "lowcode"."t_task_mind_detail"."api_url" IS '接口地址';
COMMENT ON COLUMN "lowcode"."t_task_mind_detail"."task_mind_content" IS '任务导图内容';
COMMENT ON COLUMN "lowcode"."t_task_mind_detail"."create_user" IS '创建用户';
COMMENT ON COLUMN "lowcode"."t_task_mind_detail"."create_time" IS '创建时间';
COMMENT ON COLUMN "lowcode"."t_task_mind_detail"."update_user" IS '更新用户';
COMMENT ON COLUMN "lowcode"."t_task_mind_detail"."update_time" IS '更新时间';

-- ----------------------------
-- Table structure for u_do_it
-- ----------------------------
DROP TABLE IF EXISTS "lowcode"."u_do_it";
CREATE TABLE "lowcode"."u_do_it" (
  "id" int8 NOT NULL,
  "name" varchar(51) COLLATE "pg_catalog"."default" NOT NULL,
  "sex" int2,
  "birth_date" date,
  "height" numeric(5,2),
  "weight" numeric(5,2),
  "addres" varchar(25) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "lowcode"."u_do_it"."id" IS '主键ID';
COMMENT ON COLUMN "lowcode"."u_do_it"."name" IS '姓名';
COMMENT ON COLUMN "lowcode"."u_do_it"."sex" IS '性别';
COMMENT ON COLUMN "lowcode"."u_do_it"."birth_date" IS '生日';
COMMENT ON COLUMN "lowcode"."u_do_it"."height" IS '身高';
COMMENT ON COLUMN "lowcode"."u_do_it"."weight" IS '体重';
COMMENT ON COLUMN "lowcode"."u_do_it"."addres" IS '住址';
COMMENT ON TABLE "lowcode"."u_do_it" IS '搞个表';

-- ----------------------------
-- Table structure for u_import_column_mapping
-- ----------------------------
DROP TABLE IF EXISTS "lowcode"."u_import_column_mapping";
CREATE TABLE "lowcode"."u_import_column_mapping" (
  "column_mapping_id" int8 NOT NULL,
  "template_column_id" varchar(50) COLLATE "pg_catalog"."default",
  "field_type" varchar(50) COLLATE "pg_catalog"."default",
  "table_name" varchar(50) COLLATE "pg_catalog"."default",
  "filter_field_name" varchar(50) COLLATE "pg_catalog"."default",
  "created_by" varchar(50) COLLATE "pg_catalog"."default",
  "created_dt" varchar(50) COLLATE "pg_catalog"."default",
  "updated_by" varchar(50) COLLATE "pg_catalog"."default",
  "updated_dt" varchar(50) COLLATE "pg_catalog"."default",
  "created_id" varchar(50) COLLATE "pg_catalog"."default",
  "deleted" varchar(50) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "lowcode"."u_import_column_mapping"."column_mapping_id" IS '主键ID';
COMMENT ON COLUMN "lowcode"."u_import_column_mapping"."template_column_id" IS '模板字段表 id 模板字段表 id';
COMMENT ON COLUMN "lowcode"."u_import_column_mapping"."field_type" IS '配置类型 配置类型， 1：字典、 2：表、 3：用户表 、 4：部门';
COMMENT ON COLUMN "lowcode"."u_import_column_mapping"."table_name" IS '表名 表名';
COMMENT ON COLUMN "lowcode"."u_import_column_mapping"."filter_field_name" IS '筛选字段 筛选字段，即 where 条件';
COMMENT ON COLUMN "lowcode"."u_import_column_mapping"."created_by" IS '创建者 创建者';
COMMENT ON COLUMN "lowcode"."u_import_column_mapping"."created_dt" IS '创建时间 创建时间';
COMMENT ON COLUMN "lowcode"."u_import_column_mapping"."updated_by" IS '更新者 更新者';
COMMENT ON COLUMN "lowcode"."u_import_column_mapping"."updated_dt" IS '更新时间 更新时间';
COMMENT ON COLUMN "lowcode"."u_import_column_mapping"."created_id" IS '创建人ID 创建人ID';
COMMENT ON COLUMN "lowcode"."u_import_column_mapping"."deleted" IS '是否删除 是否删除';
COMMENT ON TABLE "lowcode"."u_import_column_mapping" IS '字段映射关系表';

-- ----------------------------
-- Table structure for u_import_column_mapping_list
-- ----------------------------
DROP TABLE IF EXISTS "lowcode"."u_import_column_mapping_list";
CREATE TABLE "lowcode"."u_import_column_mapping_list" (
  "mapping_list_id" int8 NOT NULL,
  "mapping_id" varchar(50) COLLATE "pg_catalog"."default",
  "sourece_field" varchar(50) COLLATE "pg_catalog"."default",
  "target_field" varchar(50) COLLATE "pg_catalog"."default",
  "created_by" varchar(64) COLLATE "pg_catalog"."default",
  "created_dt" timestamp(6),
  "updated_by" varchar(50) COLLATE "pg_catalog"."default",
  "updated_dt" timestamp(6),
  "created_id" int8,
  "deleted" int2
)
;
COMMENT ON COLUMN "lowcode"."u_import_column_mapping_list"."mapping_list_id" IS '主键ID';
COMMENT ON COLUMN "lowcode"."u_import_column_mapping_list"."mapping_id" IS '映射关系 id';
COMMENT ON COLUMN "lowcode"."u_import_column_mapping_list"."sourece_field" IS '原始字段';
COMMENT ON COLUMN "lowcode"."u_import_column_mapping_list"."target_field" IS '目标字段';
COMMENT ON COLUMN "lowcode"."u_import_column_mapping_list"."created_by" IS '创建者';
COMMENT ON COLUMN "lowcode"."u_import_column_mapping_list"."created_dt" IS '创建时间';
COMMENT ON COLUMN "lowcode"."u_import_column_mapping_list"."updated_by" IS '更新者';
COMMENT ON COLUMN "lowcode"."u_import_column_mapping_list"."updated_dt" IS '更新时间';
COMMENT ON COLUMN "lowcode"."u_import_column_mapping_list"."created_id" IS '创建人ID';
COMMENT ON COLUMN "lowcode"."u_import_column_mapping_list"."deleted" IS '是否删除';
COMMENT ON TABLE "lowcode"."u_import_column_mapping_list" IS '规则映射关系字段列表表';

-- ----------------------------
-- Table structure for u_import_template_column
-- ----------------------------
DROP TABLE IF EXISTS "lowcode"."u_import_template_column";
CREATE TABLE "lowcode"."u_import_template_column" (
  "template_table_id" int8 NOT NULL,
  "template_id" int8,
  "table_name" varchar(200) COLLATE "pg_catalog"."default",
  "master" bool,
  "key_name" varchar(128) COLLATE "pg_catalog"."default",
  "created_by" varchar(200) COLLATE "pg_catalog"."default",
  "created_dt" varchar(200) COLLATE "pg_catalog"."default",
  "updated_by" varchar(200) COLLATE "pg_catalog"."default",
  "updated_dt" timestamp(6),
  "created_id" int8,
  "deleted" int2
)
;
COMMENT ON COLUMN "lowcode"."u_import_template_column"."template_table_id" IS '主键ID 主键';
COMMENT ON COLUMN "lowcode"."u_import_template_column"."template_id" IS '模板ID 模板 id';
COMMENT ON COLUMN "lowcode"."u_import_template_column"."table_name" IS '表名称';
COMMENT ON COLUMN "lowcode"."u_import_template_column"."master" IS '主表';
COMMENT ON COLUMN "lowcode"."u_import_template_column"."key_name" IS '主键值';
COMMENT ON COLUMN "lowcode"."u_import_template_column"."created_by" IS '创建者';
COMMENT ON COLUMN "lowcode"."u_import_template_column"."created_dt" IS '创建时间';
COMMENT ON COLUMN "lowcode"."u_import_template_column"."updated_by" IS '更新者';
COMMENT ON COLUMN "lowcode"."u_import_template_column"."updated_dt" IS '更新时间';
COMMENT ON COLUMN "lowcode"."u_import_template_column"."created_id" IS '创建人ID';
COMMENT ON COLUMN "lowcode"."u_import_template_column"."deleted" IS '是否删除';
COMMENT ON TABLE "lowcode"."u_import_template_column" IS '模型关联关系表';

-- ----------------------------
-- Table structure for u_inbou
-- ----------------------------
DROP TABLE IF EXISTS "lowcode"."u_inbou";
CREATE TABLE "lowcode"."u_inbou" (
  "inbound_id" int8 NOT NULL,
  "inbound_code" varchar(100) COLLATE "pg_catalog"."default",
  "inbound_type_dict_id" varchar(50) COLLATE "pg_catalog"."default",
  "inbound_title" varchar(50) COLLATE "pg_catalog"."default",
  "remarks" varchar(50) COLLATE "pg_catalog"."default",
  "director_name" varchar(50) COLLATE "pg_catalog"."default",
  "director_org_name" varchar(50) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "lowcode"."u_inbou"."inbound_id" IS '入库单ID';
COMMENT ON COLUMN "lowcode"."u_inbou"."inbound_code" IS '入库单号';
COMMENT ON COLUMN "lowcode"."u_inbou"."inbound_type_dict_id" IS '入库单类型';
COMMENT ON COLUMN "lowcode"."u_inbou"."inbound_title" IS '入库单主题';
COMMENT ON COLUMN "lowcode"."u_inbou"."remarks" IS '备注';
COMMENT ON COLUMN "lowcode"."u_inbou"."director_name" IS '责任人';
COMMENT ON COLUMN "lowcode"."u_inbou"."director_org_name" IS '责任部门';
COMMENT ON TABLE "lowcode"."u_inbou" IS '入库单';

-- ----------------------------
-- Table structure for u_order
-- ----------------------------
DROP TABLE IF EXISTS "lowcode"."u_order";
CREATE TABLE "lowcode"."u_order" (
  "id" int8 NOT NULL,
  "order_code" varchar(50) COLLATE "pg_catalog"."default",
  "order_status" int2,
  "director_name" varchar(50) COLLATE "pg_catalog"."default",
  "director_org_name" varchar(50) COLLATE "pg_catalog"."default",
  "created_by" varchar(50) COLLATE "pg_catalog"."default",
  "created_dt" timestamp(6),
  "updated_by" varchar(50) COLLATE "pg_catalog"."default",
  "updated_dt" timestamp(6)
)
;
COMMENT ON COLUMN "lowcode"."u_order"."id" IS '主键ID';
COMMENT ON COLUMN "lowcode"."u_order"."order_code" IS '订单编码';
COMMENT ON COLUMN "lowcode"."u_order"."order_status" IS '订单状态';
COMMENT ON COLUMN "lowcode"."u_order"."director_name" IS '责任人';
COMMENT ON COLUMN "lowcode"."u_order"."director_org_name" IS '责任部门';
COMMENT ON COLUMN "lowcode"."u_order"."created_by" IS '创建人';
COMMENT ON COLUMN "lowcode"."u_order"."created_dt" IS '创建时间';
COMMENT ON COLUMN "lowcode"."u_order"."updated_by" IS '更新人';
COMMENT ON COLUMN "lowcode"."u_order"."updated_dt" IS '更新时间';
COMMENT ON TABLE "lowcode"."u_order" IS '订单表';

-- ----------------------------
-- Table structure for u_outbou
-- ----------------------------
DROP TABLE IF EXISTS "lowcode"."u_outbou";
CREATE TABLE "lowcode"."u_outbou" (
  "outbound_id" int8 NOT NULL,
  "outbound_code" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "outbound_type_dict_id" varchar(50) COLLATE "pg_catalog"."default",
  "outbound_title" varchar(255) COLLATE "pg_catalog"."default",
  "remarks" varchar(255) COLLATE "pg_catalog"."default",
  "director_name" varchar(50) COLLATE "pg_catalog"."default",
  "director_org_name" varchar(50) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "lowcode"."u_outbou"."outbound_id" IS '出库单ID';
COMMENT ON COLUMN "lowcode"."u_outbou"."outbound_code" IS '出库单号';
COMMENT ON COLUMN "lowcode"."u_outbou"."outbound_type_dict_id" IS '出库单类型';
COMMENT ON COLUMN "lowcode"."u_outbou"."outbound_title" IS '出库单主题';
COMMENT ON COLUMN "lowcode"."u_outbou"."remarks" IS '备注';
COMMENT ON COLUMN "lowcode"."u_outbou"."director_name" IS '责任人';
COMMENT ON COLUMN "lowcode"."u_outbou"."director_org_name" IS '责任部门';
COMMENT ON TABLE "lowcode"."u_outbou" IS '出库单';

-- ----------------------------
-- Table structure for u_payment
-- ----------------------------
DROP TABLE IF EXISTS "lowcode"."u_payment";
CREATE TABLE "lowcode"."u_payment" (
  "pay_id" int8 NOT NULL,
  "form_type" int2 NOT NULL DEFAULT 1,
  "pay_code" varchar(50) COLLATE "pg_catalog"."default",
  "apply_code" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "recive_apply_id" int8,
  "currency_dict_id" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "currency_dict_name" varchar(50) COLLATE "pg_catalog"."default",
  "customer_id" int8 NOT NULL,
  "pay_total_amt" numeric(18,2) DEFAULT 0,
  "remaining_amt" numeric(18,2) DEFAULT 0,
  "form_dt" timestamp(6),
  "data_status" int2 DEFAULT 0,
  "approved_dt" timestamp(6),
  "approved_by" varchar(50) COLLATE "pg_catalog"."default",
  "in_bill_account_id" int8,
  "in_bill_amt" numeric(18,2) DEFAULT 0,
  "account_name" varchar(50) COLLATE "pg_catalog"."default",
  "discount_amt" numeric(18,2) DEFAULT 0,
  "pay_write_amt" numeric(18,2) DEFAULT 0,
  "bill_write_amt" numeric(18,2) DEFAULT 0,
  "in_bill_type_str" varchar(18) COLLATE "pg_catalog"."default",
  "in_bill_type_list" int4[],
  "bill_list" _int8,
  "advance_pay_list" jsonb,
  "director_id" int8,
  "director_org_id" varchar(12) COLLATE "pg_catalog"."default",
  "created_by" varchar(50) COLLATE "pg_catalog"."default",
  "created_dt" timestamp(6),
  "updated_by" varchar(50) COLLATE "pg_catalog"."default",
  "updated_dt" timestamp(6),
  "tenant_id" int8,
  "remark" varchar(255) COLLATE "pg_catalog"."default",
  "over_amt" numeric(18,2) DEFAULT 0,
  "created_id" int8,
  "version" int4 DEFAULT 0,
  "recive_payment_id" int8
)
;
COMMENT ON COLUMN "lowcode"."u_payment"."pay_id" IS '收款单主键ID';
COMMENT ON COLUMN "lowcode"."u_payment"."form_type" IS '单据类型';
COMMENT ON COLUMN "lowcode"."u_payment"."pay_code" IS '收款单号';
COMMENT ON COLUMN "lowcode"."u_payment"."apply_code" IS '申请单号';
COMMENT ON COLUMN "lowcode"."u_payment"."recive_apply_id" IS '申请主键ID';
COMMENT ON COLUMN "lowcode"."u_payment"."currency_dict_id" IS '币种id';
COMMENT ON COLUMN "lowcode"."u_payment"."currency_dict_name" IS '币种名称';
COMMENT ON COLUMN "lowcode"."u_payment"."customer_id" IS '客户id';
COMMENT ON COLUMN "lowcode"."u_payment"."pay_total_amt" IS '收款总额';
COMMENT ON COLUMN "lowcode"."u_payment"."remaining_amt" IS '剩余金额';
COMMENT ON COLUMN "lowcode"."u_payment"."form_dt" IS '单据时间';
COMMENT ON COLUMN "lowcode"."u_payment"."data_status" IS '审核状态';
COMMENT ON COLUMN "lowcode"."u_payment"."approved_dt" IS '审核时间';
COMMENT ON COLUMN "lowcode"."u_payment"."approved_by" IS '审核人';
COMMENT ON COLUMN "lowcode"."u_payment"."in_bill_account_id" IS '入账账户';
COMMENT ON COLUMN "lowcode"."u_payment"."in_bill_amt" IS '入账金额';
COMMENT ON COLUMN "lowcode"."u_payment"."account_name" IS '账户名称';
COMMENT ON COLUMN "lowcode"."u_payment"."discount_amt" IS '折扣金额';
COMMENT ON COLUMN "lowcode"."u_payment"."pay_write_amt" IS '预收款核销金额';
COMMENT ON COLUMN "lowcode"."u_payment"."bill_write_amt" IS '汇票核销金额';
COMMENT ON COLUMN "lowcode"."u_payment"."in_bill_type_str" IS '入账方式（多选）';
COMMENT ON COLUMN "lowcode"."u_payment"."in_bill_type_list" IS '入账方式集合';
COMMENT ON COLUMN "lowcode"."u_payment"."bill_list" IS '汇票集合';
COMMENT ON COLUMN "lowcode"."u_payment"."advance_pay_list" IS '预收款集合';
COMMENT ON COLUMN "lowcode"."u_payment"."director_id" IS '责任人';
COMMENT ON COLUMN "lowcode"."u_payment"."director_org_id" IS '责任部门';
COMMENT ON COLUMN "lowcode"."u_payment"."created_by" IS '创建人';
COMMENT ON COLUMN "lowcode"."u_payment"."created_dt" IS '创建时间';
COMMENT ON COLUMN "lowcode"."u_payment"."updated_by" IS '更新人';
COMMENT ON COLUMN "lowcode"."u_payment"."updated_dt" IS '更新时间';
COMMENT ON COLUMN "lowcode"."u_payment"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "lowcode"."u_payment"."remark" IS '备注';
COMMENT ON COLUMN "lowcode"."u_payment"."over_amt" IS '超收金额';
COMMENT ON COLUMN "lowcode"."u_payment"."created_id" IS '创建人ID';
COMMENT ON COLUMN "lowcode"."u_payment"."version" IS '版本号';
COMMENT ON COLUMN "lowcode"."u_payment"."recive_payment_id" IS '应付账款主键ID';
COMMENT ON TABLE "lowcode"."u_payment" IS '收款单';

-- ----------------------------
-- Table structure for u_sale_quote_field
-- ----------------------------
DROP TABLE IF EXISTS "lowcode"."u_sale_quote_field";
CREATE TABLE "lowcode"."u_sale_quote_field" (
  "sale_quote_field_id" int8 NOT NULL,
  "sale_quote_detail_id" int8,
  "field_id" int8,
  "field_name" varchar(50) COLLATE "pg_catalog"."default",
  "field_value" varchar(50) COLLATE "pg_catalog"."default",
  "tenant_id" int8,
  "sale_quote_id" int8
)
;
COMMENT ON COLUMN "lowcode"."u_sale_quote_field"."sale_quote_field_id" IS '销售报价字段ID';
COMMENT ON COLUMN "lowcode"."u_sale_quote_field"."sale_quote_detail_id" IS '销售报价明细ID';
COMMENT ON COLUMN "lowcode"."u_sale_quote_field"."field_id" IS '字段ID';
COMMENT ON COLUMN "lowcode"."u_sale_quote_field"."field_name" IS '字段名称';
COMMENT ON COLUMN "lowcode"."u_sale_quote_field"."field_value" IS '字段值';
COMMENT ON COLUMN "lowcode"."u_sale_quote_field"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "lowcode"."u_sale_quote_field"."sale_quote_id" IS '销售报价ID';
COMMENT ON TABLE "lowcode"."u_sale_quote_field" IS '销售报价字段';

-- ----------------------------
-- Table structure for u_test_01
-- ----------------------------
DROP TABLE IF EXISTS "lowcode"."u_test_01";
CREATE TABLE "lowcode"."u_test_01" (
  "id" int8 NOT NULL,
  "product_name" varchar(100) COLLATE "pg_catalog"."default",
  "director_name" varchar(50) COLLATE "pg_catalog"."default",
  "director_org_name" varchar(50) COLLATE "pg_catalog"."default",
  "created_by" varchar(50) COLLATE "pg_catalog"."default",
  "created_dt" timestamp(6),
  "updated_by" varchar(50) COLLATE "pg_catalog"."default",
  "updated_dt" timestamp(6),
  "product_status" int2,
  "num" numeric(16,2)
)
;
COMMENT ON COLUMN "lowcode"."u_test_01"."id" IS '主键ID';
COMMENT ON COLUMN "lowcode"."u_test_01"."product_name" IS '商品名称';
COMMENT ON COLUMN "lowcode"."u_test_01"."director_name" IS '责任人';
COMMENT ON COLUMN "lowcode"."u_test_01"."director_org_name" IS '责任部门';
COMMENT ON COLUMN "lowcode"."u_test_01"."created_by" IS '创建人';
COMMENT ON COLUMN "lowcode"."u_test_01"."created_dt" IS '创建时间';
COMMENT ON COLUMN "lowcode"."u_test_01"."updated_by" IS '更新人';
COMMENT ON COLUMN "lowcode"."u_test_01"."updated_dt" IS '更新时间';
COMMENT ON COLUMN "lowcode"."u_test_01"."product_status" IS '商品状态';
COMMENT ON TABLE "lowcode"."u_test_01" IS 'F-01测试表';

-- ----------------------------
-- Table structure for u_test_demo
-- ----------------------------
DROP TABLE IF EXISTS "lowcode"."u_test_demo";
CREATE TABLE "lowcode"."u_test_demo" (
  "id" int8 NOT NULL,
  "name" varchar(50) COLLATE "pg_catalog"."default",
  "sex" int2,
  "age" int4,
  "addres" varchar(25) COLLATE "pg_catalog"."default",
  "basic_info" jsonb,
  "created_by" varchar(50) COLLATE "pg_catalog"."default",
  "created_dt" timestamp(6),
  "updated_by" varchar(50) COLLATE "pg_catalog"."default",
  "updated_dt" timestamp(6),
  "tenant_id" int8,
  "birth_date" date
)
;
COMMENT ON COLUMN "lowcode"."u_test_demo"."id" IS '主键ID';
COMMENT ON COLUMN "lowcode"."u_test_demo"."name" IS '姓名';
COMMENT ON COLUMN "lowcode"."u_test_demo"."sex" IS '性别';
COMMENT ON COLUMN "lowcode"."u_test_demo"."age" IS '年龄';
COMMENT ON COLUMN "lowcode"."u_test_demo"."addres" IS '住址';
COMMENT ON COLUMN "lowcode"."u_test_demo"."basic_info" IS '基本信息';
COMMENT ON COLUMN "lowcode"."u_test_demo"."created_by" IS '创建人';
COMMENT ON COLUMN "lowcode"."u_test_demo"."created_dt" IS '创建时间';
COMMENT ON COLUMN "lowcode"."u_test_demo"."updated_by" IS '更新人';
COMMENT ON COLUMN "lowcode"."u_test_demo"."updated_dt" IS '更新时间';
COMMENT ON COLUMN "lowcode"."u_test_demo"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "lowcode"."u_test_demo"."birth_date" IS '生日';
COMMENT ON TABLE "lowcode"."u_test_demo" IS '演示表';

-- ----------------------------
-- Table structure for u_test_translation
-- ----------------------------
DROP TABLE IF EXISTS "lowcode"."u_test_translation";
CREATE TABLE "lowcode"."u_test_translation" (
  "test_translation_id" int8 NOT NULL,
  "device_code" varchar(50) COLLATE "pg_catalog"."default",
  "device_name" varchar(100) COLLATE "pg_catalog"."default",
  "product_name" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "device_type" int2 NOT NULL,
  "specification_model" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "device_status" int2 NOT NULL,
  "remarks" varchar(200) COLLATE "pg_catalog"."default" NOT NULL,
  "department" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "document_status" int2 NOT NULL,
  "created_by" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "created_dt" timestamp(6) NOT NULL,
  "updated_by" varchar(5) COLLATE "pg_catalog"."default" NOT NULL,
  "updated_dt" timestamp(6) NOT NULL,
  "auditor" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "audit_time" timestamp(6) NOT NULL,
  "material_code" varchar(50) COLLATE "pg_catalog"."default",
  "scrap_date" date
)
;
COMMENT ON COLUMN "lowcode"."u_test_translation"."test_translation_id" IS '测试翻译主键';
COMMENT ON COLUMN "lowcode"."u_test_translation"."device_code" IS '设备编码';
COMMENT ON COLUMN "lowcode"."u_test_translation"."device_name" IS '设备名称';
COMMENT ON COLUMN "lowcode"."u_test_translation"."product_name" IS '商品名称';
COMMENT ON COLUMN "lowcode"."u_test_translation"."device_type" IS '设备类型';
COMMENT ON COLUMN "lowcode"."u_test_translation"."specification_model" IS '规格型号';
COMMENT ON COLUMN "lowcode"."u_test_translation"."device_status" IS '设备状态';
COMMENT ON COLUMN "lowcode"."u_test_translation"."remarks" IS '备注';
COMMENT ON COLUMN "lowcode"."u_test_translation"."department" IS '所属部门';
COMMENT ON COLUMN "lowcode"."u_test_translation"."document_status" IS '单据状态';
COMMENT ON COLUMN "lowcode"."u_test_translation"."created_by" IS '创建人';
COMMENT ON COLUMN "lowcode"."u_test_translation"."created_dt" IS '创建时间';
COMMENT ON COLUMN "lowcode"."u_test_translation"."updated_by" IS '修改人';
COMMENT ON COLUMN "lowcode"."u_test_translation"."updated_dt" IS '修改时间';
COMMENT ON COLUMN "lowcode"."u_test_translation"."auditor" IS '审核人';
COMMENT ON COLUMN "lowcode"."u_test_translation"."audit_time" IS '审核时间';
COMMENT ON COLUMN "lowcode"."u_test_translation"."material_code" IS '物料编码';
COMMENT ON COLUMN "lowcode"."u_test_translation"."scrap_date" IS '报废日期';
COMMENT ON TABLE "lowcode"."u_test_translation" IS '测试翻译';

-- ----------------------------
-- Table structure for u_testlistify
-- ----------------------------
DROP TABLE IF EXISTS "lowcode"."u_testlistify";
CREATE TABLE "lowcode"."u_testlistify" (
  "id" int8 NOT NULL,
  "name" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "age" int4,
  "sex" int2,
  "device_code" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "device_name" varchar(100) COLLATE "pg_catalog"."default",
  "device_type" int2,
  "spec_model" varchar(5) COLLATE "pg_catalog"."default",
  "device_status" int2,
  "aaaaaaaaaaaaaaaaaaaaaaaaaaaaa" varchar(50) COLLATE "pg_catalog"."default",
  "utility_config_id" int8 NOT NULL,
  "yong_hu_1" varchar(50) COLLATE "pg_catalog"."default",
  "yong_hu_2" varchar(50) COLLATE "pg_catalog"."default",
  "yong_hu_3" varchar(50) COLLATE "pg_catalog"."default",
  "yong_hu_4" varchar(50) COLLATE "pg_catalog"."default",
  "yong_hu_5" varchar(50) COLLATE "pg_catalog"."default",
  "yong_hu_6" varchar(50) COLLATE "pg_catalog"."default",
  "yong_hu_7" varchar(50) COLLATE "pg_catalog"."default",
  "yong_hu_8" varchar(50) COLLATE "pg_catalog"."default",
  "yong_hu_9" varchar(50) COLLATE "pg_catalog"."default",
  "yong_hu_10" varchar(50) COLLATE "pg_catalog"."default",
  "yong_hu_11" varchar(50) COLLATE "pg_catalog"."default",
  "12_yong_hu" varchar(50) COLLATE "pg_catalog"."default",
  "yong_hu_13" varchar(50) COLLATE "pg_catalog"."default",
  "yong_hu_14" varchar(50) COLLATE "pg_catalog"."default",
  "yong_hu_15" varchar(50) COLLATE "pg_catalog"."default",
  "yong_hu_16" varchar(50) COLLATE "pg_catalog"."default",
  "yong_hu_17" varchar(50) COLLATE "pg_catalog"."default",
  "yong_hu_18" varchar(50) COLLATE "pg_catalog"."default",
  "yong_hu_19" varchar(50) COLLATE "pg_catalog"."default",
  "yong_hu_20" varchar(50) COLLATE "pg_catalog"."default",
  "yong_hu_21" varchar(50) COLLATE "pg_catalog"."default",
  "yong_hu_22" varchar(50) COLLATE "pg_catalog"."default",
  "yong_hu_23" varchar(50) COLLATE "pg_catalog"."default",
  "yong_hu_24" varchar(50) COLLATE "pg_catalog"."default",
  "yong_hu_25" varchar(50) COLLATE "pg_catalog"."default",
  "yong_hu_26" varchar(50) COLLATE "pg_catalog"."default",
  "yong_hu_27" varchar(50) COLLATE "pg_catalog"."default",
  "yong_hu_28" varchar(50) COLLATE "pg_catalog"."default",
  "yong_hu_29" varchar(50) COLLATE "pg_catalog"."default",
  "yong_hu_30" varchar(50) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "lowcode"."u_testlistify"."id" IS '主键ID';
COMMENT ON COLUMN "lowcode"."u_testlistify"."name" IS '姓名';
COMMENT ON COLUMN "lowcode"."u_testlistify"."age" IS '年龄';
COMMENT ON COLUMN "lowcode"."u_testlistify"."sex" IS '性别';
COMMENT ON COLUMN "lowcode"."u_testlistify"."device_code" IS '设备编码';
COMMENT ON COLUMN "lowcode"."u_testlistify"."device_name" IS '设备名称';
COMMENT ON COLUMN "lowcode"."u_testlistify"."device_type" IS '设备类型';
COMMENT ON COLUMN "lowcode"."u_testlistify"."spec_model" IS '规格型号';
COMMENT ON COLUMN "lowcode"."u_testlistify"."device_status" IS '设备状态';
COMMENT ON COLUMN "lowcode"."u_testlistify"."aaaaaaaaaaaaaaaaaaaaaaaaaaaaa" IS '中文';
COMMENT ON COLUMN "lowcode"."u_testlistify"."yong_hu_1" IS '用户1';
COMMENT ON COLUMN "lowcode"."u_testlistify"."yong_hu_2" IS '用户2';
COMMENT ON COLUMN "lowcode"."u_testlistify"."yong_hu_3" IS '用户3';
COMMENT ON COLUMN "lowcode"."u_testlistify"."yong_hu_4" IS '用户4';
COMMENT ON COLUMN "lowcode"."u_testlistify"."yong_hu_5" IS '用户5';
COMMENT ON COLUMN "lowcode"."u_testlistify"."yong_hu_6" IS '用户6';
COMMENT ON COLUMN "lowcode"."u_testlistify"."yong_hu_7" IS '用户7';
COMMENT ON COLUMN "lowcode"."u_testlistify"."yong_hu_8" IS '用户8';
COMMENT ON COLUMN "lowcode"."u_testlistify"."yong_hu_9" IS '用户9';
COMMENT ON COLUMN "lowcode"."u_testlistify"."yong_hu_10" IS '用户10';
COMMENT ON COLUMN "lowcode"."u_testlistify"."yong_hu_11" IS '用户11';
COMMENT ON COLUMN "lowcode"."u_testlistify"."12_yong_hu" IS '12用户';
COMMENT ON COLUMN "lowcode"."u_testlistify"."yong_hu_13" IS '用户13';
COMMENT ON COLUMN "lowcode"."u_testlistify"."yong_hu_14" IS '用户14';
COMMENT ON COLUMN "lowcode"."u_testlistify"."yong_hu_15" IS '用户15';
COMMENT ON COLUMN "lowcode"."u_testlistify"."yong_hu_16" IS '用户16';
COMMENT ON COLUMN "lowcode"."u_testlistify"."yong_hu_17" IS '用户17';
COMMENT ON COLUMN "lowcode"."u_testlistify"."yong_hu_18" IS '用户18';
COMMENT ON COLUMN "lowcode"."u_testlistify"."yong_hu_19" IS '用户19';
COMMENT ON COLUMN "lowcode"."u_testlistify"."yong_hu_20" IS '用户20';
COMMENT ON COLUMN "lowcode"."u_testlistify"."yong_hu_21" IS '用户21';
COMMENT ON COLUMN "lowcode"."u_testlistify"."yong_hu_22" IS '用户22';
COMMENT ON COLUMN "lowcode"."u_testlistify"."yong_hu_23" IS '用户23';
COMMENT ON COLUMN "lowcode"."u_testlistify"."yong_hu_24" IS '用户24';
COMMENT ON COLUMN "lowcode"."u_testlistify"."yong_hu_25" IS '用户25';
COMMENT ON COLUMN "lowcode"."u_testlistify"."yong_hu_26" IS '用户26';
COMMENT ON COLUMN "lowcode"."u_testlistify"."yong_hu_27" IS '用户27';
COMMENT ON COLUMN "lowcode"."u_testlistify"."yong_hu_28" IS '用户28';
COMMENT ON COLUMN "lowcode"."u_testlistify"."yong_hu_29" IS '用户29';
COMMENT ON COLUMN "lowcode"."u_testlistify"."yong_hu_30" IS '用户30';
COMMENT ON TABLE "lowcode"."u_testlistify" IS 'testestset';

-- ----------------------------
-- Table structure for u_testt
-- ----------------------------
DROP TABLE IF EXISTS "lowcode"."u_testt";
CREATE TABLE "lowcode"."u_testt" (
  "id" int8 NOT NULL,
  "device_name" varchar(50) COLLATE "pg_catalog"."default",
  "created_by" varchar(50) COLLATE "pg_catalog"."default",
  "created_dt" timestamp(6),
  "updated_by" varchar(50) COLLATE "pg_catalog"."default",
  "updated_dt" timestamp(6),
  "tenant_id" int8,
  "device_code" varchar(50) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "lowcode"."u_testt"."id" IS '主键ID';
COMMENT ON COLUMN "lowcode"."u_testt"."device_name" IS '设备名称';
COMMENT ON COLUMN "lowcode"."u_testt"."created_by" IS '创建人';
COMMENT ON COLUMN "lowcode"."u_testt"."created_dt" IS '创建时间';
COMMENT ON COLUMN "lowcode"."u_testt"."updated_by" IS '更新人';
COMMENT ON COLUMN "lowcode"."u_testt"."updated_dt" IS '更新时间';
COMMENT ON COLUMN "lowcode"."u_testt"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "lowcode"."u_testt"."device_code" IS '设备编号';
COMMENT ON TABLE "lowcode"."u_testt" IS '测试初始化字段';

-- ----------------------------
-- Table structure for u_user
-- ----------------------------
DROP TABLE IF EXISTS "lowcode"."u_user";
CREATE TABLE "lowcode"."u_user" (
  "id" int8 NOT NULL,
  "name" varchar(100) COLLATE "pg_catalog"."default",
  "sex" int2,
  "id_typpe" int2,
  "id_num" varchar(100) COLLATE "pg_catalog"."default",
  "address" varchar(100) COLLATE "pg_catalog"."default",
  "director_name" varchar(50) COLLATE "pg_catalog"."default",
  "director_org_name" varchar(50) COLLATE "pg_catalog"."default",
  "created_by" varchar(50) COLLATE "pg_catalog"."default",
  "created_dt" timestamp(6),
  "updated_by" varchar(50) COLLATE "pg_catalog"."default",
  "updated_dt" timestamp(6)
)
;
COMMENT ON COLUMN "lowcode"."u_user"."id" IS '主键ID';
COMMENT ON COLUMN "lowcode"."u_user"."name" IS '姓名';
COMMENT ON COLUMN "lowcode"."u_user"."sex" IS '性别';
COMMENT ON COLUMN "lowcode"."u_user"."id_typpe" IS '证件类型';
COMMENT ON COLUMN "lowcode"."u_user"."id_num" IS '证件号码';
COMMENT ON COLUMN "lowcode"."u_user"."address" IS '地址';
COMMENT ON COLUMN "lowcode"."u_user"."director_name" IS '责任人';
COMMENT ON COLUMN "lowcode"."u_user"."director_org_name" IS '责任部门';
COMMENT ON COLUMN "lowcode"."u_user"."created_by" IS '创建人';
COMMENT ON COLUMN "lowcode"."u_user"."created_dt" IS '创建时间';
COMMENT ON COLUMN "lowcode"."u_user"."updated_by" IS '更新人';
COMMENT ON COLUMN "lowcode"."u_user"."updated_dt" IS '更新时间';
COMMENT ON TABLE "lowcode"."u_user" IS '人员表';

-- ----------------------------
-- Table structure for u_utility_config
-- ----------------------------
DROP TABLE IF EXISTS "lowcode"."u_utility_config";
CREATE TABLE "lowcode"."u_utility_config" (
  "utility_config_id" int8 NOT NULL,
  "cost_price" numeric(12,2),
  "utility_name" varchar(50) COLLATE "pg_catalog"."default",
  "director_id" int8,
  "director_org_id" varchar(50) COLLATE "pg_catalog"."default",
  "updated_by" varchar(50) COLLATE "pg_catalog"."default",
  "created_by" varchar(50) COLLATE "pg_catalog"."default",
  "created_dt" timestamp(6),
  "updated_dt" timestamp(6),
  "tenant_id" int8,
  "created_id" int8,
  "age" int4
)
;
COMMENT ON COLUMN "lowcode"."u_utility_config"."utility_config_id" IS '水电气表id 水电气表id';
COMMENT ON COLUMN "lowcode"."u_utility_config"."cost_price" IS '费用单价 费用单价';
COMMENT ON COLUMN "lowcode"."u_utility_config"."utility_name" IS '表名称 表名称';
COMMENT ON COLUMN "lowcode"."u_utility_config"."director_id" IS '责任人 责任人';
COMMENT ON COLUMN "lowcode"."u_utility_config"."director_org_id" IS '责任部门 责任部门';
COMMENT ON COLUMN "lowcode"."u_utility_config"."updated_by" IS '更新人 更新人';
COMMENT ON COLUMN "lowcode"."u_utility_config"."created_by" IS '创建人 创建人';
COMMENT ON COLUMN "lowcode"."u_utility_config"."created_dt" IS '创建时间 创建时间';
COMMENT ON COLUMN "lowcode"."u_utility_config"."updated_dt" IS '更新时间 更新时间';
COMMENT ON COLUMN "lowcode"."u_utility_config"."tenant_id" IS '租户ID 租户ID';
COMMENT ON COLUMN "lowcode"."u_utility_config"."created_id" IS '创建人ID 创建人ID';
COMMENT ON COLUMN "lowcode"."u_utility_config"."age" IS '年龄';
COMMENT ON TABLE "lowcode"."u_utility_config" IS '水电气费用标准';

-- ----------------------------
-- Function structure for gin_extract_query_trgm
-- ----------------------------
DROP FUNCTION IF EXISTS "lowcode"."gin_extract_query_trgm"(text, internal, int2, internal, internal, internal, internal);
CREATE OR REPLACE FUNCTION "lowcode"."gin_extract_query_trgm"(text, internal, int2, internal, internal, internal, internal)
  RETURNS "pg_catalog"."internal" AS '$libdir/pg_trgm', 'gin_extract_query_trgm'
  LANGUAGE c IMMUTABLE STRICT
  COST 1;

-- ----------------------------
-- Function structure for gin_extract_value_trgm
-- ----------------------------
DROP FUNCTION IF EXISTS "lowcode"."gin_extract_value_trgm"(text, internal);
CREATE OR REPLACE FUNCTION "lowcode"."gin_extract_value_trgm"(text, internal)
  RETURNS "pg_catalog"."internal" AS '$libdir/pg_trgm', 'gin_extract_value_trgm'
  LANGUAGE c IMMUTABLE STRICT
  COST 1;

-- ----------------------------
-- Function structure for gin_trgm_consistent
-- ----------------------------
DROP FUNCTION IF EXISTS "lowcode"."gin_trgm_consistent"(internal, int2, text, int4, internal, internal, internal, internal);
CREATE OR REPLACE FUNCTION "lowcode"."gin_trgm_consistent"(internal, int2, text, int4, internal, internal, internal, internal)
  RETURNS "pg_catalog"."bool" AS '$libdir/pg_trgm', 'gin_trgm_consistent'
  LANGUAGE c IMMUTABLE STRICT
  COST 1;

-- ----------------------------
-- Function structure for gin_trgm_triconsistent
-- ----------------------------
DROP FUNCTION IF EXISTS "lowcode"."gin_trgm_triconsistent"(internal, int2, text, int4, internal, internal, internal);
CREATE OR REPLACE FUNCTION "lowcode"."gin_trgm_triconsistent"(internal, int2, text, int4, internal, internal, internal)
  RETURNS "pg_catalog"."char" AS '$libdir/pg_trgm', 'gin_trgm_triconsistent'
  LANGUAGE c IMMUTABLE STRICT
  COST 1;

-- ----------------------------
-- Function structure for gtrgm_compress
-- ----------------------------
DROP FUNCTION IF EXISTS "lowcode"."gtrgm_compress"(internal);
CREATE OR REPLACE FUNCTION "lowcode"."gtrgm_compress"(internal)
  RETURNS "pg_catalog"."internal" AS '$libdir/pg_trgm', 'gtrgm_compress'
  LANGUAGE c IMMUTABLE STRICT
  COST 1;

-- ----------------------------
-- Function structure for gtrgm_consistent
-- ----------------------------
DROP FUNCTION IF EXISTS "lowcode"."gtrgm_consistent"(internal, text, int2, oid, internal);
CREATE OR REPLACE FUNCTION "lowcode"."gtrgm_consistent"(internal, text, int2, oid, internal)
  RETURNS "pg_catalog"."bool" AS '$libdir/pg_trgm', 'gtrgm_consistent'
  LANGUAGE c IMMUTABLE STRICT
  COST 1;

-- ----------------------------
-- Function structure for gtrgm_decompress
-- ----------------------------
DROP FUNCTION IF EXISTS "lowcode"."gtrgm_decompress"(internal);
CREATE OR REPLACE FUNCTION "lowcode"."gtrgm_decompress"(internal)
  RETURNS "pg_catalog"."internal" AS '$libdir/pg_trgm', 'gtrgm_decompress'
  LANGUAGE c IMMUTABLE STRICT
  COST 1;

-- ----------------------------
-- Function structure for gtrgm_distance
-- ----------------------------
DROP FUNCTION IF EXISTS "lowcode"."gtrgm_distance"(internal, text, int2, oid, internal);
CREATE OR REPLACE FUNCTION "lowcode"."gtrgm_distance"(internal, text, int2, oid, internal)
  RETURNS "pg_catalog"."float8" AS '$libdir/pg_trgm', 'gtrgm_distance'
  LANGUAGE c IMMUTABLE STRICT
  COST 1;

-- ----------------------------
-- Function structure for gtrgm_in
-- ----------------------------
DROP FUNCTION IF EXISTS "lowcode"."gtrgm_in"(cstring);
CREATE OR REPLACE FUNCTION "lowcode"."gtrgm_in"(cstring)
  RETURNS "lowcode"."gtrgm" AS '$libdir/pg_trgm', 'gtrgm_in'
  LANGUAGE c IMMUTABLE STRICT
  COST 1;

-- ----------------------------
-- Function structure for gtrgm_options
-- ----------------------------
DROP FUNCTION IF EXISTS "lowcode"."gtrgm_options"(internal);
CREATE OR REPLACE FUNCTION "lowcode"."gtrgm_options"(internal)
  RETURNS "pg_catalog"."void" AS '$libdir/pg_trgm', 'gtrgm_options'
  LANGUAGE c IMMUTABLE
  COST 1;

-- ----------------------------
-- Function structure for gtrgm_out
-- ----------------------------
DROP FUNCTION IF EXISTS "lowcode"."gtrgm_out"("lowcode"."gtrgm");
CREATE OR REPLACE FUNCTION "lowcode"."gtrgm_out"("lowcode"."gtrgm")
  RETURNS "pg_catalog"."cstring" AS '$libdir/pg_trgm', 'gtrgm_out'
  LANGUAGE c IMMUTABLE STRICT
  COST 1;

-- ----------------------------
-- Function structure for gtrgm_penalty
-- ----------------------------
DROP FUNCTION IF EXISTS "lowcode"."gtrgm_penalty"(internal, internal, internal);
CREATE OR REPLACE FUNCTION "lowcode"."gtrgm_penalty"(internal, internal, internal)
  RETURNS "pg_catalog"."internal" AS '$libdir/pg_trgm', 'gtrgm_penalty'
  LANGUAGE c IMMUTABLE STRICT
  COST 1;

-- ----------------------------
-- Function structure for gtrgm_picksplit
-- ----------------------------
DROP FUNCTION IF EXISTS "lowcode"."gtrgm_picksplit"(internal, internal);
CREATE OR REPLACE FUNCTION "lowcode"."gtrgm_picksplit"(internal, internal)
  RETURNS "pg_catalog"."internal" AS '$libdir/pg_trgm', 'gtrgm_picksplit'
  LANGUAGE c IMMUTABLE STRICT
  COST 1;

-- ----------------------------
-- Function structure for gtrgm_same
-- ----------------------------
DROP FUNCTION IF EXISTS "lowcode"."gtrgm_same"("lowcode"."gtrgm", "lowcode"."gtrgm", internal);
CREATE OR REPLACE FUNCTION "lowcode"."gtrgm_same"("lowcode"."gtrgm", "lowcode"."gtrgm", internal)
  RETURNS "pg_catalog"."internal" AS '$libdir/pg_trgm', 'gtrgm_same'
  LANGUAGE c IMMUTABLE STRICT
  COST 1;

-- ----------------------------
-- Function structure for gtrgm_union
-- ----------------------------
DROP FUNCTION IF EXISTS "lowcode"."gtrgm_union"(internal, internal);
CREATE OR REPLACE FUNCTION "lowcode"."gtrgm_union"(internal, internal)
  RETURNS "lowcode"."gtrgm" AS '$libdir/pg_trgm', 'gtrgm_union'
  LANGUAGE c IMMUTABLE STRICT
  COST 1;

-- ----------------------------
-- Function structure for set_limit
-- ----------------------------
DROP FUNCTION IF EXISTS "lowcode"."set_limit"(float4);
CREATE OR REPLACE FUNCTION "lowcode"."set_limit"(float4)
  RETURNS "pg_catalog"."float4" AS '$libdir/pg_trgm', 'set_limit'
  LANGUAGE c VOLATILE STRICT
  COST 1;

-- ----------------------------
-- Function structure for show_limit
-- ----------------------------
DROP FUNCTION IF EXISTS "lowcode"."show_limit"();
CREATE OR REPLACE FUNCTION "lowcode"."show_limit"()
  RETURNS "pg_catalog"."float4" AS '$libdir/pg_trgm', 'show_limit'
  LANGUAGE c STABLE STRICT
  COST 1;

-- ----------------------------
-- Function structure for show_trgm
-- ----------------------------
DROP FUNCTION IF EXISTS "lowcode"."show_trgm"(text);
CREATE OR REPLACE FUNCTION "lowcode"."show_trgm"(text)
  RETURNS "pg_catalog"."_text" AS '$libdir/pg_trgm', 'show_trgm'
  LANGUAGE c IMMUTABLE STRICT
  COST 1;

-- ----------------------------
-- Function structure for similarity
-- ----------------------------
DROP FUNCTION IF EXISTS "lowcode"."similarity"(text, text);
CREATE OR REPLACE FUNCTION "lowcode"."similarity"(text, text)
  RETURNS "pg_catalog"."float4" AS '$libdir/pg_trgm', 'similarity'
  LANGUAGE c IMMUTABLE STRICT
  COST 1;

-- ----------------------------
-- Function structure for similarity_dist
-- ----------------------------
DROP FUNCTION IF EXISTS "lowcode"."similarity_dist"(text, text);
CREATE OR REPLACE FUNCTION "lowcode"."similarity_dist"(text, text)
  RETURNS "pg_catalog"."float4" AS '$libdir/pg_trgm', 'similarity_dist'
  LANGUAGE c IMMUTABLE STRICT
  COST 1;

-- ----------------------------
-- Function structure for similarity_op
-- ----------------------------
DROP FUNCTION IF EXISTS "lowcode"."similarity_op"(text, text);
CREATE OR REPLACE FUNCTION "lowcode"."similarity_op"(text, text)
  RETURNS "pg_catalog"."bool" AS '$libdir/pg_trgm', 'similarity_op'
  LANGUAGE c STABLE STRICT
  COST 1;

-- ----------------------------
-- Function structure for snake_to_camel
-- ----------------------------
DROP FUNCTION IF EXISTS "lowcode"."snake_to_camel"("snake_case" text);
CREATE OR REPLACE FUNCTION "lowcode"."snake_to_camel"("snake_case" text)
  RETURNS "pg_catalog"."text" AS $BODY$
DECLARE
    result TEXT := '';
    word TEXT;
BEGIN
    -- 将字符串按下划线分割并逐个处理
    FOREACH word IN ARRAY regexp_split_to_array(snake_case, '_') LOOP
        IF result = '' THEN
            result := lower(word);  -- 保持第一个单词小写
        ELSE
            result := result || initcap(word);  -- 其余单词首字母大写
        END IF;
    END LOOP;
    RETURN result;
END;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100;

-- ----------------------------
-- Function structure for strict_word_similarity
-- ----------------------------
DROP FUNCTION IF EXISTS "lowcode"."strict_word_similarity"(text, text);
CREATE OR REPLACE FUNCTION "lowcode"."strict_word_similarity"(text, text)
  RETURNS "pg_catalog"."float4" AS '$libdir/pg_trgm', 'strict_word_similarity'
  LANGUAGE c IMMUTABLE STRICT
  COST 1;

-- ----------------------------
-- Function structure for strict_word_similarity_commutator_op
-- ----------------------------
DROP FUNCTION IF EXISTS "lowcode"."strict_word_similarity_commutator_op"(text, text);
CREATE OR REPLACE FUNCTION "lowcode"."strict_word_similarity_commutator_op"(text, text)
  RETURNS "pg_catalog"."bool" AS '$libdir/pg_trgm', 'strict_word_similarity_commutator_op'
  LANGUAGE c STABLE STRICT
  COST 1;

-- ----------------------------
-- Function structure for strict_word_similarity_dist_commutator_op
-- ----------------------------
DROP FUNCTION IF EXISTS "lowcode"."strict_word_similarity_dist_commutator_op"(text, text);
CREATE OR REPLACE FUNCTION "lowcode"."strict_word_similarity_dist_commutator_op"(text, text)
  RETURNS "pg_catalog"."float4" AS '$libdir/pg_trgm', 'strict_word_similarity_dist_commutator_op'
  LANGUAGE c IMMUTABLE STRICT
  COST 1;

-- ----------------------------
-- Function structure for strict_word_similarity_dist_op
-- ----------------------------
DROP FUNCTION IF EXISTS "lowcode"."strict_word_similarity_dist_op"(text, text);
CREATE OR REPLACE FUNCTION "lowcode"."strict_word_similarity_dist_op"(text, text)
  RETURNS "pg_catalog"."float4" AS '$libdir/pg_trgm', 'strict_word_similarity_dist_op'
  LANGUAGE c IMMUTABLE STRICT
  COST 1;

-- ----------------------------
-- Function structure for strict_word_similarity_op
-- ----------------------------
DROP FUNCTION IF EXISTS "lowcode"."strict_word_similarity_op"(text, text);
CREATE OR REPLACE FUNCTION "lowcode"."strict_word_similarity_op"(text, text)
  RETURNS "pg_catalog"."bool" AS '$libdir/pg_trgm', 'strict_word_similarity_op'
  LANGUAGE c STABLE STRICT
  COST 1;

-- ----------------------------
-- Function structure for sync_zhprs_custom_word
-- ----------------------------
DROP FUNCTION IF EXISTS "lowcode"."sync_zhprs_custom_word"();
CREATE OR REPLACE FUNCTION "lowcode"."sync_zhprs_custom_word"()
  RETURNS "pg_catalog"."void" AS $BODY$
declare
	data_dir text;
	dict_path text;
	time_tag_path text;
	query text;
begin
	select setting from pg_settings where name='data_directory' into data_dir;

	select data_dir || '/base' || '/zhprs_dict_' || current_database() || '.txt' into dict_path;
	select data_dir || '/base' || '/zhprs_dict_' || current_database() || '.tag' into time_tag_path;

	query = 'copy (select word, tf, idf, attr from zhparser.zhprs_custom_word) to ' || chr(39) || dict_path || chr(39) || ' encoding ' || chr(39) || 'utf8' || chr(39) ;
	execute query;
	query = 'copy (select now()) to ' || chr(39) || time_tag_path || chr(39) ;
	execute query;
end;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100;

-- ----------------------------
-- Function structure for word_similarity
-- ----------------------------
DROP FUNCTION IF EXISTS "lowcode"."word_similarity"(text, text);
CREATE OR REPLACE FUNCTION "lowcode"."word_similarity"(text, text)
  RETURNS "pg_catalog"."float4" AS '$libdir/pg_trgm', 'word_similarity'
  LANGUAGE c IMMUTABLE STRICT
  COST 1;

-- ----------------------------
-- Function structure for word_similarity_commutator_op
-- ----------------------------
DROP FUNCTION IF EXISTS "lowcode"."word_similarity_commutator_op"(text, text);
CREATE OR REPLACE FUNCTION "lowcode"."word_similarity_commutator_op"(text, text)
  RETURNS "pg_catalog"."bool" AS '$libdir/pg_trgm', 'word_similarity_commutator_op'
  LANGUAGE c STABLE STRICT
  COST 1;

-- ----------------------------
-- Function structure for word_similarity_dist_commutator_op
-- ----------------------------
DROP FUNCTION IF EXISTS "lowcode"."word_similarity_dist_commutator_op"(text, text);
CREATE OR REPLACE FUNCTION "lowcode"."word_similarity_dist_commutator_op"(text, text)
  RETURNS "pg_catalog"."float4" AS '$libdir/pg_trgm', 'word_similarity_dist_commutator_op'
  LANGUAGE c IMMUTABLE STRICT
  COST 1;

-- ----------------------------
-- Function structure for word_similarity_dist_op
-- ----------------------------
DROP FUNCTION IF EXISTS "lowcode"."word_similarity_dist_op"(text, text);
CREATE OR REPLACE FUNCTION "lowcode"."word_similarity_dist_op"(text, text)
  RETURNS "pg_catalog"."float4" AS '$libdir/pg_trgm', 'word_similarity_dist_op'
  LANGUAGE c IMMUTABLE STRICT
  COST 1;

-- ----------------------------
-- Function structure for word_similarity_op
-- ----------------------------
DROP FUNCTION IF EXISTS "lowcode"."word_similarity_op"(text, text);
CREATE OR REPLACE FUNCTION "lowcode"."word_similarity_op"(text, text)
  RETURNS "pg_catalog"."bool" AS '$libdir/pg_trgm', 'word_similarity_op'
  LANGUAGE c STABLE STRICT
  COST 1;

-- ----------------------------
-- Function structure for zhprs_end
-- ----------------------------
DROP FUNCTION IF EXISTS "lowcode"."zhprs_end"(internal);
CREATE OR REPLACE FUNCTION "lowcode"."zhprs_end"(internal)
  RETURNS "pg_catalog"."void" AS '$libdir/zhparser', 'zhprs_end'
  LANGUAGE c VOLATILE STRICT
  COST 1;

-- ----------------------------
-- Function structure for zhprs_getlexeme
-- ----------------------------
DROP FUNCTION IF EXISTS "lowcode"."zhprs_getlexeme"(internal, internal, internal);
CREATE OR REPLACE FUNCTION "lowcode"."zhprs_getlexeme"(internal, internal, internal)
  RETURNS "pg_catalog"."internal" AS '$libdir/zhparser', 'zhprs_getlexeme'
  LANGUAGE c VOLATILE STRICT
  COST 1;

-- ----------------------------
-- Function structure for zhprs_lextype
-- ----------------------------
DROP FUNCTION IF EXISTS "lowcode"."zhprs_lextype"(internal);
CREATE OR REPLACE FUNCTION "lowcode"."zhprs_lextype"(internal)
  RETURNS "pg_catalog"."internal" AS '$libdir/zhparser', 'zhprs_lextype'
  LANGUAGE c VOLATILE STRICT
  COST 1;

-- ----------------------------
-- Function structure for zhprs_start
-- ----------------------------
DROP FUNCTION IF EXISTS "lowcode"."zhprs_start"(internal, int4);
CREATE OR REPLACE FUNCTION "lowcode"."zhprs_start"(internal, int4)
  RETURNS "pg_catalog"."internal" AS '$libdir/zhparser', 'zhprs_start'
  LANGUAGE c VOLATILE STRICT
  COST 1;

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
SELECT setval('"lowcode"."a_codegen_data_source_config_seq"', 20, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "lowcode"."s_properties_prop_pk_id_seq"
OWNED BY "lowcode"."s_properties"."prop_pk_id";
SELECT setval('"lowcode"."s_properties_prop_pk_id_seq"', 1, false);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "lowcode"."sys_model_function_history_data_id_seq"
OWNED BY "lowcode"."sys_model_function_history"."data_id";
SELECT setval('"lowcode"."sys_model_function_history_data_id_seq"', 1, false);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "lowcode"."sys_model_function_version_id_seq"
OWNED BY "lowcode"."sys_model_function_version"."id";
SELECT setval('"lowcode"."sys_model_function_version_id_seq"', 1, false);

-- ----------------------------
-- Primary Key structure for table a_codegen_column
-- ----------------------------
ALTER TABLE "lowcode"."a_codegen_column" ADD CONSTRAINT "a_codegen_column_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table a_codegen_data_source_config
-- ----------------------------
ALTER TABLE "lowcode"."a_codegen_data_source_config" ADD CONSTRAINT "a_codegen_data_source_config_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table a_codegen_table
-- ----------------------------
ALTER TABLE "lowcode"."a_codegen_table" ADD CONSTRAINT "a_codegen_table_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table s_auth_role
-- ----------------------------
ALTER TABLE "lowcode"."s_auth_role" ADD CONSTRAINT "s_auth_role_pkey" PRIMARY KEY ("role_id");

-- ----------------------------
-- Primary Key structure for table s_auth_user
-- ----------------------------
ALTER TABLE "lowcode"."s_auth_user" ADD CONSTRAINT "s_auth_user_pkey" PRIMARY KEY ("user_id");

-- ----------------------------
-- Primary Key structure for table s_auth_user_passport
-- ----------------------------
ALTER TABLE "lowcode"."s_auth_user_passport" ADD CONSTRAINT "s_auth_user_passport_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table s_auth_user_role
-- ----------------------------
ALTER TABLE "lowcode"."s_auth_user_role" ADD CONSTRAINT "s_auth_user_role_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table s_file_log
-- ----------------------------
ALTER TABLE "lowcode"."s_file_log" ADD CONSTRAINT "s_file_log_pkey" PRIMARY KEY ("file_id");

-- ----------------------------
-- Primary Key structure for table s_properties
-- ----------------------------
ALTER TABLE "lowcode"."s_properties" ADD CONSTRAINT "s_properties_pkey" PRIMARY KEY ("prop_pk_id");

-- ----------------------------
-- Primary Key structure for table sys_api_case_info
-- ----------------------------
ALTER TABLE "lowcode"."sys_api_case_info" ADD CONSTRAINT "pk_case_info" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table sys_item
-- ----------------------------
CREATE INDEX "idx_item_name" ON "lowcode"."sys_item" USING btree (
  "item_name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_item_project_id" ON "lowcode"."sys_item" USING btree (
  "project_id" "pg_catalog"."int8_ops" ASC NULLS LAST
);
CREATE INDEX "idx_item_type" ON "lowcode"."sys_item" USING btree (
  "item_type" "pg_catalog"."int2_ops" ASC NULLS LAST
);
CREATE INDEX "idx_item_user_id" ON "lowcode"."sys_item" USING btree (
  "user_id" "pg_catalog"."int8_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table sys_item
-- ----------------------------
ALTER TABLE "lowcode"."sys_item" ADD CONSTRAINT "sys_item_pkey" PRIMARY KEY ("item_id");

-- ----------------------------
-- Indexes structure for table sys_item_test
-- ----------------------------
CREATE INDEX "idx_item_name_copy1" ON "lowcode"."sys_item_test" USING btree (
  "item_name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_item_project_id_copy1" ON "lowcode"."sys_item_test" USING btree (
  "project_id" "pg_catalog"."int8_ops" ASC NULLS LAST
);
CREATE INDEX "idx_item_type_copy1" ON "lowcode"."sys_item_test" USING btree (
  "item_type" "pg_catalog"."int2_ops" ASC NULLS LAST
);
CREATE INDEX "idx_item_user_id_copy1" ON "lowcode"."sys_item_test" USING btree (
  "user_id" "pg_catalog"."int8_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table sys_item_test
-- ----------------------------
ALTER TABLE "lowcode"."sys_item_test" ADD CONSTRAINT "sys_item_copy1_pkey" PRIMARY KEY ("item_id");

-- ----------------------------
-- Primary Key structure for table sys_model_field
-- ----------------------------
ALTER TABLE "lowcode"."sys_model_field" ADD CONSTRAINT "sys_model_field_pkey" PRIMARY KEY ("field_id");

-- ----------------------------
-- Primary Key structure for table sys_model_field_conf
-- ----------------------------
ALTER TABLE "lowcode"."sys_model_field_conf" ADD CONSTRAINT "sys_model_field_copy1_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table sys_model_field_json
-- ----------------------------
ALTER TABLE "lowcode"."sys_model_field_json" ADD CONSTRAINT "sys_model_field_json_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table sys_model_function
-- ----------------------------
ALTER TABLE "lowcode"."sys_model_function" ADD CONSTRAINT "sys_model_function_pkey" PRIMARY KEY ("fun_id");

-- ----------------------------
-- Primary Key structure for table sys_model_function_history
-- ----------------------------
ALTER TABLE "lowcode"."sys_model_function_history" ADD CONSTRAINT "sys_model_function_history_pkey" PRIMARY KEY ("data_id");

-- ----------------------------
-- Primary Key structure for table sys_model_function_version
-- ----------------------------
ALTER TABLE "lowcode"."sys_model_function_version" ADD CONSTRAINT "sys_model_function_version_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table sys_model_query
-- ----------------------------
ALTER TABLE "lowcode"."sys_model_query" ADD CONSTRAINT "sys_model_query_pkey" PRIMARY KEY ("query_id");

-- ----------------------------
-- Primary Key structure for table sys_model_query_param
-- ----------------------------
ALTER TABLE "lowcode"."sys_model_query_param" ADD CONSTRAINT "sys_model_query_param_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table sys_model_query_result
-- ----------------------------
ALTER TABLE "lowcode"."sys_model_query_result" ADD CONSTRAINT "sys_model_query_result_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table sys_model_report
-- ----------------------------
ALTER TABLE "lowcode"."sys_model_report" ADD CONSTRAINT "sys_model_report_pkey" PRIMARY KEY ("report_id");

-- ----------------------------
-- Primary Key structure for table sys_model_report_config
-- ----------------------------
ALTER TABLE "lowcode"."sys_model_report_config" ADD CONSTRAINT "sys_model_report_condition_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table sys_model_report_version
-- ----------------------------
ALTER TABLE "lowcode"."sys_model_report_version" ADD CONSTRAINT "sys_model_report_version_pkey" PRIMARY KEY ("report_id");

-- ----------------------------
-- Primary Key structure for table sys_model_table
-- ----------------------------
ALTER TABLE "lowcode"."sys_model_table" ADD CONSTRAINT "sys_model_table_pkey" PRIMARY KEY ("table_id");

-- ----------------------------
-- Primary Key structure for table sys_model_table_index
-- ----------------------------
ALTER TABLE "lowcode"."sys_model_table_index" ADD CONSTRAINT "sys_model_table_index_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table sys_project_api_env
-- ----------------------------
ALTER TABLE "lowcode"."sys_project_api_env" ADD CONSTRAINT "sys_project_api_env_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table sys_project_api_params
-- ----------------------------
ALTER TABLE "lowcode"."sys_project_api_params" ADD CONSTRAINT "sys_project_api_params_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table sys_project_comp
-- ----------------------------
ALTER TABLE "lowcode"."sys_project_comp" ADD CONSTRAINT "sys_project_comp_pkey" PRIMARY KEY ("comp_id");

-- ----------------------------
-- Primary Key structure for table sys_project_field_type
-- ----------------------------
ALTER TABLE "lowcode"."sys_project_field_type" ADD CONSTRAINT "sys_project_model_field_copy1_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table sys_project_login_auth
-- ----------------------------
ALTER TABLE "lowcode"."sys_project_login_auth" ADD CONSTRAINT "sys_project_login_auth_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table sys_project_model_field
-- ----------------------------
ALTER TABLE "lowcode"."sys_project_model_field" ADD CONSTRAINT "sys_project_model_field_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table sys_sql_log
-- ----------------------------
ALTER TABLE "lowcode"."sys_sql_log" ADD CONSTRAINT "sys_sql_log_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table sys_table_relation_conf
-- ----------------------------
ALTER TABLE "lowcode"."sys_table_relation_conf" ADD CONSTRAINT "sys_table_relation_conf_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table t_item
-- ----------------------------
CREATE INDEX "idx_user_id" ON "lowcode"."t_item" USING btree (
  "user_id" "pg_catalog"."int4_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table t_item
-- ----------------------------
ALTER TABLE "lowcode"."t_item" ADD CONSTRAINT "t_item_pkey" PRIMARY KEY ("item_id");

-- ----------------------------
-- Primary Key structure for table t_task_mind_detail
-- ----------------------------
ALTER TABLE "lowcode"."t_task_mind_detail" ADD CONSTRAINT "t_task_mind_detail_pkey" PRIMARY KEY ("task_id");

-- ----------------------------
-- Indexes structure for table u_do_it
-- ----------------------------
CREATE INDEX "idx_name11111" ON "lowcode"."u_do_it" USING btree (
  "name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
COMMENT ON INDEX "lowcode"."idx_name11111" IS 'test';

-- ----------------------------
-- Primary Key structure for table u_do_it
-- ----------------------------
ALTER TABLE "lowcode"."u_do_it" ADD CONSTRAINT "u_do_it_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table u_import_column_mapping
-- ----------------------------
ALTER TABLE "lowcode"."u_import_column_mapping" ADD CONSTRAINT "u_import_column_mapping_pkey" PRIMARY KEY ("column_mapping_id");

-- ----------------------------
-- Primary Key structure for table u_import_column_mapping_list
-- ----------------------------
ALTER TABLE "lowcode"."u_import_column_mapping_list" ADD CONSTRAINT "u_import_column_mapping_list_pkey" PRIMARY KEY ("mapping_list_id");

-- ----------------------------
-- Primary Key structure for table u_import_template_column
-- ----------------------------
ALTER TABLE "lowcode"."u_import_template_column" ADD CONSTRAINT "u_import_template_column_pkey" PRIMARY KEY ("template_table_id");

-- ----------------------------
-- Primary Key structure for table u_inbou
-- ----------------------------
ALTER TABLE "lowcode"."u_inbou" ADD CONSTRAINT "u_inbou_pkey" PRIMARY KEY ("inbound_id");

-- ----------------------------
-- Primary Key structure for table u_order
-- ----------------------------
ALTER TABLE "lowcode"."u_order" ADD CONSTRAINT "u_order_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table u_outbou
-- ----------------------------
ALTER TABLE "lowcode"."u_outbou" ADD CONSTRAINT "u_outbou_pkey" PRIMARY KEY ("outbound_id");

-- ----------------------------
-- Primary Key structure for table u_payment
-- ----------------------------
ALTER TABLE "lowcode"."u_payment" ADD CONSTRAINT "u_payment_pkey" PRIMARY KEY ("pay_id");

-- ----------------------------
-- Primary Key structure for table u_sale_quote_field
-- ----------------------------
ALTER TABLE "lowcode"."u_sale_quote_field" ADD CONSTRAINT "u_sale_quote_field_pkey" PRIMARY KEY ("sale_quote_field_id");

-- ----------------------------
-- Indexes structure for table u_test_01
-- ----------------------------
CREATE INDEX "idx_test111" ON "lowcode"."u_test_01" USING btree (
  "product_name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table u_test_01
-- ----------------------------
ALTER TABLE "lowcode"."u_test_01" ADD CONSTRAINT "u_test_01_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table u_test_demo
-- ----------------------------
ALTER TABLE "lowcode"."u_test_demo" ADD CONSTRAINT "u_test_demo_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table u_test_translation
-- ----------------------------
ALTER TABLE "lowcode"."u_test_translation" ADD CONSTRAINT "u_test_translation_pkey" PRIMARY KEY ("test_translation_id");

-- ----------------------------
-- Indexes structure for table u_testlistify
-- ----------------------------
CREATE INDEX "bbb" ON "lowcode"."u_testlistify" USING btree (
  "device_name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "ccc" ON "lowcode"."u_testlistify" USING btree (
  "name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE UNIQUE INDEX "ddd" ON "lowcode"."u_testlistify" USING btree (
  "device_name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "eee" ON "lowcode"."u_testlistify" USING btree (
  "spec_model" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table u_testlistify
-- ----------------------------
ALTER TABLE "lowcode"."u_testlistify" ADD CONSTRAINT "u_testlistify_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table u_testt
-- ----------------------------
CREATE INDEX "idx_device_code" ON "lowcode"."u_testt" USING btree (
  "device_code" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
COMMENT ON INDEX "lowcode"."idx_device_code" IS '编码索引';

-- ----------------------------
-- Primary Key structure for table u_testt
-- ----------------------------
ALTER TABLE "lowcode"."u_testt" ADD CONSTRAINT "u_testt_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table u_user
-- ----------------------------
ALTER TABLE "lowcode"."u_user" ADD CONSTRAINT "u_user_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table u_utility_config
-- ----------------------------
ALTER TABLE "lowcode"."u_utility_config" ADD CONSTRAINT "u_utility_config_pkey" PRIMARY KEY ("utility_config_id");





INSERT INTO "lowcode"."sys_project_comp" ("comp_id", "cmpt_list") VALUES (1, '[{"itemName":"组件类型","expanded":true,"children":[{"itemName":"表单组件","expanded":true,"children":[{"itemName":"cInput","children":[],"expanded":false,"selected":false,"partSelected":false,"sn":"0.0.0","level":3,"index":0,"dragging":false,"editing":false,"touched":false,"_selected":true,"_styles":{},"_dragging":false,"_editing":true,"_index":0,"_sn":"0.0.0","_level":3,"_touched":false,"_multipleLine":false},{"itemName":"c-select","children":[],"expanded":false,"selected":false,"partSelected":false,"sn":"0.0.1","level":3,"index":1,"dragging":false,"editing":false,"touched":false,"_selected":false,"_styles":{},"_dragging":false,"_editing":false,"_index":1,"_sn":"0.0.1","_level":3,"_touched":false},{"itemName":"c-itemtree","children":[],"expanded":false,"selected":false,"partSelected":false,"sn":"0.0.2","level":3,"index":2,"dragging":false,"editing":false,"touched":false,"_selected":false,"_styles":{},"_dragging":false,"_editing":false,"_index":2,"_sn":"0.0.2","_level":3,"_touched":false},{"itemName":"c-date-picker","children":[],"expanded":false,"selected":false,"partSelected":false,"sn":"0.0.3","level":3,"index":3,"dragging":false,"editing":false,"touched":false,"_selected":false,"_styles":{},"_dragging":false,"_editing":false,"_index":3,"_sn":"0.0.3","_level":3,"_touched":false},{"itemName":"c-month-picker","children":[],"expanded":false,"selected":false,"partSelected":false,"sn":"0.0.4","level":3,"index":4,"dragging":false,"editing":false,"touched":false,"_selected":false,"_styles":{},"_dragging":false,"_editing":false,"_index":4,"_sn":"0.0.4","_level":3,"_touched":false},{"itemName":"c-year-picker","children":[],"expanded":false,"selected":false,"partSelected":false,"sn":"0.0.5","level":3,"index":5,"dragging":false,"editing":false,"touched":false,"_selected":false,"_styles":{},"_dragging":false,"_editing":false,"_index":5,"_sn":"0.0.5","_level":3,"_touched":false},{"itemName":"c-dropdown + c-select","children":[],"expanded":false,"selected":false,"partSelected":false,"sn":"0.0.6","level":3,"index":6,"dragging":false,"editing":false,"touched":false,"_selected":false,"_styles":{},"_dragging":false,"_editing":false,"_index":6,"_sn":"0.0.6","_level":3,"_touched":false},{"itemName":"c-dropdown + c-itemtree","children":[],"expanded":false,"selected":false,"partSelected":false,"sn":"0.0.7","level":3,"index":7,"dragging":false,"editing":false,"touched":false,"_selected":false,"_styles":{},"_dragging":false,"_editing":false,"_index":7,"_sn":"0.0.7","_level":3,"_touched":false},{"itemName":"c-table","children":[],"expanded":false,"selected":false,"partSelected":false,"sn":"0.0.8","level":3,"index":8,"dragging":false,"editing":false,"touched":false,"_selected":false,"_styles":{},"_dragging":false,"_editing":false,"_index":8,"_sn":"0.0.8","_level":3,"_touched":false},{"itemName":"c-switch","children":[],"expanded":false,"selected":false,"partSelected":false,"sn":"0.0.9","level":3,"index":9,"dragging":false,"editing":false,"touched":false,"_selected":false,"_styles":{},"_dragging":false,"_editing":false,"_index":9,"_sn":"0.0.9","_level":3,"_touched":false},{"itemName":"c-button","children":[],"expanded":false,"selected":false,"partSelected":false,"sn":"0.0.10","level":3,"index":10,"dragging":false,"editing":false,"touched":false,"_selected":false,"_styles":{},"_dragging":false,"_editing":false,"_index":10,"_sn":"0.0.10","_level":3,"_touched":false},{"itemName":"c-popper","children":[],"expanded":false,"selected":false,"partSelected":false,"sn":"0.0.11","level":3,"index":11,"dragging":false,"editing":false,"touched":false,"_selected":false,"_styles":{},"_dragging":false,"_editing":false,"_index":11,"_sn":"0.0.11","_level":3,"_touched":false}],"selected":false,"partSelected":false,"sn":"0.0","level":2,"index":0,"dragging":false,"editing":false,"touched":false,"_selected":false,"_styles":{},"_dragging":false,"_editing":false,"_index":0,"_sn":"0.0","_level":2,"_expanded":true,"_touched":false},{"itemName":"自定义组件","expanded":true,"children":[{"itemName":"houtePicekr","remark":"","editing":false,"expanded":false,"selected":false,"children":[],"_itemId":null,"color":"dark","sn":"0.1.0","level":3,"index":0,"multipleLine":false,"touched":false,"dragging":false,"partSelected":false,"_selected":false,"_styles":{},"_dragging":false,"_editing":false,"_touched":false,"_index":0,"_sn":"0.1.0","_level":3},{"itemName":"supperPicker","remark":"","editing":false,"expanded":false,"selected":false,"children":[],"_itemId":null,"color":"dark","sn":"0.1.1","level":3,"index":1,"multipleLine":false,"partSelected":false,"dragging":false,"touched":false,"_selected":false,"_styles":{},"_dragging":false,"_editing":false,"_touched":false,"_index":1,"_sn":"0.1.1","_level":3},{"_selected":false,"_editing":false,"_expanded":false,"_styles":{},"children":[],"remark":"","itemName":"fieldPicker","_copied":false,"_itemId":null,"_index":2,"_sn":"0.1.2","_level":3,"_touched":false,"_dragging":false,"expanded":false,"selected":false,"partSelected":false,"sn":"0.1.2","level":3,"index":2,"dragging":false,"editing":false},{"_selected":false,"_editing":false,"_expanded":false,"_styles":{},"children":[],"remark":"","itemName":"orgPicker","_copied":false,"_itemId":null,"_level":3,"_index":3,"_dragging":false,"_touched":false,"_sn":"0.1.3","expanded":false,"selected":false,"partSelected":false,"sn":"0.1.3","level":3,"index":3,"dragging":false,"editing":false},{"itemName":"re","remark":"","_editing":false,"_expanded":false,"_selected":false,"_creating":false,"children":[],"_styles":{},"_itemId":null,"_index":4,"_sn":"0.1.4","_level":3,"_multipleLine":false,"_dragging":false,"_touched":false,"expanded":false,"selected":false,"partSelected":false,"sn":"0.1.4","level":3,"index":4,"dragging":false,"editing":false}],"selected":false,"partSelected":false,"sn":"0.1","level":2,"index":1,"dragging":false,"editing":false,"touched":false,"_selected":false,"_styles":{},"_dragging":false,"_editing":false,"_index":1,"_sn":"0.1","_level":2,"_touched":false,"_expanded":true,"_copied":false}],"selected":false,"partSelected":false,"startIndex":null,"sn":"0","level":1,"index":0,"dragging":false,"editing":false,"touched":false,"_selected":false,"_styles":{},"_index":0,"_sn":"0","_level":1,"_dragging":false,"_editing":false,"_expanded":true,"_touched":false,"_startIndex":null}]');
INSERT INTO "lowcode"."sys_project_field_type" ("id", "project_id", "field_type_list", "db_type", "created_by", "created_dt", "updated_by", "updated_dt") VALUES (1, 0, '[{"des": "1 字节的整数，范围：-128 到 127", "name": "TINYINT", "type": "TINYINT", "isBase": 1, "selected": 1, "allowEditLeng": false, "allowEditPrecision": false}, {"des": "2 字节的整数，范围：-32,768 到 32,767", "name": "SMALLINT", "type": "SMALLINT", "isBase": 1, "selected": 1, "allowEditLeng": false, "allowEditPrecision": false}, {"des": "3 字节的整数，范围：-8,388,608 到 8,388,607", "name": "MEDIUMINT", "type": "MEDIUMINT", "isBase": 1, "selected": 1, "allowEditLeng": false, "allowEditPrecision": false}, {"des": "4 字节的整数，范围：-2,147,483,648 到 2,147,483,647", "name": "INT", "type": "INT", "isBase": 1, "selected": 1, "allowEditLeng": false, "allowEditPrecision": false}, {"des": "8 字节的整数，范围：-9,223,372,036,854,775,808 到 9,223,372,036,854,775,807", "name": "BIGINT", "type": "BIGINT", "isBase": 1, "selected": 1, "allowEditLeng": false, "allowEditPrecision": false}, {"des": "单精度浮点数", "name": "FLOAT", "type": "FLOAT", "isBase": 1, "selected": 1, "defaultLeng": 12, "allowEditLeng": false, "defaultPrecision": 2, "allowEditPrecision": true}, {"des": "双精度浮点数", "name": "DOUBLE", "type": "DOUBLE", "isBase": 1, "selected": 1, "defaultLeng": 18, "allowEditLeng": false, "defaultPrecision": 2, "allowEditPrecision": true}, {"des": "定点数，指定总位数和小数位数", "name": "DECIMAL", "type": "DECIMAL", "isBase": 1, "selected": 1, "defaultLeng": 18, "allowEditLeng": true, "defaultPrecision": 2, "allowEditPrecision": true}, {"des": "可变长度字符串", "name": "VARCHAR", "type": "VARCHAR", "isBase": 1, "selected": 1, "defaultLeng": 50, "allowEditLeng": true, "allowEditPrecision": false}, {"des": "固定长度字符串", "name": "CHAR", "type": "CHAR", "isBase": 1, "selected": 1, "defaultLeng": 5, "allowEditLeng": true, "allowEditPrecision": false}, {"des": "用于存储长文本", "name": "TEXT", "type": "TEXT", "isBase": 1, "selected": 1, "defaultLeng": 0, "allowEditLeng": true, "defaultPrecision": 0, "allowEditPrecision": false}, {"des": "日期", "name": "DATE", "type": "DATE", "isBase": 1, "selected": 1, "allowEditLeng": false, "allowEditPrecision": false}, {"des": "日期时间", "name": "DATETIME", "type": "DATETIME", "isBase": 1, "selected": 1, "allowEditLeng": false, "allowEditPrecision": false}, {"des": "时间戳", "name": "TIMESTAMP", "type": "TIMESTAMP", "isBase": 1, "selected": 1, "allowEditLeng": false, "allowEditPrecision": false}]', 'mysql', '系统', '2025-04-01 16:10:12', 'admin/管理员', '2025-04-03 17:05:43.980511');
INSERT INTO "lowcode"."sys_project_field_type" ("id", "project_id", "field_type_list", "db_type", "created_by", "created_dt", "updated_by", "updated_dt") VALUES (2, 0, '[{"des": "2 字节的整数", "name": "INT2", "type": "INT2", "isBase": 1, "selected": 1, "defaultLeng": 0, "allowEditLeng": false, "defaultPrecision": 0, "allowEditPrecision": false}, {"des": "4 字节的整数", "name": "INT4", "type": "INT4", "isBase": 1, "selected": 1, "defaultLeng": 0, "allowEditLeng": false, "defaultPrecision": 0, "allowEditPrecision": false}, {"des": "8 字节的整数", "name": "INT8", "type": "INT8", "isBase": 1, "selected": 1, "defaultLeng": 19, "allowEditLeng": false, "defaultPrecision": 0, "allowEditPrecision": false}, {"des": "布尔类型", "name": "BOOL", "type": "BOOL", "isBase": 1, "selected": 1, "defaultLeng": 1, "allowEditLeng": false, "defaultPrecision": 0, "allowEditPrecision": false}, {"des": "带精度的数字", "name": "NUMERIC", "type": "NUMERIC", "isBase": 1, "selected": 1, "defaultLeng": 18, "allowEditLeng": false, "defaultPrecision": 2, "allowEditPrecision": false}, {"des": "固定长度字符串", "name": "CHAR", "type": "CHAR", "isBase": 1, "selected": 1, "defaultLeng": 10, "allowEditLeng": true, "defaultPrecision": 0, "allowEditPrecision": false}, {"des": "可变长度字符串", "name": "VARCHAR", "type": "VARCHAR", "isBase": 1, "selected": 1, "defaultLeng": 50, "allowEditLeng": true, "defaultPrecision": 0, "allowEditPrecision": false}, {"des": "富文本", "name": "TEXT", "type": "TEXT", "isBase": 1, "selected": 1, "defaultLeng": 0, "allowEditLeng": true, "defaultPrecision": 0, "allowEditPrecision": false}, {"des": "日期", "name": "DATE", "type": "DATE", "isBase": 1, "selected": 1, "allowEditLeng": false, "allowEditPrecision": false}, {"des": "日期时间", "name": "DATETIME", "type": "DATETIME", "isBase": 1, "selected": 0, "allowEditLeng": false, "allowEditPrecision": false}, {"des": "日期时间", "name": "TIMESTAMP", "type": "TIMESTAMP", "isBase": 1, "selected": 1, "allowEditLeng": false, "allowEditPrecision": false}, {"des": "自增整数，通常用于主键", "name": "SERIAL8", "type": "SERIAL8", "isBase": 1, "selected": 0, "defaultLeng": 0, "allowEditLeng": false, "defaultPrecision": 0, "allowEditPrecision": false}, {"des": "自增整数4字节", "name": "SERIAL4", "type": "SERIAL4", "isBase": 1, "selected": 0, "defaultLeng": 0, "allowEditLeng": false, "defaultPrecision": 0, "allowEditPrecision": false}, {"des": "自增整数2字节", "name": "SERIAL2", "type": "SERIAL2", "isBase": 1, "selected": 0, "defaultLeng": 0, "allowEditLeng": false, "defaultPrecision": 0, "allowEditPrecision": false}, {"des": "2 字节的整数集合", "name": "_INT2", "type": "_INT2", "isBase": 1, "selected": 1, "defaultLeng": 0, "allowEditLeng": false, "defaultPrecision": 0, "allowEditPrecision": false}, {"des": "4 字节的整数集合", "name": "_INT4", "type": "_INT4", "isBase": 1, "selected": 1, "defaultLeng": 0, "allowEditLeng": false, "defaultPrecision": 0, "allowEditPrecision": false}, {"des": "8 字节的整数集合", "name": "_INT8", "type": "_INT8", "isBase": 1, "selected": 1, "defaultLeng": 0, "allowEditLeng": false, "defaultPrecision": 0, "allowEditPrecision": false}, {"des": "单精度浮点数", "name": "REAL", "type": "REAL", "isBase": 1, "selected": 0, "allowEditLeng": false, "allowEditPrecision": true}, {"des": "双精度浮点数", "name": "DOUBLE PRECISION", "type": "DOUBLE PRECISION", "isBase": 1, "selected": 0, "allowEditLeng": false, "allowEditPrecision": true}, {"des": "JSON", "name": "JSON", "type": "JSON", "isBase": 1, "selected": 0, "defaultLeng": 0, "allowEditLeng": false, "defaultPrecision": 0, "allowEditPrecision": false}, {"des": "JSONB", "name": "JSONB", "type": "JSONB", "isBase": 1, "selected": 1, "defaultLeng": 0, "allowEditLeng": false, "defaultPrecision": 0, "allowEditPrecision": false}]', 'postgresql', '系统', '2025-04-01 16:15:31', 'admin/管理员', '2025-04-03 17:10:33.525813');
INSERT INTO "lowcode"."s_auth_user" ("user_id", "user_name", "user_account", "user_password", "phone_number", "email", "remark", "is_admin", "enable", "deleted", "created_by", "created_dt", "updated_by", "updated_dt", "tenant_id", "login_status", "last_login_error_time", "login_error_count", "data_role_id", "data_role_name") VALUES (1, '管理员', 'admin', '$2a$10$h15fcuI6JihSShqXMGy8VOjGzJ5UsJMdy7qilUSNFB9gp1B0Mc1zy', '***********', NULL, NULL, 1, 1, 0, 'admin1/管理员-租户1', '2024-08-07 15:05:00.762', NULL, '2024-09-20 17:28:37.383', NULL, 0, '2025-06-20 14:18:11.731588', 0, NULL, '');
