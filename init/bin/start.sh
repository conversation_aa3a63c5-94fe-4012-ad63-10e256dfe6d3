#!/bin/bash

########## 开启java服务  ############

# 检查是否提供了两个命令行参数
if [ $# -ne 2 ]; then
    echo "需传两2个命令行参数,  1：端口，2：环境"
    echo "用法样例: ./start.sh <9000> <test>"
    exit 1
fi

##### 动态参数  #######
# 端口，从终端传过来的
PORT=$1

# 环境，从终端传过来的
PROFILES_ACTIVE=$2

##### 动态参数  #######


# 当前路径，默认会获取
BASE_PATH=''

# jar包名，默认会获取
JAR_NAME=''

# JVM 参数
JAVA_OPS="-Xms512m -Xmx512m"

# 主机地址和端口号
host="localhost"  # 主机地址，默认本地
port=$PORT        # 端口号从java传过来的

max_attempts=60  # 最大尝试次数，校验端口占用的
sleep_time=1     # 每次尝试之间的等待时间，校验端口占用的

# 获取当前shell脚本的绝对路径
function init_abs_path() {

    BASE_PATH=$(dirname "$(realpath "$0")")

    # 如果有系统不兼容上面命令，则可以切换这个
    # BASE_PATH=$(dirname "$(readlink -f "$0")")

    cd $BASE_PATH
    echo "[auto_abs_path] 进入绝对路径: $BASE_PATH"

}

# 获取当前路径下的jar包
function init_jar_name() {
  echo "[init_jar_name] 查询当前路径下的jar包"
  # 获取当前路径下的jar包
  jar_files=( $(find . -maxdepth 1 -name "*.jar" -type f) )

  # 获取当前路径下的文件包
  if [[ ${#jar_files[@]} -gt 0 ]]; then
      for jar in "${jar_files[@]}"; do
          JAR_NAME=$jar
      done
      echo "[init_jar_name] 已找到jar包：$JAR_NAME"
  else
      echo "[init_jar_name] 未找到jar包，请先上传jar包"
      exit 1 # 未找到包，直接退出
  fi
}

# 检查端口是否已被占用
function check_port() {
    nc -z localhost "$port"
}

# 检测服务是否启动
function check_server() {
  echo "[check_server] 检测服务 $port"
  for ((attempts=0; attempts<max_attempts; attempts++)); do
      if check_port; then
          echo "服务启动成功"
          exit 0
      else
          echo "等待服务启动中... (尝试 $((attempts+1))/$max_attempts)"
          sleep "$sleep_time"
      fi
  done

  echo "服务启动失败"
  exit 1
}

# 开启服务
function start() {

  echo "[start] 检测端口 $port"

  if check_port; then
      echo "端口 $port 已被占用，无法启动服务"
      exit 1
  fi

  # 打印启动参数
  echo "[start] 开始启动: $BASE_PATH/$JAR_NAME"
  echo "[start] 启动参数: $JAVA_OPS"
  echo "[start] 启动环境: $PROFILES_ACTIVE"

  # 启动应用
  nohup java $JAVA_OPS -Dspring.profiles.active=$PROFILES_ACTIVE -Dserver.port=$PORT -jar $BASE_PATH/$JAR_NAME > nohup.out 2>&1 &

}

# 入口
function main() {

  # 获取当前shell脚本的绝对路径
  init_abs_path

  # 获取当前路径下的jar名
  init_jar_name

  # 执行启动命令
  start

  # 检测服务是否启动成功
  check_server

}


main



