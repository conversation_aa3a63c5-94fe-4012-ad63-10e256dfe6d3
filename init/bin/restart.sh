#!/bin/bash

########## 停止和启动 JAVA 服务 ##########

export SDKMAN_DIR="$HOME/.sdkman"
[[ -s "$SDKMAN_DIR/bin/sdkman-init.sh" ]] && source "$SDKMAN_DIR/bin/sdkman-init.sh"

# 当前路径，默认会获取
BASE_PATH=''

# jar包名，默认会获取
JAR_NAME=''

# JVM 参数
JAVA_OPS="-Xms512m -Xmx1024m"

# 主机地址和端口号
host="localhost"  # 主机地址，默认本地
PORT=8018         # 端口号从 java 传过来的
PROFILES_ACTIVE=dev  # 环境

max_attempts=60  # 最大尝试次数，校验端口占用的
sleep_time=1     # 每次尝试之间的等待时间，校验端口占用的

# 获取当前shell脚本的绝对路径
function init_abs_path() {
    BASE_PATH=$(dirname "$(realpath "$0")")

    # 如果有系统不兼容上面命令，则可以切换这个
    # BASE_PATH=$(dirname "$(readlink -f "$0")")

    cd "$BASE_PATH"
    echo "[auto_abs_path] 进入绝对路径: $BASE_PATH"
}

# 获取当前路径下的jar包
function init_jar_name() {
    echo "[init_jar_name] 查询当前路径下的jar包"
    jar_files=( $(find . -maxdepth 1 -name "*.jar" -type f) )

    if [[ ${#jar_files[@]} -gt 0 ]]; then
        JAR_NAME=${jar_files[0]}  # 只使用第一个找到的 JAR 文件
        echo "[init_jar_name] 已找到jar包：$JAR_NAME"
    else
        echo "[init_jar_name] 未找到jar包，请先上传jar包"
        exit 1 # 未找到包，直接退出
    fi
}

# 停止服务
function stop() {
    echo "[stop] 开始关闭: $JAR_NAME"
    local PID
    PID=$(pgrep -f "$JAR_NAME")  # 使用 pgrep 简化查找

    if [ -n "$PID" ]; then
        kill -TERM "$PID"
        echo "[stop] 关闭中: $JAR_NAME (pid:$PID)"
        while kill -0 "$PID" &>/dev/null; do
            sleep 1
        done
        echo "[stop] 关闭成功: $JAR_NAME"
    else
        echo "[stop] 关闭失败，未启动: $JAR_NAME"
    fi
}

# 检查端口是否已被占用
function check_port() {
    nc -z localhost "$PORT"
}

# 检测服务是否启动
function check_server() {
    echo "[check_server] 检测服务 $PORT"
    for ((attempts=0; attempts<max_attempts; attempts++)); do
        if check_port; then
            echo "服务启动成功"
            return 0
        else
            echo "等待服务启动中... (尝试 $((attempts+1))/$max_attempts)"
            sleep "$sleep_time"
        fi
    done

    echo "服务启动失败"
    return 1
}

# 开启服务
function start() {
    echo "[start] 检测端口 $PORT"

    if check_port; then
        echo "端口 $PORT 已被占用，无法启动服务"
        exit 1
    fi

    # 打印启动参数
    echo "[start] 开始启动: $BASE_PATH/$JAR_NAME"
    echo "[start] 启动参数: $JAVA_OPS"
    echo "[start] 启动环境: $PROFILES_ACTIVE"

    # 临时切换到 JDK 17
    sdk use java 17.0.14-zulu || { echo "切换到 JDK 17 失败"; exit 1; }

    # 启动应用
    nohup java $JAVA_OPS -Dloader.path=lib -Dspring.profiles.active=$PROFILES_ACTIVE -Dserver.port=$PORT -jar "$BASE_PATH/$JAR_NAME" > nohup.out 2>&1 &

    echo "[start] 应用已启动，查看日志请使用 tail -f nohup.out"
}


# 入口
function main() {
    # 获取当前shell脚本的绝对路径，并进入
    init_abs_path

    # 获取当前路径下的jar包
    init_jar_name

    # 执行停止命令
    stop

    # 执行启动命令
    start

    # 检测服务是否启动成功
    check_server
}

main

