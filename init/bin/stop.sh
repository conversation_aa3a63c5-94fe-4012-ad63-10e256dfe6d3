#!/bin/bash

########## 停止java服务  ##########

# jar包名，默认会获取
JAR_NAME=''

# 获取当前shell脚本的绝对路径，并进入
function init_abs_path() {

    BASE_PATH=$(dirname "$(realpath "$0")")

    # 如果有系统不兼容上面命令，则可以切换这个
    # BASE_PATH=$(dirname "$(readlink -f "$0")")

    cd $BASE_PATH
    echo "[auto_abs_path] 进入绝对路径: $BASE_PATH"
}

# 获取当前路径下的jar包
function init_jar_name() {
  echo "[init_jar_name] 查询当前路径下的jar包"
  # 获取当前路径下的jar包
  jar_files=( $(find . -maxdepth 1 -name "*.jar" -type f) )

  # 获取当前路径下的文件包
  if [[ ${#jar_files[@]} -gt 0 ]]; then
      for jar in "${jar_files[@]}"; do
          JAR_NAME=$jar
      done
      echo "[init_jar_name] 已找到jar包：$JAR_NAME"
  else
      echo "[init_jar_name] 未找到jar包，请先上传jar包"
      exit 1 # 未找到包，直接退出
  fi
}

# 停止服务
function stop() {
  echo "[stop] 开始关闭: $JAR_NAME"
	PID=""
	query(){
		PID=`ps -ef |grep java|grep $JAR_NAME|grep -v grep|awk '{print $2}'`
	}
	query
	if [ x"$PID" != x"" ]; then
		kill -TERM $PID
		echo "[stop] 关闭中: $JAR_NAME (pid:$PID)"
		while [ x"$PID" != x"" ]
		do
			sleep 1
			query
		done
		echo "[stop] 关闭成功: $JAR_NAME"
	else
		echo "[stop] 关闭失败，未启动: $JAR_NAME"
	fi
}

# 入口
function main() {

  # 获取当前shell脚本的绝对路径，并进入
  init_abs_path

  # 获取当前路径下的jar包
  init_jar_name

  # 执行停止命令
  stop

}

main



