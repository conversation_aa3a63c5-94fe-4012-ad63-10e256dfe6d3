<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>
    <groupId>com.mongoso.cloud</groupId>

    <artifactId>zzy-lowcode</artifactId>
    <version>1.0.0</version>
    <description>
        总项目包
    </description>

    <properties>
        <!-- mgs-微服务 -->
        <mongoso-cloud-starter.version>1.0.4-jdk17</mongoso-cloud-starter.version>
        <!--        <mongoso-cloud-starter.version>1.2.7-common</mongoso-cloud-starter.version>-->
        <!--        <mongoso-cloud-starter.version>1.2.7-platform</mongoso-cloud-starter.version>-->

        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>

        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

    </properties>

    <dependencyManagement>
        <dependencies>

            <!-- mgs-微服务统一版本管理 -->
            <dependency>
                <groupId>com.mongoso.cloud</groupId>
                <artifactId>mgs-dependencies</artifactId>
                <version>${mongoso-cloud-starter.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- 微服务starter -->
            <dependency>
                <groupId>com.mongoso.cloud</groupId>
                <artifactId>mgs-cloud-starter</artifactId>
                <version>${mongoso-cloud-starter.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <dependencies>

        <!-- 参数校验 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>4.0.3</version>
        </dependency>

        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <version>1.15</version> <!-- 请根据需要选择版本 -->
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot3-starter</artifactId>
        </dependency>

        <!--            <dependency>-->
        <!--                <groupId>org.springframework.boot</groupId>-->
        <!--                <artifactId>spring-boot-starter-web</artifactId>-->
        <!--            </dependency>-->

        <!--            &lt;!&ndash;参数校验&ndash;&gt;-->
        <!--            <dependency>-->
        <!--                <groupId>org.springframework.boot</groupId>-->
        <!--                <artifactId>spring-boot-starter-validation</artifactId>-->
        <!--            </dependency>-->

        <!--        <dependency>-->
        <!--            <groupId>com.alibaba</groupId>-->
        <!--            <artifactId>druid</artifactId>-->
        <!--&lt;!&ndash;            <version>1.1.21</version>&ndash;&gt;-->
        <!--        </dependency>-->

        <!-- mysql驱动 -->
        <!--            <dependency>-->
        <!--                <groupId>mysql</groupId>-->
        <!--                <artifactId>mysql-connector-java</artifactId>-->
        <!--                <version>8.0.17</version>-->
        <!--            </dependency>-->
        <!-- oracle驱动 -->
        <!--        <dependency>-->
        <!--            <groupId>com.oracle</groupId>-->
        <!--            <artifactId>ojdbc6</artifactId>-->
        <!--            <version>11.2.0.3</version>-->
        <!--        </dependency>-->
        <!-- sqlserver驱动 -->
        <!--        <dependency>-->
        <!--            <groupId>com.microsoft.sqlserver</groupId>-->
        <!--            <artifactId>sqljdbc4</artifactId>-->
        <!--            <version>4.0</version>-->
        <!--        </dependency>-->
        <!-- postgresql驱动 -->
        <!--            <dependency>-->
        <!--                <groupId>org.postgresql</groupId>-->
        <!--                <artifactId>postgresql</artifactId>-->
        <!--            </dependency>-->

        <!--            <dependency>-->
        <!--                <groupId>org.springframework.boot</groupId>-->
        <!--                <artifactId>spring-boot-starter-data-elasticsearch</artifactId>-->
        <!--                <version>3.3.5</version>-->
        <!--            </dependency>-->
        <!--            <dependency>-->
        <!--                <groupId>org.elasticsearch.client</groupId>-->
        <!--                <artifactId>elasticsearch-rest-high-level-client</artifactId>-->
        <!--                <version>7.17.25</version>-->
        <!--            </dependency>-->
        <dependency>
            <groupId>cn.smallbun.screw</groupId>
            <artifactId>screw-core</artifactId>
            <version>1.0.5</version>
        </dependency>

        <!--        <dependency>-->
        <!--            <groupId>com.alibaba</groupId>-->
        <!--            <artifactId>easyexcel</artifactId>-->
        <!--            <version>3.2.0</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>com.google.guava</groupId>-->
        <!--            <artifactId>guava</artifactId>-->
        <!--            <version>31.1-jre</version>-->
        <!--        </dependency>-->

        <!--            <dependency>-->
        <!--                <groupId>com.baomidou</groupId>-->
        <!--                <artifactId>mybatis-plus-boot-starter</artifactId>-->
        <!--                <version>3.5.3.1</version>-->
        <!--            </dependency>-->
        <!--            <dependency>-->
        <!--                <groupId>com.baomidou</groupId>-->
        <!--                <artifactId>dynamic-datasource-spring-boot-starter</artifactId>-->
        <!--                <version>4.3.0</version>-->
        <!--            </dependency>-->
        <!--            <dependency>-->
        <!--                <groupId>com.baomidou</groupId>-->
        <!--                <artifactId>mybatis-plus-extension</artifactId>-->
        <!--                <version>3.5.8</version>-->
        <!--            </dependency>-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId>
            <version>3.5.8</version>
        </dependency>

        <dependency>
            <groupId>org.apache.velocity</groupId>
            <artifactId>velocity-engine-core</artifactId> <!-- 实现代码生成 -->
            <version>2.3</version>
        </dependency>

        <!--        <dependency>-->
        <!--            <groupId>org.springframework.boot</groupId>-->
        <!--            <artifactId>spring-boot-starter-data-elasticsearch</artifactId>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>org.elasticsearch.client</groupId>-->
        <!--            <artifactId>elasticsearch-rest-high-level-client</artifactId>-->
        <!--        </dependency>-->

        <!-- 微服务starter -->
        <dependency>
            <groupId>com.mongoso.cloud</groupId>
            <artifactId>mgs-cloud-starter</artifactId>
            <version>${mongoso-cloud-starter.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba.cloud</groupId>
                    <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.cloud</groupId>
                    <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-starter-bootstrap</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-starter-loadbalancer</artifactId>
                </exclusion>
            </exclusions>
            <scope>provided</scope>
        </dependency>

        <!-- Flowable工作流引擎 -->
        <dependency>
            <groupId>org.flowable</groupId>
            <artifactId>flowable-spring-boot-starter</artifactId>
            <version>7.0.1</version>
        </dependency>

        <!-- Flowable UI相关依赖（可选，用于流程设计器） -->
<!--        <dependency>-->
<!--            <groupId>org.flowable</groupId>-->
<!--            <artifactId>flowable-spring-boot-starter-ui-modeler</artifactId>-->
<!--            <version>7.0.1</version>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>org.flowable</groupId>-->
<!--            <artifactId>flowable-spring-boot-starter-ui-admin</artifactId>-->
<!--            <version>7.0.1</version>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>org.flowable</groupId>-->
<!--            <artifactId>flowable-spring-boot-starter-ui-task</artifactId>-->
<!--            <version>7.0.1</version>-->
<!--        </dependency>-->

        <!-- Drools规则引擎 -->
        <dependency>
            <groupId>org.drools</groupId>
            <artifactId>drools-core</artifactId>
            <version>8.44.0.Final</version>
        </dependency>
        <dependency>
            <groupId>org.drools</groupId>
            <artifactId>drools-compiler</artifactId>
            <version>8.44.0.Final</version>
        </dependency>
        <dependency>
            <groupId>org.drools</groupId>
            <artifactId>drools-mvel</artifactId>
            <version>8.44.0.Final</version>
        </dependency>
        <dependency>
            <groupId>org.drools</groupId>
            <artifactId>drools-decisiontables</artifactId>
            <version>8.44.0.Final</version>
        </dependency>

    </dependencies>

    <build>
        <!--包名-->
        <!--    <finalName>mgs-lowcode2</finalName>-->
        <plugins>
<!--                这是作为jar包嵌入sass的打包需要排除一些类-->
<!--        <plugin>-->
<!--            <groupId>org.apache.maven.plugins</groupId>-->
<!--            <artifactId>maven-jar-plugin</artifactId>-->
<!--            <version>3.2.0</version>-->
<!--            <configuration>-->
<!--                <excludes>-->
<!--                    <exclude>com/mongoso/mgs/SystemApplication.class</exclude> &lt;!&ndash; 替换为您的启动类路径 &ndash;&gt;-->
<!--                    <exclude>com/mongoso/mgs/framework/**</exclude>-->
<!--                    <exclude>com/mongoso/mgs/common/security/**</exclude>-->
<!--                    <exclude>admin-ui/**</exclude>-->
<!--                </excludes>-->
<!--            </configuration>-->
<!--        </plugin>-->
<!--             Spring Boot Maven Plugin -->

<!--             独立作为jar项目运行 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>3.2.2</version> <!-- 如果 spring.boot.version 版本修改，则这里也要跟着修改 -->
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal> <!-- 将引入的 jar 打入其中 -->
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!--            解决上传文件的问题-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.13.0</version>
                <configuration>
                    <source>17</source>
                    <target>17</target>
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                </configuration>
            </plugin>
            <!--    提取lib -->
<!--            <plugin>-->
<!--                <groupId>org.springframework.boot</groupId>-->
<!--                <artifactId>spring-boot-maven-plugin</artifactId>-->
<!--                <version>3.2.2</version> &lt;!&ndash; 如果 spring.boot.version 版本修改，则这里也要跟着修改 &ndash;&gt;-->
<!--                <configuration>-->
<!--                    <mainClass>com.mongoso.mgs.SystemApplication</mainClass> &lt;!&ndash; 替换为你的主类全名 &ndash;&gt;-->
<!--                    <layout>ZIP</layout>-->
<!--&lt;!&ndash;               先注释打包，解压出lib，再排除掉lib&ndash;&gt;-->
<!--                    <includes>-->
<!--                        <include>-->
<!--                          <groupId>nothing</groupId>-->
<!--                          <artifactId>nothing</artifactId>-->
<!--                        </include>-->
<!--                   </includes>-->
<!--                </configuration>-->
<!--                <executions>-->
<!--                    <execution>-->
<!--                        <goals>-->
<!--                            <goal>repackage</goal> &lt;!&ndash; 将引入的 jar 打入其中 &ndash;&gt;-->
<!--                        </goals>-->
<!--                    </execution>-->
<!--                </executions>-->
<!--            </plugin>-->

            <!--升级spring boot之后无法访问通用动态接口-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.12.0</version>
                <configuration>
                    <parameters>true</parameters>
                </configuration>
            </plugin>

        </plugins>
    </build>

    <!-- 上传maven私服-->
    <distributionManagement>
        <repository>
            <id>mongoso-releases</id>
            <url>http://maven.mongoso.com:8081/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>mongoso-snapshots</id>
            <url>http://maven.mongoso.com:8081/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

</project>